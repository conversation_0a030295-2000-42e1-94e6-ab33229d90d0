export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  preferences: {
    categories: string[];
    budget: "low" | "medium" | "high";
    travelStyle: string[];
  };
}

export interface Destination {
  id: string;
  name: string;
  location: string;
  description: string;
  imageUrl: string;
  rating: number;
  price: number;
  duration: string;
  tags: string[];
  highlights: string[];
  included: string[];
  notIncluded: string[];
}

export interface Event {
  id: string;
  title: string;
  location: string;
  date: string;
  time: string;
  imageUrl: string;
  price: number;
  category: string;
  description: string;
}

export interface Booking {
  id: string;
  type: "flight" | "hotel" | "experience" | "insurance" | "transport" | "tour" | "accommodation" | "event";
  title: string;
  status: "confirmed" | "pending" | "cancelled" | "upcoming" | "completed";
  date: string;
  price: number;
  imageUrl: string;
  confirmationCode: string;
  details?: any;
}

export interface Insurance {
  id: string;
  name: string;
  provider: string;
  coverage: string[];
  price: number;
  duration: string;
  description: string;
}

export interface FlightBooking {
  id: string;
  airline: string;
  flightNumber: string;
  departure: {
    airport: string;
    city: string;
    time: string;
    date: string;
  };
  arrival: {
    airport: string;
    city: string;
    time: string;
    date: string;
  };
  passenger: {
    name: string;
    email: string;
    phone: string;
  };
  seat: string;
  class: string;
  price: number;
  bookingReference: string;
}

export interface Car {
  id: string;
  name: string;
  category: string;
  imageUrl: string;
  pricePerDay: number;
  rating: number;
  seats: number;
  fuelType: string;
  transmission: string;
  location: string;
  available: boolean;
  features: string[];
}