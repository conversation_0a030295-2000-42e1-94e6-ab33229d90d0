import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, ScrollView, FlatList, Pressable, Dimensions } from 'react-native';
import { useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, Filter, ArrowUpRight } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';
import { cars, carCategories, Car } from '@/mocks/cars';
import { CarCard } from '@/components/CarCard';
import { CategoryPill } from '@/components/CategoryPill';

const { width: screenWidth } = Dimensions.get('window');

export default function CarRentalScreen() {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [filteredCars, setFilteredCars] = useState(cars);

  useEffect(() => {
    if (selectedCategory === 'All') {
      setFilteredCars(cars);
    } else {
      setFilteredCars(
        cars.filter((car) => car.category === selectedCategory)
      );
    }
  }, [selectedCategory]);

  const handleCarPress = (car: Car) => {
    router.push(`/cars/${car.id}`);
  };

  const handleBackPress = () => {
    router.back();
  };

  const handleFilterPress = () => {
    // Handle filter press
    console.log('Filter pressed');
  };

  const getCategoryCount = (category: string) => {
    if (category === 'All') return cars.length;
    return cars.filter((car) => car.category === category).length;
  };

  const renderCategorySection = (category: string, categoryTitle: string) => {
    const categoryCars = cars.filter((car) => car.category === category);
    
    if (categoryCars.length === 0) return null;

    return (
      <View key={category} style={styles.categorySection}>
        <View style={styles.categoryHeader}>
          <Text style={styles.categoryTitle}>{categoryTitle}</Text>
          <Pressable style={styles.viewAllButton}>
            <Text style={styles.viewAllText}>View all</Text>
            <ArrowUpRight size={16} color={colors.accent} />
          </Pressable>
        </View>
        
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.horizontalScrollContent}
          decelerationRate="fast"
          snapToInterval={screenWidth * 0.85 + spacing.lg}
          snapToAlignment="start"
          pagingEnabled={false}
        >
          {categoryCars.map((car, index) => (
            <View key={car.id} style={[styles.largeCarCard, index === 0 && styles.firstCard]}>
              <CarCard car={car} onPress={handleCarPress} isLarge />
            </View>
          ))}
        </ScrollView>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      {/* Header */}
      <View style={styles.header}>
        <Pressable style={styles.backButton} onPress={handleBackPress}>
          <ArrowLeft size={24} color={colors.accent} />
        </Pressable>
        <Text style={styles.headerTitle}>Car rent</Text>
        <Pressable style={styles.filterButton} onPress={handleFilterPress}>
          <Text style={styles.filterText}>Filters</Text>
        </Pressable>
      </View>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.content}>
        {renderCategorySection('SUV', 'SUV')}
        {renderCategorySection('Hatchback', 'Hatchback')}
        {renderCategorySection('Sedan', 'Sedan')}
        {renderCategorySection('Luxury', 'Luxury')}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  backButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 22,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    letterSpacing: -0.5,
  },
  filterButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.accent,
    borderRadius: 20,
  },
  filterText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: colors.white,
  },
  content: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    paddingTop: spacing.xl,
  },
  categorySection: {
    marginBottom: spacing.xxl + spacing.lg,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xl,
    paddingHorizontal: spacing.lg,
  },
  categoryTitle: {
    fontSize: 28,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    letterSpacing: -0.5,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },
  viewAllText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.accent,
  },
  horizontalScrollContent: {
    paddingLeft: spacing.lg,
    paddingRight: spacing.xl,
  },
  largeCarCard: {
    width: screenWidth * 0.85,
    marginRight: spacing.lg,
  },
  firstCard: {
    marginLeft: 0,
  },
});