import React, { useState, useRef } from "react";
import { StyleSheet, View, Text, ScrollView, FlatList, Pressable, Dimensions, Platform, RefreshControl } from "react-native";
import { useRouter } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { MapPin, Navigation, Compass, Info, Camera, Scan, Zap, Globe, Star, Clock, Users, Award, ChevronRight, Play, Sparkles, Eye, Bell, Layers, Headphones, Shield, Wifi, Smartphone, TrendingUp, Target } from "lucide-react-native";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { SearchBar } from "@/components/SearchBar";
import { Button } from "@/components/Button";
import { QuickActionButton } from "@/components/QuickActionButton";
import { PromotionalBanner } from "@/components/PromotionalBanner";
import { useUserStore } from "@/store/userStore";

const { width: screenWidth } = Dimensions.get('window');

const nearbyAttractions = [
  {
    id: "1",
    title: "Ancient Temple",
    subtitle: "Historical landmark",
    distance: "0.5 km",
    rating: 4.8,
    visitors: "2.3k",
    category: "Heritage",
    imageUrl: "https://images.unsplash.com/photo-1564511287139-7a3bd6b4a1f0?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80",
    arFeatures: ["3D Models", "Audio Guide", "Historical Timeline"]
  },
  {
    id: "2",
    title: "Historic Market",
    subtitle: "Local culture hub",
    distance: "1.2 km",
    rating: 4.6,
    visitors: "1.8k",
    category: "Culture",
    imageUrl: "https://images.unsplash.com/photo-1519181245277-cffeb31da2e3?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80",
    arFeatures: ["Live Translation", "Price Scanner", "Vendor Info"]
  },
  {
    id: "3",
    title: "Art Museum",
    subtitle: "Contemporary gallery",
    distance: "2.0 km",
    rating: 4.9,
    visitors: "3.1k",
    category: "Art",
    imageUrl: "https://images.unsplash.com/photo-1566054757965-8c4085344c96?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80",
    arFeatures: ["Artist Info", "Artwork Details", "Virtual Tours"]
  },
  {
    id: "4",
    title: "City Observatory",
    subtitle: "Astronomical wonder",
    distance: "3.5 km",
    rating: 4.7,
    visitors: "1.2k",
    category: "Science",
    imageUrl: "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80",
    arFeatures: ["Star Map", "Planet Info", "Night Sky Guide"]
  }
];

const arFeatures = [
  {
    id: "1",
    title: "Live Translation",
    description: "Real-time text translation",
    icon: <Globe size={20} color={colors.primary} />,
    backgroundColor: "#FFF5F5"
  },
  {
    id: "2",
    title: "Object Recognition",
    description: "Identify landmarks instantly",
    icon: <Scan size={20} color={colors.secondary} />,
    backgroundColor: "#F0FDFA"
  },
  {
    id: "3",
    title: "Navigation",
    description: "AR-powered directions",
    icon: <Navigation size={20} color={colors.accent} />,
    backgroundColor: "#FFF7ED"
  },
  {
    id: "4",
    title: "Smart Camera",
    description: "Enhanced photo capture",
    icon: <Camera size={20} color="#8B5CF6" />,
    backgroundColor: "#F3F4F6"
  },
  {
    id: "5",
    title: "Audio Guide",
    description: "Immersive storytelling",
    icon: <Headphones size={20} color="#F59E0B" />,
    backgroundColor: "#FFFBEB"
  },
  {
    id: "6",
    title: "Offline Mode",
    description: "Works without internet",
    icon: <Wifi size={20} color="#10B981" />,
    backgroundColor: "#ECFDF5"
  }
];

const arStats = [
  {
    id: "1",
    title: "Active Users",
    value: "2.4M+",
    icon: <Users size={20} color={colors.primary} />,
    trend: "+12%"
  },
  {
    id: "2",
    title: "AR Experiences",
    value: "15K+",
    icon: <Layers size={20} color={colors.secondary} />,
    trend: "+8%"
  },
  {
    id: "3",
    title: "Locations",
    value: "500+",
    icon: <MapPin size={20} color={colors.accent} />,
    trend: "+25%"
  },
  {
    id: "4",
    title: "Accuracy",
    value: "99.2%",
    icon: <Target size={20} color="#10B981" />,
    trend: "+2%"
  }
];

const popularGuides = [
  {
    id: "1",
    title: "Tokyo Historical Tour",
    subtitle: "Ancient temples & modern culture",
    duration: "2-3 hours",
    rating: 4.9,
    reviews: 1247,
    price: "Free",
    category: "Heritage",
    imageUrl: "https://images.unsplash.com/photo-1503899036084-c55cdd92da26?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80",
    highlights: ["Senso-ji Temple", "Traditional Markets", "Cultural Stories"]
  },
  {
    id: "2",
    title: "Rome Ancient Ruins",
    subtitle: "Walk through history",
    duration: "3-4 hours",
    rating: 4.8,
    reviews: 892,
    price: "$12",
    category: "History",
    imageUrl: "https://images.unsplash.com/photo-1552832230-c0197dd311b5?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80",
    highlights: ["Colosseum AR", "Forum Stories", "Emperor Tales"]
  },
  {
    id: "3",
    title: "Paris Art Walk",
    subtitle: "Masterpieces come alive",
    duration: "2 hours",
    rating: 4.7,
    reviews: 634,
    price: "$8",
    category: "Art",
    imageUrl: "https://images.unsplash.com/photo-1502602898657-3e91760cbb34?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80",
    highlights: ["Louvre AR", "Street Art", "Artist Stories"]
  },
  {
    id: "4",
    title: "NYC Architecture",
    subtitle: "Skyscrapers & stories",
    duration: "1.5 hours",
    rating: 4.6,
    reviews: 423,
    price: "$15",
    category: "Architecture",
    imageUrl: "https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80",
    highlights: ["Empire State", "Art Deco", "Building History"]
  }
];

const categories = ["All", "Heritage", "Art", "Culture", "Science", "Architecture", "Nature"];

export default function ARGuideScreen() {
  const router = useRouter();
  const { user } = useUserStore();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [refreshing, setRefreshing] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  const handleStartARGuide = () => {
    router.push("/ar-guide");
  };

  const handleRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 1500);
  };

  const handleFilterPress = () => {
    console.log("Filter pressed");
  };

  const handleCategoryPress = (category: string) => {
    setSelectedCategory(category);
  };

  const handleGuidePress = (guide: any) => {
    router.push(`/guides/swipe?guideId=${guide.id}`);
  };

  const filteredAttractions = selectedCategory === "All" 
    ? nearbyAttractions 
    : nearbyAttractions.filter(attraction => attraction.category === selectedCategory);

  const filteredGuides = selectedCategory === "All" 
    ? popularGuides 
    : popularGuides.filter(guide => guide.category === selectedCategory);

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <ScrollView 
        ref={scrollViewRef}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Header - Matching Home Screen Style */}
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <Text style={styles.greeting}>Hello, {user?.name || "Explorer"}</Text>
            <Pressable style={styles.iconButton}>
              <Bell size={24} color={colors.text} />
            </Pressable>
          </View>
          <View style={styles.headerBottom}>
            <Text style={styles.mainTitle}>Discover the world through AR</Text>
          </View>
        </View>

        {/* Search Bar - Matching Home Screen */}
        <View style={styles.searchContainer}>
          <SearchBar
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search AR experiences and guides"
            onFilterPress={handleFilterPress}
          />
        </View>

        {/* Hero AR Preview */}
        <View style={styles.heroContainer}>
          <Image
            source={{ uri: "https://images.unsplash.com/photo-1569336415962-a4bd9f69cd83?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80" }}
            style={styles.heroImage}
            contentFit="cover"
          />
          <LinearGradient
            colors={["transparent", "rgba(0,0,0,0.7)"]}
            style={styles.heroGradient}
          />
          <View style={styles.heroContent}>
            <View style={styles.heroTop}>
              <View style={styles.liveIndicator}>
                <View style={styles.liveDot} />
                <Text style={styles.liveText}>Live AR</Text>
              </View>
              <View style={styles.locationBadge}>
                <MapPin size={14} color={colors.white} />
                <Text style={styles.locationText}>Downtown</Text>
              </View>
            </View>
            <View style={styles.heroBottom}>
              <Text style={styles.heroTitle}>Start Your AR Journey</Text>
              <Text style={styles.heroSubtitle}>Point your camera and discover hidden stories</Text>
              <Button
                title="Launch AR Camera"
                onPress={handleStartARGuide}
                icon={<Camera size={18} color={colors.white} />}
                variant="primary"
                size="medium"
              />
            </View>
          </View>
        </View>

        {/* AR Features - Enhanced */}
        <View style={styles.featuresContainer}>
          <Text style={styles.featuresTitle}>AR Capabilities</Text>
          <View style={styles.featuresGrid}>
            {arFeatures.map((item) => (
              <Pressable
                key={item.id}
                style={[styles.featureCard, { backgroundColor: item.backgroundColor }]}
                onPress={() => console.log(`${item.title} pressed`)}
              >
                <View style={styles.featureIconContainer}>
                  {item.icon}
                </View>
                <Text style={styles.featureTitle} numberOfLines={1}>{item.title}</Text>
                <Text style={styles.featureDescription} numberOfLines={2}>{item.description}</Text>
              </Pressable>
            ))}
          </View>
        </View>

        {/* AR Innovation Showcase */}
        <View style={styles.innovationContainer}>
          <View style={styles.innovationHeader}>
            <Sparkles size={24} color={colors.primary} />
            <Text style={styles.innovationTitle}>Latest AR Innovation</Text>
          </View>
          <View style={styles.innovationCard}>
            <Image
              source={{ uri: "https://images.unsplash.com/photo-1592478411213-6153e4ebc696?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80" }}
              style={styles.innovationImage}
              contentFit="cover"
            />
            <LinearGradient
              colors={["transparent", "rgba(0,0,0,0.7)"]}
              style={styles.innovationGradient}
            />
            <View style={styles.innovationContent}>
              <View style={styles.innovationBadge}>
                <Zap size={14} color={colors.white} />
                <Text style={styles.innovationBadgeText}>NEW</Text>
              </View>
              <Text style={styles.innovationCardTitle}>Neural Object Recognition</Text>
              <Text style={styles.innovationCardSubtitle}>AI-powered instant identification with 99.8% accuracy</Text>
              <Pressable style={styles.tryNowButton} onPress={() => console.log('Try now pressed')}>
                <Text style={styles.tryNowText}>Try Now</Text>
                <ChevronRight size={16} color={colors.white} />
              </Pressable>
            </View>
          </View>
        </View>

        {/* AR Statistics - Enhanced Cards */}
        <View style={styles.statsContainer}>
          <View style={styles.statsHeader}>
            <TrendingUp size={20} color={colors.primary} />
            <Text style={styles.statsTitle}>AR by the Numbers</Text>
          </View>
          <Text style={styles.statsSubtitle}>Real-time insights into our AR ecosystem</Text>
          <View style={styles.statsGrid}>
            {arStats.map((stat, index) => (
              <View key={stat.id} style={[styles.statCard, { backgroundColor: index % 2 === 0 ? colors.white : colors.backgroundSecondary }]}>
                <LinearGradient
                  colors={index % 2 === 0 ? [colors.primary + '10', colors.secondary + '10'] : [colors.accent + '10', colors.primary + '10']}
                  style={styles.statGradient}
                />
                <View style={styles.statHeader}>
                  <View style={[styles.statIconContainer, { backgroundColor: index % 2 === 0 ? colors.primary + '15' : colors.accent + '15' }]}>
                    {stat.icon}
                  </View>
                  <View style={styles.trendContainer}>
                    <TrendingUp size={12} color="#10B981" />
                    <Text style={styles.trendText}>{stat.trend}</Text>
                  </View>
                </View>
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.title}</Text>
                <View style={styles.statProgress}>
                  <View style={[styles.statProgressBar, { width: `${75 + (index * 5)}%`, backgroundColor: index % 2 === 0 ? colors.primary : colors.accent }]} />
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* AR Learning Hub - Redesigned */}
        <View style={styles.learningContainer}>
          <View style={styles.learningHeader}>
            <View style={styles.learningHeaderLeft}>
              <View style={styles.learningIconBadge}>
                <Eye size={18} color={colors.white} />
              </View>
              <View>
                <Text style={styles.learningTitle}>AR Learning Hub</Text>
                <Text style={styles.learningSubtitle}>Master AR features with interactive tutorials</Text>
              </View>
            </View>
            <Pressable style={styles.viewAllButton}>
              <Text style={styles.viewAllText}>View All</Text>
              <ChevronRight size={14} color={colors.primary} />
            </Pressable>
          </View>
          
          <View style={styles.learningGrid}>
            <Pressable style={styles.learningCard}>
              <LinearGradient
                colors={[colors.primary + '15', colors.primary + '05']}
                style={styles.learningCardGradient}
              />
              <View style={styles.learningCardHeader}>
                <View style={[styles.learningIconContainer, { backgroundColor: colors.primary + '20' }]}>
                  <Play size={18} color={colors.primary} />
                </View>
                <View style={styles.learningBadge}>
                  <Text style={styles.learningBadgeText}>NEW</Text>
                </View>
              </View>
              <Text style={styles.learningCardTitle}>Getting Started</Text>
              <Text style={styles.learningCardText}>5 min interactive tutorial</Text>
              <View style={styles.learningProgress}>
                <View style={styles.learningProgressBar}>
                  <View style={[styles.learningProgressFill, { width: '0%', backgroundColor: colors.primary }]} />
                </View>
                <Text style={styles.learningProgressText}>Start Now</Text>
              </View>
            </Pressable>
            
            <Pressable style={styles.learningCard}>
              <LinearGradient
                colors={[colors.accent + '15', colors.accent + '05']}
                style={styles.learningCardGradient}
              />
              <View style={styles.learningCardHeader}>
                <View style={[styles.learningIconContainer, { backgroundColor: colors.accent + '20' }]}>
                  <Target size={18} color={colors.accent} />
                </View>
                <View style={[styles.learningBadge, { backgroundColor: colors.accent }]}>
                  <Text style={styles.learningBadgeText}>PRO</Text>
                </View>
              </View>
              <Text style={styles.learningCardTitle}>Advanced Tips</Text>
              <Text style={styles.learningCardText}>Pro techniques & shortcuts</Text>
              <View style={styles.learningProgress}>
                <View style={styles.learningProgressBar}>
                  <View style={[styles.learningProgressFill, { width: '65%', backgroundColor: colors.accent }]} />
                </View>
                <Text style={styles.learningProgressText}>65% Complete</Text>
              </View>
            </Pressable>
          </View>
          
          <View style={styles.learningStats}>
            <View style={styles.learningStatItem}>
              <Users size={16} color={colors.primary} />
              <Text style={styles.learningStatText}>12.5k learners</Text>
            </View>
            <View style={styles.learningStatItem}>
              <Award size={16} color={colors.secondary} />
              <Text style={styles.learningStatText}>8 courses</Text>
            </View>
            <View style={styles.learningStatItem}>
              <Clock size={16} color={colors.accent} />
              <Text style={styles.learningStatText}>2h avg time</Text>
            </View>
          </View>
        </View>

        {/* Nearby Attractions */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Nearby Attractions</Text>
            <Pressable>
              <Text style={styles.seeAllText}>See All</Text>
            </Pressable>
          </View>
          <FlatList
            data={filteredAttractions}
            renderItem={({ item }) => (
              <View style={styles.attractionCard}>
                <Image
                  source={{ uri: item.imageUrl }}
                  style={styles.attractionImage}
                  contentFit="cover"
                />
                <LinearGradient
                  colors={["transparent", "rgba(0,0,0,0.6)"]}
                  style={styles.attractionGradient}
                />
                <View style={styles.attractionContent}>
                  <View style={styles.attractionTop}>
                    <View style={styles.categoryBadge}>
                      <Text style={styles.categoryBadgeText}>{item.category}</Text>
                    </View>
                    <View style={styles.ratingContainer}>
                      <Star size={12} color="#FFD700" fill="#FFD700" />
                      <Text style={styles.ratingText}>{item.rating}</Text>
                    </View>
                  </View>
                  <View style={styles.attractionBottom}>
                    <Text style={styles.attractionTitle}>{item.title}</Text>
                    <Text style={styles.attractionSubtitle}>{item.subtitle}</Text>
                    <View style={styles.attractionMeta}>
                      <View style={styles.metaItem}>
                        <MapPin size={12} color={colors.white} />
                        <Text style={styles.metaText}>{item.distance}</Text>
                      </View>
                      <View style={styles.metaItem}>
                        <Users size={12} color={colors.white} />
                        <Text style={styles.metaText}>{item.visitors}</Text>
                      </View>
                    </View>
                    <Pressable style={styles.arButton} onPress={handleStartARGuide}>
                      <Scan size={14} color={colors.white} />
                      <Text style={styles.arButtonText}>Start AR</Text>
                    </Pressable>
                  </View>
                </View>
              </View>
            )}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.attractionsList}
          />
        </View>

        {/* Popular AR Spots */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionHeaderLeft}>
              <MapPin size={20} color={colors.primary} />
              <Text style={styles.sectionTitle}>Popular AR Spots</Text>
            </View>
            <Pressable onPress={() => router.push('/guides')}>
              <Text style={styles.seeAllText}>See All</Text>
            </Pressable>
          </View>
          <View style={styles.guidesGrid}>
            {filteredGuides.slice(0, 4).map((guide) => (
              <Pressable
                key={guide.id}
                style={styles.guideCard}
                onPress={() => handleGuidePress(guide)}
              >
                <Image
                  source={{ uri: guide.imageUrl }}
                  style={styles.guideImage}
                  contentFit="cover"
                />
                <LinearGradient
                  colors={["transparent", "rgba(0,0,0,0.8)"]}
                  style={styles.guideGradient}
                />
                <View style={styles.guideContent}>
                  <View style={styles.guideTop}>
                    <View style={styles.guideBadge}>
                      <Text style={styles.guideBadgeText}>{guide.category}</Text>
                    </View>
                    <View style={styles.priceTag}>
                      <Text style={styles.priceText}>{guide.price}</Text>
                    </View>
                  </View>
                  <View style={styles.guideBottom}>
                    <Text style={styles.guideTitle} numberOfLines={2}>{guide.title}</Text>
                    <Text style={styles.guideSubtitle} numberOfLines={1}>{guide.subtitle}</Text>
                    <View style={styles.guideMeta}>
                      <View style={styles.guideMetaItem}>
                        <Clock size={12} color={colors.white} />
                        <Text style={styles.guideMetaText}>{guide.duration}</Text>
                      </View>
                      <View style={styles.guideMetaItem}>
                        <Star size={12} color="#FFD700" fill="#FFD700" />
                        <Text style={styles.guideMetaText}>{guide.rating}</Text>
                      </View>
                    </View>
                    <Pressable style={styles.playButton}>
                      <Play size={12} color={colors.white} fill={colors.white} />
                    </Pressable>
                  </View>
                </View>
              </Pressable>
            ))}
          </View>
        </View>

        {/* Enhanced How AR Guide Works Section */}
        <View style={styles.infoSection}>
          <LinearGradient
            colors={[colors.primary + '08', colors.secondary + '08']}
            style={styles.infoGradient}
          />
          <View style={styles.infoHeader}>
            <View style={styles.infoIconBadge}>
              <Info size={20} color={colors.white} />
            </View>
            <View style={styles.infoHeaderContent}>
              <Text style={styles.infoTitle}>How AR Guide Works</Text>
              <Text style={styles.infoHeaderSubtitle}>Discover the magic behind our technology</Text>
            </View>
          </View>
          
          <Text style={styles.infoText}>
            Experience the world like never before with our cutting-edge AR technology. 
            Point your camera at landmarks, historical sites, and points of interest to unlock 
            immersive stories, detailed information, and interactive experiences.
          </Text>
          
          <View style={styles.infoSteps}>
            <View style={styles.infoStep}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>1</Text>
              </View>
              <View style={styles.stepContent}>
                <View style={styles.stepHeader}>
                  <Camera size={18} color={colors.primary} />
                  <Text style={styles.stepTitle}>Point & Scan</Text>
                </View>
                <Text style={styles.stepDescription}>Aim your camera at any landmark or object</Text>
              </View>
            </View>
            
            <View style={styles.stepConnector} />
            
            <View style={styles.infoStep}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>2</Text>
              </View>
              <View style={styles.stepContent}>
                <View style={styles.stepHeader}>
                  <Scan size={18} color={colors.secondary} />
                  <Text style={styles.stepTitle}>AI Recognition</Text>
                </View>
                <Text style={styles.stepDescription}>Our AI instantly identifies and analyzes</Text>
              </View>
            </View>
            
            <View style={styles.stepConnector} />
            
            <View style={styles.infoStep}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>3</Text>
              </View>
              <View style={styles.stepContent}>
                <View style={styles.stepHeader}>
                  <Sparkles size={18} color={colors.accent} />
                  <Text style={styles.stepTitle}>AR Experience</Text>
                </View>
                <Text style={styles.stepDescription}>Immersive content appears in real-time</Text>
              </View>
            </View>
          </View>
          
          <View style={styles.infoFeatures}>
            <View style={styles.infoFeature}>
              <View style={[styles.infoFeatureIcon, { backgroundColor: colors.primary + '15' }]}>
                <Shield size={18} color={colors.primary} />
              </View>
              <View style={styles.infoFeatureContent}>
                <Text style={styles.infoFeatureTitle}>99.2% Accuracy</Text>
                <Text style={styles.infoFeatureText}>Precise object recognition</Text>
              </View>
            </View>
            <View style={styles.infoFeature}>
              <View style={[styles.infoFeatureIcon, { backgroundColor: colors.secondary + '15' }]}>
                <Wifi size={18} color={colors.secondary} />
              </View>
              <View style={styles.infoFeatureContent}>
                <Text style={styles.infoFeatureTitle}>Offline Ready</Text>
                <Text style={styles.infoFeatureText}>Works without internet</Text>
              </View>
            </View>
            <View style={styles.infoFeature}>
              <View style={[styles.infoFeatureIcon, { backgroundColor: colors.accent + '15' }]}>
                <Smartphone size={18} color={colors.accent} />
              </View>
              <View style={styles.infoFeatureContent}>
                <Text style={styles.infoFeatureTitle}>Cross Platform</Text>
                <Text style={styles.infoFeatureText}>iOS & Android support</Text>
              </View>
            </View>
          </View>
          
          <View style={styles.infoActions}>
            <Button
              title="Try AR Demo"
              onPress={handleStartARGuide}
              icon={<Zap size={16} color={colors.white} />}
              variant="primary"
              size="large"
              fullWidth
            />
            <Pressable style={styles.watchVideoButton} onPress={() => console.log('Watch video pressed')}>
              <Play size={16} color={colors.primary} />
              <Text style={styles.watchVideoText}>Watch Demo Video</Text>
            </Pressable>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.xl,
  },
  headerTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  headerBottom: {
    paddingRight: spacing.xl,
  },
  greeting: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
  },
  mainTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    lineHeight: typography.lineHeights.tight * typography.sizes.xxl,
  },
  iconButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.white,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  searchContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  heroContainer: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.xl,
    borderRadius: 20,
    overflow: "hidden",
    height: 280,
    shadowColor: colors.shadowDark,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
  },
  heroImage: {
    ...StyleSheet.absoluteFillObject,
  },
  heroGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  heroContent: {
    ...StyleSheet.absoluteFillObject,
    padding: spacing.lg,
    justifyContent: "space-between",
  },
  heroTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  liveIndicator: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 56, 92, 0.9)",
    borderRadius: 20,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },
  liveDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.white,
    marginRight: spacing.xs,
  },
  liveText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium as any,
    color: colors.white,
  },
  locationBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.5)",
    borderRadius: 16,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },
  locationText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.white,
    marginLeft: spacing.xs,
  },
  heroBottom: {
    alignItems: "flex-start",
  },
  heroTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  heroSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.white,
    opacity: 0.9,
    marginBottom: spacing.lg,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
  },
  featuresContainer: {
    marginBottom: spacing.xl,
  },
  featuresTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.md,
  },
  featuresGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    paddingHorizontal: spacing.lg,
  },
  featureCard: {
    width: (screenWidth - spacing.lg * 2 - spacing.md) / 2,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
    alignItems: "center",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
  },
  featureIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.white,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: spacing.sm,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  featureTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    textAlign: "center",
    marginBottom: spacing.xs,
  },
  featureDescription: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    textAlign: "center",
    lineHeight: typography.lineHeights.normal * typography.sizes.xs,
  },
  innovationContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  innovationHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.lg,
  },
  innovationTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  innovationCard: {
    height: 200,
    borderRadius: 20,
    overflow: "hidden",
    shadowColor: colors.shadowDark,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
  },
  innovationImage: {
    ...StyleSheet.absoluteFillObject,
  },
  innovationGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  innovationContent: {
    ...StyleSheet.absoluteFillObject,
    padding: spacing.lg,
    justifyContent: "space-between",
  },
  innovationBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.primary,
    borderRadius: 16,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    alignSelf: "flex-start",
  },
  innovationBadgeText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    marginLeft: spacing.xs,
  },
  innovationCardTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  innovationCardSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.white,
    opacity: 0.9,
    marginBottom: spacing.lg,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
  },
  tryNowButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 25,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    alignSelf: "flex-start",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  tryNowText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: colors.white,
    marginRight: spacing.xs,
  },
  learningContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  learningHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: spacing.lg,
  },
  learningHeaderLeft: {
    flexDirection: "row",
    alignItems: "flex-start",
    flex: 1,
  },
  learningIconBadge: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.secondary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  learningTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  learningSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
  },
  viewAllButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.primary + '10',
    borderRadius: 20,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  viewAllText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium as any,
    color: colors.primary,
    marginRight: spacing.xs,
  },
  learningGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.lg,
  },
  learningCard: {
    width: (screenWidth - spacing.lg * 2 - spacing.md) / 2,
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: spacing.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 6,
    overflow: "hidden",
  },
  learningCardGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  learningCardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  learningIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
  },
  learningBadge: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingHorizontal: spacing.sm,
    paddingVertical: 4,
  },
  learningBadgeText: {
    fontFamily: typography.fontFamily,
    fontSize: 10,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
  },
  learningCardTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  learningCardText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.md,
    lineHeight: typography.lineHeights.normal * typography.sizes.sm,
  },
  learningProgress: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  learningProgressBar: {
    flex: 1,
    height: 6,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 3,
    marginRight: spacing.sm,
    overflow: "hidden",
  },
  learningProgressFill: {
    height: "100%",
    borderRadius: 3,
  },
  learningProgressText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium as any,
    color: colors.textSecondary,
  },
  learningStats: {
    flexDirection: "row",
    justifyContent: "space-around",
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 16,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
  },
  learningStatItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  learningStatText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium as any,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  section: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.lg,
  },
  sectionHeaderLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  sectionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  seeAllText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontWeight: typography.weights.medium as any,
  },
  attractionsList: {
    paddingVertical: spacing.sm,
  },
  attractionCard: {
    width: screenWidth * 0.75,
    height: 220,
    borderRadius: 16,
    overflow: "hidden",
    marginRight: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  attractionImage: {
    ...StyleSheet.absoluteFillObject,
  },
  attractionGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  attractionContent: {
    ...StyleSheet.absoluteFillObject,
    padding: spacing.md,
    justifyContent: "space-between",
  },
  attractionTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  categoryBadge: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 12,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },
  categoryBadgeText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium as any,
    color: colors.white,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.5)",
    borderRadius: 12,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },
  ratingText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium as any,
    color: colors.white,
    marginLeft: spacing.xs,
  },
  attractionBottom: {
    alignItems: "flex-start",
  },
  attractionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  attractionSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.white,
    opacity: 0.9,
    marginBottom: spacing.sm,
  },
  attractionMeta: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  metaItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: spacing.md,
  },
  metaText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.white,
    marginLeft: spacing.xs,
  },
  arButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.primary,
    borderRadius: 20,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    alignSelf: "flex-start",
  },
  arButtonText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium as any,
    color: colors.white,
    marginLeft: spacing.xs,
  },
  guidesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  guideCard: {
    width: (screenWidth - spacing.lg * 2 - spacing.sm) / 2,
    height: 200,
    borderRadius: 16,
    overflow: "hidden",
    marginBottom: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  guideImage: {
    ...StyleSheet.absoluteFillObject,
  },
  guideGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  guideContent: {
    ...StyleSheet.absoluteFillObject,
    padding: spacing.sm,
    justifyContent: "space-between",
  },
  guideTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  guideBadge: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 8,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
  },
  guideBadgeText: {
    fontFamily: typography.fontFamily,
    fontSize: 10,
    fontWeight: typography.weights.medium as any,
    color: colors.white,
  },
  priceTag: {
    backgroundColor: colors.accent,
    borderRadius: 8,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
  },
  priceText: {
    fontFamily: typography.fontFamily,
    fontSize: 10,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
  },
  guideBottom: {
    alignItems: "flex-start",
  },
  guideTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  guideSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.white,
    opacity: 0.9,
    marginBottom: spacing.xs,
  },
  guideMeta: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.xs,
  },
  guideMetaItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: spacing.sm,
  },
  guideMetaText: {
    fontFamily: typography.fontFamily,
    fontSize: 10,
    color: colors.white,
    marginLeft: 2,
  },
  playButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "flex-end",
  },
  infoSection: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.xl,
    padding: spacing.xl,
    backgroundColor: colors.white,
    borderRadius: 24,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 8,
    overflow: "hidden",
  },
  infoGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  infoHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: spacing.lg,
  },
  infoIconBadge: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  infoHeaderContent: {
    flex: 1,
  },
  infoTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  infoHeaderSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
  },
  infoText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.xl,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
  },
  infoSteps: {
    marginBottom: spacing.xl,
  },
  infoStep: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: spacing.lg,
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
  },
  stepNumberText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
  },
  stepContent: {
    flex: 1,
  },
  stepHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.xs,
  },
  stepTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  stepDescription: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.normal * typography.sizes.sm,
  },
  stepConnector: {
    width: 2,
    height: 20,
    backgroundColor: colors.backgroundSecondary,
    marginLeft: 15,
    marginBottom: spacing.sm,
  },
  infoFeatures: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.xl,
  },
  infoFeature: {
    alignItems: "center",
    flex: 1,
  },
  infoFeatureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  infoFeatureContent: {
    alignItems: "center",
  },
  infoFeatureTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
    textAlign: "center",
  },
  infoFeatureText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    textAlign: "center",
    lineHeight: typography.lineHeights.normal * typography.sizes.xs,
  },
  infoActions: {
    gap: spacing.md,
  },
  watchVideoButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 16,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
  },
  watchVideoText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium as any,
    color: colors.primary,
    marginLeft: spacing.sm,
  },
  statsContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  statsHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  statsTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  statsSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.lg,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
  },
  statsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  statCard: {
    width: (screenWidth - spacing.lg * 2 - spacing.sm) / 2,
    borderRadius: 20,
    padding: spacing.lg,
    marginBottom: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 6,
    overflow: "hidden",
  },
  statGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  statHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  statIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  trendContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#ECFDF5",
    borderRadius: 12,
    paddingHorizontal: spacing.xs,
    paddingVertical: 4,
  },
  trendText: {
    fontFamily: typography.fontFamily,
    fontSize: 10,
    fontWeight: typography.weights.bold as any,
    color: "#10B981",
    marginLeft: 2,
  },
  statValue: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
    marginBottom: spacing.sm,
  },
  statProgress: {
    height: 4,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 2,
    overflow: "hidden",
  },
  statProgressBar: {
    height: "100%",
    borderRadius: 2,
  },
});