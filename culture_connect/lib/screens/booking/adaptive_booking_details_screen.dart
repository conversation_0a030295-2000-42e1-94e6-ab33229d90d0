import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:culture_connect/models/premium_booking.dart';
import 'package:culture_connect/design_system/premium_design_tokens.dart';
import 'package:culture_connect/widgets/booking_details/flight_booking_layout.dart';
import 'package:culture_connect/widgets/booking_details/hotel_booking_layout.dart';
import 'package:culture_connect/widgets/booking_details/experience_booking_layout.dart';
import 'package:culture_connect/widgets/booking_details/package_booking_layout.dart';
import 'package:culture_connect/services/booking_pdf_service.dart';
import 'package:open_file/open_file.dart';

/// Adaptive booking details screen that provides excellent UX design
/// tailored to different booking types with smooth animations and micro-interactions
class AdaptiveBookingDetailsScreen extends StatefulWidget {
  final PremiumBooking booking;

  const AdaptiveBookingDetailsScreen({
    super.key,
    required this.booking,
  });

  @override
  State<AdaptiveBookingDetailsScreen> createState() =>
      _AdaptiveBookingDetailsScreenState();
}

class _AdaptiveBookingDetailsScreenState
    extends State<AdaptiveBookingDetailsScreen> with TickerProviderStateMixin {
  late AnimationController _heroAnimationController;
  late AnimationController _contentAnimationController;
  late Animation<double> _heroFadeAnimation;
  late Animation<Offset> _heroSlideAnimation;
  late Animation<double> _contentFadeAnimation;
  late Animation<Offset> _contentSlideAnimation;

  bool _isLoading = false;
  bool _isFavorite = false;
  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  String _downloadStatus = '';

  @override
  void initState() {
    super.initState();
    _isFavorite = widget.booking.isFavorite;
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _heroAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _contentAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _heroFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _heroAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _heroSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _heroAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _contentFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _contentAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _contentSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _contentAnimationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _startAnimations() {
    _heroAnimationController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _contentAnimationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _heroAnimationController.dispose();
    _contentAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: PremiumDesignTokens.neutralGray50,
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          _buildHeroSection(),
          SliverToBoxAdapter(
            child: SlideTransition(
              position: _contentSlideAnimation,
              child: FadeTransition(
                opacity: _contentFadeAnimation,
                child: Column(
                  children: [
                    _buildQuickActionsSection(),
                    _buildAdaptiveContent(),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeroSection() {
    return SliverAppBar(
      expandedHeight: 300,
      pinned: true,
      backgroundColor: PremiumDesignTokens.neutralWhite,
      elevation: 0,
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: PremiumDesignTokens.neutralWhite.withAlpha(230),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: PremiumDesignTokens.neutralGray900.withAlpha(26),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.arrow_back_ios_new,
            color: PremiumDesignTokens.neutralGray900,
            size: 20,
          ),
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: PremiumDesignTokens.neutralWhite.withAlpha(230),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: PremiumDesignTokens.neutralGray900.withAlpha(26),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            onPressed: _toggleFavorite,
            icon: AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: Icon(
                _isFavorite ? Icons.favorite : Icons.favorite_border,
                key: ValueKey(_isFavorite),
                color: _isFavorite
                    ? const Color(0xFFFF385C)
                    : PremiumDesignTokens.neutralGray900,
                size: 20,
              ),
            ),
          ),
        ),
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: PremiumDesignTokens.neutralWhite.withAlpha(230),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: PremiumDesignTokens.neutralGray900.withAlpha(26),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            onPressed: _shareBooking,
            icon: Icon(
              Icons.share,
              color: PremiumDesignTokens.neutralGray900,
              size: 20,
            ),
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: SlideTransition(
          position: _heroSlideAnimation,
          child: FadeTransition(
            opacity: _heroFadeAnimation,
            child: _buildHeroImage(),
          ),
        ),
      ),
    );
  }

  Widget _buildHeroImage() {
    return Stack(
      fit: StackFit.expand,
      children: [
        CachedNetworkImage(
          imageUrl: widget.booking.imageUrl,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: PremiumDesignTokens.neutralGray200,
            child: Center(
              child: CircularProgressIndicator(
                color: PremiumDesignTokens.primaryBlue,
              ),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            color: PremiumDesignTokens.neutralGray200,
            child: Icon(
              Icons.image_not_supported,
              size: 48,
              color: PremiumDesignTokens.neutralGray500,
            ),
          ),
        ),
        // Gradient overlay
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.transparent,
                Colors.transparent,
                PremiumDesignTokens.neutralGray900.withAlpha(77),
                PremiumDesignTokens.neutralGray900.withAlpha(128),
              ],
            ),
          ),
        ),
        // Booking info overlay
        Positioned(
          bottom: 20,
          left: 20,
          right: 20,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: widget.booking.statusColor,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      widget.booking.statusIcon,
                      size: 16,
                      color: PremiumDesignTokens.neutralWhite,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      widget.booking.statusText,
                      style: PremiumDesignTokens.labelMedium.copyWith(
                        color: PremiumDesignTokens.neutralWhite,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              Text(
                widget.booking.title,
                style: PremiumDesignTokens.headlineMedium.copyWith(
                  color: PremiumDesignTokens.neutralWhite,
                  fontWeight: FontWeight.w700,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                widget.booking.subtitle,
                style: PremiumDesignTokens.bodyLarge.copyWith(
                  color: PremiumDesignTokens.neutralWhite.withAlpha(204),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActionsSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: PremiumDesignTokens.headlineSmall.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 16),
          Column(
            children: [
              Row(
                children: [
                  Expanded(
                      child: _buildActionButton(
                    icon: Icons.check_circle_outline,
                    label: 'Check In',
                    color: const Color(0xFF10B981),
                    onTap: _handleCheckIn,
                  )),
                  const SizedBox(width: 12),
                  Expanded(
                      child: _buildActionButton(
                    icon: Icons.phone,
                    label: 'Contact',
                    color: const Color(0xFF06B6D4),
                    onTap: _handleContact,
                  )),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                      child: _buildActionButton(
                    icon: Icons.directions,
                    label: 'Directions',
                    color: const Color(0xFFF59E0B),
                    onTap: _handleDirections,
                  )),
                  const SizedBox(width: 12),
                  Expanded(child: _buildDownloadButton()),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: color.withAlpha(26),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withAlpha(51),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: PremiumDesignTokens.labelMedium.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build sophisticated download button with loading states
  Widget _buildDownloadButton() {
    const downloadColor = Color(0xFF8B5CF6);

    return GestureDetector(
      onTap: _isDownloading ? null : _handleDownloadBooking,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: _isDownloading
              ? downloadColor.withAlpha(51)
              : downloadColor.withAlpha(26),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: downloadColor.withAlpha(51),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            if (_isDownloading) ...[
              SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  value: _downloadProgress,
                  strokeWidth: 2.5,
                  valueColor: AlwaysStoppedAnimation<Color>(downloadColor),
                  backgroundColor: downloadColor.withAlpha(51),
                ),
              ),
            ] else ...[
              Icon(
                Icons.download_rounded,
                color: downloadColor,
                size: 24,
              ),
            ],
            const SizedBox(height: 8),
            Text(
              _isDownloading ? 'Downloading...' : 'Download',
              style: PremiumDesignTokens.labelMedium.copyWith(
                color: downloadColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            if (_isDownloading && _downloadStatus.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                _downloadStatus,
                style: PremiumDesignTokens.labelSmall.copyWith(
                  color: downloadColor.withAlpha(179),
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAdaptiveContent() {
    switch (widget.booking.type) {
      case PremiumBookingType.flight:
        return FlightBookingLayout(booking: widget.booking);
      case PremiumBookingType.hotel:
        return HotelBookingLayout(booking: widget.booking);
      case PremiumBookingType.experience:
        return ExperienceBookingLayout(booking: widget.booking);
      case PremiumBookingType.package:
        return PackageBookingLayout(booking: widget.booking);
      default:
        return ExperienceBookingLayout(booking: widget.booking);
    }
  }

  void _toggleFavorite() {
    HapticFeedback.lightImpact();
    setState(() {
      _isFavorite = !_isFavorite;
    });
    // TODO: Update favorite status in backend
  }

  void _shareBooking() {
    HapticFeedback.lightImpact();
    // TODO: Implement sharing functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sharing ${widget.booking.title}...'),
        backgroundColor: PremiumDesignTokens.primaryBlue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _handleCheckIn() {
    // TODO: Implement check-in functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Check-in initiated...'),
        backgroundColor: const Color(0xFF10B981),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _handleContact() {
    // TODO: Implement contact functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Opening contact options...'),
        backgroundColor: const Color(0xFF06B6D4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _handleDirections() {
    // TODO: Implement directions functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Opening directions...'),
        backgroundColor: const Color(0xFFF59E0B),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  /// Handle download booking with sophisticated loading experience
  Future<void> _handleDownloadBooking() async {
    if (_isDownloading) return;

    HapticFeedback.lightImpact();

    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
      _downloadStatus = 'Preparing download...';
    });

    try {
      // Simulate progress updates for better UX
      await _updateDownloadProgress(0.2, 'Analyzing booking details...');
      await Future.delayed(const Duration(milliseconds: 500));

      await _updateDownloadProgress(0.4, 'Generating PDF layout...');
      await Future.delayed(const Duration(milliseconds: 800));

      await _updateDownloadProgress(0.7, 'Adding specialized content...');
      await Future.delayed(const Duration(milliseconds: 600));

      await _updateDownloadProgress(0.9, 'Finalizing document...');

      // Generate the actual PDF
      final pdfFile =
          await BookingPdfService.generateBookingPdf(widget.booking);

      await _updateDownloadProgress(1.0, 'Download complete!');
      await Future.delayed(const Duration(milliseconds: 500));

      // Provide haptic feedback for completion
      HapticFeedback.mediumImpact();

      // Show success message and open PDF
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: PremiumDesignTokens.neutralWhite,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Booking PDF downloaded successfully!',
                    style: TextStyle(
                      color: PremiumDesignTokens.neutralWhite,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: const Color(0xFF10B981),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            action: SnackBarAction(
              label: 'OPEN',
              textColor: PremiumDesignTokens.neutralWhite,
              onPressed: () => OpenFile.open(pdfFile.path),
            ),
          ),
        );
      }
    } catch (error) {
      // Handle errors gracefully
      HapticFeedback.heavyImpact();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: PremiumDesignTokens.neutralWhite,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Failed to download booking. Please try again.',
                    style: TextStyle(
                      color: PremiumDesignTokens.neutralWhite,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: const Color(0xFFEF4444),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
          _downloadProgress = 0.0;
          _downloadStatus = '';
        });
      }
    }
  }

  /// Update download progress with status message
  Future<void> _updateDownloadProgress(double progress, String status) async {
    if (mounted) {
      setState(() {
        _downloadProgress = progress;
        _downloadStatus = status;
      });
    }
  }
}
