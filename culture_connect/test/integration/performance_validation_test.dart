import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/services/cache_service.dart';
import 'package:culture_connect/services/cache_maintenance_service.dart';
import 'package:culture_connect/services/compute_isolation_service.dart';
import 'package:culture_connect/services/progressive_loading_service.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Performance Validation Integration Tests', () {
    late CacheService cacheService;
    late CacheMaintenanceService maintenanceService;
    late ComputeIsolationService computeService;
    late ProgressiveLoadingService progressiveService;

    setUpAll(() async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});

      cacheService = CacheService();
      maintenanceService = CacheMaintenanceService();
      computeService = ComputeIsolationService();
      progressiveService = ProgressiveLoadingService();

      await computeService.initialize();
    });

    tearDownAll(() {
      maintenanceService.dispose();
      computeService.dispose();
      progressiveService.dispose();
    });

    group('Memory Usage Validation', () {
      test('should maintain memory usage under 100MB target', () async {
        // This test validates that our optimizations keep memory usage low

        // Arrange - Create substantial data load
        final largeDataSets = <String, dynamic>{};
        for (int i = 0; i < 50; i++) {
          largeDataSets['dataset_$i'] = {
            'items': List.generate(
                100,
                (j) => {
                      'id': j,
                      'name': 'Item $j in dataset $i',
                      'description': 'Description for item $j' * 10,
                      'metadata': {
                        'created': DateTime.now().toIso8601String(),
                        'tags': List.generate(5, (k) => 'tag${j}_$k'),
                      }
                    }),
            'summary': {
              'total': 100,
              'created': DateTime.now().toIso8601String(),
            }
          };
        }

        // Act - Store data using optimized cache
        final stopwatch = Stopwatch()..start();

        for (final entry in largeDataSets.entries) {
          await cacheService.saveData(entry.key, entry.value);
        }

        // Retrieve data to populate memory cache
        for (final key in largeDataSets.keys) {
          await cacheService.getData(key);
        }

        stopwatch.stop();

        // Assert - Operations should complete efficiently
        expect(
            stopwatch.elapsedMilliseconds, lessThan(10000)); // 10 seconds max

        // Verify cache statistics show good performance
        final stats = cacheService.getCacheStatistics();
        expect(stats['hit_rate'], greaterThan(0.0));
        expect(stats['memory_cache_size'], lessThanOrEqualTo(100)); // LRU limit

        // Memory usage should be reasonable (this is a proxy test)
        // In a real integration test, you would measure actual memory usage
        expect(
            stats['total_cache_size'], lessThan(50 * 1024 * 1024)); // 50MB max
      });

      test('should handle memory pressure gracefully', () async {
        // Arrange - Create memory pressure scenario
        final memoryPressureData = <String, dynamic>{};
        for (int i = 0; i < 200; i++) {
          memoryPressureData['pressure_$i'] =
              List.generate(1000, (j) => 'data_$j');
        }

        // Act - Store large amount of data
        for (final entry in memoryPressureData.entries) {
          await cacheService.saveData(entry.key, entry.value);
        }

        // Trigger maintenance to handle memory pressure
        maintenanceService.startMaintenance();
        await Future.delayed(const Duration(seconds: 1));

        // Assert - System should remain stable
        final stats = cacheService.getCacheStatistics();
        expect(stats['memory_cache_size'],
            lessThanOrEqualTo(100)); // LRU should limit

        final healthMetrics = maintenanceService.getCacheHealthMetrics();
        expect(healthMetrics['maintenance_active'], isTrue);
      });
    });

    group('Frame Rate Performance Validation', () {
      test('should maintain 60fps during heavy operations', () async {
        // This test simulates heavy operations that could impact frame rate

        // Arrange - Prepare heavy computational tasks
        final heavyTasks = <Future>[];

        // Act - Run multiple heavy operations concurrently
        final frameRateTest = Stopwatch()..start();

        // Simulate heavy JSON processing
        for (int i = 0; i < 10; i++) {
          final largeJson = jsonEncode({
            'data': List.generate(1000, (j) => 'heavy_data_${i}_$j'),
            'metadata': {
              'batch': i,
              'timestamp': DateTime.now().toIso8601String()
            }
          });
          heavyTasks.add(computeService.processLargeJson(largeJson));
        }

        // Simulate heavy data compression
        for (int i = 0; i < 5; i++) {
          final heavyData = 'Heavy compression data ' * 10000;
          heavyTasks.add(computeService.compressDataInIsolate(heavyData));
        }

        // Wait for all heavy operations
        await Future.wait(heavyTasks);

        frameRateTest.stop();

        // Assert - Operations should complete without blocking main thread
        // Target: <16ms per frame for 60fps, so heavy operations should not
        // significantly impact main thread timing
        expect(frameRateTest.elapsedMilliseconds,
            lessThan(30000)); // 30 seconds max

        // Verify isolate status shows healthy operation
        final isolateStatus = computeService.getIsolateStatus();
        expect(isolateStatus['active_isolates'], greaterThan(0));
        expect(isolateStatus['pending_operations'], equals(0)); // All completed
      });

      test('should handle progressive loading without frame drops', () async {
        // Arrange - Create large dataset for progressive loading
        final largeItemList = List.generate(500, (i) => 'Progressive Item $i');

        // Act - Create progressive loader
        final loader = progressiveService.createLoader<String>(
          loaderId: 'frame_rate_test',
          items: largeItemList,
          itemBuilder: (item) => Container(child: Text(item)),
          batchSize: 10,
          batchDelay: const Duration(milliseconds: 16), // Target 60fps timing
        );

        final loadingStopwatch = Stopwatch()..start();
        loader.startLoading();

        // Monitor loading progress
        while (!loader.isCompleted) {
          await Future.delayed(
              const Duration(milliseconds: 16)); // 60fps frame time

          // Verify progress is being made
          expect(loader.progress, greaterThanOrEqualTo(0.0));
          expect(loader.progress, lessThanOrEqualTo(1.0));

          // Prevent infinite loop
          if (loadingStopwatch.elapsedMilliseconds > 30000) break;
        }

        loadingStopwatch.stop();

        // Assert - Progressive loading should complete efficiently
        expect(loader.isCompleted, isTrue);
        expect(loader.loadedItems.length, equals(largeItemList.length));
        expect(loadingStopwatch.elapsedMilliseconds, lessThan(30000));

        // Verify service statistics
        final stats = progressiveService.getStatistics();
        expect(stats['active_loaders'], greaterThanOrEqualTo(0));
      });
    });

    group('Cache Performance Validation', () {
      test('should achieve 85% cache hit rate target', () async {
        // Arrange - Create test data with access patterns
        final testData = <String, dynamic>{};
        for (int i = 0; i < 100; i++) {
          testData['cache_test_$i'] = {
            'id': i,
            'data': 'Test data for item $i',
            'timestamp': DateTime.now().toIso8601String(),
          };
        }

        // Act - Store all data
        for (final entry in testData.entries) {
          await cacheService.saveData(entry.key, entry.value);
        }

        // Simulate realistic access patterns (80/20 rule)
        final accessPattern = <String>[];

        // 80% of accesses to 20% of data (hot data)
        final hotKeys = testData.keys.take(20).toList();
        for (int i = 0; i < 400; i++) {
          accessPattern.add(hotKeys[i % hotKeys.length]);
        }

        // 20% of accesses to remaining 80% of data (cold data)
        final coldKeys = testData.keys.skip(20).toList();
        for (int i = 0; i < 100; i++) {
          accessPattern.add(coldKeys[i % coldKeys.length]);
        }

        // Perform access pattern
        for (final key in accessPattern) {
          await cacheService.getData(key);
        }

        // Assert - Cache hit rate should meet target
        final stats = cacheService.getCacheStatistics();
        expect(stats['hit_rate'], greaterThan(0.85)); // 85% target
        expect(stats['cache_hits'], greaterThan(stats['cache_misses']));

        // Verify compression is working
        expect(stats['total_cache_size'], greaterThan(0));
      });

      test('should maintain cache performance under load', () async {
        // Arrange - High-load scenario
        final loadTestData = <String, dynamic>{};
        for (int i = 0; i < 1000; i++) {
          loadTestData['load_test_$i'] = {
            'batch': i ~/ 100,
            'item': i % 100,
            'payload': List.generate(50, (j) => 'payload_${i}_$j'),
          };
        }

        // Act - Concurrent cache operations
        final concurrentOperations = <Future>[];

        // Concurrent writes
        for (int i = 0; i < 100; i++) {
          concurrentOperations.add(cacheService.saveData(
              'concurrent_write_$i', loadTestData['load_test_$i']));
        }

        // Concurrent reads
        for (int i = 0; i < 100; i++) {
          concurrentOperations
              .add(cacheService.getData('concurrent_write_${i % 50}'));
        }

        final loadTestStopwatch = Stopwatch()..start();
        await Future.wait(concurrentOperations);
        loadTestStopwatch.stop();

        // Assert - Should handle concurrent load efficiently
        expect(loadTestStopwatch.elapsedMilliseconds,
            lessThan(15000)); // 15 seconds max

        final stats = cacheService.getCacheStatistics();
        expect(stats['hit_rate'], greaterThan(0.3)); // Should have some hits
        expect(stats['memory_cache_size'], lessThanOrEqualTo(100)); // LRU limit
      });
    });

    group('Storage Efficiency Validation', () {
      test('should achieve 60% storage reduction through compression',
          () async {
        // Arrange - Create highly compressible data
        final compressibleData = {
          'repeated_content': 'This content repeats many times. ' * 1000,
          'structured_data': List.generate(
              500,
              (i) => {
                    'id': i,
                    'name': 'Item $i',
                    'description': 'Standard description for all items',
                    'category': 'category_${i % 10}',
                    'tags': ['tag1', 'tag2', 'tag3', 'tag4', 'tag5'],
                  }),
          'metadata': {
            'version': '1.0.0',
            'created': DateTime.now().toIso8601String(),
            'format': 'json',
          }
        };

        // Act - Store data with compression
        final originalSize = jsonEncode(compressibleData).length;
        await cacheService.saveData('compression_test', compressibleData);

        // Retrieve to verify integrity
        final retrievedData = await cacheService.getData('compression_test');

        // Assert - Data should be compressed but retrievable
        expect(retrievedData, equals(compressibleData));

        final stats = cacheService.getCacheStatistics();
        final compressedSize = stats['total_cache_size'] as int;

        // Verify compression ratio (should be significantly smaller)
        final compressionRatio = compressedSize / originalSize;
        expect(compressionRatio, lessThan(0.8)); // At least 20% compression

        // Ideally should achieve 60% reduction (40% of original size)
        // This depends on the actual compression algorithm effectiveness
        print(
            'Compression ratio: ${(compressionRatio * 100).toStringAsFixed(1)}%');
        print('Original size: ${originalSize} bytes');
        print('Compressed size: ${compressedSize} bytes');
      });
    });

    group('End-to-End Performance Integration', () {
      test('should maintain all performance targets simultaneously', () async {
        // This comprehensive test validates all performance optimizations working together

        // Arrange - Complex real-world scenario
        final scenarios = <Future>[];

        // Scenario 1: Heavy cache operations
        scenarios.add(Future(() async {
          for (int i = 0; i < 50; i++) {
            await cacheService.saveData('e2e_cache_$i', {
              'data': List.generate(100, (j) => 'item_${i}_$j'),
              'timestamp': DateTime.now().toIso8601String(),
            });
          }
        }));

        // Scenario 2: Compute-intensive operations
        scenarios.add(Future(() async {
          for (int i = 0; i < 10; i++) {
            await computeService.processLargeJson(jsonEncode({
              'batch': i,
              'items': List.generate(200, (j) => 'compute_item_${i}_$j'),
            }));
          }
        }));

        // Scenario 3: Progressive loading
        scenarios.add(Future(() async {
          final loader = progressiveService.createLoader<String>(
            loaderId: 'e2e_progressive',
            items: List.generate(200, (i) => 'Progressive $i'),
            itemBuilder: (item) => Container(child: Text(item)),
          );

          loader.startLoading();
          while (!loader.isCompleted) {
            await Future.delayed(const Duration(milliseconds: 50));
          }
        }));

        // Scenario 4: Cache maintenance
        scenarios.add(Future(() async {
          maintenanceService.startMaintenance();
          await Future.delayed(const Duration(seconds: 2));
        }));

        // Act - Run all scenarios concurrently
        final e2eStopwatch = Stopwatch()..start();
        await Future.wait(scenarios);
        e2eStopwatch.stop();

        // Assert - All performance targets should be maintained
        expect(
            e2eStopwatch.elapsedMilliseconds, lessThan(60000)); // 1 minute max

        // Verify cache performance
        final cacheStats = cacheService.getCacheStatistics();
        expect(cacheStats['hit_rate'], greaterThan(0.0));
        expect(cacheStats['memory_cache_size'], lessThanOrEqualTo(100));

        // Verify compute isolation
        final isolateStatus = computeService.getIsolateStatus();
        expect(isolateStatus['active_isolates'], greaterThan(0));

        // Verify progressive loading
        final progressiveStats = progressiveService.getStatistics();
        expect(progressiveStats['active_loaders'], greaterThanOrEqualTo(0));

        // Verify maintenance
        final healthMetrics = maintenanceService.getCacheHealthMetrics();
        expect(healthMetrics['maintenance_active'], isTrue);

        print(
            'End-to-end test completed in ${e2eStopwatch.elapsedMilliseconds}ms');
        print(
            'Cache hit rate: ${(cacheStats['hit_rate'] * 100).toStringAsFixed(1)}%');
        print('Memory cache size: ${cacheStats['memory_cache_size']} items');
        print('Active isolates: ${isolateStatus['active_isolates']}');
      });
    });
  });
}
