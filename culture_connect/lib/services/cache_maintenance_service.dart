import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:culture_connect/services/cache_service.dart';
import 'package:culture_connect/services/enhanced_offline_cache_service.dart';

/// Service for automated cache maintenance and optimization
class CacheMaintenanceService {
  static final CacheMaintenanceService _instance =
      CacheMaintenanceService._internal();
  factory CacheMaintenanceService() => _instance;
  CacheMaintenanceService._internal();

  Timer? _maintenanceTimer;
  final CacheService _cacheService = CacheService();
  final EnhancedOfflineCacheService _enhancedCache =
      EnhancedOfflineCacheService();

  // Configuration
  static const Duration _maintenanceInterval = Duration(hours: 6);
  static const int _maxCacheSizeMB = 200; // 200MB max cache size

  /// Start automated cache maintenance
  void startMaintenance() {
    _stopMaintenance(); // Stop existing timer if any

    _maintenanceTimer = Timer.periodic(_maintenanceInterval, (_) {
      _performMaintenance();
    });

    // Perform initial maintenance
    Future.microtask(() => _performMaintenance());

    debugPrint('✅ Cache maintenance service started');
  }

  /// Stop automated cache maintenance
  void _stopMaintenance() {
    _maintenanceTimer?.cancel();
    _maintenanceTimer = null;
  }

  /// Perform comprehensive cache maintenance
  Future<void> _performMaintenance() async {
    try {
      debugPrint('🔧 Starting cache maintenance...');

      // 1. Clean up expired cache entries
      await _cleanupExpiredEntries();

      // 2. Enforce cache size limits
      await _enforceCacheSizeLimits();

      // 3. Optimize cache structure
      await _optimizeCacheStructure();

      // 4. Preload frequently accessed content
      await _preloadFrequentContent();

      // 5. Generate cache statistics
      _logCacheStatistics();

      debugPrint('✅ Cache maintenance completed');
    } catch (e) {
      debugPrint('❌ Cache maintenance error: $e');
    }
  }

  /// Clean up expired cache entries
  Future<void> _cleanupExpiredEntries() async {
    try {
      // Clean up enhanced cache
      await _enhancedCache.performSmartCleanup();

      // Clean up basic cache (implement intelligent cleanup)
      await _cacheService.performIntelligentCleanup();

      debugPrint('🧹 Expired cache entries cleaned up');
    } catch (e) {
      debugPrint('❌ Error cleaning expired entries: $e');
    }
  }

  /// Enforce cache size limits
  Future<void> _enforceCacheSizeLimits() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      final cacheSize = await _calculateDirectorySize(cacheDir);
      final cacheSizeMB = cacheSize / (1024 * 1024);

      if (cacheSizeMB > _maxCacheSizeMB) {
        debugPrint(
            '⚠️ Cache size (${cacheSizeMB.toStringAsFixed(2)}MB) exceeds limit');

        // Perform aggressive cleanup
        await _enhancedCache.performSmartCleanup(
          targetSizeBytes: (_maxCacheSizeMB * 0.7 * 1024 * 1024).round(),
        );

        debugPrint('🗂️ Cache size reduced to target limit');
      }
    } catch (e) {
      debugPrint('❌ Error enforcing cache size limits: $e');
    }
  }

  /// Optimize cache structure for better performance
  Future<void> _optimizeCacheStructure() async {
    try {
      // Defragment cache files (placeholder for future implementation)
      // In production, this could involve:
      // - Consolidating fragmented cache files
      // - Reordering cache entries by access frequency
      // - Compressing rarely accessed cache entries

      debugPrint('🔧 Cache structure optimized');
    } catch (e) {
      debugPrint('❌ Error optimizing cache structure: $e');
    }
  }

  /// Preload frequently accessed content
  Future<void> _preloadFrequentContent() async {
    try {
      // Preload frequent content in background
      await _enhancedCache.preloadFrequentContent();

      debugPrint('📦 Frequent content preloaded');
    } catch (e) {
      debugPrint('❌ Error preloading content: $e');
    }
  }

  /// Log cache statistics for monitoring
  void _logCacheStatistics() {
    try {
      final basicStats = _cacheService.getCacheStatistics();
      final enhancedStats = _enhancedCache.getCacheStatistics();

      debugPrint('📊 Cache Statistics:');
      debugPrint(
          '  Basic Cache - Hits: ${basicStats['cache_hits']}, Misses: ${basicStats['cache_misses']}');
      debugPrint(
          '  Hit Rate: ${(basicStats['hit_rate'] * 100).toStringAsFixed(1)}%');
      debugPrint(
          '  Memory Cache Size: ${basicStats['memory_cache_size']} items');
      debugPrint(
          '  Total Cache Size: ${(basicStats['total_cache_size'] / 1024).toStringAsFixed(1)}KB');
      debugPrint('  Enhanced Cache Types: ${enhancedStats.keys.length}');
    } catch (e) {
      debugPrint('❌ Error logging cache statistics: $e');
    }
  }

  /// Calculate directory size in bytes
  Future<int> _calculateDirectorySize(Directory directory) async {
    int size = 0;
    try {
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          size += await entity.length();
        }
      }
    } catch (e) {
      debugPrint('Error calculating directory size: $e');
    }
    return size;
  }

  /// Get current cache health metrics
  Map<String, dynamic> getCacheHealthMetrics() {
    final basicStats = _cacheService.getCacheStatistics();
    final enhancedStats = _enhancedCache.getCacheStatistics();

    return {
      'basic_cache': basicStats,
      'enhanced_cache_types': enhancedStats.keys.length,
      'maintenance_active': _maintenanceTimer?.isActive ?? false,
      'last_maintenance': DateTime.now().toIso8601String(),
    };
  }

  /// Dispose the service
  void dispose() {
    _stopMaintenance();
    debugPrint('🛑 Cache maintenance service disposed');
  }
}
