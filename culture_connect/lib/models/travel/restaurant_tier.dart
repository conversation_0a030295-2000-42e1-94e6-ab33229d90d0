import 'package:flutter/material.dart';
import 'restaurant.dart';

/// Enum representing the three-tier restaurant classification system
enum RestaurantTier {
  /// High-end restaurants requiring deposits with strict cancellation policies
  highEnd,

  /// Middle-class restaurants with optional deposits and moderate policies
  middleClass,

  /// Low-end restaurants with no deposits and flexible policies
  lowEnd,
}

/// Extension for restaurant tier functionality
extension RestaurantTierExtension on RestaurantTier {
  /// Get the display name for the restaurant tier
  String get displayName {
    switch (this) {
      case RestaurantTier.highEnd:
        return 'High-End';
      case RestaurantTier.middleClass:
        return 'Middle-Class';
      case RestaurantTier.lowEnd:
        return 'Low-End';
    }
  }

  /// Get the icon for the restaurant tier
  IconData get icon {
    switch (this) {
      case RestaurantTier.highEnd:
        return Icons.diamond;
      case RestaurantTier.middleClass:
        return Icons.restaurant;
      case RestaurantTier.lowEnd:
        return Icons.fastfood;
    }
  }

  /// Get the color for the restaurant tier
  Color get color {
    switch (this) {
      case RestaurantTier.highEnd:
        return const Color(0xFFD4AF37); // Gold
      case RestaurantTier.middleClass:
        return const Color(0xFF6366F1); // Primary blue
      case RestaurantTier.lowEnd:
        return const Color(0xFF10B981); // Green
    }
  }

  /// Map RestaurantType to RestaurantTier
  static RestaurantTier fromRestaurantType(RestaurantType type) {
    switch (type) {
      case RestaurantType.fineDining:
      case RestaurantType.lounge:
        return RestaurantTier.highEnd;
      case RestaurantType.casual:
      case RestaurantType.bistro:
      case RestaurantType.pub:
      case RestaurantType.bar:
        return RestaurantTier.middleClass;
      case RestaurantType.fastFood:
      case RestaurantType.cafe:
      case RestaurantType.buffet:
      case RestaurantType.streetFood:
        return RestaurantTier.lowEnd;
    }
  }
}

/// Model representing deposit policy for a restaurant tier
class DepositPolicy {
  /// The deposit amount required
  final double amount;

  /// Whether the deposit is required or optional
  final bool isRequired;

  /// Description of incentives for paying the deposit
  final String incentiveDescription;

  /// Whether the deposit is credited to the final bill
  final bool isCreditedToBill;

  /// Currency symbol for the deposit
  final String currency;

  const DepositPolicy({
    required this.amount,
    required this.isRequired,
    required this.incentiveDescription,
    required this.isCreditedToBill,
    this.currency = '\$',
  });

  /// Get deposit policy for a specific tier
  static DepositPolicy forTier(RestaurantTier tier) {
    switch (tier) {
      case RestaurantTier.highEnd:
        return const DepositPolicy(
          amount: 30.0,
          isRequired: true,
          incentiveDescription:
              'Secures your table and will be deducted from your bill',
          isCreditedToBill: true,
        );
      case RestaurantTier.middleClass:
        return const DepositPolicy(
          amount: 10.0,
          isRequired: false,
          incentiveDescription: 'Lock your table and get 10% off your bill',
          isCreditedToBill: true,
        );
      case RestaurantTier.lowEnd:
        return const DepositPolicy(
          amount: 0.0,
          isRequired: false,
          incentiveDescription: 'No deposit required - book freely',
          isCreditedToBill: false,
        );
    }
  }

  /// Get formatted deposit amount string
  String get formattedAmount {
    if (amount == 0.0) return 'Free';
    return '$currency${amount.toStringAsFixed(0)}';
  }

  /// Get deposit badge text
  String get badgeText {
    if (amount == 0.0) return 'No Deposit';
    if (isRequired) {
      return 'Deposit: $formattedAmount (credited to bill)';
    } else {
      return 'Optional: $formattedAmount for 10% off';
    }
  }
}

/// Model representing cancellation policy for a restaurant tier
class CancellationPolicy {
  /// Minimum hours before reservation for full refund
  final int minimumHoursForRefund;

  /// Description of the refund policy
  final String refundDescription;

  /// Whether partial refunds are available
  final bool hasPartialRefund;

  /// Partial refund percentage (if applicable)
  final double partialRefundPercentage;

  const CancellationPolicy({
    required this.minimumHoursForRefund,
    required this.refundDescription,
    this.hasPartialRefund = false,
    this.partialRefundPercentage = 0.0,
  });

  /// Get cancellation policy for a specific tier
  static CancellationPolicy forTier(RestaurantTier tier) {
    switch (tier) {
      case RestaurantTier.highEnd:
        return const CancellationPolicy(
          minimumHoursForRefund: 48,
          refundDescription: 'Full refund if cancelled ≥48h prior',
        );
      case RestaurantTier.middleClass:
        return const CancellationPolicy(
          minimumHoursForRefund: 24,
          refundDescription: 'Full credit if cancelled ≥24h prior',
        );
      case RestaurantTier.lowEnd:
        return const CancellationPolicy(
          minimumHoursForRefund: 2,
          refundDescription: 'No penalty if cancelled ≥2h prior',
        );
    }
  }

  /// Get cancellation badge text
  String get badgeText {
    return 'Cancel free ≥${minimumHoursForRefund}h before';
  }

  /// Check if cancellation is allowed for a given time before reservation
  bool canCancelWithoutPenalty(DateTime reservationDateTime) {
    final now = DateTime.now();
    final hoursUntilReservation = reservationDateTime.difference(now).inHours;
    return hoursUntilReservation >= minimumHoursForRefund;
  }
}

/// Utility class for restaurant tier operations
class RestaurantTierUtils {
  /// Get all available tiers
  static List<RestaurantTier> get allTiers => RestaurantTier.values;

  /// Get tier from string (for serialization)
  static RestaurantTier? tierFromString(String tierString) {
    try {
      return RestaurantTier.values.firstWhere(
        (tier) => tier.toString().split('.').last == tierString,
      );
    } catch (e) {
      return null;
    }
  }

  /// Convert tier to string (for serialization)
  static String tierToString(RestaurantTier tier) {
    return tier.toString().split('.').last;
  }

  /// Get tier priority (for sorting, higher number = higher tier)
  static int getTierPriority(RestaurantTier tier) {
    switch (tier) {
      case RestaurantTier.highEnd:
        return 3;
      case RestaurantTier.middleClass:
        return 2;
      case RestaurantTier.lowEnd:
        return 1;
    }
  }
}
