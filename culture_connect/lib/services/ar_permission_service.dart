import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';

/// Service to handle AR-related permissions with user-friendly messaging
class ARPermissionService {
  static const String _logTag = 'ARPermissionService';

  /// Check and request all AR-related permissions
  static Future<ARPermissionResult> requestARPermissions(BuildContext context) async {
    try {
      debugPrint('$_logTag: Requesting AR permissions');

      // Check camera permission first (most critical for AR)
      final cameraResult = await _requestCameraPermission(context);
      if (!cameraResult.isGranted) {
        return cameraResult;
      }

      // Check location permission (needed for location-based AR)
      final locationResult = await _requestLocationPermission(context);
      if (!locationResult.isGranted) {
        return locationResult;
      }

      // Check microphone permission (optional for AR voice features)
      final microphoneResult = await _requestMicrophonePermission(context);

      return ARPermissionResult(
        isGranted: true,
        permissionType: ARPermissionType.all,
        message: 'All AR permissions granted successfully',
        canShowRationale: false,
        hasOptionalPermissions: microphoneResult.isGranted,
      );
    } catch (e) {
      debugPrint('$_logTag: Error requesting AR permissions: $e');
      return ARPermissionResult(
        isGranted: false,
        permissionType: ARPermissionType.camera,
        message: 'Error requesting permissions: $e',
        canShowRationale: false,
      );
    }
  }

  /// Request camera permission with user education
  static Future<ARPermissionResult> _requestCameraPermission(BuildContext context) async {
    final permission = Permission.camera;
    final status = await permission.status;

    debugPrint('$_logTag: Camera permission status: $status');

    switch (status) {
      case PermissionStatus.granted:
        return ARPermissionResult(
          isGranted: true,
          permissionType: ARPermissionType.camera,
          message: 'Camera permission already granted',
          canShowRationale: false,
        );

      case PermissionStatus.denied:
        // Show educational dialog before requesting
        final shouldRequest = await _showPermissionEducationDialog(
          context,
          'Camera Access Required',
          'CultureConnect needs camera access to provide immersive AR experiences that bring cultural landmarks to life.',
          'Allow Camera Access',
        );

        if (!shouldRequest) {
          return ARPermissionResult(
            isGranted: false,
            permissionType: ARPermissionType.camera,
            message: 'Camera permission denied by user',
            canShowRationale: true,
          );
        }

        final result = await permission.request();
        return ARPermissionResult(
          isGranted: result == PermissionStatus.granted,
          permissionType: ARPermissionType.camera,
          message: result == PermissionStatus.granted
              ? 'Camera permission granted'
              : 'Camera permission denied',
          canShowRationale: result == PermissionStatus.denied,
        );

      case PermissionStatus.permanentlyDenied:
        await _showPermissionSettingsDialog(
          context,
          'Camera Permission Required',
          'Camera access is required for AR features. Please enable camera permission in Settings.',
        );
        return ARPermissionResult(
          isGranted: false,
          permissionType: ARPermissionType.camera,
          message: 'Camera permission permanently denied',
          canShowRationale: false,
          requiresSettings: true,
        );

      default:
        return ARPermissionResult(
          isGranted: false,
          permissionType: ARPermissionType.camera,
          message: 'Camera permission status unknown',
          canShowRationale: false,
        );
    }
  }

  /// Request location permission for location-based AR
  static Future<ARPermissionResult> _requestLocationPermission(BuildContext context) async {
    final permission = Permission.locationWhenInUse;
    final status = await permission.status;

    debugPrint('$_logTag: Location permission status: $status');

    switch (status) {
      case PermissionStatus.granted:
        return ARPermissionResult(
          isGranted: true,
          permissionType: ARPermissionType.location,
          message: 'Location permission already granted',
          canShowRationale: false,
        );

      case PermissionStatus.denied:
        final shouldRequest = await _showPermissionEducationDialog(
          context,
          'Location Access Required',
          'CultureConnect needs location access to show nearby cultural landmarks and provide location-based AR experiences.',
          'Allow Location Access',
        );

        if (!shouldRequest) {
          return ARPermissionResult(
            isGranted: false,
            permissionType: ARPermissionType.location,
            message: 'Location permission denied by user',
            canShowRationale: true,
          );
        }

        final result = await permission.request();
        return ARPermissionResult(
          isGranted: result == PermissionStatus.granted,
          permissionType: ARPermissionType.location,
          message: result == PermissionStatus.granted
              ? 'Location permission granted'
              : 'Location permission denied',
          canShowRationale: result == PermissionStatus.denied,
        );

      case PermissionStatus.permanentlyDenied:
        await _showPermissionSettingsDialog(
          context,
          'Location Permission Required',
          'Location access is required for location-based AR features. Please enable location permission in Settings.',
        );
        return ARPermissionResult(
          isGranted: false,
          permissionType: ARPermissionType.location,
          message: 'Location permission permanently denied',
          canShowRationale: false,
          requiresSettings: true,
        );

      default:
        return ARPermissionResult(
          isGranted: false,
          permissionType: ARPermissionType.location,
          message: 'Location permission status unknown',
          canShowRationale: false,
        );
    }
  }

  /// Request microphone permission (optional for AR voice features)
  static Future<ARPermissionResult> _requestMicrophonePermission(BuildContext context) async {
    final permission = Permission.microphone;
    final status = await permission.status;

    debugPrint('$_logTag: Microphone permission status: $status');

    if (status == PermissionStatus.granted) {
      return ARPermissionResult(
        isGranted: true,
        permissionType: ARPermissionType.microphone,
        message: 'Microphone permission granted',
        canShowRationale: false,
      );
    }

    // Microphone is optional, so we don't force it
    return ARPermissionResult(
      isGranted: false,
      permissionType: ARPermissionType.microphone,
      message: 'Microphone permission not granted (optional)',
      canShowRationale: false,
    );
  }

  /// Show educational dialog before requesting permission
  static Future<bool> _showPermissionEducationDialog(
    BuildContext context,
    String title,
    String message,
    String actionText,
  ) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Not Now'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(actionText),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// Show dialog to guide user to settings
  static Future<void> _showPermissionSettingsDialog(
    BuildContext context,
    String title,
    String message,
  ) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  /// Check if all required AR permissions are granted
  static Future<bool> areARPermissionsGranted() async {
    final cameraStatus = await Permission.camera.status;
    final locationStatus = await Permission.locationWhenInUse.status;

    return cameraStatus == PermissionStatus.granted &&
           locationStatus == PermissionStatus.granted;
  }

  /// Get current permission status for all AR permissions
  static Future<Map<ARPermissionType, PermissionStatus>> getARPermissionStatus() async {
    return {
      ARPermissionType.camera: await Permission.camera.status,
      ARPermissionType.location: await Permission.locationWhenInUse.status,
      ARPermissionType.microphone: await Permission.microphone.status,
    };
  }
}

/// Result of AR permission request
class ARPermissionResult {
  final bool isGranted;
  final ARPermissionType permissionType;
  final String message;
  final bool canShowRationale;
  final bool requiresSettings;
  final bool hasOptionalPermissions;

  const ARPermissionResult({
    required this.isGranted,
    required this.permissionType,
    required this.message,
    required this.canShowRationale,
    this.requiresSettings = false,
    this.hasOptionalPermissions = false,
  });

  @override
  String toString() {
    return 'ARPermissionResult(isGranted: $isGranted, type: $permissionType, message: $message)';
  }
}

/// Types of AR permissions
enum ARPermissionType {
  camera,
  location,
  microphone,
  all,
}
