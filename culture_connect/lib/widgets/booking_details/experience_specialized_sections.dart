import 'package:flutter/material.dart';
import 'package:culture_connect/design_system/premium_design_tokens.dart';

/// Specialized sections for different experience types
/// This file contains the remaining specialized section builders
class ExperienceSpecializedSections {
  // ============================================================================
  // COOKING CLASS SPECIALIZED SECTIONS (CONTINUED)
  // ============================================================================

  /// Cooking Class - Menu & Recipes Section
  static Widget buildMenuRecipesSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.restaurant_menu,
                color: const Color(0xFFEF4444),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Menu & Recipes',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildMenuCard('Appetizer', 'Bruschetta with Tomato & Basil',
              'Fresh tomatoes, basil, garlic, olive oil'),
          _buildMenuCard('Main Course', 'Homemade Pasta Carbonara',
              'Eggs, pancetta, parmesan, black pepper'),
          _buildMenuCard(
              'Dessert', 'Tiramisu', 'Mascarpone, coffee, ladyfingers, cocoa'),
        ],
      ),
    );
  }

  static Widget _buildMenuCard(String course, String dish, String ingredients) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFEF4444).withAlpha(26),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFEF4444).withAlpha(51),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFFEF4444),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  course,
                  style: PremiumDesignTokens.labelSmall.copyWith(
                    color: PremiumDesignTokens.neutralWhite,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  dish,
                  style: PremiumDesignTokens.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            ingredients,
            style: PremiumDesignTokens.bodyMedium.copyWith(
              color: PremiumDesignTokens.neutralGray600,
            ),
          ),
        ],
      ),
    );
  }

  /// Cooking Class - Kitchen Facilities Section
  static Widget buildKitchenFacilitiesSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.kitchen,
                color: const Color(0xFF06B6D4),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Kitchen Facilities',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: [
              _buildFacilityChip(
                  'Professional Stoves', Icons.local_fire_department),
              _buildFacilityChip('Fresh Ingredients', Icons.eco),
              _buildFacilityChip('All Utensils', Icons.restaurant),
              _buildFacilityChip('Recipe Cards', Icons.receipt),
              _buildFacilityChip('Aprons Provided', Icons.checkroom),
              _buildFacilityChip('Take-home Containers', Icons.takeout_dining),
            ],
          ),
        ],
      ),
    );
  }

  static Widget _buildFacilityChip(String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFF06B6D4).withAlpha(26),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF06B6D4).withAlpha(51),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: const Color(0xFF06B6D4),
            size: 16,
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: PremiumDesignTokens.bodySmall.copyWith(
              fontWeight: FontWeight.w500,
              color: const Color(0xFF06B6D4),
            ),
          ),
        ],
      ),
    );
  }

  // ============================================================================
  // ART WORKSHOP SPECIALIZED SECTIONS
  // ============================================================================

  /// Art Workshop - Instructor Profile Section
  static Widget buildInstructorProfileSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.palette,
                color: const Color(0xFF8B5CF6),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Your Instructor',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: const Color(0xFF8B5CF6),
                  borderRadius: BorderRadius.circular(40),
                ),
                child: Icon(
                  Icons.brush,
                  color: PremiumDesignTokens.neutralWhite,
                  size: 40,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Isabella Chen',
                      style: PremiumDesignTokens.bodyLarge.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Professional artist & gallery owner',
                      style: PremiumDesignTokens.bodyMedium.copyWith(
                        color: PremiumDesignTokens.neutralGray600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          color: const Color(0xFFF59E0B),
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '4.8 (89 reviews)',
                          style: PremiumDesignTokens.bodySmall.copyWith(
                            color: PremiumDesignTokens.neutralGray600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Art Workshop - Materials Provided Section
  static Widget buildMaterialsProvidedSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.art_track,
                color: const Color(0xFF10B981),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Materials Provided',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: [
              _buildMaterialChip('Canvas (16x20")', Icons.crop_landscape),
              _buildMaterialChip('Acrylic Paints', Icons.palette),
              _buildMaterialChip('Brushes Set', Icons.brush),
              _buildMaterialChip('Easel', Icons.architecture),
              _buildMaterialChip('Apron', Icons.checkroom),
              _buildMaterialChip('Take-home Frame', Icons.photo),
            ],
          ),
        ],
      ),
    );
  }

  static Widget _buildMaterialChip(String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFF10B981).withAlpha(26),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF10B981).withAlpha(51),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: const Color(0xFF10B981),
            size: 16,
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: PremiumDesignTokens.bodySmall.copyWith(
              fontWeight: FontWeight.w500,
              color: const Color(0xFF10B981),
            ),
          ),
        ],
      ),
    );
  }

  /// Art Workshop - Studio Information Section
  static Widget buildStudioInformationSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.business,
                color: const Color(0xFFF59E0B),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Studio Information',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildStudioInfoRow(
              Icons.location_on, 'Location', 'SoHo Art District'),
          _buildStudioInfoRow(
              Icons.wb_sunny, 'Lighting', 'Natural north-facing light'),
          _buildStudioInfoRow(Icons.people, 'Class Size', 'Maximum 8 students'),
          _buildStudioInfoRow(
              Icons.access_time, 'Duration', '3 hours with breaks'),
        ],
      ),
    );
  }

  static Widget _buildStudioInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            color: const Color(0xFFF59E0B),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  label,
                  style: PremiumDesignTokens.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                    color: PremiumDesignTokens.neutralGray700,
                  ),
                ),
                Flexible(
                  child: Text(
                    value,
                    style: PremiumDesignTokens.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // ============================================================================
  // ADVENTURE ACTIVITY SPECIALIZED SECTIONS
  // ============================================================================

  /// Adventure Activity - Safety Equipment Section
  static Widget buildSafetyEquipmentSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.security,
                color: const Color(0xFFEF4444),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Safety Equipment',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: [
              _buildSafetyChip('Helmets', Icons.sports_motorsports),
              _buildSafetyChip('Harnesses', Icons.safety_divider),
              _buildSafetyChip('First Aid Kit', Icons.medical_services),
              _buildSafetyChip('Emergency Radio', Icons.radio),
              _buildSafetyChip('Life Jackets', Icons.pool),
              _buildSafetyChip('Professional Guide', Icons.person_pin),
            ],
          ),
        ],
      ),
    );
  }

  static Widget _buildSafetyChip(String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFFEF4444).withAlpha(26),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFFEF4444).withAlpha(51),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: const Color(0xFFEF4444),
            size: 16,
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: PremiumDesignTokens.bodySmall.copyWith(
              fontWeight: FontWeight.w500,
              color: const Color(0xFFEF4444),
            ),
          ),
        ],
      ),
    );
  }
}
