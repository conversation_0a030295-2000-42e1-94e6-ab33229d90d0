import React, { useState, useEffect } from "react";
import { StyleSheet, View, Text, ScrollView, Pressable, Switch, Alert, Dimensions, RefreshControl } from "react-native";
import { useRouter } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { Image } from "expo-image";
import { 
  User, Settings, CreditCard, Bell, Shield, HelpCircle, LogOut, 
  MapPin, Calendar, Plane, Award, Star, TrendingUp, Heart, 
  Clock, Globe, Camera, Edit3, ChevronRight, Bookmark, 
  MessageCircle, Eye, Zap, Target, Crown, Gift, Compass,
  Phone, Mail, Lock, Palette, Moon, Sun, Languages,
  Download, Share, FileText, AlertCircle, CheckCircle
} from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { useUserStore } from "@/store/userStore";
import { useBookingStore } from "@/store/bookingStore";

const { width } = Dimensions.get('window');

const travelStats = [
  {
    id: "1",
    title: "Countries Visited",
    value: "12",
    icon: <Globe size={20} color={colors.primary} />,
    trend: "+2",
    color: colors.primary,
    description: "This year",
  },
  {
    id: "2",
    title: "Total Trips",
    value: "28",
    icon: <MapPin size={20} color={colors.secondary} />,
    trend: "+5",
    color: colors.secondary,
    description: "All time",
  },
  {
    id: "3",
    title: "Miles Traveled",
    value: "45.2K",
    icon: <Plane size={20} color={colors.accent} />,
    trend: "+8.1K",
    color: colors.accent,
    description: "This year",
  },
  {
    id: "4",
    title: "Saved Favorites",
    value: "156",
    icon: <Heart size={20} color="#E91E63" />,
    trend: "+23",
    color: "#E91E63",
    description: "Destinations",
  },
];

const achievements = [
  {
    id: "1",
    title: "Explorer",
    description: "Visited 10+ countries",
    icon: <Compass size={24} color={colors.white} />,
    gradient: [colors.primary, colors.primaryLight] as const,
    unlocked: true,
    progress: 100,
  },
  {
    id: "2",
    title: "Frequent Flyer",
    description: "Booked 20+ flights",
    icon: <Plane size={24} color={colors.white} />,
    gradient: [colors.secondary, colors.accent] as const,
    unlocked: true,
    progress: 100,
  },
  {
    id: "3",
    title: "Travel Guru",
    description: "50+ bookings completed",
    icon: <Crown size={24} color={colors.white} />,
    gradient: ["#FFD700", "#FFA500"] as const,
    unlocked: false,
    progress: 76,
  },
  {
    id: "4",
    title: "Social Traveler",
    description: "Share 25+ experiences",
    icon: <Share size={24} color={colors.white} />,
    gradient: ["#FF6B6B", "#FF8E8E"] as const,
    unlocked: false,
    progress: 32,
  },
];

const quickActions = [
  {
    id: "1",
    title: "Edit Profile",
    description: "Update your personal information",
    icon: <Edit3 size={20} color={colors.primary} />,
    backgroundColor: colors.primary + '15',
    onPress: () => {},
  },
  {
    id: "2",
    title: "Travel Preferences",
    description: "Customize your travel style",
    icon: <Settings size={20} color={colors.secondary} />,
    backgroundColor: colors.secondary + '15',
    onPress: () => {},
  },
  {
    id: "3",
    title: "Saved Places",
    description: "View your favorite destinations",
    icon: <Bookmark size={20} color={colors.accent} />,
    backgroundColor: colors.accent + '15',
    onPress: () => {},
  },
  {
    id: "4",
    title: "Travel Documents",
    description: "Manage passports & visas",
    icon: <FileText size={20} color="#8B5CF6" />,
    backgroundColor: "#8B5CF6" + '15',
    onPress: () => {},
  },
];

export default function ProfileScreen() {
  const router = useRouter();
  const { user, logout } = useUserStore();
  const { bookings, fetchBookings } = useBookingStore();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [locationEnabled, setLocationEnabled] = useState(true);
  const [darkModeEnabled, setDarkModeEnabled] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchBookings();
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchBookings().finally(() => setRefreshing(false));
  };

  const handleLogout = () => {
    Alert.alert(
      "Logout",
      "Are you sure you want to logout?",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Logout",
          onPress: () => {
            logout();
            router.replace("/");
          },
          style: "destructive",
        },
      ],
      { cancelable: true }
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <ScrollView 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <Text style={styles.greeting}>Your Profile</Text>
            <Pressable style={styles.iconButton}>
              <Settings size={20} color={colors.text} />
            </Pressable>
          </View>
        </View>

        {/* Profile Card */}
        <View style={styles.profileContainer}>
          <View style={styles.profileCard}>
            <LinearGradient
              colors={[colors.primary + '08', colors.secondary + '08']}
              style={styles.profileGradient}
            />
            <View style={styles.profileContent}>
              <View style={styles.profileHeader}>
                <View style={styles.avatarContainer}>
                  {user?.avatar ? (
                    <Image
                      source={{ uri: user.avatar }}
                      style={styles.avatar}
                      contentFit="cover"
                    />
                  ) : (
                    <View style={styles.avatarPlaceholder}>
                      <User size={32} color={colors.white} />
                    </View>
                  )}
                  <Pressable style={styles.cameraButton}>
                    <Camera size={14} color={colors.white} />
                  </Pressable>
                </View>
                <View style={styles.profileInfo}>
                  <Text style={styles.userName}>{user?.name || "Travel Explorer"}</Text>
                  <Text style={styles.userEmail}>{user?.email || "<EMAIL>"}</Text>
                  <View style={styles.membershipBadge}>
                    <Crown size={12} color="#FFD700" />
                    <Text style={styles.membershipText}>Premium Member</Text>
                  </View>
                </View>
              </View>
              
              <View style={styles.profileStats}>
                <View style={styles.profileStat}>
                  <Text style={styles.profileStatValue}>{bookings.length}</Text>
                  <Text style={styles.profileStatLabel}>Bookings</Text>
                </View>
                <View style={styles.profileStatDivider} />
                <View style={styles.profileStat}>
                  <Text style={styles.profileStatValue}>4.8</Text>
                  <Text style={styles.profileStatLabel}>Rating</Text>
                </View>
                <View style={styles.profileStatDivider} />
                <View style={styles.profileStat}>
                  <Text style={styles.profileStatValue}>2.5K</Text>
                  <Text style={styles.profileStatLabel}>Points</Text>
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            {quickActions.map((action) => (
              <Pressable
                key={action.id}
                style={[styles.quickActionCard, { backgroundColor: action.backgroundColor }]}
                onPress={action.onPress}
              >
                <View style={styles.quickActionIcon}>
                  {action.icon}
                </View>
                <Text style={styles.quickActionTitle}>{action.title}</Text>
                <Text style={styles.quickActionDescription}>{action.description}</Text>
              </Pressable>
            ))}
          </View>
        </View>

        {/* Travel Statistics */}
        <View style={styles.statsContainer}>
          <View style={styles.statsHeader}>
            <TrendingUp size={20} color={colors.primary} />
            <Text style={styles.statsTitle}>Travel Statistics</Text>
          </View>
          <Text style={styles.statsSubtitle}>Your journey at a glance</Text>
          <View style={styles.statsGrid}>
            {travelStats.map((stat, index) => (
              <View key={stat.id} style={[styles.statCard, { backgroundColor: index % 2 === 0 ? colors.white : colors.backgroundSecondary }]}>
                <LinearGradient
                  colors={[stat.color + '10', stat.color + '05']}
                  style={styles.statGradient}
                />
                <View style={styles.statHeader}>
                  <View style={[styles.statIconContainer, { backgroundColor: stat.color + '15' }]}>
                    {stat.icon}
                  </View>
                  <View style={styles.trendContainer}>
                    <TrendingUp size={12} color="#10B981" />
                    <Text style={styles.trendText}>{stat.trend}</Text>
                  </View>
                </View>
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.title}</Text>
                <Text style={styles.statDescription}>{stat.description}</Text>
                <View style={styles.statProgress}>
                  <View style={[styles.statProgressBar, { width: `${75 + (index * 5)}%`, backgroundColor: stat.color }]} />
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Achievements */}
        <View style={styles.achievementsContainer}>
          <View style={styles.achievementsHeader}>
            <View style={styles.achievementsHeaderLeft}>
              <View style={styles.achievementsIconBadge}>
                <Award size={18} color={colors.white} />
              </View>
              <View>
                <Text style={styles.achievementsTitle}>Achievements</Text>
                <Text style={styles.achievementsSubtitle}>Your travel milestones</Text>
              </View>
            </View>
            <Pressable style={styles.viewAllButton}>
              <Text style={styles.viewAllText}>View All</Text>
              <ChevronRight size={14} color={colors.primary} />
            </Pressable>
          </View>
          
          <View style={styles.achievementsList}>
            {achievements.map((achievement) => (
              <View key={achievement.id} style={styles.achievementCard}>
                <LinearGradient
                  colors={achievement.unlocked ? achievement.gradient : [colors.textLight + '20', colors.textLight + '10']}
                  style={styles.achievementGradient}
                />
                <View style={styles.achievementContent}>
                  <View style={styles.achievementIcon}>
                    {achievement.unlocked ? achievement.icon : <Lock size={24} color={colors.textLight} />}
                  </View>
                  <View style={styles.achievementInfo}>
                    <Text style={[styles.achievementTitle, { color: achievement.unlocked ? colors.white : colors.textLight }]}>
                      {achievement.title}
                    </Text>
                    <Text style={[styles.achievementDescription, { color: achievement.unlocked ? 'rgba(255,255,255,0.9)' : colors.textLight }]}>
                      {achievement.description}
                    </Text>
                    {!achievement.unlocked && (
                      <View style={styles.achievementProgress}>
                        <View style={styles.achievementProgressBar}>
                          <View style={[styles.achievementProgressFill, { width: `${achievement.progress}%` }]} />
                        </View>
                        <Text style={styles.achievementProgressText}>{achievement.progress}%</Text>
                      </View>
                    )}
                  </View>
                  {achievement.unlocked && (
                    <View style={styles.achievementBadge}>
                      <CheckCircle size={16} color={colors.white} />
                    </View>
                  )}
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Account Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account Settings</Text>
          
          <Pressable style={styles.menuItem}>
            <View style={styles.menuIconContainer}>
              <User size={20} color={colors.white} />
            </View>
            <View style={styles.menuItemContent}>
              <Text style={styles.menuItemText}>Personal Information</Text>
              <Text style={styles.menuItemSubtext}>Name, email, phone number</Text>
            </View>
            <ChevronRight size={16} color={colors.textLight} />
          </Pressable>
          
          <Pressable style={styles.menuItem}>
            <View style={[styles.menuIconContainer, { backgroundColor: colors.secondary }]}>
              <CreditCard size={20} color={colors.white} />
            </View>
            <View style={styles.menuItemContent}>
              <Text style={styles.menuItemText}>Payment Methods</Text>
              <Text style={styles.menuItemSubtext}>Cards, wallets, billing</Text>
            </View>
            <ChevronRight size={16} color={colors.textLight} />
          </Pressable>
          
          <Pressable style={styles.menuItem}>
            <View style={[styles.menuIconContainer, { backgroundColor: colors.accent }]}>
              <Shield size={20} color={colors.white} />
            </View>
            <View style={styles.menuItemContent}>
              <Text style={styles.menuItemText}>Security & Privacy</Text>
              <Text style={styles.menuItemSubtext}>Password, 2FA, privacy</Text>
            </View>
            <ChevronRight size={16} color={colors.textLight} />
          </Pressable>
        </View>

        {/* App Preferences */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>App Preferences</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <View style={[styles.menuIconContainer, { backgroundColor: colors.info }]}>
                <Bell size={20} color={colors.white} />
              </View>
              <View style={styles.settingContent}>
                <Text style={styles.settingText}>Push Notifications</Text>
                <Text style={styles.settingSubtext}>Travel updates & offers</Text>
              </View>
            </View>
            <Switch
              value={notificationsEnabled}
              onValueChange={setNotificationsEnabled}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.white}
            />
          </View>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <View style={[styles.menuIconContainer, { backgroundColor: colors.success }]}>
                <MapPin size={20} color={colors.white} />
              </View>
              <View style={styles.settingContent}>
                <Text style={styles.settingText}>Location Services</Text>
                <Text style={styles.settingSubtext}>For personalized recommendations</Text>
              </View>
            </View>
            <Switch
              value={locationEnabled}
              onValueChange={setLocationEnabled}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.white}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <View style={[styles.menuIconContainer, { backgroundColor: "#6366F1" }]}>
                {darkModeEnabled ? <Moon size={20} color={colors.white} /> : <Sun size={20} color={colors.white} />}
              </View>
              <View style={styles.settingContent}>
                <Text style={styles.settingText}>Dark Mode</Text>
                <Text style={styles.settingSubtext}>Easier on the eyes</Text>
              </View>
            </View>
            <Switch
              value={darkModeEnabled}
              onValueChange={setDarkModeEnabled}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.white}
            />
          </View>
        </View>

        {/* Support & Legal */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support & Legal</Text>
          
          <Pressable style={styles.menuItem}>
            <View style={[styles.menuIconContainer, { backgroundColor: colors.info }]}>
              <HelpCircle size={20} color={colors.white} />
            </View>
            <View style={styles.menuItemContent}>
              <Text style={styles.menuItemText}>Help Center</Text>
              <Text style={styles.menuItemSubtext}>FAQs, guides, contact support</Text>
            </View>
            <ChevronRight size={16} color={colors.textLight} />
          </Pressable>
          
          <Pressable style={styles.menuItem}>
            <View style={[styles.menuIconContainer, { backgroundColor: colors.warning }]}>
              <Shield size={20} color={colors.white} />
            </View>
            <View style={styles.menuItemContent}>
              <Text style={styles.menuItemText}>Privacy Policy</Text>
              <Text style={styles.menuItemSubtext}>How we protect your data</Text>
            </View>
            <ChevronRight size={16} color={colors.textLight} />
          </Pressable>
          
          <Pressable style={styles.menuItem}>
            <View style={[styles.menuIconContainer, { backgroundColor: "#8B5CF6" }]}>
              <FileText size={20} color={colors.white} />
            </View>
            <View style={styles.menuItemContent}>
              <Text style={styles.menuItemText}>Terms of Service</Text>
              <Text style={styles.menuItemSubtext}>Usage terms & conditions</Text>
            </View>
            <ChevronRight size={16} color={colors.textLight} />
          </Pressable>
        </View>

        {/* Logout Button */}
        <View style={styles.logoutContainer}>
          <Pressable style={styles.logoutButton} onPress={handleLogout}>
            <LogOut size={20} color={colors.error} />
            <Text style={styles.logoutText}>Sign Out</Text>
          </Pressable>
        </View>

        {/* App Info */}
        <View style={styles.appInfoContainer}>
          <Text style={styles.versionText}>TravelApp v1.2.0</Text>
          <Text style={styles.buildText}>Build 2024.01.15</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.md,
  },
  headerTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  greeting: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
  iconButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.white,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },

  // Profile Card
  profileContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  profileCard: {
    backgroundColor: colors.white,
    borderRadius: 24,
    padding: spacing.xl,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 6,
    overflow: "hidden",
  },
  profileGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  profileContent: {
    alignItems: "center",
  },
  profileHeader: {
    alignItems: "center",
    marginBottom: spacing.lg,
  },
  avatarContainer: {
    position: "relative",
    marginBottom: spacing.md,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: colors.white,
  },
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 3,
    borderColor: colors.white,
  },
  cameraButton: {
    position: "absolute",
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: colors.white,
  },
  profileInfo: {
    alignItems: "center",
  },
  userName: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  userEmail: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  membershipBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFF9E6",
    borderRadius: 16,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },
  membershipText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium as any,
    color: "#B8860B",
    marginLeft: spacing.xs,
  },
  profileStats: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
    paddingTop: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
  },
  profileStat: {
    alignItems: "center",
    flex: 1,
  },
  profileStatValue: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  profileStatLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
  },
  profileStatDivider: {
    width: 1,
    height: 32,
    backgroundColor: colors.borderLight,
    marginHorizontal: spacing.md,
  },

  // Quick Actions
  quickActionsContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  quickActionsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  quickActionCard: {
    width: (width - spacing.lg * 2 - spacing.md) / 2,
    borderRadius: 16,
    padding: spacing.lg,
    marginBottom: spacing.md,
    alignItems: "center",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.white,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: spacing.sm,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  quickActionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    textAlign: "center",
    marginBottom: spacing.xs,
  },
  quickActionDescription: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    textAlign: "center",
    lineHeight: typography.lineHeights.normal * typography.sizes.xs,
  },

  // Statistics
  statsContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  statsHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  statsTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  statsSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.lg,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
  },
  statsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  statCard: {
    width: (width - spacing.lg * 2 - spacing.sm) / 2,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    overflow: "hidden",
  },
  statGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  statHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  statIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
  },
  trendContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#ECFDF5",
    borderRadius: 12,
    paddingHorizontal: spacing.xs,
    paddingVertical: 4,
  },
  trendText: {
    fontFamily: typography.fontFamily,
    fontSize: 10,
    fontWeight: typography.weights.bold as any,
    color: "#10B981",
    marginLeft: 2,
  },
  statValue: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
    marginBottom: spacing.xs,
  },
  statDescription: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textLight,
    marginBottom: spacing.sm,
  },
  statProgress: {
    height: 4,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 2,
    overflow: "hidden",
  },
  statProgressBar: {
    height: "100%",
    borderRadius: 2,
  },

  // Achievements
  achievementsContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  achievementsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: spacing.lg,
  },
  achievementsHeaderLeft: {
    flexDirection: "row",
    alignItems: "flex-start",
    flex: 1,
  },
  achievementsIconBadge: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: "#FFD700",
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  achievementsTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  achievementsSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
  },
  viewAllButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.primary + '10',
    borderRadius: 20,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  viewAllText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium as any,
    color: colors.primary,
    marginRight: spacing.xs,
  },
  achievementsList: {
    gap: spacing.md,
  },
  achievementCard: {
    borderRadius: 16,
    overflow: "hidden",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 6,
  },
  achievementGradient: {
    padding: spacing.lg,
  },
  achievementContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  achievementIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
  },
  achievementInfo: {
    flex: 1,
  },
  achievementTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    marginBottom: spacing.xs,
  },
  achievementDescription: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    lineHeight: typography.lineHeights.normal * typography.sizes.sm,
    marginBottom: spacing.sm,
  },
  achievementProgress: {
    flexDirection: "row",
    alignItems: "center",
    gap: spacing.sm,
  },
  achievementProgressBar: {
    flex: 1,
    height: 6,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    borderRadius: 3,
    overflow: "hidden",
  },
  achievementProgressFill: {
    height: "100%",
    backgroundColor: colors.white,
    borderRadius: 3,
  },
  achievementProgressText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium as any,
    color: colors.textLight,
  },
  achievementBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
  },

  // Settings Sections
  section: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.lg,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  menuIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
  },
  menuItemContent: {
    flex: 1,
  },
  menuItemText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.text,
    fontWeight: typography.weights.medium as any,
    marginBottom: spacing.xs,
  },
  menuItemSubtext: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.normal * typography.sizes.sm,
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  settingInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  settingContent: {
    flex: 1,
  },
  settingText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.text,
    fontWeight: typography.weights.medium as any,
    marginBottom: spacing.xs,
  },
  settingSubtext: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.normal * typography.sizes.sm,
  },

  // Logout
  logoutContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  logoutButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: spacing.lg,
    borderRadius: 16,
    borderWidth: 1.5,
    borderColor: colors.error,
    backgroundColor: colors.error + '08',
  },
  logoutText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.error,
    fontWeight: typography.weights.semibold as any,
    marginLeft: spacing.sm,
  },

  // App Info
  appInfoContainer: {
    alignItems: "center",
    paddingBottom: spacing.xl,
  },
  versionText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textLight,
    fontWeight: typography.weights.medium as any,
    marginBottom: spacing.xs,
  },
  buildText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textLight,
  },
});