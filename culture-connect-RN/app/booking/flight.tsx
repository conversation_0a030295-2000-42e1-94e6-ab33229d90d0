import React, { useState } from "react";
import { StyleSheet, View, Text, ScrollView, Pressable, TextInput, TouchableOpacity } from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { ArrowLeft, Calendar, Users, Plus, Minus, ArrowUpDown, Circle, PlaneTakeoff, PlaneLanding } from "lucide-react-native";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { Button } from "@/components/Button";
import { CalendarModal } from "@/components/CalendarModal";
import { ClassSelectionModal } from "@/components/ClassSelectionModal";
import { AirportDropdown } from "@/components/AirportDropdown";
import { Airport } from "@/mocks/airports";
import { LinearGradient } from "expo-linear-gradient";

export default function FlightBookingScreen() {
  const router = useRouter();
  const { services, experience } = useLocalSearchParams();
  const [from, setFrom] = useState("New York");
  const [fromCode, setFromCode] = useState("JFK");
  const [to, setTo] = useState("London");
  const [toCode, setToCode] = useState("LHR");
  const [departDate, setDepartDate] = useState<Date>(new Date(2024, 5, 10)); // June 10, 2024
  const [returnDate, setReturnDate] = useState<Date | null>(new Date(2024, 5, 17)); // June 17, 2024
  const [travelers, setTravelers] = useState(1);
  const [tripType, setTripType] = useState<"round-trip" | "one-way">("round-trip");
  const [showCalendar, setShowCalendar] = useState(false);
  const [selectedClass, setSelectedClass] = useState('Economy Class');
  const [showClassSelection, setShowClassSelection] = useState(false);
  const [showFromDropdown, setShowFromDropdown] = useState(false);
  const [showToDropdown, setShowToDropdown] = useState(false);

  const handleIncrementTravelers = () => {
    setTravelers(prev => prev + 1);
  };

  const handleDecrementTravelers = () => {
    if (travelers > 1) {
      setTravelers(prev => prev - 1);
    }
  };

  const handleSearchFlights = () => {
    router.push("/booking/flight-results?from=" + encodeURIComponent(from) + "&to=" + encodeURIComponent(to));
  };

  const handleDateSelection = (selectedDepartDate: Date, selectedReturnDate?: Date) => {
    setDepartDate(selectedDepartDate);
    if (tripType === 'round-trip' && selectedReturnDate) {
      setReturnDate(selectedReturnDate);
    } else if (tripType === 'one-way') {
      setReturnDate(null);
    }
  };

  const handleClassSelection = (classType: string, className: string) => {
    setSelectedClass(className);
  };

  const handleFromAirportSelection = (airport: Airport) => {
    setFrom(airport.city);
    setFromCode(airport.code);
  };

  const handleToAirportSelection = (airport: Airport) => {
    setTo(airport.city);
    setToCode(airport.code);
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short'
    });
  };

  const formatDateYear = (date: Date) => {
    return date.getFullYear().toString();
  };

  const handleSwapLocations = () => {
    const tempFrom = from;
    const tempFromCode = fromCode;
    setFrom(to);
    setFromCode(toCode);
    setTo(tempFrom);
    setToCode(tempFromCode);
  };



  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <LinearGradient
        colors={['#4C4DDC', '#3F3D9B']}
        style={styles.header}
      >
        <Pressable style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={20} color={colors.white} />
        </Pressable>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Securely book</Text>
          <Text style={styles.headerSubtitle}>your flight</Text>
          <View style={styles.headerAccent} />
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.searchFormContainer}>
          <View style={styles.searchForm}>
            {/* Trip Type Selector */}
            <View style={styles.tripTypeContainer}>
              <TouchableOpacity 
                style={[styles.tripTypeButton, tripType === 'round-trip' && styles.tripTypeActive]}
                onPress={() => setTripType('round-trip')}
                activeOpacity={0.8}
              >
                <Text style={[styles.tripTypeText, tripType === 'round-trip' && styles.tripTypeTextActive]}>Round Trip</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.tripTypeButton, tripType === 'one-way' && styles.tripTypeActive]}
                onPress={() => {
                  setTripType('one-way');
                  setReturnDate(null);
                }}
                activeOpacity={0.8}
              >
                <Text style={[styles.tripTypeText, tripType === 'one-way' && styles.tripTypeTextActive]}>One Way</Text>
              </TouchableOpacity>
            </View>

            {/* Location Inputs */}
            <View style={styles.locationContainer}>
              <View style={styles.locationInputWrapper}>
                <TouchableOpacity 
                  style={styles.locationInput}
                  onPress={() => setShowFromDropdown(true)}
                  activeOpacity={0.8}
                >
                  <View style={styles.locationIconContainer}>
                    <View style={styles.departureIcon}>
                      <PlaneTakeoff size={12} color="white" />
                    </View>
                  </View>
                  <View style={styles.locationContent}>
                    <Text style={styles.locationLabel}>FROM</Text>
                    <Text style={styles.locationText}>{from}</Text>
                    <Text style={styles.locationCode}>{fromCode}</Text>
                  </View>
                </TouchableOpacity>
                
                <View style={styles.routeConnectionContainer}>
                  <View style={styles.routeLine} />
                  <View style={styles.routeDot} />
                  <View style={styles.routeLine} />
                </View>
                
                <TouchableOpacity 
                  style={styles.locationInput}
                  onPress={() => setShowToDropdown(true)}
                  activeOpacity={0.8}
                >
                  <View style={styles.locationIconContainer}>
                    <View style={styles.destinationIcon}>
                      <PlaneLanding size={12} color="white" />
                    </View>
                  </View>
                  <View style={styles.locationContent}>
                    <Text style={styles.locationLabel}>TO</Text>
                    <Text style={styles.locationText}>{to}</Text>
                    <Text style={styles.locationCode}>{toCode}</Text>
                  </View>
                </TouchableOpacity>
              </View>
              
              <TouchableOpacity style={styles.swapButton} onPress={handleSwapLocations} activeOpacity={0.8}>
                <ArrowUpDown size={16} color="#4C4DDC" />
              </TouchableOpacity>
            </View>

            {/* Date Selection */}
            <View style={styles.dateSection}>
              <TouchableOpacity 
                style={[styles.dateCard, { marginRight: spacing.sm }]} 
                activeOpacity={0.8}
                onPress={() => setShowCalendar(true)}
              >
                <View style={styles.dateIconContainer}>
                  <Calendar size={16} color="#4C4DDC" />
                </View>
                <View style={styles.dateContent}>
                  <Text style={styles.dateLabel}>DEPARTURE</Text>
                  <Text style={styles.dateValue}>{formatDate(departDate)}</Text>
                  <Text style={styles.dateYear}>{formatDateYear(departDate)}</Text>
                </View>
              </TouchableOpacity>
              
              {tripType === 'round-trip' && (
                <TouchableOpacity 
                  style={styles.dateCard} 
                  activeOpacity={0.8}
                  onPress={() => setShowCalendar(true)}
                >
                  <View style={styles.dateIconContainer}>
                    <Calendar size={16} color="#4C4DDC" />
                  </View>
                  <View style={styles.dateContent}>
                    <Text style={styles.dateLabel}>RETURN</Text>
                    <Text style={styles.dateValue}>{returnDate ? formatDate(returnDate) : 'Select'}</Text>
                    <Text style={styles.dateYear}>{returnDate ? formatDateYear(returnDate) : ''}</Text>
                  </View>
                </TouchableOpacity>
              )}
            </View>

            {/* Travelers Selection */}
            <TouchableOpacity 
              style={styles.travelersCard} 
              activeOpacity={0.8}
              onPress={() => setShowClassSelection(true)}
            >
              <View style={styles.travelersIconContainer}>
                <Users size={16} color="#4C4DDC" />
              </View>
              <View style={styles.travelersContent}>
                <Text style={styles.travelersLabel}>TRAVELERS</Text>
                <Text style={styles.travelersValue}>{travelers} {travelers === 1 ? 'Passenger' : 'Passengers'}</Text>
                <Text style={styles.travelersSubtext}>{selectedClass}</Text>
              </View>
              <View style={styles.travelersControls}>
                <TouchableOpacity 
                  style={[styles.controlButton, travelers <= 1 && styles.controlButtonDisabled]} 
                  onPress={(e) => {
                    e.stopPropagation();
                    handleDecrementTravelers();
                  }}
                  disabled={travelers <= 1}
                  activeOpacity={0.8}
                >
                  <Minus size={14} color={travelers <= 1 ? colors.textSecondary : "#4C4DDC"} />
                </TouchableOpacity>
                <Text style={styles.travelersCount}>{travelers}</Text>
                <TouchableOpacity 
                  style={styles.controlButton} 
                  onPress={(e) => {
                    e.stopPropagation();
                    handleIncrementTravelers();
                  }} 
                  activeOpacity={0.8}
                >
                  <Plus size={14} color="#4C4DDC" />
                </TouchableOpacity>
              </View>
            </TouchableOpacity>

            {/* Search Button */}
            <TouchableOpacity style={styles.searchButton} onPress={handleSearchFlights} activeOpacity={0.9}>
              <LinearGradient
                colors={['#4C4DDC', '#3F3D9B']}
                style={styles.searchButtonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Text style={styles.searchButtonText}>Search Flights</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>


      </ScrollView>

      {/* Calendar Modal */}
      <CalendarModal
        visible={showCalendar}
        onClose={() => setShowCalendar(false)}
        onSelectDates={handleDateSelection}
        tripType={tripType}
        initialDepartDate={departDate}
        initialReturnDate={returnDate || undefined}
      />

      {/* Class Selection Modal */}
      <ClassSelectionModal
        visible={showClassSelection}
        onClose={() => setShowClassSelection(false)}
        onSelectClass={handleClassSelection}
        initialClass={'economy'}
      />

      {/* Airport Dropdowns */}
      <AirportDropdown
        visible={showFromDropdown}
        onClose={() => setShowFromDropdown(false)}
        onSelectAirport={handleFromAirportSelection}
        placeholder="Search departure airport..."
        initialValue={from}
        title="Select Departure Airport"
      />

      <AirportDropdown
        visible={showToDropdown}
        onClose={() => setShowToDropdown(false)}
        onSelectAirport={handleToAirportSelection}
        placeholder="Search destination airport..."
        initialValue={to}
        title="Select Destination Airport"
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: 40,
    flexDirection: "row",
    alignItems: "flex-start",
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.lg,
    marginTop: 4,
  },
  headerContent: {
    flex: 1,
    position: 'relative',
  },
  headerTitle: {
    fontFamily: typography.fontFamily,
    fontSize: 28,
    fontWeight: '800' as any,
    color: colors.white,
    marginBottom: -2,
    letterSpacing: -0.5,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  headerSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: 28,
    color: colors.white,
    fontWeight: '800' as any,
    letterSpacing: -0.5,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  headerAccent: {
    position: 'absolute',
    bottom: -8,
    left: 0,
    width: 60,
    height: 3,
    backgroundColor: 'rgba(255,255,255,0.8)',
    borderRadius: 2,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  searchFormContainer: {
    marginTop: 30,
    flex: 1,
    justifyContent: 'center',
    paddingBottom: spacing.xl,
  },
  searchForm: {
    backgroundColor: colors.white,
    borderRadius: 24,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.2,
    shadowRadius: 20,
    elevation: 10,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  tripTypeContainer: {
    flexDirection: 'row',
    backgroundColor: '#F1F5F9',
    borderRadius: 12,
    padding: 4,
    marginBottom: 24,
    marginTop: 4,
  },
  tripTypeButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 10,
    alignItems: 'center',
  },
  tripTypeActive: {
    backgroundColor: colors.white,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  tripTypeText: {
    fontFamily: typography.fontFamily,
    fontSize: 14,
    fontWeight: '500' as any,
    color: '#9CA3AF',
  },
  tripTypeTextActive: {
    color: '#1E293B',
    fontWeight: '600' as any,
  },
  locationContainer: {
    position: 'relative',
    marginBottom: 20,
  },
  locationInputWrapper: {
    backgroundColor: '#F8FAFC',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    overflow: 'hidden',
  },
  locationInput: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  locationIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  departureIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#FF385C',
    justifyContent: 'center',
    alignItems: 'center',
  },
  destinationIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#4C4DDC',
    justifyContent: 'center',
    alignItems: 'center',
  },
  locationContent: {
    flex: 1,
  },
  locationLabel: {
    fontFamily: typography.fontFamily,
    fontSize: 10,
    fontWeight: '700' as any,
    color: '#9CA3AF',
    letterSpacing: 1,
    marginBottom: 4,
  },
  locationText: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    fontWeight: '600' as any,
    color: '#1E293B',
    marginBottom: 2,
  },
  locationCode: {
    fontFamily: typography.fontFamily,
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '500' as any,
  },
  routeConnectionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginVertical: -8,
  },
  routeLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#E2E8F0',
  },
  routeDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#9CA3AF',
    marginHorizontal: 4,
  },
  swapButton: {
    position: 'absolute',
    right: 16,
    top: '50%',
    marginTop: -16,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    zIndex: 10,
  },
  dateSection: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  dateCard: {
    flex: 1,
    backgroundColor: '#F8FAFC',
    borderRadius: 16,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  dateIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  dateContent: {
    flex: 1,
  },
  dateLabel: {
    fontFamily: typography.fontFamily,
    fontSize: 10,
    fontWeight: '700' as any,
    color: '#9CA3AF',
    letterSpacing: 1,
    marginBottom: 2,
  },
  dateValue: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    fontWeight: '600' as any,
    color: '#1E293B',
    marginBottom: 1,
  },
  dateYear: {
    fontFamily: typography.fontFamily,
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '500' as any,
  },
  travelersCard: {
    backgroundColor: '#F8FAFC',
    borderRadius: 16,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  travelersIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  travelersContent: {
    flex: 1,
  },
  travelersLabel: {
    fontFamily: typography.fontFamily,
    fontSize: 10,
    fontWeight: '700' as any,
    color: '#9CA3AF',
    letterSpacing: 1,
    marginBottom: 2,
  },
  travelersValue: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    fontWeight: '600' as any,
    color: '#1E293B',
    marginBottom: 1,
  },
  travelersSubtext: {
    fontFamily: typography.fontFamily,
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '500' as any,
  },
  travelersControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  controlButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  controlButtonDisabled: {
    opacity: 0.5,
  },
  travelersCount: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    fontWeight: '600' as any,
    color: '#1E293B',
    marginHorizontal: 12,
    minWidth: 20,
    textAlign: 'center',
  },
  searchButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#4C4DDC',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 6,
  },
  searchButtonGradient: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  searchButtonText: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    fontWeight: '600' as any,
    color: colors.white,
    letterSpacing: 0.5,
  },

});