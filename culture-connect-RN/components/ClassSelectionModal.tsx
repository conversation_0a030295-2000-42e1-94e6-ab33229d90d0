import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ScrollView,
  Pressable,
} from 'react-native';
import { X, Check, Plane, Star, Crown, Zap } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';
import { LinearGradient } from 'expo-linear-gradient';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

type ClassType = 'economy' | 'premium-economy' | 'business' | 'first';

interface ClassOption {
  id: ClassType;
  name: string;
  description: string;
  features: string[];
  icon: React.ReactNode;
  color: string;
  gradient: string[];
}

interface ClassSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectClass: (classType: ClassType, className: string) => void;
  initialClass?: ClassType;
}

const classOptions: ClassOption[] = [
  {
    id: 'economy',
    name: 'Economy Class',
    description: 'Comfortable and affordable travel',
    features: ['Standard seat', 'Meal included', 'Entertainment system', 'Carry-on bag'],
    icon: <Plane size={20} color="#6B7280" />,
    color: '#6B7280',
    gradient: ['#F3F4F6', '#E5E7EB'],
  },
  {
    id: 'premium-economy',
    name: 'Premium Economy',
    description: 'Extra comfort and space',
    features: ['Extra legroom', 'Premium meals', 'Priority boarding', 'Enhanced entertainment'],
    icon: <Zap size={20} color="#059669" />,
    color: '#059669',
    gradient: ['#D1FAE5', '#A7F3D0'],
  },
  {
    id: 'business',
    name: 'Business Class',
    description: 'Premium experience and service',
    features: ['Lie-flat seats', 'Gourmet dining', 'Lounge access', 'Priority check-in'],
    icon: <Star size={20} color="#DC2626" />,
    color: '#DC2626',
    gradient: ['#FEE2E2', '#FECACA'],
  },
  {
    id: 'first',
    name: 'First Class',
    description: 'Ultimate luxury and privacy',
    features: ['Private suites', 'Personal butler', 'Fine dining', 'Spa services'],
    icon: <Crown size={20} color="#7C2D12" />,
    color: '#7C2D12',
    gradient: ['#FED7AA', '#FDBA74'],
  },
];

export function ClassSelectionModal({
  visible,
  onClose,
  onSelectClass,
  initialClass = 'economy',
}: ClassSelectionModalProps) {
  const [selectedClass, setSelectedClass] = useState<ClassType>(initialClass);

  const handleSelectClass = (classType: ClassType, className: string) => {
    setSelectedClass(classType);
    onSelectClass(classType, className);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitle}>Select Class</Text>
            <Text style={styles.headerSubtitle}>Choose your travel experience</Text>
          </View>
          <TouchableOpacity style={styles.closeButton} onPress={onClose} activeOpacity={0.8}>
            <X size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>

        {/* Class Options */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {classOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.classCard,
                selectedClass === option.id && styles.classCardSelected,
              ]}
              onPress={() => handleSelectClass(option.id, option.name)}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={selectedClass === option.id ? ['#4C4DDC', '#3F3D9B'] as const : option.gradient as [string, string]}
                style={styles.classCardGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <View style={styles.classCardContent}>
                  <View style={styles.classHeader}>
                    <View style={[
                      styles.classIconContainer,
                      selectedClass === option.id && styles.classIconContainerSelected
                    ]}>
                      {selectedClass === option.id ? (
                        <Check size={20} color={colors.white} />
                      ) : (
                        option.icon
                      )}
                    </View>
                    <View style={styles.classInfo}>
                      <Text style={[
                        styles.className,
                        selectedClass === option.id && styles.classNameSelected
                      ]}>
                        {option.name}
                      </Text>
                      <Text style={[
                        styles.classDescription,
                        selectedClass === option.id && styles.classDescriptionSelected
                      ]}>
                        {option.description}
                      </Text>
                    </View>
                    {selectedClass === option.id && (
                      <View style={styles.selectedIndicator}>
                        <Check size={16} color={colors.white} />
                      </View>
                    )}
                  </View>
                  
                  <View style={styles.featuresContainer}>
                    {option.features.map((feature, index) => (
                      <View key={index} style={styles.featureItem}>
                        <View style={[
                          styles.featureDot,
                          selectedClass === option.id && styles.featureDotSelected
                        ]} />
                        <Text style={[
                          styles.featureText,
                          selectedClass === option.id && styles.featureTextSelected
                        ]}>
                          {feature}
                        </Text>
                      </View>
                    ))}
                  </View>
                </View>
              </LinearGradient>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Bottom Action */}
        <View style={styles.bottomContainer}>
          <TouchableOpacity
            style={styles.confirmButton}
            onPress={() => {
              const selectedOption = classOptions.find(opt => opt.id === selectedClass);
              if (selectedOption) {
                handleSelectClass(selectedClass, selectedOption.name);
              }
            }}
            activeOpacity={0.9}
          >
            <LinearGradient
              colors={['#4C4DDC', '#3F3D9B'] as const}
              style={styles.confirmButtonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={styles.confirmButtonText}>Confirm Selection</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
    paddingBottom: spacing.lg,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontFamily: typography.fontFamily,
    fontSize: 24,
    fontWeight: '700' as any,
    color: colors.text,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: 14,
    color: colors.textSecondary,
    fontWeight: '500' as any,
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F1F5F9',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: spacing.md,
    marginTop: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
  },
  classCard: {
    marginBottom: spacing.md,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  classCardSelected: {
    shadowColor: '#4C4DDC',
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  classCardGradient: {
    padding: 20,
  },
  classCardContent: {
    // No additional styles needed
  },
  classHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  classIconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  classIconContainerSelected: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    shadowColor: 'transparent',
  },
  classInfo: {
    flex: 1,
  },
  className: {
    fontFamily: typography.fontFamily,
    fontSize: 18,
    fontWeight: '700' as any,
    color: '#1E293B',
    marginBottom: 2,
  },
  classNameSelected: {
    color: colors.white,
  },
  classDescription: {
    fontFamily: typography.fontFamily,
    fontSize: 14,
    color: '#64748B',
    fontWeight: '500' as any,
  },
  classDescriptionSelected: {
    color: 'rgba(255,255,255,0.8)',
  },
  selectedIndicator: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  featuresContainer: {
    // No additional styles needed
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#94A3B8',
    marginRight: 12,
  },
  featureDotSelected: {
    backgroundColor: 'rgba(255,255,255,0.8)',
  },
  featureText: {
    fontFamily: typography.fontFamily,
    fontSize: 14,
    color: '#64748B',
    fontWeight: '500' as any,
  },
  featureTextSelected: {
    color: 'rgba(255,255,255,0.9)',
  },
  bottomContainer: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.xl,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
  },
  confirmButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#4C4DDC',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 6,
  },
  confirmButtonGradient: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  confirmButtonText: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    fontWeight: '600' as any,
    color: colors.white,
    letterSpacing: 0.5,
  },
});