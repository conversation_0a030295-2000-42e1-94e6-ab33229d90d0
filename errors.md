flutter: ⚠️ WARNING [2025-09-11T16:47:11.063893] [PerformanceMonitoringService] High memory usage detected {"memory_mb":163.0}
flutter: 🐛 DEBUG [2025-09-11T16:47:20.319861] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-09-11T16:47:21.964266] [PerformanceMonitoringService] Slow frame detected {"duration_ms":127}
flutter: 🐛 DEBUG [2025-09-11T16:47:22.038891] [PerformanceMonitoringService] Slow frame detected {"duration_ms":72}
flutter: 🐛 DEBUG [2025-09-11T16:47:22.070150] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-09-11T16:47:22.106107] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-09-11T16:47:22.144937] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-09-11T16:47:22.236968] [PerformanceMonitoringService] Slow frame detected {"duration_ms":91}
flutter: 🐛 DEBUG [2025-09-11T16:47:22.303305] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-09-11T16:47:26.064556] [PerformanceMonitoringService] High memory usage detected {"memory_mb":164.0}
flutter: 🐛 DEBUG [2025-09-11T16:47:26.770876] [PerformanceMonitoringService] Slow frame detected {"duration_ms":383}
flutter: ❌ ERROR [2025-09-11T16:47:26.822446] [FlutterError] A RenderFlex overflowed by 5.0 pixels on the bottom. A RenderFlex overflowed by 5.0 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #13     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #20     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #21     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #22     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #23     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #24     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #25     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #26     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #27     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #28     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #29     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #30     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #32     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #38     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #39     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #40     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #41     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #42     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #43     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #44     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #45     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #46     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #47     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #48     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #49     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #50     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #51     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #52     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #53     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #54     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #55     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #56     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #57     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #58     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #59     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #60     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #61     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #62     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #63     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #64     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #65     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #66     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #67     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #68     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #69     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #70     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #71     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #72     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #73     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #74     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #77     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #78     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #80     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #81     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #82     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #83     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #84     RenderSliverSingleBoxAdapter.paint (package:flutter/src/rendering/sliver.dart:1880:15)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #88     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #89     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #90     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #91     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #92     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #93     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #94     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #98     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #99     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #102    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #103    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #104    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #105    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #106    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #107    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #108    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #109    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #110    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #111    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #112    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #113    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #114    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #115    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #116    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #117    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #118    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #119    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #120    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #124    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #125    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #134    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #135    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #137    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #140    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #141    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #142    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #143    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #144    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #145    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #146    RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #147    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #148    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #149    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #150    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #151    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #152    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #153    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #154    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #155    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #156    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #157    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #158    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #159    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #160    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #161    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #162    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #163    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #164    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #165    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #166    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #167    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #168    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #169    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #170    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #171    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #172    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #173    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #174    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #175    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #176    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #177    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #178    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #179    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #180    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #181    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #182    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #183    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #184    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #185    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #186    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #187    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #188    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #189    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #190    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #191    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #192    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #193    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #194    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #195    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #196    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #197    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #198    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #199    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #200    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #201    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #202    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #203    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #204    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #205    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #206    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #207    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #208    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #209    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #210    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #211    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #212    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #213    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #214    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #215    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #216    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #217    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #218    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #219    _invoke (dart:ui/hooks.dart:312:13)
flutter: #220    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #221    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-09-11T16:47:26.965572] [FlutterError] A RenderFlex overflowed by 5.0 pixels on the bottom. A RenderFlex overflowed by 5.0 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #13     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #20     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #21     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #22     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #23     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #24     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #25     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #26     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #27     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #28     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #29     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #30     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #32     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #38     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #39     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #40     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #41     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #42     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #43     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #44     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #45     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #46     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #47     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #48     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #49     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #50     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #51     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #52     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #53     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #54     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #55     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #56     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #57     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #58     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #59     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #60     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #61     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #62     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #63     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #64     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #65     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #66     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #67     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #68     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #69     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #70     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #71     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #72     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #73     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #74     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #77     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #78     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #80     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #81     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #82     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #83     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #84     RenderSliverSingleBoxAdapter.paint (package:flutter/src/rendering/sliver.dart:1880:15)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #88     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #89     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #90     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #91     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #92     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #93     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #94     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #98     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #99     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #102    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #103    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #104    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #105    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #106    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #107    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #108    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #109    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #110    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #111    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #112    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #113    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #114    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #115    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #116    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #117    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #118    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #119    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #120    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #124    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #125    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #134    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #135    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #137    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #140    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #141    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #142    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #143    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #144    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #145    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #146    RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #147    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #148    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #149    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #150    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #151    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #152    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #153    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #154    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #155    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #156    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #157    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #158    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #159    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #160    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #161    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #162    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #163    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #164    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #165    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #166    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #167    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #168    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #169    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #170    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #171    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #172    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #173    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #174    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #175    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #176    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #177    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #178    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #179    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #180    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #181    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #182    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #183    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #184    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #185    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #186    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #187    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #188    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #189    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #190    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #191    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #192    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #193    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #194    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #195    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #196    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #197    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #198    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #199    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #200    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #201    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #202    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #203    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #204    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #205    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #206    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #207    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #208    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #209    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #210    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #211    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #212    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #213    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #214    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #215    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #216    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #217    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #218    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #219    _invoke (dart:ui/hooks.dart:312:13)
flutter: #220    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #221    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-09-11T16:47:27.037033] [PerformanceMonitoringService] Slow frame detected {"duration_ms":266}
flutter: 🐛 DEBUG [2025-09-11T16:47:27.087503] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ❌ ERROR [2025-09-11T16:47:27.199421] [FlutterError] HTTP request failed, statusCode: 404, https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80 HTTP request failed, statusCode: 404, https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80
flutter: Stack trace:
flutter: #0      NetworkImage._loadAsync (package:flutter/src/painting/_network_image_io.dart:115:9)
flutter: <asynchronous suspension>
flutter: #1      MultiFrameImageStreamCompleter._handleCodecReady (package:flutter/src/painting/image_stream.dart:1013:3)
flutter: <asynchronous suspension>
flutter:
flutter: 🐛 DEBUG [2025-09-11T16:47:28.201594] [PerformanceMonitoringService] Slow frame detected {"duration_ms":247}
flutter: 🐛 DEBUG [2025-09-11T16:47:28.220461] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-09-11T16:47:29.453605] [PerformanceMonitoringService] Slow frame detected {"duration_ms":249}
flutter: 🐛 DEBUG [2025-09-11T16:47:30.517031] [PerformanceMonitoringService] Slow frame detected {"duration_ms":80}
flutter: 🐛 DEBUG [2025-09-11T16:47:30.536732] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
