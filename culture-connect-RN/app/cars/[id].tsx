import React, { useState } from 'react';
import { StyleSheet, View, Text, ScrollView, Image, Pressable, Dimensions, Animated } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, Star, Users, Fuel, Settings, Snowflake, MapPin, Calendar, Clock, Heart, Share2 } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';
import { cars } from '@/mocks/cars';
import { Button } from '@/components/Button';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function CarDetailScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const [selectedDuration, setSelectedDuration] = useState('1 day');
  const [isFavorite, setIsFavorite] = useState(false);
  const scrollY = new Animated.Value(0);
  
  const car = cars.find(c => c.id === id);

  if (!car) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Car not found</Text>
      </SafeAreaView>
    );
  }

  const handleBackPress = () => {
    router.back();
  };

  const handleBookNow = () => {
    router.push({
      pathname: '/cars/booking',
      params: { carId: car.id, duration: selectedDuration }
    });
  };

  const handleShare = () => {
    // Handle share functionality
    console.log('Share car');
  };

  const handleFavorite = () => {
    setIsFavorite(!isFavorite);
  };

  const calculateTotalPrice = () => {
    const multiplier = selectedDuration === '1 day' ? 1 : 
                     selectedDuration === '3 days' ? 3 : 
                     selectedDuration === '1 week' ? 7 : 30;
    return car.pricePerDay * multiplier;
  };

  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 200],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  const imageScale = scrollY.interpolate({
    inputRange: [-100, 0, 100],
    outputRange: [1.2, 1, 0.8],
    extrapolate: 'clamp',
  });

  const durations = ['1 day', '3 days', '1 week', '1 month'];

  return (
    <View style={styles.container}>
      {/* Animated Header */}
      <Animated.View style={[styles.animatedHeader, { opacity: headerOpacity }]}>
        <SafeAreaView edges={['top']}>
          <View style={styles.headerContent}>
            <Pressable style={styles.headerButton} onPress={handleBackPress}>
              <ArrowLeft size={24} color={colors.text} />
            </Pressable>
            <Text style={styles.headerTitle} numberOfLines={1}>{car.name}</Text>
            <View style={styles.headerActions}>
              <Pressable style={styles.headerButton} onPress={handleShare}>
                <Share2 size={20} color={colors.text} />
              </Pressable>
              <Pressable style={styles.headerButton} onPress={handleFavorite}>
                <Heart size={20} color={isFavorite ? colors.error : colors.text} fill={isFavorite ? colors.error : 'none'} />
              </Pressable>
            </View>
          </View>
        </SafeAreaView>
      </Animated.View>

      <Animated.ScrollView 
        showsVerticalScrollIndicator={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false }
        )}
        scrollEventThrottle={16}
      >
        {/* Hero Section */}
        <View style={styles.heroSection}>
          <Animated.View style={[styles.imageContainer, { transform: [{ scale: imageScale }] }]}>
            <Image source={{ uri: car.imageUrl }} style={styles.carImage} />
          </Animated.View>
          
          {/* Floating Header */}
          <View style={styles.floatingHeader}>
            <Pressable style={styles.floatingButton} onPress={handleBackPress}>
              <ArrowLeft size={24} color={colors.white} />
            </Pressable>
            <View style={styles.floatingActions}>
              <Pressable style={styles.floatingButton} onPress={handleShare}>
                <Share2 size={20} color={colors.white} />
              </Pressable>
              <Pressable style={styles.floatingButton} onPress={handleFavorite}>
                <Heart size={20} color={colors.white} fill={isFavorite ? colors.white : 'none'} />
              </Pressable>
            </View>
          </View>

          {/* Rating Badge */}
          <View style={styles.ratingBadge}>
            <Star size={16} color={colors.warning} fill={colors.warning} />
            <Text style={styles.ratingText}>{car.rating}</Text>
            <Text style={styles.reviewText}>({car.reviewCount})</Text>
          </View>
        </View>

        {/* Main Content */}
        <View style={styles.contentContainer}>
          {/* Car Header */}
          <View style={styles.carHeader}>
            <View style={styles.carTitleSection}>
              <Text style={styles.carName}>{car.name}</Text>
              <View style={styles.categoryContainer}>
                <Text style={styles.carCategory}>{car.category}</Text>
                <View style={styles.availabilityBadge}>
                  <View style={styles.availabilityDot} />
                  <Text style={styles.availabilityText}>Available</Text>
                </View>
              </View>
            </View>
            <View style={styles.priceSection}>
              <Text style={styles.price}>${car.pricePerDay}</Text>
              <Text style={styles.priceUnit}>per day</Text>
            </View>
          </View>

          {/* Quick Specs */}
          <View style={styles.quickSpecs}>
            <View style={styles.specsRow}>
              <View style={styles.specCard}>
                <View style={styles.specIcon}>
                  <Users size={20} color={colors.accent} />
                </View>
                <Text style={styles.specLabel}>Seats</Text>
                <Text style={styles.specValue}>{car.seats}</Text>
              </View>
              <View style={styles.specCard}>
                <View style={styles.specIcon}>
                  <Fuel size={20} color={colors.accent} />
                </View>
                <Text style={styles.specLabel}>Fuel</Text>
                <Text style={styles.specValue}>{car.fuelType}</Text>
              </View>
            </View>
            <View style={styles.specsRow}>
              <View style={styles.specCard}>
                <View style={styles.specIcon}>
                  <Settings size={20} color={colors.accent} />
                </View>
                <Text style={styles.specLabel}>Transmission</Text>
                <Text style={styles.specValue}>{car.transmission}</Text>
              </View>
              <View style={styles.specCard}>
                <View style={styles.specIcon}>
                  <Snowflake size={20} color={colors.accent} />
                </View>
                <Text style={styles.specLabel}>AC</Text>
                <Text style={styles.specValue}>Available</Text>
              </View>
            </View>
          </View>

          {/* Location */}
          <View style={styles.locationSection}>
            <View style={styles.sectionHeader}>
              <MapPin size={20} color={colors.accent} />
              <Text style={styles.sectionTitle}>Pickup Location</Text>
            </View>
            <View style={styles.locationCard}>
              <Text style={styles.locationName}>{car.location}</Text>
              <Text style={styles.locationAddress}>Available 24/7 • Free pickup</Text>
            </View>
          </View>

          {/* Description */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>About this car</Text>
            <Text style={styles.description}>{car.description}</Text>
          </View>

          {/* Features */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Features & Amenities</Text>
            <View style={styles.featuresGrid}>
              {car.features.map((feature, index) => (
                <View key={index} style={styles.featureChip}>
                  <Text style={styles.featureText}>{feature}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* Duration Selection */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Select Rental Duration</Text>
            <View style={styles.durationGrid}>
              {durations.map((duration) => {
                const isSelected = selectedDuration === duration;
                return (
                  <Pressable
                    key={duration}
                    style={[
                      styles.durationCard,
                      isSelected && styles.selectedDurationCard
                    ]}
                    onPress={() => setSelectedDuration(duration)}
                  >
                    <Text style={[
                      styles.durationText,
                      isSelected && styles.selectedDurationText
                    ]}>
                      {duration}
                    </Text>
                    <Text style={[
                      styles.durationPrice,
                      isSelected && styles.selectedDurationPrice
                    ]}>
                      ${car.pricePerDay * (duration === '1 day' ? 1 : duration === '3 days' ? 3 : duration === '1 week' ? 7 : 30)}
                    </Text>
                  </Pressable>
                );
              })}
            </View>
          </View>

        </View>
      </Animated.ScrollView>

      {/* Floating Bottom Bar */}
      <View style={styles.bottomBar}>
        <View style={styles.priceInfo}>
          <Text style={styles.totalLabel}>Total</Text>
          <Text style={styles.totalPrice}>${calculateTotalPrice()}</Text>
          <Text style={styles.totalDuration}>for {selectedDuration}</Text>
        </View>
        <View style={styles.bookingButton}>
          <Button
            title="Book Now"
            onPress={handleBookNow}
            variant="primary"
            size="large"
          />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  animatedHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 100,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    flex: 1,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    textAlign: 'center',
    marginHorizontal: spacing.md,
  },
  headerActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  heroSection: {
    position: 'relative',
    height: screenHeight * 0.5,
    backgroundColor: '#f8f9fa',
  },
  imageContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  carImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  floatingHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl + spacing.lg,
  },
  floatingButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  floatingActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  ratingBadge: {
    position: 'absolute',
    bottom: spacing.xl,
    right: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    gap: spacing.xs,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  ratingText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
  reviewText: {
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
  },
  contentContainer: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    marginTop: -24,
    paddingTop: spacing.xl,
    paddingHorizontal: spacing.lg,
    paddingBottom: 120,
  },
  carHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.xl,
  },
  carTitleSection: {
    flex: 1,
  },
  carName: {
    fontSize: 28,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.sm,
    lineHeight: 34,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  carCategory: {
    fontSize: typography.sizes.md,
    color: colors.accent,
    fontWeight: typography.weights.semibold as any,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  availabilityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  availabilityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.success,
  },
  availabilityText: {
    fontSize: typography.sizes.sm,
    color: colors.success,
    fontWeight: typography.weights.medium as any,
  },
  priceSection: {
    alignItems: 'flex-end',
  },
  price: {
    fontSize: 32,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    lineHeight: 38,
  },
  priceUnit: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
  },
  quickSpecs: {
    gap: spacing.md,
    marginBottom: spacing.xl,
  },
  specsRow: {
    flexDirection: 'row',
    gap: spacing.md,
    marginBottom: spacing.md,
  },
  specCard: {
    flex: 1,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 16,
    padding: spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 90,
  },
  specIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.accent + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  specLabel: {
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    textAlign: 'center',
    marginTop: spacing.sm,
  },
  specValue: {
    fontSize: typography.sizes.md,
    color: colors.text,
    fontWeight: typography.weights.semibold as any,
    textAlign: 'center',
    marginTop: spacing.xs,
    lineHeight: typography.sizes.md * 1.2,
  },
  locationSection: {
    marginBottom: spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  locationCard: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 16,
    padding: spacing.lg,
  },
  locationName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  locationAddress: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  section: {
    marginBottom: spacing.xl,
  },
  description: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.md,
    marginTop: spacing.sm,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
    marginTop: spacing.sm,
  },
  featureChip: {
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  featureText: {
    fontSize: typography.sizes.sm,
    color: colors.text,
    fontWeight: typography.weights.medium as any,
  },
  durationGrid: {
    flexDirection: 'row',
    gap: spacing.md,
    flexWrap: 'wrap',
    marginTop: spacing.sm,
  },
  durationCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 16,
    padding: spacing.lg,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedDurationCard: {
    backgroundColor: colors.accent + '10',
    borderColor: colors.accent,
  },
  durationText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  selectedDurationText: {
    color: colors.accent,
  },
  durationPrice: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
  },
  selectedDurationPrice: {
    color: colors.accent,
  },
  bottomBar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    paddingBottom: spacing.lg + 20,
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  priceInfo: {
    flex: 1,
  },
  totalLabel: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
  },
  totalPrice: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    lineHeight: typography.sizes.xl * 1.2,
  },
  totalDuration: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  bookingButton: {
    minWidth: 140,
  },
});