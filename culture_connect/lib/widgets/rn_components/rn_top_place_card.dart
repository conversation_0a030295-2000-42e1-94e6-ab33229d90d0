import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Pixel-perfect React Native TopPlaceCard replica
/// Horizontal scrolling card for Top Places section
class RNTopPlaceCard extends StatelessWidget {
  final String title;
  final String location;
  final String imageUrl;
  final double rating;
  final int reviewCount;
  final VoidCallback onPressed;
  final VoidCallback? onFavoritePressed;
  final bool isFavorite;

  const RNTopPlaceCard({
    super.key,
    required this.title,
    required this.location,
    required this.imageUrl,
    required this.rating,
    required this.reviewCount,
    required this.onPressed,
    this.onFavoritePressed,
    this.isFavorite = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 280, // Fixed width for horizontal scrolling
        margin: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
          color: AppTheme.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: const [
            BoxShadow(
              color: AppTheme.shadowColor,
              offset: Offset(0, 2),
              blurRadius: 8,
              spreadRadius: 0,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            children: [
              // Background Image
              Container(
                height: 180, // Compact height for horizontal cards
                width: double.infinity,
                color: AppTheme.backgroundSecondary,
                child: imageUrl.isNotEmpty
                    ? Image.network(
                        imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: AppTheme.backgroundSecondary,
                            child: const Icon(
                              Icons.image_outlined,
                              size: 48,
                              color: AppTheme.textSecondaryColor,
                            ),
                          );
                        },
                      )
                    : const Icon(
                        Icons.image_outlined,
                        size: 48,
                        color: AppTheme.textSecondaryColor,
                      ),
              ),

              // Wishlist Heart - Top Right
              if (onFavoritePressed != null)
                Positioned(
                  top: 12,
                  right: 12,
                  child: GestureDetector(
                    onTap: onFavoritePressed,
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: AppTheme.white.withAlpha(230), // 90% opacity
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        isFavorite ? Icons.favorite : Icons.favorite_border,
                        size: 18,
                        color: isFavorite
                            ? Colors.red
                            : AppTheme.textSecondaryColor,
                      ),
                    ),
                  ),
                ),

              // Content Overlay - Bottom
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        AppTheme.black.withAlpha(153), // 60% opacity
                        AppTheme.black.withAlpha(179), // 70% opacity at bottom
                      ],
                      stops: const [0.0, 0.7, 1.0],
                    ),
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Title
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: AppTheme.fontSizeMd, // 16px
                          fontWeight: AppTheme.fontWeightBold,
                          color: AppTheme.white,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      
                      // Location
                      Text(
                        location,
                        style: const TextStyle(
                          fontSize: AppTheme.fontSizeSm, // 14px
                          color: AppTheme.white,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      
                      // Rating
                      Row(
                        children: [
                          const Icon(
                            Icons.star,
                            size: 14,
                            color: Colors.amber,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${rating.toStringAsFixed(1)} ($reviewCount)',
                            style: const TextStyle(
                              fontSize: AppTheme.fontSizeXs, // 12px
                              color: AppTheme.white,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
