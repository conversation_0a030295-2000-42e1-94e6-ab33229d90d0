import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:culture_connect/models/premium_booking.dart';
import 'package:culture_connect/design_system/premium_design_tokens.dart';

/// Premium booking card with stunning visual design and smooth animations
class PremiumBookingCard extends StatefulWidget {
  final PremiumBooking booking;
  final VoidCallback? onTap;

  final bool showActions;
  final double? width;
  final double? height;

  const PremiumBookingCard({
    super.key,
    required this.booking,
    this.onTap,
    this.showActions = true,
    this.width,
    this.height,
  });

  @override
  State<PremiumBookingCard> createState() => _PremiumBookingCardState();
}

class _PremiumBookingCardState extends State<PremiumBookingCard>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _pressController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  late Animation<double> _shimmerAnimation;

  bool _isPressed = false;

  @override
  void initState() {
    super.initState();

    _hoverController = AnimationController(
      duration: PremiumDesignTokens.animationMedium,
      vsync: this,
    );

    _pressController = AnimationController(
      duration: PremiumDesignTokens.animationFast,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: PremiumDesignTokens.curveEaseOut,
    ));

    _elevationAnimation = Tween<double>(
      begin: 8.0,
      end: 16.0,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: PremiumDesignTokens.curveEaseOut,
    ));

    _shimmerAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: PremiumDesignTokens.curveEaseInOut,
    ));
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _pressController.dispose();
    super.dispose();
  }

  void _handleHover(bool isHovered) {
    // Hover functionality removed for mobile optimization
    // Mobile devices don't support hover states
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _pressController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _pressController.reverse();
    widget.onTap?.call();
  }

  void _handleTapCancel() {
    setState(() => _isPressed = false);
    _pressController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_hoverController, _pressController]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value * (_isPressed ? 0.98 : 1.0),
          child: Container(
            width: widget.width ?? 300,
            height: widget.height ?? 240,
            margin: const EdgeInsets.symmetric(
              horizontal: PremiumDesignTokens.spacing4,
              vertical: PremiumDesignTokens.spacing8,
            ),
            child: MouseRegion(
              onEnter: (_) => _handleHover(true),
              onExit: (_) => _handleHover(false),
              child: GestureDetector(
                onTapDown: _handleTapDown,
                onTapUp: _handleTapUp,
                onTapCancel: _handleTapCancel,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius:
                        BorderRadius.circular(PremiumDesignTokens.radiusXLarge),
                    boxShadow: [
                      BoxShadow(
                        color: widget.booking.typeColor.withValues(alpha: 0.1),
                        offset: const Offset(0, 8),
                        blurRadius: _elevationAnimation.value,
                        spreadRadius: 0,
                      ),
                      ...PremiumDesignTokens.shadowLarge,
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius:
                        BorderRadius.circular(PremiumDesignTokens.radiusXLarge),
                    child: Stack(
                      children: [
                        _buildImageSection(),
                        _buildGradientOverlay(),
                        _buildContentSection(),
                        _buildStatusBadge(),
                        _buildTypeBadge(),
                        if (widget.showActions) _buildActionButtons(),
                        _buildShimmerEffect(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildImageSection() {
    return Positioned.fill(
      child: CachedNetworkImage(
        imageUrl: widget.booking.imageUrl,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                widget.booking.typeColor.withValues(alpha: 0.3),
                widget.booking.typeColor.withValues(alpha: 0.1),
              ],
            ),
          ),
          child: Center(
            child: Icon(
              widget.booking.typeIcon,
              size: 48,
              color: widget.booking.typeColor.withValues(alpha: 0.5),
            ),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                widget.booking.typeColor.withValues(alpha: 0.3),
                widget.booking.typeColor.withValues(alpha: 0.1),
              ],
            ),
          ),
          child: Center(
            child: Icon(
              widget.booking.typeIcon,
              size: 48,
              color: widget.booking.typeColor.withValues(alpha: 0.7),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGradientOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.transparent,
              Color(0x66000000),
              Color(0xCC000000),
            ],
            stops: [0.0, 0.4, 0.7, 1.0],
          ),
        ),
      ),
    );
  }

  Widget _buildContentSection() {
    return Positioned(
      left: PremiumDesignTokens.spacing20,
      right: PremiumDesignTokens.spacing20,
      bottom: PremiumDesignTokens.spacing20,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title
          Text(
            widget.booking.title,
            style: PremiumDesignTokens.headlineMedium.copyWith(
              color: PremiumDesignTokens.neutralWhite,
              fontWeight: FontWeight.w700,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: PremiumDesignTokens.spacing4),

          // Subtitle
          Text(
            widget.booking.subtitle,
            style: PremiumDesignTokens.bodyMedium.copyWith(
              color: PremiumDesignTokens.neutralWhite.withValues(alpha: 0.9),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: PremiumDesignTokens.spacing12),

          // Location and Rating Row
          Row(
            children: [
              Icon(
                Icons.location_on_rounded,
                size: 16,
                color: PremiumDesignTokens.neutralWhite.withValues(alpha: 0.8),
              ),
              const SizedBox(width: PremiumDesignTokens.spacing4),
              Expanded(
                child: Text(
                  widget.booking.location,
                  style: PremiumDesignTokens.bodySmall.copyWith(
                    color:
                        PremiumDesignTokens.neutralWhite.withValues(alpha: 0.8),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: PremiumDesignTokens.spacing12),
              Icon(
                Icons.star_rounded,
                size: 16,
                color: PremiumDesignTokens.accentOrange,
              ),
              const SizedBox(width: PremiumDesignTokens.spacing2),
              Text(
                widget.booking.rating.toString(),
                style: PremiumDesignTokens.bodySmall.copyWith(
                  color: PremiumDesignTokens.neutralWhite,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: PremiumDesignTokens.spacing8),

          // Price and Date Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                flex: 2,
                child: Text(
                  widget.booking.formattedPrice,
                  style: PremiumDesignTokens.headlineSmall.copyWith(
                    color: PremiumDesignTokens.neutralWhite,
                    fontWeight: FontWeight.w800,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
              const SizedBox(width: PremiumDesignTokens.spacing8),
              Flexible(
                flex: 1,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: PremiumDesignTokens.spacing8,
                    vertical: PremiumDesignTokens.spacing4,
                  ),
                  decoration: BoxDecoration(
                    color:
                        PremiumDesignTokens.neutralWhite.withValues(alpha: 0.2),
                    borderRadius:
                        BorderRadius.circular(PremiumDesignTokens.radiusSmall),
                  ),
                  child: Text(
                    widget.booking.dateRange,
                    style: PremiumDesignTokens.bodySmall.copyWith(
                      color: PremiumDesignTokens.neutralWhite,
                      fontWeight: FontWeight.w600,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge() {
    return Positioned(
      top: PremiumDesignTokens.spacing16,
      left: PremiumDesignTokens.spacing16,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: PremiumDesignTokens.spacing12,
          vertical: PremiumDesignTokens.spacing6,
        ),
        decoration: BoxDecoration(
          color: widget.booking.statusColor,
          borderRadius: BorderRadius.circular(PremiumDesignTokens.radiusRound),
          boxShadow: PremiumDesignTokens.shadowSmall,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              widget.booking.statusIcon,
              size: 14,
              color: PremiumDesignTokens.neutralWhite,
            ),
            const SizedBox(width: PremiumDesignTokens.spacing4),
            Text(
              widget.booking.statusText,
              style: PremiumDesignTokens.labelSmall.copyWith(
                color: PremiumDesignTokens.neutralWhite,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeBadge() {
    return Positioned(
      top: PremiumDesignTokens.spacing16,
      right: PremiumDesignTokens.spacing16,
      child: Container(
        padding: const EdgeInsets.all(PremiumDesignTokens.spacing8),
        decoration: BoxDecoration(
          color: PremiumDesignTokens.neutralWhite.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(PremiumDesignTokens.radiusSmall),
          boxShadow: PremiumDesignTokens.shadowSmall,
        ),
        child: Icon(
          widget.booking.typeIcon,
          size: 20,
          color: widget.booking.typeColor,
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    // Removed favorite/share buttons to prevent overlap with date text
    // This creates cleaner card layout without visual conflicts
    return const SizedBox.shrink();
  }

  Widget _buildShimmerEffect() {
    return Positioned.fill(
      child: AnimatedBuilder(
        animation: _shimmerAnimation,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.transparent,
                  PremiumDesignTokens.neutralWhite.withValues(
                    alpha: 0.1 * _shimmerAnimation.value,
                  ),
                  Colors.transparent,
                ],
                stops: const [0.0, 0.5, 1.0],
              ),
            ),
          );
        },
      ),
    );
  }
}
