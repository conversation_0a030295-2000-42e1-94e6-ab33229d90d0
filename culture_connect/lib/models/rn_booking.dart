import 'package:flutter/material.dart';

/// Booking types matching React Native implementation
enum BookingType {
  tour,
  accommodation,
  event,
  flight,
  experience,
  transport,
}

/// Booking status matching React Native implementation
enum BookingStatus {
  upcoming,
  confirmed,
  pending,
  completed,
  cancelled,
}

/// React Native compatible booking model
class RNBooking {
  final String id;
  final BookingType type;
  final String title;
  final BookingStatus status;
  final String date;
  final double price;
  final String imageUrl;
  final String confirmationCode;
  final String location;
  final double rating;
  final String duration;
  final int participants;
  final Map<String, String>? details;

  const RNBooking({
    required this.id,
    required this.type,
    required this.title,
    required this.status,
    required this.date,
    required this.price,
    required this.imageUrl,
    required this.confirmationCode,
    required this.location,
    required this.rating,
    required this.duration,
    required this.participants,
    this.details,
  });

  /// Get status color matching React Native design
  Color getStatusColor() {
    switch (status) {
      case BookingStatus.upcoming:
      case BookingStatus.confirmed:
        return const Color(0xFF06B6D4); // Secondary color
      case BookingStatus.pending:
        return const Color(0xFFF59E0B); // Warning color
      case BookingStatus.completed:
        return const Color(0xFF10B981); // Success color
      case BookingStatus.cancelled:
        return const Color(0xFFEF4444); // Error color
    }
  }

  /// Get status icon matching React Native design
  IconData getStatusIcon() {
    switch (status) {
      case BookingStatus.upcoming:
      case BookingStatus.confirmed:
        return Icons.check_circle;
      case BookingStatus.pending:
        return Icons.schedule;
      case BookingStatus.completed:
        return Icons.done_all;
      case BookingStatus.cancelled:
        return Icons.cancel;
    }
  }

  /// Get status text
  String getStatusText() {
    switch (status) {
      case BookingStatus.upcoming:
        return 'Upcoming';
      case BookingStatus.confirmed:
        return 'Confirmed';
      case BookingStatus.pending:
        return 'Pending';
      case BookingStatus.completed:
        return 'Completed';
      case BookingStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Get type icon
  IconData getTypeIcon() {
    switch (type) {
      case BookingType.tour:
        return Icons.tour;
      case BookingType.accommodation:
        return Icons.hotel;
      case BookingType.event:
        return Icons.event;
      case BookingType.flight:
        return Icons.flight;
      case BookingType.experience:
        return Icons.local_activity;
      case BookingType.transport:
        return Icons.directions_car;
    }
  }

  /// Get type text
  String getTypeText() {
    switch (type) {
      case BookingType.tour:
        return 'Tour';
      case BookingType.accommodation:
        return 'Hotel';
      case BookingType.event:
        return 'Event';
      case BookingType.flight:
        return 'Flight';
      case BookingType.experience:
        return 'Experience';
      case BookingType.transport:
        return 'Transport';
    }
  }

  /// Get formatted price
  String getFormattedPrice() {
    return '\$${price.toStringAsFixed(0)}';
  }

  /// Get participant text
  String getParticipantText() {
    return participants == 1 ? '1 person' : '$participants people';
  }

  /// Check if booking can be cancelled
  bool canBeCancelled() {
    return status == BookingStatus.confirmed || status == BookingStatus.pending;
  }

  /// Check if booking can be rescheduled
  bool canBeRescheduled() {
    return status == BookingStatus.confirmed;
  }

  /// Copy with method for immutable updates
  RNBooking copyWith({
    String? id,
    BookingType? type,
    String? title,
    BookingStatus? status,
    String? date,
    double? price,
    String? imageUrl,
    String? confirmationCode,
    String? location,
    double? rating,
    String? duration,
    int? participants,
    Map<String, String>? details,
  }) {
    return RNBooking(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      status: status ?? this.status,
      date: date ?? this.date,
      price: price ?? this.price,
      imageUrl: imageUrl ?? this.imageUrl,
      confirmationCode: confirmationCode ?? this.confirmationCode,
      location: location ?? this.location,
      rating: rating ?? this.rating,
      duration: duration ?? this.duration,
      participants: participants ?? this.participants,
      details: details ?? this.details,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'status': status.name,
      'date': date,
      'price': price,
      'imageUrl': imageUrl,
      'confirmationCode': confirmationCode,
      'location': location,
      'rating': rating,
      'duration': duration,
      'participants': participants,
      'details': details,
    };
  }

  /// Create from JSON
  factory RNBooking.fromJson(Map<String, dynamic> json) {
    return RNBooking(
      id: json['id'],
      type: BookingType.values.firstWhere((e) => e.name == json['type']),
      title: json['title'],
      status: BookingStatus.values.firstWhere((e) => e.name == json['status']),
      date: json['date'],
      price: json['price'].toDouble(),
      imageUrl: json['imageUrl'],
      confirmationCode: json['confirmationCode'],
      location: json['location'],
      rating: json['rating'].toDouble(),
      duration: json['duration'],
      participants: json['participants'],
      details: json['details'] != null 
          ? Map<String, String>.from(json['details'])
          : null,
    );
  }
}

/// Booking statistics model matching React Native structure
class BookingStatistics {
  final int totalTrips;
  final int countries;
  final double averageRating;
  final double totalSavings;
  final int upcomingBookings;
  final int totalBookings;

  const BookingStatistics({
    required this.totalTrips,
    required this.countries,
    required this.averageRating,
    required this.totalSavings,
    required this.upcomingBookings,
    required this.totalBookings,
  });

  /// Get formatted average rating
  String getFormattedRating() {
    return averageRating.toStringAsFixed(1);
  }

  /// Get formatted total savings
  String getFormattedSavings() {
    return '\$${totalSavings.toStringAsFixed(0)}';
  }

  /// Copy with method
  BookingStatistics copyWith({
    int? totalTrips,
    int? countries,
    double? averageRating,
    double? totalSavings,
    int? upcomingBookings,
    int? totalBookings,
  }) {
    return BookingStatistics(
      totalTrips: totalTrips ?? this.totalTrips,
      countries: countries ?? this.countries,
      averageRating: averageRating ?? this.averageRating,
      totalSavings: totalSavings ?? this.totalSavings,
      upcomingBookings: upcomingBookings ?? this.upcomingBookings,
      totalBookings: totalBookings ?? this.totalBookings,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'totalTrips': totalTrips,
      'countries': countries,
      'averageRating': averageRating,
      'totalSavings': totalSavings,
      'upcomingBookings': upcomingBookings,
      'totalBookings': totalBookings,
    };
  }

  /// Create from JSON
  factory BookingStatistics.fromJson(Map<String, dynamic> json) {
    return BookingStatistics(
      totalTrips: json['totalTrips'],
      countries: json['countries'],
      averageRating: json['averageRating'].toDouble(),
      totalSavings: json['totalSavings'].toDouble(),
      upcomingBookings: json['upcomingBookings'],
      totalBookings: json['totalBookings'],
    );
  }
}
