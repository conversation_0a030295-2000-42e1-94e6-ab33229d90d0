// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/screens/travel/visa/visa_provider_marketplace_screen.dart';
import 'package:culture_connect/models/travel/document/visa_service_provider.dart';

/// Mandatory disclaimer and education quiz screen
/// Users must complete this before accessing consultant services
class DisclaimerQuizScreen extends ConsumerStatefulWidget {
  const DisclaimerQuizScreen({
    super.key,
    this.destinationCountry,
    this.visaType,
  });

  final String? destinationCountry;
  final VisaProviderSpecialization? visaType;

  @override
  ConsumerState<DisclaimerQuizScreen> createState() =>
      _DisclaimerQuizScreenState();
}

class _DisclaimerQuizScreenState extends ConsumerState<DisclaimerQuizScreen> {
  final PageController _pageController = PageController();

  // Quiz state
  int _currentPage = 0;
  final Map<int, int?> _answers = {};
  bool _hasReadDisclaimer = false;
  bool _canProceed = false;

  // Quiz questions and answers
  final List<QuizQuestion> _questions = [
    QuizQuestion(
      question: 'Who makes the final decision on visa approval?',
      options: [
        'CultureConnect consultants',
        'The embassy or consulate',
        'Our AI system',
        'The payment processor',
      ],
      correctAnswer: 1,
      explanation:
          'Only embassies and consulates have the authority to approve or deny visa applications. Consultants provide guidance but cannot guarantee approval.',
    ),
    QuizQuestion(
      question: 'What does our escrow payment system protect?',
      options: [
        'Guarantees visa approval',
        'Protects your payment until service completion',
        'Provides embassy influence',
        'Ensures faster processing',
      ],
      correctAnswer: 1,
      explanation:
          'Our escrow system holds your payment securely until the consultant completes their services. It does not guarantee visa approval or embassy influence.',
    ),
    QuizQuestion(
      question: 'What should you do if a consultant claims embassy influence?',
      options: [
        'Pay them immediately',
        'Ask for a discount',
        'Report them to us',
        'Request faster service',
      ],
      correctAnswer: 2,
      explanation:
          'No consultant has special influence with embassies. Any such claims should be reported immediately as they violate our terms of service.',
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        title: Text(
          'Important Information',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: AppTheme.fontWeightBold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
      ),
      body: Column(
        children: [
          _buildProgressIndicator(),
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (page) => setState(() => _currentPage = page),
              children: [
                _buildDisclaimerPage(theme),
                ..._questions.asMap().entries.map(
                    (entry) => _buildQuizPage(theme, entry.key, entry.value)),
                _buildCompletionPage(theme),
              ],
            ),
          ),
          _buildBottomNavigation(theme),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final totalPages =
        _questions.length + 2; // disclaimer + questions + completion
    final progress = (_currentPage + 1) / totalPages;

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Step ${_currentPage + 1} of $totalPages',
                style: const TextStyle(
                  fontSize: AppTheme.fontSizeSm,
                  color: AppTheme.textSecondaryColor,
                  fontWeight: AppTheme.fontWeightMedium,
                ),
              ),
              Text(
                '${(progress * 100).round()}% Complete',
                style: const TextStyle(
                  fontSize: AppTheme.fontSizeSm,
                  color: AppTheme.primaryColor,
                  fontWeight: AppTheme.fontWeightSemibold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSm),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: AppTheme.borderColor,
            valueColor:
                const AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
          ),
        ],
      ),
    );
  }

  Widget _buildDisclaimerPage(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingLg),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppTheme.warningColor.withValues(alpha: 0.1),
                  AppTheme.errorColor.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
              border: Border.all(
                color: AppTheme.warningColor.withValues(alpha: 0.3),
                width: 2,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(AppTheme.spacingSm),
                      decoration: BoxDecoration(
                        color: AppTheme.warningColor,
                        borderRadius:
                            BorderRadius.circular(AppTheme.borderRadiusSmall),
                      ),
                      child: const Icon(
                        Icons.warning,
                        color: AppTheme.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingMd),
                    Expanded(
                      child: Text(
                        'IMPORTANT LEGAL DISCLAIMER',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: AppTheme.fontWeightBold,
                          color: AppTheme.warningColor,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppTheme.spacingLg),
                _buildDisclaimerSection(
                  'This is NOT an official government service',
                  'We do not represent any embassy, consulate, or government agency. We are a private service connecting you with certified visa consultants.',
                ),
                const SizedBox(height: AppTheme.spacingMd),
                _buildDisclaimerSection(
                  'No guarantee of visa approval',
                  'Visa approval is always at the sole discretion of the embassy or consulate. No consultant can guarantee approval, regardless of their experience or claims.',
                ),
                const SizedBox(height: AppTheme.spacingMd),
                _buildDisclaimerSection(
                  'Your documents are secure',
                  'Your documents are used solely for visa application preparation and are protected by bank-grade encryption. They are automatically deleted after 90 days.',
                ),
                const SizedBox(height: AppTheme.spacingMd),
                _buildDisclaimerSection(
                  'Escrow payment protection',
                  'Our escrow system protects your payment until services are completed. This does not guarantee visa approval - only service delivery.',
                ),
              ],
            ),
          ),
          const SizedBox(height: AppTheme.spacingLg),
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMd),
            decoration: BoxDecoration(
              color: AppTheme.infoColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
              border: Border.all(
                color: AppTheme.infoColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Checkbox(
                  value: _hasReadDisclaimer,
                  onChanged: (value) =>
                      setState(() => _hasReadDisclaimer = value ?? false),
                  activeColor: AppTheme.primaryColor,
                ),
                const SizedBox(width: AppTheme.spacingSm),
                Expanded(
                  child: Text(
                    'I have read and understand the above disclaimer. I acknowledge that this is not an official government service and that visa approval cannot be guaranteed.',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.textPrimaryColor,
                      height: AppTheme.lineHeightRelaxed,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDisclaimerSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 4),
              width: 6,
              height: 6,
              decoration: const BoxDecoration(
                color: AppTheme.warningColor,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: AppTheme.spacingSm),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: AppTheme.fontWeightBold,
                      color: AppTheme.textPrimaryColor,
                      fontSize: AppTheme.fontSizeMd,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingXs),
                  Text(
                    content,
                    style: const TextStyle(
                      color: AppTheme.textSecondaryColor,
                      fontSize: AppTheme.fontSizeSm,
                      height: AppTheme.lineHeightRelaxed,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuizPage(
      ThemeData theme, int questionIndex, QuizQuestion question) {
    final selectedAnswer = _answers[questionIndex];
    final hasAnswered = selectedAnswer != null;
    final isCorrect = hasAnswered && selectedAnswer == question.correctAnswer;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingLg),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppTheme.primaryColor.withValues(alpha: 0.1),
                  AppTheme.infoColor.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
              border: Border.all(
                color: AppTheme.primaryColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(AppTheme.spacingSm),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        borderRadius:
                            BorderRadius.circular(AppTheme.borderRadiusSmall),
                      ),
                      child: Text(
                        '${questionIndex + 1}',
                        style: const TextStyle(
                          color: AppTheme.white,
                          fontWeight: AppTheme.fontWeightBold,
                          fontSize: AppTheme.fontSizeMd,
                        ),
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingMd),
                    Expanded(
                      child: Text(
                        'Question ${questionIndex + 1} of ${_questions.length}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: AppTheme.fontWeightBold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppTheme.spacingLg),
                Text(
                  question.question,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: AppTheme.fontWeightSemibold,
                    color: AppTheme.textPrimaryColor,
                    height: AppTheme.lineHeightRelaxed,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppTheme.spacingLg),
          ...question.options.asMap().entries.map((entry) {
            final optionIndex = entry.key;
            final option = entry.value;
            final isSelected = selectedAnswer == optionIndex;
            final isCorrectOption = optionIndex == question.correctAnswer;

            Color backgroundColor = AppTheme.white;
            Color borderColor = AppTheme.borderColor;
            Color textColor = AppTheme.textPrimaryColor;

            if (hasAnswered) {
              if (isSelected) {
                if (isCorrect) {
                  backgroundColor =
                      AppTheme.successColor.withValues(alpha: 0.1);
                  borderColor = AppTheme.successColor;
                  textColor = AppTheme.successColor;
                } else {
                  backgroundColor = AppTheme.errorColor.withValues(alpha: 0.1);
                  borderColor = AppTheme.errorColor;
                  textColor = AppTheme.errorColor;
                }
              } else if (isCorrectOption) {
                backgroundColor = AppTheme.successColor.withValues(alpha: 0.1);
                borderColor = AppTheme.successColor;
                textColor = AppTheme.successColor;
              }
            } else if (isSelected) {
              backgroundColor = AppTheme.primaryColor.withValues(alpha: 0.1);
              borderColor = AppTheme.primaryColor;
              textColor = AppTheme.primaryColor;
            }

            return Container(
              margin: const EdgeInsets.only(bottom: AppTheme.spacingMd),
              child: Material(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
                child: InkWell(
                  onTap: hasAnswered
                      ? null
                      : () {
                          setState(() {
                            _answers[questionIndex] = optionIndex;
                          });
                        },
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusLarge),
                  child: Container(
                    padding: const EdgeInsets.all(AppTheme.spacingMd),
                    decoration: BoxDecoration(
                      borderRadius:
                          BorderRadius.circular(AppTheme.borderRadiusLarge),
                      border: Border.all(
                        color: borderColor,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: borderColor,
                              width: 2,
                            ),
                            color:
                                isSelected ? borderColor : Colors.transparent,
                          ),
                          child: isSelected
                              ? Icon(
                                  hasAnswered && isCorrectOption
                                      ? Icons.check
                                      : hasAnswered && !isCorrect
                                          ? Icons.close
                                          : Icons.circle,
                                  size: 16,
                                  color: hasAnswered
                                      ? AppTheme.white
                                      : borderColor,
                                )
                              : null,
                        ),
                        const SizedBox(width: AppTheme.spacingMd),
                        Expanded(
                          child: Text(
                            option,
                            style: theme.textTheme.bodyLarge?.copyWith(
                              color: textColor,
                              fontWeight: isSelected
                                  ? AppTheme.fontWeightSemibold
                                  : AppTheme.fontWeightMedium,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }),
          if (hasAnswered) ...[
            const SizedBox(height: AppTheme.spacingLg),
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingMd),
              decoration: BoxDecoration(
                color: isCorrect
                    ? AppTheme.successColor.withValues(alpha: 0.1)
                    : AppTheme.infoColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
                border: Border.all(
                  color: isCorrect
                      ? AppTheme.successColor.withValues(alpha: 0.3)
                      : AppTheme.infoColor.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    isCorrect ? Icons.check_circle : Icons.info,
                    color:
                        isCorrect ? AppTheme.successColor : AppTheme.infoColor,
                    size: 20,
                  ),
                  const SizedBox(width: AppTheme.spacingMd),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isCorrect ? 'Correct!' : 'Learn More',
                          style: TextStyle(
                            fontWeight: AppTheme.fontWeightBold,
                            color: isCorrect
                                ? AppTheme.successColor
                                : AppTheme.infoColor,
                            fontSize: AppTheme.fontSizeMd,
                          ),
                        ),
                        const SizedBox(height: AppTheme.spacingXs),
                        Text(
                          question.explanation,
                          style: const TextStyle(
                            color: AppTheme.textSecondaryColor,
                            fontSize: AppTheme.fontSizeSm,
                            height: AppTheme.lineHeightRelaxed,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCompletionPage(ThemeData theme) {
    final allCorrect = _questions
        .asMap()
        .entries
        .every((entry) => _answers[entry.key] == entry.value.correctAnswer);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: AppTheme.spacingXl),
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingXl),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppTheme.successColor.withValues(alpha: 0.1),
                  AppTheme.primaryColor.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
              border: Border.all(
                color: AppTheme.successColor.withValues(alpha: 0.3),
                width: 2,
              ),
            ),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingLg),
                  decoration: const BoxDecoration(
                    color: AppTheme.successColor,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: AppTheme.white,
                    size: 48,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingLg),
                Text(
                  allCorrect ? 'Excellent!' : 'Quiz Complete',
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: AppTheme.fontWeightBold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingMd),
                Text(
                  allCorrect
                      ? 'You have successfully completed the education quiz and can now proceed to work with our certified consultants.'
                      : 'You have completed the quiz. Please review the explanations to better understand our service limitations.',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: AppTheme.textSecondaryColor,
                    height: AppTheme.lineHeightRelaxed,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppTheme.spacingLg),
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingMd),
                  decoration: BoxDecoration(
                    color: AppTheme.infoColor.withValues(alpha: 0.1),
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusLarge),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Your Score: ${_getCorrectAnswers()}/${_questions.length}',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: AppTheme.fontWeightBold,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingSm),
                      Text(
                        allCorrect
                            ? 'Perfect! You understand our service limitations.'
                            : 'Review the explanations to learn more.',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppTheme.spacingXl),
          if (allCorrect) ...[
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingMd),
              decoration: BoxDecoration(
                color: AppTheme.successColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
                border: Border.all(
                  color: AppTheme.successColor.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.verified_user,
                    color: AppTheme.successColor,
                    size: 24,
                  ),
                  const SizedBox(width: AppTheme.spacingMd),
                  Expanded(
                    child: Text(
                      'You are now qualified to work with our certified visa consultants. Your understanding of our service limitations ensures a smooth collaboration.',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: AppTheme.successColor,
                        height: AppTheme.lineHeightRelaxed,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBottomNavigation(ThemeData theme) {
    final isLastPage = _currentPage == _questions.length + 1;
    final canGoNext = _canGoToNextPage();

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: const BoxDecoration(
        color: AppTheme.white,
        border: Border(
          top: BorderSide(
            color: AppTheme.borderColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          if (_currentPage > 0)
            Expanded(
              flex: 1,
              child: OutlinedButton(
                onPressed: () {
                  _pageController.previousPage(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                style: OutlinedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(vertical: AppTheme.spacingMd),
                  shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusLarge),
                  ),
                ),
                child: const Text('Previous'),
              ),
            ),
          if (_currentPage > 0) const SizedBox(width: AppTheme.spacingMd),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: canGoNext
                  ? () {
                      if (isLastPage) {
                        _proceedToConsultants();
                      } else {
                        _pageController.nextPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      }
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    canGoNext ? AppTheme.primaryColor : AppTheme.borderColor,
                foregroundColor: AppTheme.white,
                padding:
                    const EdgeInsets.symmetric(vertical: AppTheme.spacingMd),
                shape: RoundedRectangleBorder(
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusLarge),
                ),
              ),
              child: Text(
                isLastPage
                    ? 'Find Consultants'
                    : _currentPage == 0
                        ? 'Start Quiz'
                        : 'Next Question',
                style: const TextStyle(
                  fontWeight: AppTheme.fontWeightSemibold,
                  fontSize: AppTheme.fontSizeMd,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _canGoToNextPage() {
    if (_currentPage == 0) {
      return _hasReadDisclaimer;
    } else if (_currentPage <= _questions.length) {
      final questionIndex = _currentPage - 1;
      return _answers.containsKey(questionIndex);
    }
    return true;
  }

  int _getCorrectAnswers() {
    return _questions
        .asMap()
        .entries
        .where((entry) => _answers[entry.key] == entry.value.correctAnswer)
        .length;
  }

  void _proceedToConsultants() {
    final allCorrect = _getCorrectAnswers() == _questions.length;

    if (allCorrect) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => VisaProviderMarketplaceScreen(
            destinationCountry: widget.destinationCountry,
            visaType: widget.visaType,
          ),
        ),
      );
    } else {
      // Show retry option or educational content
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Review Required'),
          content: const Text(
            'Please review the quiz explanations to better understand our service limitations before proceeding.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Review Quiz'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                    builder: (context) => VisaProviderMarketplaceScreen(
                      destinationCountry: widget.destinationCountry,
                      visaType: widget.visaType,
                    ),
                  ),
                );
              },
              child: const Text('Proceed Anyway'),
            ),
          ],
        ),
      );
    }
  }
}

/// Quiz question model
class QuizQuestion {
  final String question;
  final List<String> options;
  final int correctAnswer;
  final String explanation;

  const QuizQuestion({
    required this.question,
    required this.options,
    required this.correctAnswer,
    required this.explanation,
  });
}
