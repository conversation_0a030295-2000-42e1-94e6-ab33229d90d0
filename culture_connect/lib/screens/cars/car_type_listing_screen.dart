import 'package:flutter/material.dart';
import 'package:culture_connect/models/car.dart';
import 'package:culture_connect/services/car_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/cars/car_card.dart';
import 'package:culture_connect/screens/cars/car_detail_screen.dart';

/// Vehicle type listing screen with filtering, sorting, and view mode options
/// Displays all cars of a specific category with comprehensive controls
class CarTypeListingScreen extends StatefulWidget {
  final String category;

  const CarTypeListingScreen({
    super.key,
    required this.category,
  });

  @override
  State<CarTypeListingScreen> createState() => _CarTypeListingScreenState();
}

class _CarTypeListingScreenState extends State<CarTypeListingScreen>
    with TickerProviderStateMixin {
  // State variables
  List<Car> _allCars = [];
  List<Car> _filteredCars = [];
  bool _isGridView = true;
  String _sortBy = 'default';
  double _minPrice = 0;
  double _maxPrice = 1000;
  String _selectedFuelType = 'All';
  String _selectedLocation = 'All';
  bool _showFilters = false;

  // Animation controllers
  late AnimationController _filterAnimationController;
  late AnimationController _viewModeAnimationController;
  late Animation<double> _filterSlideAnimation;
  late Animation<double> _viewModeRotationAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadCars();
  }

  void _initializeAnimations() {
    _filterAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _viewModeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _filterSlideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _filterAnimationController,
      curve: Curves.easeInOut,
    ));

    _viewModeRotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _viewModeAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  void _loadCars() {
    setState(() {
      _allCars = CarService.getCarsByCategory(widget.category);
      _filteredCars = List.from(_allCars);

      // Set price range based on available cars
      if (_allCars.isNotEmpty) {
        _minPrice = _allCars
            .map((car) => car.pricePerDay)
            .reduce((a, b) => a < b ? a : b);
        _maxPrice = _allCars
            .map((car) => car.pricePerDay)
            .reduce((a, b) => a > b ? a : b);
      }
    });
  }

  void _applyFiltersAndSort() {
    setState(() {
      // Start with all cars
      List<Car> filtered = List.from(_allCars);

      // Apply price filter
      filtered = filtered.where((car) {
        return car.pricePerDay >= _minPrice && car.pricePerDay <= _maxPrice;
      }).toList();

      // Apply fuel type filter
      if (_selectedFuelType != 'All') {
        filtered =
            filtered.where((car) => car.fuelType == _selectedFuelType).toList();
      }

      // Apply location filter
      if (_selectedLocation != 'All') {
        filtered =
            filtered.where((car) => car.location == _selectedLocation).toList();
      }

      // Apply sorting
      filtered = CarService.sortCars(filtered, _sortBy);

      _filteredCars = filtered;
    });
  }

  void _toggleViewMode() {
    setState(() {
      _isGridView = !_isGridView;
    });

    if (_isGridView) {
      _viewModeAnimationController.reverse();
    } else {
      _viewModeAnimationController.forward();
    }
  }

  void _toggleFilters() {
    setState(() {
      _showFilters = !_showFilters;
    });

    if (_showFilters) {
      _filterAnimationController.forward();
    } else {
      _filterAnimationController.reverse();
    }
  }

  void _handleCarPress(Car car) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CarDetailScreen(car: car),
      ),
    );
  }

  @override
  void dispose() {
    _filterAnimationController.dispose();
    _viewModeAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildControlBar(),
          _buildFilterPanel(),
          Expanded(
            child: _buildCarsList(),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppTheme.backgroundColor,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(
          Icons.arrow_back_ios,
          color: AppTheme.textPrimaryColor,
          size: 20,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        '${widget.category} Cars',
        style: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: AppTheme.textPrimaryColor,
          letterSpacing: -0.5,
        ),
      ),
      centerTitle: false,
      actions: [
        Container(
          margin: const EdgeInsets.only(right: AppTheme.spacingMd),
          child: Center(
            child: Text(
              '${_filteredCars.length} cars',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildControlBar() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: const BoxDecoration(
        color: AppTheme.white,
        border: Border(
          bottom: BorderSide(color: AppTheme.borderLight, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Filter button
          Expanded(
            flex: 2, // Balanced space allocation
            child: GestureDetector(
              onTap: _toggleFilters,
              child: Container(
                height: 48, // Fixed height for consistent button sizing
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingMd,
                  vertical: AppTheme.spacingSm,
                ),
                decoration: BoxDecoration(
                  color: _showFilters
                      ? AppTheme.accentColor.withValues(alpha: 0.1)
                      : AppTheme.backgroundSecondary,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _showFilters
                        ? AppTheme.accentColor
                        : AppTheme.borderLight,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min, // Prevent overflow
                  children: [
                    Icon(
                      Icons.tune,
                      size: 18,
                      color: _showFilters
                          ? AppTheme.accentColor
                          : AppTheme.textSecondaryColor,
                    ),
                    const SizedBox(width: AppTheme.spacingSm),
                    Flexible(
                      // Allow text to shrink if needed
                      child: Text(
                        'Filter',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: _showFilters
                              ? AppTheme.accentColor
                              : AppTheme.textSecondaryColor,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: AppTheme.spacingSm), // Reduced spacing

          // Sort dropdown
          Expanded(
            flex: 2, // Matching flex ratio for identical height
            child: Container(
              height: 48, // Fixed height matching filter button
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingMd,
                vertical: AppTheme.spacingSm,
              ),
              decoration: BoxDecoration(
                color: AppTheme.backgroundSecondary,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppTheme.borderLight, width: 1),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: _sortBy,
                  isExpanded: true, // Ensure dropdown uses full width
                  icon: const Icon(Icons.keyboard_arrow_down, size: 18),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textSecondaryColor,
                  ),
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _sortBy = newValue;
                      });
                      _applyFiltersAndSort();
                    }
                  },
                  items: const [
                    DropdownMenuItem(value: 'default', child: Text('Default')),
                    DropdownMenuItem(
                        value: 'price_low_to_high',
                        child: Text('Price: Low to High')),
                    DropdownMenuItem(
                        value: 'price_high_to_low',
                        child: Text('Price: High to Low')),
                    DropdownMenuItem(
                        value: 'rating', child: Text('Highest Rated')),
                    DropdownMenuItem(value: 'name', child: Text('Name A-Z')),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: AppTheme.spacingSm), // Reduced spacing

          // View mode toggle - more compact
          GestureDetector(
            onTap: _toggleViewMode,
            child: Container(
              padding: const EdgeInsets.all(AppTheme.spacingSm),
              decoration: BoxDecoration(
                color: AppTheme.backgroundSecondary,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppTheme.borderLight, width: 1),
              ),
              child: AnimatedBuilder(
                animation: _viewModeRotationAnimation,
                builder: (context, child) {
                  return Transform.rotate(
                    angle: _viewModeRotationAnimation.value * 3.14159,
                    child: Icon(
                      _isGridView ? Icons.grid_view : Icons.view_list,
                      size: 18, // Slightly smaller icon to save space
                      color: AppTheme.textSecondaryColor,
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterPanel() {
    return AnimatedBuilder(
      animation: _filterSlideAnimation,
      builder: (context, child) {
        return Container(
          height: _filterSlideAnimation.value * 200,
          decoration: const BoxDecoration(
            color: AppTheme.white,
            border: Border(
              bottom: BorderSide(color: AppTheme.borderLight, width: 1),
            ),
          ),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppTheme.spacingLg),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Price Range
                const Text(
                  'Price Range (per day)',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingSm),
                RangeSlider(
                  values: RangeValues(_minPrice, _maxPrice),
                  min: 0,
                  max: 1000,
                  divisions: 20,
                  activeColor: AppTheme.accentColor,
                  inactiveColor: AppTheme.accentColor.withValues(alpha: 0.3),
                  labels: RangeLabels(
                    '\$${_minPrice.round()}',
                    '\$${_maxPrice.round()}',
                  ),
                  onChanged: (RangeValues values) {
                    setState(() {
                      _minPrice = values.start;
                      _maxPrice = values.end;
                    });
                    _applyFiltersAndSort();
                  },
                ),
                const SizedBox(height: AppTheme.spacingMd),

                // Fuel Type and Location Filters
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Fuel Type',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.textPrimaryColor,
                            ),
                          ),
                          const SizedBox(height: AppTheme.spacingSm),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: AppTheme.spacingMd),
                            decoration: BoxDecoration(
                              color: AppTheme.backgroundSecondary,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                  color: AppTheme.borderLight, width: 1),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<String>(
                                value: _selectedFuelType,
                                isExpanded: true,
                                icon: const Icon(Icons.keyboard_arrow_down,
                                    size: 18),
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: AppTheme.textSecondaryColor,
                                ),
                                onChanged: (String? newValue) {
                                  if (newValue != null) {
                                    setState(() {
                                      _selectedFuelType = newValue;
                                    });
                                    _applyFiltersAndSort();
                                  }
                                },
                                items: [
                                  'All',
                                  ...CarService.getAvailableFuelTypes()
                                ].map((String value) {
                                  return DropdownMenuItem<String>(
                                    value: value,
                                    child: Text(value),
                                  );
                                }).toList(),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingMd),

                    // Location Filter
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Location',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.textPrimaryColor,
                            ),
                          ),
                          const SizedBox(height: AppTheme.spacingSm),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: AppTheme.spacingMd),
                            decoration: BoxDecoration(
                              color: AppTheme.backgroundSecondary,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                  color: AppTheme.borderLight, width: 1),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<String>(
                                value: _selectedLocation,
                                isExpanded: true,
                                icon: const Icon(Icons.keyboard_arrow_down,
                                    size: 18),
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: AppTheme.textSecondaryColor,
                                ),
                                onChanged: (String? newValue) {
                                  if (newValue != null) {
                                    setState(() {
                                      _selectedLocation = newValue;
                                    });
                                    _applyFiltersAndSort();
                                  }
                                },
                                items: [
                                  'All',
                                  ...CarService.getAvailableLocations()
                                ].map((String value) {
                                  return DropdownMenuItem<String>(
                                    value: value,
                                    child: Text(value),
                                  );
                                }).toList(),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCarsList() {
    if (_filteredCars.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.directions_car_outlined,
              size: 64,
              color: AppTheme.textSecondaryColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: AppTheme.spacingMd),
            const Text(
              'No cars found',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSm),
            const Text(
              'Try adjusting your filters',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }

    return _isGridView ? _buildGridView() : _buildListView();
  }

  Widget _buildGridView() {
    return GridView.builder(
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: AppTheme.spacingMd,
        mainAxisSpacing: AppTheme.spacingMd,
      ),
      itemCount: _filteredCars.length,
      itemBuilder: (context, index) {
        final car = _filteredCars[index];
        return CarCard(
          car: car,
          onPressed: () => _handleCarPress(car),
          isLarge: false,
        );
      },
    );
  }

  Widget _buildListView() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      itemCount: _filteredCars.length,
      physics: const AlwaysScrollableScrollPhysics(), // Ensure scrolling works
      itemBuilder: (context, index) {
        final car = _filteredCars[index];
        return Container(
          margin: EdgeInsets.only(
            bottom: index < _filteredCars.length - 1
                ? AppTheme.spacingLg // Consistent spacing between cards
                : AppTheme.spacingSm, // Visual breathing room for last card
          ),
          child: CarCard(
            car: car,
            onPressed: () => _handleCarPress(car),
            isLarge: true, // Use large cards matching main screen
          ),
        );
      },
    );
  }
}
