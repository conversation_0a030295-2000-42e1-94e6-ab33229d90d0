import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// AR Navigation Screen - Implementation of IMG_AR4.PNG design
/// Pixel-perfect match to React Native AR Navigation interface
class ARNavigationScreen extends ConsumerStatefulWidget {
  const ARNavigationScreen({super.key});

  @override
  ConsumerState<ARNavigationScreen> createState() => _ARNavigationScreenState();
}

class _ARNavigationScreenState extends ConsumerState<ARNavigationScreen> {
  @override
  void initState() {
    super.initState();
    // Set status bar for AR navigation screen
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header Section
            _buildHeader(context),

            // Main Content Area
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // AR Map Section
                    _buildARMapSection(context),

                    // Navigation Controls
                    _buildNavigationControls(context),

                    // Nearby Landmarks
                    _buildNearbyLandmarks(context),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the header section
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(
              Icons.arrow_back_ios,
              color: Colors.black,
              size: 20,
            ),
          ),
          // Title
          const Expanded(
            child: Text(
              'AR Navigation',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black,
                fontFamily: AppTheme.fontFamily,
              ),
            ),
          ),
          // Settings button
          IconButton(
            onPressed: () => _showSettings(context),
            icon: const Icon(
              Icons.settings,
              color: Colors.black,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  /// Build AR map section
  Widget _buildARMapSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Stack(
        children: [
          // Map placeholder
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.map,
                  size: 60,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(height: 12),
                const Text(
                  'AR Map View',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Tap to enable AR navigation',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          // AR toggle button
          Positioned(
            top: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Text(
                'AR ON',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build navigation controls
  Widget _buildNavigationControls(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Navigation Controls',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),

          // Control buttons grid
          Row(
            children: [
              Expanded(
                child: _buildControlButton(
                  icon: Icons.my_location,
                  label: 'My Location',
                  onTap: () => _centerOnLocation(context),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildControlButton(
                  icon: Icons.directions,
                  label: 'Directions',
                  onTap: () => _showDirections(context),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildControlButton(
                  icon: Icons.search,
                  label: 'Search',
                  onTap: () => _showSearch(context),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildControlButton(
                  icon: Icons.layers,
                  label: 'Layers',
                  onTap: () => _showLayers(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build a control button
  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: AppTheme.primaryColor,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build nearby landmarks section
  Widget _buildNearbyLandmarks(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Nearby Landmarks',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),

          // Landmarks list
          _buildLandmarkCard(
            name: 'Historic Cathedral',
            distance: '0.2 km',
            description: 'Beautiful 18th century architecture',
            hasAR: true,
          ),
          const SizedBox(height: 12),

          _buildLandmarkCard(
            name: 'Art Museum',
            distance: '0.5 km',
            description: 'Contemporary and classical art collections',
            hasAR: true,
          ),
          const SizedBox(height: 12),

          _buildLandmarkCard(
            name: 'City Park',
            distance: '0.8 km',
            description: 'Green space with walking trails',
            hasAR: false,
          ),
        ],
      ),
    );
  }

  /// Build a landmark card
  Widget _buildLandmarkCard({
    required String name,
    required String distance,
    required String description,
    required bool hasAR,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Landmark icon
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: hasAR
                  ? AppTheme.primaryColor.withValues(alpha: 0.1)
                  : Colors.grey[200],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              hasAR ? Icons.view_in_ar : Icons.location_on,
              color: hasAR ? AppTheme.primaryColor : Colors.grey[600],
              size: 24,
            ),
          ),
          const SizedBox(width: 16),

          // Landmark info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    Text(
                      distance,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Show settings
  void _showSettings(BuildContext context) {
    // Implementation for settings
  }

  /// Center on user location
  void _centerOnLocation(BuildContext context) {
    // Implementation for location centering
  }

  /// Show directions
  void _showDirections(BuildContext context) {
    // Implementation for directions
  }

  /// Show search
  void _showSearch(BuildContext context) {
    // Implementation for search
  }

  /// Show layers
  void _showLayers(BuildContext context) {
    // Implementation for map layers
  }
}
