# Flutter UI Implementation Guide

## Phase 1: Design System Foundation

### 1.1 Color System Migration

**React Native Source:**
```typescript
export const colors = {
  primary: "#FF385C",
  secondary: "#00A699", 
  background: "#FFFFFF",
  text: "#222222"
}
```

**Flutter Implementation:**
```dart
class AppColors {
  // Primary brand colors (Airbnb style)
  static const Color primary = Color(0xFFFF385C);
  static const Color primaryLight = Color(0xFFFF5A75);
  static const Color primaryDark = Color(0xFFE8294A);
  
  // Secondary colors
  static const Color secondary = Color(0xFF00A699);
  static const Color accent = Color(0xFFFC642D);
  
  // Neutral colors
  static const Color background = Color(0xFFFFFFFF);
  static const Color backgroundSecondary = Color(0xFFF7F7F7);
  
  // Text colors
  static const Color text = Color(0xFF222222);
  static const Color textSecondary = Color(0xFF717171);
  static const Color textLight = Color(0xFFB0B0B0);
  
  // UI colors
  static const Color border = Color(0xFFDDDDDD);
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
}
```

### 1.2 Typography System Migration

**React Native Source:**
```typescript
export const typography = {
  sizes: { xs: 12, sm: 14, md: 16, lg: 18, xl: 20, xxl: 28 },
  weights: { light: "300", regular: "400", medium: "500", bold: "700" }
}
```

**Flutter Implementation:**
```dart
class AppTypography {
  static const String fontFamily = 'System';
  
  // Font sizes
  static const double xs = 12.0;
  static const double sm = 14.0;
  static const double md = 16.0;
  static const double lg = 18.0;
  static const double xl = 20.0;
  static const double xxl = 28.0;
  
  // Text styles
  static const TextStyle heading1 = TextStyle(
    fontSize: xxl,
    fontWeight: FontWeight.w700,
    color: AppColors.text,
  );
  
  static const TextStyle body = TextStyle(
    fontSize: md,
    fontWeight: FontWeight.w400,
    color: AppColors.text,
  );
  
  static const TextStyle caption = TextStyle(
    fontSize: sm,
    fontWeight: FontWeight.w400,
    color: AppColors.textSecondary,
  );
}
```

### 1.3 Spacing System Migration

**React Native Source:**
```typescript
export const spacing = {
  xs: 4, sm: 8, md: 16, lg: 24, xl: 32, xxl: 48
}
```

**Flutter Implementation:**
```dart
class AppSpacing {
  static const double xs = 4.0;
  static const double sm = 8.0;
  static const double md = 16.0;
  static const double lg = 24.0;
  static const double xl = 32.0;
  static const double xxl = 48.0;
  
  // EdgeInsets shortcuts
  static const EdgeInsets paddingXS = EdgeInsets.all(xs);
  static const EdgeInsets paddingSM = EdgeInsets.all(sm);
  static const EdgeInsets paddingMD = EdgeInsets.all(md);
  static const EdgeInsets paddingLG = EdgeInsets.all(lg);
}
```

---

## Phase 2: Core Component Implementation

### 2.1 Button Component Migration

**React Native Source Analysis:**
- 3 variants: primary, secondary, outline
- 3 sizes: small, medium, large
- Loading states, icon support, full width option

**Flutter Implementation:**
```dart
enum ButtonVariant { primary, secondary, outline }
enum ButtonSize { small, medium, large }

class AppButton extends StatelessWidget {
  final String title;
  final VoidCallback onPressed;
  final ButtonVariant variant;
  final ButtonSize size;
  final bool disabled;
  final bool loading;
  final Widget? icon;
  final bool fullWidth;

  const AppButton({
    Key? key,
    required this.title,
    required this.onPressed,
    this.variant = ButtonVariant.primary,
    this.size = ButtonSize.medium,
    this.disabled = false,
    this.loading = false,
    this.icon,
    this.fullWidth = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: fullWidth ? double.infinity : null,
      child: ElevatedButton(
        onPressed: disabled || loading ? null : onPressed,
        style: _getButtonStyle(),
        child: loading
            ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    variant == ButtonVariant.outline 
                        ? AppColors.primary 
                        : AppColors.white,
                  ),
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (icon != null) ...[
                    icon!,
                    SizedBox(width: AppSpacing.xs),
                  ],
                  Text(title),
                ],
              ),
      ),
    );
  }

  ButtonStyle _getButtonStyle() {
    final colors = _getButtonColors();
    final padding = _getButtonPadding();
    
    return ElevatedButton.styleFrom(
      backgroundColor: colors.background,
      foregroundColor: colors.foreground,
      side: variant == ButtonVariant.outline 
          ? BorderSide(color: AppColors.primary) 
          : null,
      padding: padding,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: variant == ButtonVariant.outline ? 0 : 2,
    );
  }
}
```

### 2.2 Card Component Migration

**React Native DestinationCard Analysis:**
- Image background with gradient overlay
- Favorite button with heart icon
- Rating with star icon
- Price display
- Tag system

**Flutter Implementation:**
```dart
class DestinationCard extends StatefulWidget {
  final Destination destination;
  final VoidCallback onTap;

  const DestinationCard({
    Key? key,
    required this.destination,
    required this.onTap,
  }) : super(key: key);

  @override
  State<DestinationCard> createState() => _DestinationCardState();
}

class _DestinationCardState extends State<DestinationCard> {
  bool isFavorite = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        height: 280,
        margin: EdgeInsets.all(AppSpacing.sm),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            children: [
              // Background Image
              Positioned.fill(
                child: Image.network(
                  widget.destination.imageUrl,
                  fit: BoxFit.cover,
                ),
              ),
              
              // Gradient Overlay
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.3),
                        Colors.black.withOpacity(0.7),
                      ],
                      stops: [0.0, 0.6, 1.0],
                    ),
                  ),
                ),
              ),
              
              // Content
              Positioned(
                left: AppSpacing.md,
                right: AppSpacing.md,
                bottom: AppSpacing.md,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Tags and Favorite
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        if (widget.destination.tags.isNotEmpty)
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: AppSpacing.sm,
                              vertical: AppSpacing.xs,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.primary.withOpacity(0.9),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              widget.destination.tags.first,
                              style: AppTypography.caption.copyWith(
                                color: AppColors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              isFavorite = !isFavorite;
                            });
                          },
                          child: Icon(
                            isFavorite ? Icons.favorite : Icons.favorite_border,
                            color: isFavorite ? AppColors.primary : AppColors.white,
                            size: 24,
                          ),
                        ),
                      ],
                    ),
                    
                    SizedBox(height: AppSpacing.sm),
                    
                    // Title and Location
                    Text(
                      widget.destination.name,
                      style: AppTypography.heading1.copyWith(
                        color: AppColors.white,
                        fontSize: AppTypography.lg,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    Text(
                      widget.destination.location,
                      style: AppTypography.body.copyWith(
                        color: AppColors.white.withOpacity(0.8),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    SizedBox(height: AppSpacing.sm),
                    
                    // Price and Rating
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '\$${widget.destination.price}/night',
                          style: AppTypography.body.copyWith(
                            color: AppColors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Row(
                          children: [
                            Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: 16,
                            ),
                            SizedBox(width: 4),
                            Text(
                              widget.destination.rating.toStringAsFixed(1),
                              style: AppTypography.caption.copyWith(
                                color: AppColors.white,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

---

## Phase 3: Navigation System Migration

### 3.1 Tab Navigation Implementation

**React Native Source Analysis:**
- 5 tabs: Explore, AR Guide, Bookings, Kaia, Profile
- Lucide icons for each tab
- Custom styling with shadows and platform-specific heights

**Flutter Implementation:**
```dart
class MainNavigation extends StatefulWidget {
  @override
  State<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends State<MainNavigation> {
  int _currentIndex = 0;
  
  final List<Widget> _screens = [
    ExploreScreen(),
    ARGuideScreen(),
    BookingsScreen(),
    AIPlannerScreen(),
    ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: AppSpacing.md,
              vertical: AppSpacing.sm,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildNavItem(0, 'Explore', Icons.explore),
                _buildNavItem(1, 'AR Guide', Icons.map),
                _buildNavItem(2, 'Bookings', Icons.calendar_today),
                _buildNavItem(3, 'Kaia', Icons.smart_toy),
                _buildNavItem(4, 'Profile', Icons.person),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(int index, String label, IconData icon) {
    final isSelected = _currentIndex == index;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _currentIndex = index;
        });
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: isSelected ? AppColors.primary : AppColors.textLight,
            size: 24,
          ),
          SizedBox(height: 4),
          Text(
            label,
            style: AppTypography.caption.copyWith(
              color: isSelected ? AppColors.primary : AppColors.textLight,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
```

---

## Implementation Checklist

### Week 1-2: Foundation
- [ ] Set up color system constants
- [ ] Implement typography system
- [ ] Create spacing constants
- [ ] Set up theme structure
- [ ] Install/create Lucide icon system
- [ ] Create base component architecture

### Week 3-5: Core Components  
- [ ] Implement Button component with all variants
- [ ] Create Input and Form components
- [ ] Build Card components (Destination, Event, etc.)
- [ ] Implement Navigation system
- [ ] Create Search and Filter components

### Week 6-9: Complex Components
- [ ] Build Calendar modal and date pickers
- [ ] Implement Image components with transitions
- [ ] Create advanced animation components
- [ ] Build Modal and overlay systems
- [ ] Implement List and Grid components

### Week 10-12: Screen Layouts
- [ ] Migrate Home/Explore screen layout
- [ ] Implement Booking screens
- [ ] Create Profile and settings screens
- [ ] Build Authentication screens
- [ ] Implement specialized screens (AR, etc.)

### Week 13-14: Polish & Optimization
- [ ] Performance optimization
- [ ] Animation fine-tuning
- [ ] Accessibility improvements
- [ ] Cross-platform testing
- [ ] Bug fixes and refinements

**Total Estimated Effort**: 14 weeks with 2 Flutter developers
