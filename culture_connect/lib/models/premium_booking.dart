import 'package:flutter/material.dart';
import 'package:culture_connect/design_system/premium_design_tokens.dart';

/// Premium booking types with enhanced visual categorization
enum PremiumBookingType {
  flight,
  hotel,
  experience,
  tour,
  transport,
  event,
  package,
}

/// Premium booking status with visual states
enum PremiumBookingStatus {
  upcoming,
  confirmed,
  pending,
  completed,
  cancelled,
}

/// Enhanced booking model for premium visual experience
class PremiumBooking {
  final String id;
  final PremiumBookingType type;
  final String title;
  final String subtitle;
  final PremiumBookingStatus status;
  final DateTime date;
  final DateTime? endDate;
  final double price;
  final String currency;
  final String imageUrl;
  final List<String> imageGallery;
  final String confirmationCode;
  final String location;
  final String? country;
  final double rating;
  final int reviewCount;
  final String duration;
  final int participants;
  final Map<String, dynamic>? details;
  final List<String> highlights;
  final List<String> amenities;
  final bool isFavorite;
  final String? cancellationPolicy;
  final DateTime createdAt;
  final DateTime updatedAt;

  const PremiumBooking({
    required this.id,
    required this.type,
    required this.title,
    required this.subtitle,
    required this.status,
    required this.date,
    this.endDate,
    required this.price,
    this.currency = 'USD',
    required this.imageUrl,
    this.imageGallery = const [],
    required this.confirmationCode,
    required this.location,
    this.country,
    required this.rating,
    this.reviewCount = 0,
    required this.duration,
    required this.participants,
    this.details,
    this.highlights = const [],
    this.amenities = const [],
    this.isFavorite = false,
    this.cancellationPolicy,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Get premium status color
  Color get statusColor => PremiumDesignTokens.getStatusColor(status.name);

  /// Get booking type color
  Color get typeColor => PremiumDesignTokens.getBookingTypeColor(type.name);

  /// Get status icon
  IconData get statusIcon {
    switch (status) {
      case PremiumBookingStatus.upcoming:
      case PremiumBookingStatus.confirmed:
        return Icons.check_circle_rounded;
      case PremiumBookingStatus.pending:
        return Icons.schedule_rounded;
      case PremiumBookingStatus.completed:
        return Icons.done_all_rounded;
      case PremiumBookingStatus.cancelled:
        return Icons.cancel_rounded;
    }
  }

  /// Get type icon
  IconData get typeIcon {
    switch (type) {
      case PremiumBookingType.flight:
        return Icons.flight_rounded;
      case PremiumBookingType.hotel:
        return Icons.hotel_rounded;
      case PremiumBookingType.experience:
        return Icons.local_activity_rounded;
      case PremiumBookingType.tour:
        return Icons.tour_rounded;
      case PremiumBookingType.transport:
        return Icons.directions_car_rounded;
      case PremiumBookingType.event:
        return Icons.event_rounded;
      case PremiumBookingType.package:
        return Icons.card_travel_rounded;
    }
  }

  /// Get formatted status text
  String get statusText {
    switch (status) {
      case PremiumBookingStatus.upcoming:
        return 'Upcoming';
      case PremiumBookingStatus.confirmed:
        return 'Confirmed';
      case PremiumBookingStatus.pending:
        return 'Pending';
      case PremiumBookingStatus.completed:
        return 'Completed';
      case PremiumBookingStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Get formatted type text
  String get typeText {
    switch (type) {
      case PremiumBookingType.flight:
        return 'Flight';
      case PremiumBookingType.hotel:
        return 'Hotel';
      case PremiumBookingType.experience:
        return 'Experience';
      case PremiumBookingType.tour:
        return 'Tour';
      case PremiumBookingType.transport:
        return 'Transport';
      case PremiumBookingType.event:
        return 'Event';
      case PremiumBookingType.package:
        return 'Travel Package';
    }
  }

  /// Get formatted price
  String get formattedPrice {
    final formatter = currency == 'USD' ? '\$' : '$currency ';
    return '$formatter${price.toStringAsFixed(0)}';
  }

  /// Get participant text
  String get participantText {
    return participants == 1 ? '1 person' : '$participants people';
  }

  /// Get formatted date range
  String get dateRange {
    if (endDate != null) {
      final start = '${date.day}/${date.month}';
      final end = '${endDate!.day}/${endDate!.month}';
      return '$start - $end';
    }
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Check if booking can be cancelled
  bool get canBeCancelled {
    return status == PremiumBookingStatus.confirmed ||
        status == PremiumBookingStatus.pending;
  }

  /// Check if booking can be rescheduled
  bool get canBeRescheduled {
    return status == PremiumBookingStatus.confirmed;
  }

  /// Check if booking is active
  bool get isActive {
    return status == PremiumBookingStatus.upcoming ||
        status == PremiumBookingStatus.confirmed ||
        status == PremiumBookingStatus.pending;
  }

  /// Get days until booking
  int get daysUntil {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;
    return difference > 0 ? difference : 0;
  }

  /// Get urgency level for visual styling
  String get urgencyLevel {
    final days = daysUntil;
    if (days <= 1) return 'urgent';
    if (days <= 7) return 'soon';
    if (days <= 30) return 'upcoming';
    return 'future';
  }

  /// Copy with method for immutable updates
  PremiumBooking copyWith({
    String? id,
    PremiumBookingType? type,
    String? title,
    String? subtitle,
    PremiumBookingStatus? status,
    DateTime? date,
    DateTime? endDate,
    double? price,
    String? currency,
    String? imageUrl,
    List<String>? imageGallery,
    String? confirmationCode,
    String? location,
    String? country,
    double? rating,
    int? reviewCount,
    String? duration,
    int? participants,
    Map<String, dynamic>? details,
    List<String>? highlights,
    List<String>? amenities,
    bool? isFavorite,
    String? cancellationPolicy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PremiumBooking(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      status: status ?? this.status,
      date: date ?? this.date,
      endDate: endDate ?? this.endDate,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      imageUrl: imageUrl ?? this.imageUrl,
      imageGallery: imageGallery ?? this.imageGallery,
      confirmationCode: confirmationCode ?? this.confirmationCode,
      location: location ?? this.location,
      country: country ?? this.country,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      duration: duration ?? this.duration,
      participants: participants ?? this.participants,
      details: details ?? this.details,
      highlights: highlights ?? this.highlights,
      amenities: amenities ?? this.amenities,
      isFavorite: isFavorite ?? this.isFavorite,
      cancellationPolicy: cancellationPolicy ?? this.cancellationPolicy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// Premium booking statistics for dashboard
class PremiumBookingStats {
  final int totalBookings;
  final int upcomingBookings;
  final int completedTrips;
  final int countriesVisited;
  final double averageRating;
  final double totalSpent;
  final double totalSaved;
  final String favoriteDestination;
  final PremiumBookingType mostBookedType;

  const PremiumBookingStats({
    required this.totalBookings,
    required this.upcomingBookings,
    required this.completedTrips,
    required this.countriesVisited,
    required this.averageRating,
    required this.totalSpent,
    required this.totalSaved,
    required this.favoriteDestination,
    required this.mostBookedType,
  });

  /// Get formatted total spent
  String get formattedTotalSpent => '\$${totalSpent.toStringAsFixed(0)}';

  /// Get formatted total saved
  String get formattedTotalSaved => '\$${totalSaved.toStringAsFixed(0)}';

  /// Get formatted average rating
  String get formattedAverageRating => averageRating.toStringAsFixed(1);
}
