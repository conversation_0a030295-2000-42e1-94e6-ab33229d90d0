import React from 'react';
import { StyleSheet, View, Text, Image, Pressable, Animated } from 'react-native';
import { Star } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';
import { Car } from '@/mocks/cars';

interface CarCardProps {
  car: Car;
  onPress: (car: Car) => void;
  isLarge?: boolean;
}

export function CarCard({ car, onPress, isLarge = false }: CarCardProps) {
  const scaleValue = new Animated.Value(1);

  const handlePressIn = () => {
    Animated.spring(scaleValue, {
      toValue: 0.95,
      useNativeDriver: true,
      tension: 400,
      friction: 8,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
      tension: 400,
      friction: 8,
    }).start();
  };

  return (
    <Pressable
      onPress={() => onPress(car)}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      android_ripple={{ color: colors.overlayLight }}
    >
      <Animated.View style={[isLarge ? styles.largeContainer : styles.container, { transform: [{ scale: scaleValue }] }]}>
      <View style={isLarge ? styles.largeImageContainer : styles.imageContainer}>
        <Image source={{ uri: car.imageUrl }} style={styles.image} />
        <View style={styles.ratingContainer}>
          <Star size={14} color={colors.warning} fill={colors.warning} />
          <Text style={styles.rating}>{car.rating}</Text>
        </View>
        {isLarge && (
          <View style={styles.categoryBadge}>
            <Text style={styles.categoryText}>{car.category}</Text>
          </View>
        )}
      </View>
      
      <View style={isLarge ? styles.largeContent : styles.content}>
        <View style={styles.carInfo}>
          <Text style={[styles.carName, isLarge && styles.largeCarName]} numberOfLines={1}>
            {car.name}
          </Text>
          {isLarge && (
            <Text style={styles.carSpecs} numberOfLines={1}>
              {car.transmission} • {car.fuelType} • {car.seats} seats
            </Text>
          )}
        </View>
        <View style={styles.priceContainer}>
          <Text style={[styles.price, isLarge && styles.largePrice]}>
            ${car.pricePerDay}
          </Text>
          <Text style={styles.priceLabel}>/ day</Text>
        </View>
      </View>
      </Animated.View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 24,
    overflow: 'hidden',
    marginBottom: spacing.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 10,
  },
  largeContainer: {
    backgroundColor: colors.white,
    borderRadius: 28,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  imageContainer: {
    position: 'relative',
    height: 180,
    backgroundColor: '#f8f9fa',
  },
  largeImageContainer: {
    position: 'relative',
    height: 240,
    backgroundColor: '#f1f3f4',
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  ratingContainer: {
    position: 'absolute',
    top: spacing.lg,
    left: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    paddingHorizontal: spacing.sm,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  rating: {
    fontSize: 13,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
  categoryBadge: {
    position: 'absolute',
    top: spacing.lg,
    right: spacing.lg,
    backgroundColor: colors.accent,
    paddingHorizontal: spacing.md,
    paddingVertical: 8,
    borderRadius: 20,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: typography.weights.semibold as any,
    color: colors.white,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  content: {
    padding: spacing.lg,
    paddingTop: spacing.md,
    paddingBottom: spacing.lg,
  },
  largeContent: {
    padding: spacing.xl,
    paddingTop: spacing.lg,
    paddingBottom: spacing.xl,
  },
  carInfo: {
    flex: 1,
    marginBottom: spacing.md,
  },
  carName: {
    fontSize: 18,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: 6,
    lineHeight: 24,
  },
  largeCarName: {
    fontSize: 22,
    lineHeight: 28,
    marginBottom: spacing.sm,
  },
  carSpecs: {
    fontSize: 14,
    fontWeight: typography.weights.medium as any,
    color: '#8e8e93',
    lineHeight: 20,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: 4,
  },
  price: {
    fontSize: 20,
    fontWeight: typography.weights.bold as any,
    color: colors.accent,
  },
  largePrice: {
    fontSize: 24,
  },
  priceLabel: {
    fontSize: 14,
    fontWeight: typography.weights.medium as any,
    color: '#8e8e93',
  },
});