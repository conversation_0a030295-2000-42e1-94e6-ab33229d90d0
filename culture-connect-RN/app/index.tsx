import React from "react";
import { StyleSheet, View, Text, Image, Pressable } from "react-native";
import { useRouter } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { Button } from "@/components/Button";
import { useUserStore } from "@/store/userStore";

export default function WelcomeScreen() {
  const router = useRouter();
  const { setUser } = useUserStore();

  const handleLogin = () => {
    try {
      router.push("/auth/login");
    } catch (error) {
      console.error("Navigation error:", error);
    }
  };

  const handleRegister = () => {
    try {
      router.push("/auth/register");
    } catch (error) {
      console.error("Navigation error:", error);
    }
  };

  const handleSkip = () => {
    try {
      // Create a guest user
      setUser({
        id: "guest",
        name: "Guest",
        email: "<EMAIL>",
        preferences: {
          categories: [],
          budget: "medium",
          travelStyle: [],
        },
      });
      router.push("/onboarding");
    } catch (error) {
      console.error("Error creating guest user:", error);
    }
  };

  return (
    <View style={styles.container}>
      <Image
        source={{ uri: "https://images.unsplash.com/photo-1469854523086-cc02fe5d8800?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80" }}
        style={styles.backgroundImage}
      />
      <LinearGradient
        colors={["transparent", "rgba(0,0,0,0.8)"]}
        style={styles.gradient}
      />
      <View style={styles.content}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoText}>Culture Connect</Text>
        </View>
        <Text style={styles.tagline}>
          Discover the world through AI-powered travel experiences
        </Text>
        <View style={styles.buttonContainer}>
          <Button
            title="Sign In"
            onPress={handleLogin}
            variant="primary"
            fullWidth
          />
          <View style={styles.buttonSpacer} />
          <Button
            title="Create Account"
            onPress={handleRegister}
            variant="outline"
            fullWidth
          />
          <Pressable onPress={handleSkip} style={styles.skipButton}>
            <Text style={styles.skipText}>Skip for now</Text>
          </Pressable>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    ...StyleSheet.absoluteFillObject,
    width: "100%",
    height: "100%",
  },
  gradient: {
    ...StyleSheet.absoluteFillObject,
  },
  content: {
    flex: 1,
    justifyContent: "flex-end",
    padding: spacing.xl,
  },
  logoContainer: {
    marginBottom: spacing.xl,
  },
  logoText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xxxl,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
  },
  tagline: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    color: colors.white,
    marginBottom: spacing.xxl,
    lineHeight: 26,
  },
  buttonContainer: {
    width: "100%",
    marginBottom: spacing.xl,
  },
  buttonSpacer: {
    height: spacing.md,
  },
  skipButton: {
    alignItems: "center",
    marginTop: spacing.lg,
  },
  skipText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.white,
    textDecorationLine: "underline",
  },
});