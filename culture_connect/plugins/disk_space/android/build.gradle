plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace = 'de.appgewaltig.disk_space'
    compileSdk = 34

    defaultConfig {
        minSdk = 24
        testInstrumentationRunner = 'androidx.test.runner.AndroidJUnitRunner'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    lint {
        disable += ['InvalidPackage']
        textReport = true
    }
}

repositories {
    google()
    mavenCentral()
}

dependencies {
    // Rely on the Kotlin stdlib provided by the Kotlin Gradle plugin
}
