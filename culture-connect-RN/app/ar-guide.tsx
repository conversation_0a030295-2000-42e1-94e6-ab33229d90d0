import React, { useState } from "react";
import { StyleSheet, View, Text, Pressable, Dimensions } from "react-native";
import { useRouter } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { Image } from "expo-image";
import { ArrowLeft, Info, Navigation, Volume2, VolumeX, Camera } from "lucide-react-native";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { LinearGradient } from "expo-linear-gradient";

const { width, height } = Dimensions.get("window");

export default function ARGuideScreen() {
  const router = useRouter();
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [showInfo, setShowInfo] = useState(false);

  const toggleAudio = () => {
    setAudioEnabled(!audioEnabled);
  };

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  return (
    <View style={styles.container}>
      {/* AR Camera View (simulated with an image) */}
      <Image
        source={{ uri: "https://images.unsplash.com/photo-1564511287139-7a3bd6b4a1f0?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80" }}
        style={styles.cameraView}
      />

      {/* AR Overlay Elements */}
      <View style={styles.arOverlay}>
        {/* POI Marker */}
        <View style={styles.poiMarker}>
          <View style={styles.poiDot} />
          <View style={styles.poiRing} />
        </View>

        {/* POI Info Card (conditionally shown) */}
        {showInfo && (
          <View style={styles.poiInfoCard}>
            <Text style={styles.poiTitle}>Ancient Temple</Text>
            <Text style={styles.poiDescription}>
              Built in the 12th century, this temple features intricate carvings
              and was a center for religious ceremonies.
            </Text>
            <View style={styles.poiStats}>
              <View style={styles.poiStat}>
                <Text style={styles.poiStatLabel}>Age</Text>
                <Text style={styles.poiStatValue}>900 years</Text>
              </View>
              <View style={styles.poiStat}>
                <Text style={styles.poiStatLabel}>Style</Text>
                <Text style={styles.poiStatValue}>Classical</Text>
              </View>
              <View style={styles.poiStat}>
                <Text style={styles.poiStatLabel}>Status</Text>
                <Text style={styles.poiStatValue}>Preserved</Text>
              </View>
            </View>
          </View>
        )}

        {/* Navigation Arrow */}
        <View style={styles.navigationArrow}>
          <Navigation size={24} color={colors.white} />
          <Text style={styles.navigationText}>50m</Text>
        </View>
      </View>

      {/* Top Controls */}
      <SafeAreaView style={styles.topControls} edges={["top"]}>
        <Pressable style={styles.controlButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.white} />
        </Pressable>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>AR Guide</Text>
        </View>
        <Pressable style={styles.controlButton} onPress={toggleAudio}>
          {audioEnabled ? (
            <Volume2 size={24} color={colors.white} />
          ) : (
            <VolumeX size={24} color={colors.white} />
          )}
        </Pressable>
      </SafeAreaView>

      {/* Bottom Controls */}
      <SafeAreaView style={styles.bottomControls} edges={["bottom"]}>
        <LinearGradient
          colors={["transparent", "rgba(0,0,0,0.7)"]}
          style={styles.bottomGradient}
        />
        <View style={styles.controlsContainer}>
          <Pressable style={styles.infoButton} onPress={toggleInfo}>
            <Info size={24} color={colors.white} />
          </Pressable>
          <Pressable style={styles.captureButton}>
            <Camera size={28} color={colors.white} />
          </Pressable>
          <View style={{ width: 50 }} />
        </View>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  cameraView: {
    ...StyleSheet.absoluteFillObject,
  },
  arOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
  },
  poiMarker: {
    position: "absolute",
    top: height * 0.4,
    left: width * 0.6,
  },
  poiDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.secondary,
    position: "absolute",
    top: 4,
    left: 4,
  },
  poiRing: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.secondary,
    opacity: 0.8,
  },
  poiInfoCard: {
    position: "absolute",
    top: height * 0.45,
    left: width * 0.2,
    backgroundColor: "rgba(0,0,0,0.7)",
    borderRadius: 16,
    padding: spacing.md,
    width: width * 0.6,
    borderLeftWidth: 3,
    borderLeftColor: colors.secondary,
  },
  poiTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  poiDescription: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.white,
    opacity: 0.9,
    marginBottom: spacing.sm,
  },
  poiStats: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  poiStat: {
    alignItems: "center",
  },
  poiStatLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.white,
    opacity: 0.7,
  },
  poiStatValue: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium as any,
    color: colors.white,
  },
  navigationArrow: {
    position: "absolute",
    bottom: height * 0.25,
    right: width * 0.1,
    backgroundColor: "rgba(0,0,0,0.5)",
    borderRadius: 20,
    padding: spacing.sm,
    alignItems: "center",
  },
  navigationText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.white,
    marginTop: spacing.xs,
  },
  topControls: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: spacing.md,
  },
  controlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  titleContainer: {
    backgroundColor: "rgba(0,0,0,0.5)",
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderRadius: 16,
  },
  title: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium as any,
    color: colors.white,
  },
  bottomControls: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
  },
  bottomGradient: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 150,
  },
  controlsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: spacing.xl,
    paddingBottom: spacing.md,
  },
  infoButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 4,
    borderColor: "rgba(255,255,255,0.3)",
  },
});