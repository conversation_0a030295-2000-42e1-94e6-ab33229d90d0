import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Pressable,
  Image,
  Dimensions,
  FlatList,
  Animated,
  Platform,
} from 'react-native';
import { useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack } from 'expo-router';
import {
  Crown,
  Plane,
  Car,
  Shield,
  Star,
  MapPin,
  Clock,
  Users,
  Sparkles,
  ArrowRight,
  ChevronRight,
  Zap,
} from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';
import { luxuryServices, LuxuryService } from '@/mocks/luxuryServices';

const { width: screenWidth } = Dimensions.get('window');



const categories = [
  { id: 'all', title: 'All Services', icon: Crown },
  { id: 'jets', title: 'Private Jets', icon: Plane },
  { id: 'helicopters', title: 'Helicopters', icon: Plane },
  { id: 'cars', title: 'Luxury Cars', icon: Car },
  { id: 'yachts', title: 'Yachts', icon: Plane },
];

export default function LuxuryScreen() {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [filteredServices, setFilteredServices] = useState(luxuryServices);
  
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  useEffect(() => {
    if (selectedCategory === 'all') {
      setFilteredServices(luxuryServices);
    } else {
      setFilteredServices(
        luxuryServices.filter(service => service.category === selectedCategory)
      );
    }
  }, [selectedCategory]);

  const handleServicePress = (service: LuxuryService) => {
    router.push(`/luxury/service/${service.id}`);
  };

  const renderServiceCard = ({ item }: { item: LuxuryService }) => (
    <Pressable
      style={styles.serviceCard}
      onPress={() => handleServicePress(item)}
    >
      <View style={styles.serviceImageContainer}>
        <Image source={{ uri: item.imageUrl }} style={styles.serviceImage} />
        <View style={styles.serviceOverlay}>
          <View style={styles.serviceBadge}>
            <Crown size={16} color="#D946EF" />
            <Text style={styles.serviceBadgeText}>LUXURY</Text>
          </View>
          {item.securityIncluded && (
            <View style={styles.securityBadge}>
              <Shield size={14} color={colors.white} />
            </View>
          )}
        </View>
      </View>
      
      <View style={styles.serviceContent}>
        <View style={styles.serviceHeader}>
          <Text style={styles.serviceTitle} numberOfLines={1}>{item.title}</Text>
          <View style={styles.ratingContainer}>
            <Star size={14} color="#F59E0B" fill="#F59E0B" />
            <Text style={styles.ratingText}>{item.rating}</Text>
          </View>
        </View>
        
        <Text style={styles.serviceSubtitle} numberOfLines={1}>{item.subtitle}</Text>
        <Text style={styles.serviceDescription} numberOfLines={2}>{item.description}</Text>
        
        <View style={styles.serviceFeatures}>
          {item.features.slice(0, 3).map((feature, index) => (
            <View key={index} style={styles.featureTag}>
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </View>
        
        <View style={styles.serviceFooter}>
          <View style={styles.priceContainer}>
            <Text style={styles.priceText}>{item.price}</Text>
            <Text style={styles.availabilityText}>{item.availability}</Text>
          </View>
          <View style={styles.bookButton}>
            <Text style={styles.bookButtonText}>Book Now</Text>
            <ArrowRight size={16} color={colors.white} />
          </View>
        </View>
      </View>
    </Pressable>
  );

  const renderCategoryButton = (category: any) => (
    <Pressable
      key={category.id}
      style={[
        styles.categoryButton,
        selectedCategory === category.id && styles.categoryButtonActive,
      ]}
      onPress={() => setSelectedCategory(category.id)}
    >
      <category.icon
        size={20}
        color={selectedCategory === category.id ? colors.white : '#D946EF'}
      />
      <Text
        style={[
          styles.categoryButtonText,
          selectedCategory === category.id && styles.categoryButtonTextActive,
        ]}
      >
        {category.title}
      </Text>
    </Pressable>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Stack.Screen
        options={{
          title: 'Luxury Services',
          headerStyle: {
            backgroundColor: '#1a1a1a',
          },
          headerTintColor: '#D946EF',
          headerTitleStyle: {
            fontWeight: 'bold',
            fontSize: 18,
          },
          headerRight: () => (
            <View style={styles.headerRight}>
              <Crown size={24} color="#D946EF" />
            </View>
          ),
        }}
      />
      
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        {/* Hero Section */}
        <View style={styles.heroSection}>
          <View style={styles.heroContent}>
            <View style={styles.heroHeader}>
              <Crown size={32} color="#D946EF" />
              <Sparkles size={24} color="#F59E0B" style={styles.sparkle} />
            </View>
            <Text style={styles.heroTitle}>Luxury Experiences</Text>
            <Text style={styles.heroSubtitle}>
              Exclusive access to the world's finest transportation and experiences
            </Text>
            <View style={styles.heroStats}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>24/7</Text>
                <Text style={styles.statLabel}>Concierge</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>100%</Text>
                <Text style={styles.statLabel}>Vetted</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>5★</Text>
                <Text style={styles.statLabel}>Service</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Categories */}
        <View style={styles.categoriesSection}>
          <Text style={styles.sectionTitle}>Service Categories</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
          >
            {categories.map(renderCategoryButton)}
          </ScrollView>
        </View>

        {/* Services List */}
        <View style={styles.servicesSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              {selectedCategory === 'all' ? 'All Services' : categories.find(c => c.id === selectedCategory)?.title}
            </Text>
            <Text style={styles.serviceCount}>{filteredServices.length} available</Text>
          </View>
          
          <FlatList
            data={filteredServices}
            renderItem={renderServiceCard}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.servicesList}
          />
        </View>
      </Animated.View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0a0a0a',
  },
  headerRight: {
    marginRight: spacing.md,
  },
  content: {
    flex: 1,
  },
  heroSection: {
    backgroundColor: 'linear-gradient(135deg, #1a1a1a 0%, #2d1b69 100%)',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
    marginBottom: spacing.lg,
  },
  heroContent: {
    alignItems: 'center',
  },
  heroHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    position: 'relative',
  },
  sparkle: {
    position: 'absolute',
    top: -8,
    right: -8,
  },
  heroTitle: {
    fontSize: typography.sizes.xxxl,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  heroSubtitle: {
    fontSize: typography.sizes.md,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginBottom: spacing.xl,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.md,
  },
  heroStats: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(217, 70, 239, 0.1)',
    borderRadius: 16,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderWidth: 1,
    borderColor: 'rgba(217, 70, 239, 0.3)',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: '#D946EF',
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.sizes.xs,
    color: 'rgba(255, 255, 255, 0.7)',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  statDivider: {
    width: 1,
    height: 32,
    backgroundColor: 'rgba(217, 70, 239, 0.3)',
    marginHorizontal: spacing.lg,
  },
  categoriesSection: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.md,
  },
  categoriesList: {
    paddingHorizontal: spacing.lg,
    gap: spacing.sm,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 20,
    backgroundColor: 'rgba(217, 70, 239, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(217, 70, 239, 0.3)',
    gap: spacing.sm,
  },
  categoryButtonActive: {
    backgroundColor: '#D946EF',
    borderColor: '#D946EF',
    shadowColor: '#D946EF',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  categoryButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: '#D946EF',
  },
  categoryButtonTextActive: {
    color: colors.white,
  },
  servicesSection: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  serviceCount: {
    fontSize: typography.sizes.sm,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  servicesList: {
    paddingBottom: spacing.xl,
  },
  serviceCard: {
    backgroundColor: '#1a1a1a',
    borderRadius: 20,
    marginBottom: spacing.lg,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(217, 70, 239, 0.2)',
    shadowColor: '#D946EF',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
  },
  serviceImageContainer: {
    position: 'relative',
    height: 200,
  },
  serviceImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  serviceOverlay: {
    position: 'absolute',
    top: spacing.md,
    left: spacing.md,
    right: spacing.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  serviceBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    gap: spacing.xs,
  },
  serviceBadgeText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold as any,
    color: '#D946EF',
    letterSpacing: 0.5,
  },
  securityBadge: {
    backgroundColor: 'rgba(34, 197, 94, 0.9)',
    padding: spacing.sm,
    borderRadius: 12,
  },
  serviceContent: {
    padding: spacing.lg,
  },
  serviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  serviceTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    flex: 1,
    marginRight: spacing.sm,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  ratingText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: '#F59E0B',
  },
  serviceSubtitle: {
    fontSize: typography.sizes.md,
    color: '#D946EF',
    fontWeight: typography.weights.semibold as any,
    marginBottom: spacing.sm,
  },
  serviceDescription: {
    fontSize: typography.sizes.sm,
    color: 'rgba(255, 255, 255, 0.7)',
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
    marginBottom: spacing.md,
  },
  serviceFeatures: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
    marginBottom: spacing.lg,
  },
  featureTag: {
    backgroundColor: 'rgba(217, 70, 239, 0.1)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(217, 70, 239, 0.3)',
  },
  featureText: {
    fontSize: typography.sizes.xs,
    color: '#D946EF',
    fontWeight: typography.weights.medium as any,
  },
  serviceFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceContainer: {
    flex: 1,
  },
  priceText: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  availabilityText: {
    fontSize: typography.sizes.sm,
    color: '#22C55E',
    fontWeight: typography.weights.medium as any,
  },
  bookButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#D946EF',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
    gap: spacing.sm,
    shadowColor: '#D946EF',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  bookButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: colors.white,
  },
});