// Flutter imports
import 'package:flutter/foundation.dart';

// Project imports
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/travel/visa/visa_models.dart';
import 'package:culture_connect/models/travel/visa/escrow_payment_models.dart';

/// Types of collaboration activities
enum CollaborationActivityType {
  /// General message exchange
  message,
  
  /// Document shared by client
  documentShared,
  
  /// Document reviewed by consultant
  documentReviewed,
  
  /// Milestone completed
  milestoneCompleted,
  
  /// Payment status update
  paymentUpdate,
  
  /// Service status update
  serviceUpdate,
  
  /// Deadline reminder
  deadlineReminder,
  
  /// Consultation scheduled
  consultationScheduled,
  
  /// Application submitted
  applicationSubmitted,
  
  /// Embassy response received
  embassyResponse,
  
  /// Urgent attention required
  urgentAttention,
  
  /// Service completed
  serviceCompleted,
}

/// Priority levels for collaboration activities
enum CollaborationPriority {
  low,
  normal,
  high,
  urgent,
}

/// Status of collaboration session
enum CollaborationStatus {
  active,
  paused,
  completed,
  cancelled,
  disputed,
}

/// Represents a collaboration session between client and consultant
@immutable
class VisaCollaborationSession {
  /// Unique identifier for the collaboration session
  final String id;
  
  /// Associated visa service booking
  final String bookingId;
  
  /// Client user ID
  final String clientId;
  
  /// Consultant/provider ID
  final String consultantId;
  
  /// Chat ID for messaging
  final String chatId;
  
  /// Current status of the collaboration
  final CollaborationStatus status;
  
  /// Service being provided
  final String serviceName;
  
  /// Destination country for visa
  final String destinationCountry;
  
  /// Expected completion date
  final DateTime expectedCompletionDate;
  
  /// Actual completion date
  final DateTime? actualCompletionDate;
  
  /// Last activity timestamp
  final DateTime lastActivityAt;
  
  /// Progress percentage (0-100)
  final double progressPercentage;
  
  /// Current milestone
  final String? currentMilestone;
  
  /// List of completed milestones
  final List<String> completedMilestones;
  
  /// Shared documents
  final List<CollaborationDocument> sharedDocuments;
  
  /// Important deadlines
  final List<CollaborationDeadline> deadlines;
  
  /// Consultation sessions
  final List<ConsultationSession> consultations;
  
  /// Associated escrow payment ID
  final String? escrowPaymentId;
  
  /// Notes from consultant
  final String? consultantNotes;
  
  /// Notes from client
  final String? clientNotes;
  
  /// When the collaboration was created
  final DateTime createdAt;
  
  /// When the collaboration was last updated
  final DateTime updatedAt;

  const VisaCollaborationSession({
    required this.id,
    required this.bookingId,
    required this.clientId,
    required this.consultantId,
    required this.chatId,
    required this.status,
    required this.serviceName,
    required this.destinationCountry,
    required this.expectedCompletionDate,
    this.actualCompletionDate,
    required this.lastActivityAt,
    required this.progressPercentage,
    this.currentMilestone,
    required this.completedMilestones,
    required this.sharedDocuments,
    required this.deadlines,
    required this.consultations,
    this.escrowPaymentId,
    this.consultantNotes,
    this.clientNotes,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Creates a copy of this collaboration session with the given fields replaced
  VisaCollaborationSession copyWith({
    String? id,
    String? bookingId,
    String? clientId,
    String? consultantId,
    String? chatId,
    CollaborationStatus? status,
    String? serviceName,
    String? destinationCountry,
    DateTime? expectedCompletionDate,
    DateTime? actualCompletionDate,
    DateTime? lastActivityAt,
    double? progressPercentage,
    String? currentMilestone,
    List<String>? completedMilestones,
    List<CollaborationDocument>? sharedDocuments,
    List<CollaborationDeadline>? deadlines,
    List<ConsultationSession>? consultations,
    String? escrowPaymentId,
    String? consultantNotes,
    String? clientNotes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VisaCollaborationSession(
      id: id ?? this.id,
      bookingId: bookingId ?? this.bookingId,
      clientId: clientId ?? this.clientId,
      consultantId: consultantId ?? this.consultantId,
      chatId: chatId ?? this.chatId,
      status: status ?? this.status,
      serviceName: serviceName ?? this.serviceName,
      destinationCountry: destinationCountry ?? this.destinationCountry,
      expectedCompletionDate: expectedCompletionDate ?? this.expectedCompletionDate,
      actualCompletionDate: actualCompletionDate ?? this.actualCompletionDate,
      lastActivityAt: lastActivityAt ?? this.lastActivityAt,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      currentMilestone: currentMilestone ?? this.currentMilestone,
      completedMilestones: completedMilestones ?? this.completedMilestones,
      sharedDocuments: sharedDocuments ?? this.sharedDocuments,
      deadlines: deadlines ?? this.deadlines,
      consultations: consultations ?? this.consultations,
      escrowPaymentId: escrowPaymentId ?? this.escrowPaymentId,
      consultantNotes: consultantNotes ?? this.consultantNotes,
      clientNotes: clientNotes ?? this.clientNotes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Check if the collaboration is overdue
  bool get isOverdue {
    return DateTime.now().isAfter(expectedCompletionDate) && 
           status != CollaborationStatus.completed;
  }

  /// Get the next upcoming deadline
  CollaborationDeadline? get nextDeadline {
    final upcomingDeadlines = deadlines
        .where((d) => !d.isCompleted && d.dueDate.isAfter(DateTime.now()))
        .toList();
    
    if (upcomingDeadlines.isEmpty) return null;
    
    upcomingDeadlines.sort((a, b) => a.dueDate.compareTo(b.dueDate));
    return upcomingDeadlines.first;
  }

  /// Get overdue deadlines
  List<CollaborationDeadline> get overdueDeadlines {
    return deadlines
        .where((d) => !d.isCompleted && d.dueDate.isBefore(DateTime.now()))
        .toList();
  }
}

/// Represents a document shared in the collaboration
@immutable
class CollaborationDocument {
  /// Unique identifier for the document
  final String id;
  
  /// Name of the document
  final String name;
  
  /// File path or URL
  final String filePath;
  
  /// File size in bytes
  final int fileSize;
  
  /// MIME type of the file
  final String mimeType;
  
  /// Who uploaded the document
  final String uploadedBy;
  
  /// When the document was uploaded
  final DateTime uploadedAt;
  
  /// Whether the document has been reviewed
  final bool isReviewed;
  
  /// Review status/comments
  final String? reviewComments;
  
  /// Who reviewed the document
  final String? reviewedBy;
  
  /// When the document was reviewed
  final DateTime? reviewedAt;
  
  /// Whether the document is required
  final bool isRequired;
  
  /// Document category/type
  final String category;
  
  /// Whether the document is approved
  final bool isApproved;

  const CollaborationDocument({
    required this.id,
    required this.name,
    required this.filePath,
    required this.fileSize,
    required this.mimeType,
    required this.uploadedBy,
    required this.uploadedAt,
    required this.isReviewed,
    this.reviewComments,
    this.reviewedBy,
    this.reviewedAt,
    required this.isRequired,
    required this.category,
    required this.isApproved,
  });
}

/// Represents a deadline in the collaboration
@immutable
class CollaborationDeadline {
  /// Unique identifier for the deadline
  final String id;
  
  /// Title of the deadline
  final String title;
  
  /// Description of what needs to be done
  final String description;
  
  /// Due date and time
  final DateTime dueDate;
  
  /// Priority level
  final CollaborationPriority priority;
  
  /// Whether the deadline is completed
  final bool isCompleted;
  
  /// When the deadline was completed
  final DateTime? completedAt;
  
  /// Who is responsible for this deadline
  final String assignedTo;
  
  /// Category of the deadline
  final String category;
  
  /// Whether this deadline is critical
  final bool isCritical;

  const CollaborationDeadline({
    required this.id,
    required this.title,
    required this.description,
    required this.dueDate,
    required this.priority,
    required this.isCompleted,
    this.completedAt,
    required this.assignedTo,
    required this.category,
    required this.isCritical,
  });

  /// Check if the deadline is overdue
  bool get isOverdue {
    return !isCompleted && DateTime.now().isAfter(dueDate);
  }

  /// Get time remaining until deadline
  Duration get timeRemaining {
    return dueDate.difference(DateTime.now());
  }
}

/// Represents a consultation session
@immutable
class ConsultationSession {
  /// Unique identifier for the consultation
  final String id;
  
  /// Title of the consultation
  final String title;
  
  /// Scheduled date and time
  final DateTime scheduledAt;
  
  /// Duration in minutes
  final int durationMinutes;
  
  /// Type of consultation (video, phone, in-person)
  final String type;
  
  /// Meeting link or location
  final String? meetingLink;
  
  /// Status of the consultation
  final String status;
  
  /// Notes from the consultation
  final String? notes;
  
  /// Recording URL if available
  final String? recordingUrl;
  
  /// When the consultation was created
  final DateTime createdAt;

  const ConsultationSession({
    required this.id,
    required this.title,
    required this.scheduledAt,
    required this.durationMinutes,
    required this.type,
    this.meetingLink,
    required this.status,
    this.notes,
    this.recordingUrl,
    required this.createdAt,
  });
}

/// Represents an activity in the collaboration timeline
@immutable
class CollaborationActivity {
  /// Unique identifier for the activity
  final String id;
  
  /// Type of activity
  final CollaborationActivityType type;
  
  /// Title of the activity
  final String title;
  
  /// Description of the activity
  final String description;
  
  /// User who performed the activity
  final String performedBy;
  
  /// When the activity occurred
  final DateTime timestamp;
  
  /// Priority level
  final CollaborationPriority priority;
  
  /// Additional metadata
  final Map<String, dynamic>? metadata;
  
  /// Whether this activity requires attention
  final bool requiresAttention;

  const CollaborationActivity({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.performedBy,
    required this.timestamp,
    required this.priority,
    this.metadata,
    required this.requiresAttention,
  });
}
