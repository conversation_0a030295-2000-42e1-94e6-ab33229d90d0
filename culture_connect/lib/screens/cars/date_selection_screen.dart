import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/app_button.dart';

/// Date selection screen with calendar interface and duration validation
/// Matches the design from calCulture.jpeg reference
class DateSelectionScreen extends StatefulWidget {
  final DateTime initialPickupDate;
  final DateTime initialReturnDate;
  final int requiredDuration; // Duration in days from car detail screen
  final Function(DateTime pickupDate, DateTime returnDate) onDatesSelected;

  const DateSelectionScreen({
    super.key,
    required this.initialPickupDate,
    required this.initialReturnDate,
    required this.requiredDuration,
    required this.onDatesSelected,
  });

  @override
  State<DateSelectionScreen> createState() => _DateSelectionScreenState();
}

class _DateSelectionScreenState extends State<DateSelectionScreen> {
  late DateTime _currentMonth;
  DateTime? _selectedPickupDate;
  DateTime? _selectedReturnDate;
  bool _isSelectingPickup = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _currentMonth =
        DateTime(widget.initialPickupDate.year, widget.initialPickupDate.month);
    _selectedPickupDate = widget.initialPickupDate;
    _selectedReturnDate = widget.initialReturnDate;
  }

  void _selectDate(DateTime date) {
    setState(() {
      _errorMessage = null;

      if (_isSelectingPickup) {
        _selectedPickupDate = date;
        // Auto-calculate return date based on required duration
        _selectedReturnDate = date.add(Duration(days: widget.requiredDuration));
        _isSelectingPickup = false;
      } else {
        // Validate return date matches required duration
        if (_selectedPickupDate != null) {
          final daysDifference = date.difference(_selectedPickupDate!).inDays;

          if (daysDifference == widget.requiredDuration) {
            _selectedReturnDate = date;
          } else {
            _errorMessage =
                'Return date must be exactly ${widget.requiredDuration} ${widget.requiredDuration == 1 ? 'day' : 'days'} from pickup date to match your selected rental duration';
            return;
          }
        }
      }
    });
  }

  void _previousMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month - 1);
    });
  }

  void _nextMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + 1);
    });
  }

  void _handleConfirmDates() {
    if (_selectedPickupDate != null && _selectedReturnDate != null) {
      widget.onDatesSelected(_selectedPickupDate!, _selectedReturnDate!);
      Navigator.pop(context);
    }
  }

  void _clearDates() {
    setState(() {
      _selectedPickupDate = null;
      _selectedReturnDate = null;
      _isSelectingPickup = true;
      _errorMessage = null;
    });
  }

  Widget _buildCalendarGrid() {
    final daysInMonth =
        DateTime(_currentMonth.year, _currentMonth.month + 1, 0).day;
    final firstDayOfMonth =
        DateTime(_currentMonth.year, _currentMonth.month, 1);
    final firstWeekday = firstDayOfMonth.weekday % 7; // 0 = Sunday

    return Column(
      children: [
        // Weekday headers
        Container(
          padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
          child: Row(
            children: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
                .map((day) => Expanded(
                      child: Center(
                        child: Text(
                          day,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ),
                    ))
                .toList(),
          ),
        ),
        const SizedBox(height: AppTheme.spacingMd),

        // Calendar days grid
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 7,
                childAspectRatio: 1,
                crossAxisSpacing: 4,
                mainAxisSpacing: 4,
              ),
              itemCount: 42, // 6 weeks
              itemBuilder: (context, index) {
                final dayNumber = index - firstWeekday + 1;

                if (dayNumber <= 0 || dayNumber > daysInMonth) {
                  return const SizedBox(); // Empty cell
                }

                final date = DateTime(
                    _currentMonth.year, _currentMonth.month, dayNumber);
                final isToday = _isSameDay(date, DateTime.now());
                final isPickupDate = _selectedPickupDate != null &&
                    _isSameDay(date, _selectedPickupDate!);
                final isReturnDate = _selectedReturnDate != null &&
                    _isSameDay(date, _selectedReturnDate!);
                final isInRange = _isDateInRange(date);
                final isPastDate = date
                    .isBefore(DateTime.now().subtract(const Duration(days: 1)));

                return GestureDetector(
                  onTap: isPastDate ? null : () => _selectDate(date),
                  child: Container(
                    decoration: BoxDecoration(
                      color: _getDateBackgroundColor(
                          isPickupDate, isReturnDate, isInRange, isToday),
                      borderRadius: BorderRadius.circular(8),
                      border: isToday && !isPickupDate && !isReturnDate
                          ? Border.all(color: AppTheme.accentColor, width: 2)
                          : null,
                    ),
                    child: Center(
                      child: Text(
                        dayNumber.toString(),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: (isPickupDate || isReturnDate)
                              ? FontWeight.bold
                              : FontWeight.w500,
                          color: _getDateTextColor(isPickupDate, isReturnDate,
                              isInRange, isPastDate),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  bool _isDateInRange(DateTime date) {
    if (_selectedPickupDate == null || _selectedReturnDate == null) {
      return false;
    }
    return date.isAfter(_selectedPickupDate!) &&
        date.isBefore(_selectedReturnDate!);
  }

  Color _getDateBackgroundColor(
      bool isPickupDate, bool isReturnDate, bool isInRange, bool isToday) {
    if (isPickupDate || isReturnDate) {
      return AppTheme.accentColor; // Orange for selected dates
    }
    if (isInRange) {
      return AppTheme.accentColor
          .withValues(alpha: 0.1); // Light orange for range
    }
    if (isToday) {
      return Colors.transparent; // Transparent with border
    }
    return Colors.transparent;
  }

  Color _getDateTextColor(
      bool isPickupDate, bool isReturnDate, bool isInRange, bool isPastDate) {
    if (isPickupDate || isReturnDate) {
      return AppTheme.white;
    }
    if (isPastDate) {
      return AppTheme.textSecondaryColor.withValues(alpha: 0.5);
    }
    if (isInRange) {
      return AppTheme.accentColor;
    }
    return AppTheme.textPrimaryColor;
  }

  String _formatMonthYear(DateTime date) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  String _formatHeaderDate(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${months[date.month - 1]} ${date.day}';
  }

  String _formatSummaryDate(DateTime date) {
    const weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${weekdays[date.weekday - 1]}, ${months[date.month - 1]} ${date.day}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Column(
        children: [
          // Orange header section matching cars booking flow
          Container(
            padding: EdgeInsets.fromLTRB(
              AppTheme.spacingLg,
              MediaQuery.of(context).padding.top + AppTheme.spacingMd,
              AppTheme.spacingLg,
              AppTheme.spacingLg,
            ),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFFFC642D), // Orange (AppTheme.accentColor)
                  Color(0xFFE55A26), // Darker orange
                ],
              ),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(24),
                bottomRight: Radius.circular(24),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with close button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Select Dates',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.white,
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppTheme.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.close,
                          color: AppTheme.white,
                          size: 20,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppTheme.spacingSm),

                // Date range display
                Text(
                  _selectedPickupDate != null && _selectedReturnDate != null
                      ? '${_formatHeaderDate(_selectedPickupDate!)} - ${_formatHeaderDate(_selectedReturnDate!)}'
                      : 'Select your travel dates',
                  style: const TextStyle(
                    fontSize: 16,
                    color: AppTheme.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingMd),

                // Trip type and duration info
                Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: AppTheme.white,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingSm),
                    const Text(
                      'Round Trip',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.white,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppTheme.spacingXs),
                Text(
                  '${widget.requiredDuration} ${widget.requiredDuration == 1 ? 'day' : 'days'} selected',
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.white,
                  ),
                ),
                Text(
                  'Maximum: ${widget.requiredDuration} ${widget.requiredDuration == 1 ? 'day' : 'days'}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(
                        0xFF2D3748), // Dark gray for better contrast against orange
                    fontWeight: FontWeight
                        .w600, // Slightly bolder for better visibility
                  ),
                ),
              ],
            ),
          ),

          // Calendar section
          Expanded(
            child: Container(
              color: AppTheme.white,
              child: Column(
                children: [
                  // Month navigation
                  Container(
                    padding: const EdgeInsets.all(AppTheme.spacingLg),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        GestureDetector(
                          onTap: _previousMonth,
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: AppTheme.backgroundSecondary,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(
                              Icons.chevron_left,
                              color: AppTheme.textPrimaryColor,
                            ),
                          ),
                        ),
                        Text(
                          _formatMonthYear(_currentMonth),
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                        GestureDetector(
                          onTap: _nextMonth,
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: AppTheme.backgroundSecondary,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(
                              Icons.chevron_right,
                              color: AppTheme.textPrimaryColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Calendar grid
                  Expanded(
                    child: _buildCalendarGrid(),
                  ),

                  // Error message
                  if (_errorMessage != null)
                    Container(
                      margin: const EdgeInsets.all(AppTheme.spacingLg),
                      padding: const EdgeInsets.all(AppTheme.spacingMd),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFFEBEE),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: const Color(0xFFFF6B6B)),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.error_outline,
                            color: Color(0xFFFF6B6B),
                            size: 20,
                          ),
                          const SizedBox(width: AppTheme.spacingSm),
                          Expanded(
                            child: Text(
                              _errorMessage!,
                              style: const TextStyle(
                                fontSize: 14,
                                color: Color(0xFFFF6B6B),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Selected dates summary
                  if (_selectedPickupDate != null &&
                      _selectedReturnDate != null)
                    Container(
                      margin: const EdgeInsets.all(AppTheme.spacingLg),
                      padding: const EdgeInsets.all(AppTheme.spacingMd),
                      decoration: BoxDecoration(
                        color: AppTheme.backgroundSecondary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              children: [
                                const Text(
                                  'DEPARTURE',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                    color: AppTheme.textSecondaryColor,
                                    letterSpacing: 0.5,
                                  ),
                                ),
                                const SizedBox(height: AppTheme.spacingXs),
                                Text(
                                  _formatSummaryDate(_selectedPickupDate!),
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppTheme.textPrimaryColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            width: 1,
                            height: 40,
                            color: AppTheme.borderLight,
                          ),
                          Expanded(
                            child: Column(
                              children: [
                                const Text(
                                  'RETURN',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                    color: AppTheme.textSecondaryColor,
                                    letterSpacing: 0.5,
                                  ),
                                ),
                                const SizedBox(height: AppTheme.spacingXs),
                                Text(
                                  _formatSummaryDate(_selectedReturnDate!),
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppTheme.textPrimaryColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Bottom action buttons
          Container(
            padding: EdgeInsets.fromLTRB(
              AppTheme.spacingLg,
              AppTheme.spacingMd,
              AppTheme.spacingLg,
              AppTheme.spacingMd + MediaQuery.of(context).padding.bottom,
            ),
            decoration: const BoxDecoration(
              color: AppTheme.white,
              border: Border(
                top: BorderSide(color: AppTheme.borderLight, width: 1),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 1,
                  child: AppButton(
                    text: 'Clear',
                    onPressed: _clearDates,
                    variant: ButtonVariant.outlined,
                    height: 48,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingMd),
                Expanded(
                  flex: 2,
                  child: AppButton(
                    text: 'Confirm Dates',
                    onPressed: _handleConfirmDates,
                    variant: ButtonVariant.filled,
                    color: AppTheme
                        .accentColor, // Orange to match header and cars flow
                    height: 48,
                    isDisabled: !(_selectedPickupDate != null &&
                        _selectedReturnDate != null &&
                        _errorMessage == null),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
