import React, { useState, useRef } from "react";
import { StyleSheet, View, Text, FlatList, Dimensions, Pressable } from "react-native";
import { useRouter } from "expo-router";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { Button } from "@/components/Button";
import { OnboardingSlide } from "@/components/OnboardingSlide";
import { useUserStore } from "@/store/userStore";

const { width } = Dimensions.get("window");

const slides = [
  {
    id: "1",
    title: "Discover Amazing Places",
    description: "Explore the world's most beautiful destinations with personalized recommendations powered by AI.",
    imageUrl: "https://images.unsplash.com/photo-1488085061387-422e29b40080?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80",
  },
  {
    id: "2",
    title: "Immersive AR Experiences",
    description: "Bring history and culture to life with our augmented reality guides at landmarks around the world.",
    imageUrl: "https://images.unsplash.com/photo-1633356122102-3fe601e05bd2?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80",
  },
  {
    id: "3",
    title: "Seamless Bookings",
    description: "Book tours, accommodations, and experiences with just a few taps and manage all your trips in one place.",
    imageUrl: "https://images.unsplash.com/photo-1445019980597-93fa8acb246c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80",
  },
];

export default function OnboardingScreen() {
  const router = useRouter();
  const flatListRef = useRef<FlatList>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const { completeOnboarding } = useUserStore();

  const handleNext = () => {
    if (currentIndex < slides.length - 1) {
      flatListRef.current?.scrollToIndex({
        index: currentIndex + 1,
        animated: true,
      });
    } else {
      completeOnboarding();
      router.replace("/(tabs)");
    }
  };

  const handleSkip = () => {
    completeOnboarding();
    router.replace("/(tabs)");
  };

  const handleScroll = (event: any) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffsetX / width);
    setCurrentIndex(index);
  };

  return (
    <View style={styles.container}>
      <FlatList
        ref={flatListRef}
        data={slides}
        renderItem={({ item }) => (
          <OnboardingSlide
            title={item.title}
            description={item.description}
            imageUrl={item.imageUrl}
          />
        )}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => item.id}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      />
      
      <View style={styles.footer}>
        <View style={styles.pagination}>
          {slides.map((_, index) => (
            <View
              key={index}
              style={[
                styles.paginationDot,
                index === currentIndex && styles.paginationDotActive,
              ]}
            />
          ))}
        </View>
        
        <View style={styles.buttonContainer}>
          {currentIndex < slides.length - 1 ? (
            <>
              <Pressable onPress={handleSkip}>
                <Text style={styles.skipText}>Skip</Text>
              </Pressable>
              <Button
                title="Next"
                onPress={handleNext}
                variant="primary"
              />
            </>
          ) : (
            <Button
              title="Get Started"
              onPress={handleNext}
              variant="primary"
              fullWidth
            />
          )}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  footer: {
    padding: spacing.xl,
  },
  pagination: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: spacing.xl,
  },
  paginationDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.border,
    marginHorizontal: 5,
  },
  paginationDotActive: {
    backgroundColor: colors.primary,
    width: 20,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  skipText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textLight,
  },
});