import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Flight class option model
class FlightClassOption {
  final String type;
  final String name;
  final String description;
  final IconData icon;

  const FlightClassOption({
    required this.type,
    required this.name,
    required this.description,
    required this.icon,
  });
}

/// Available flight classes
const List<FlightClassOption> flightClasses = [
  FlightClassOption(
    type: 'economy',
    name: 'Economy Class',
    description: 'Standard seating and service',
    icon: Icons.airline_seat_recline_normal,
  ),
  FlightClassOption(
    type: 'premium',
    name: 'Premium Economy',
    description: 'Extra legroom and enhanced service',
    icon: Icons.airline_seat_recline_extra,
  ),
  FlightClassOption(
    type: 'business',
    name: 'Business Class',
    description: 'Lie-flat seats and premium service',
    icon: Icons.airline_seat_flat,
  ),
  FlightClassOption(
    type: 'first',
    name: 'First Class',
    description: 'Ultimate luxury and privacy',
    icon: Icons.airline_seat_individual_suite,
  ),
];

/// Pixel-perfect React Native ClassSelectionModal replica
class RNClassSelectionModal extends StatefulWidget {
  final bool visible;
  final VoidCallback onClose;
  final Function(String, String) onSelectClass;
  final String initialClass;

  const RNClassSelectionModal({
    super.key,
    required this.visible,
    required this.onClose,
    required this.onSelectClass,
    this.initialClass = 'economy',
  });

  @override
  State<RNClassSelectionModal> createState() => _RNClassSelectionModalState();
}

class _RNClassSelectionModalState extends State<RNClassSelectionModal> {
  String _selectedClass = 'economy';

  @override
  void initState() {
    super.initState();
    _selectedClass = widget.initialClass;
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.visible) return const SizedBox.shrink();

    return Material(
      color: Colors.black54,
      child: SafeArea(
        child: Container(
          color: AppTheme.backgroundColor,
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: AppTheme.backgroundColor,
                  border: Border(
                    bottom: BorderSide(color: AppTheme.borderColor, width: 1),
                  ),
                ),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: widget.onClose,
                      icon: const Icon(Icons.close),
                      color: AppTheme.textPrimaryColor,
                    ),
                    const Expanded(
                      child: Text(
                        'Select Class',
                        style: TextStyle(
                          fontSize: AppTheme.fontSizeLg,
                          fontWeight: AppTheme.fontWeightBold,
                          color: AppTheme.textPrimaryColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    TextButton(
                      onPressed: _handleDone,
                      child: const Text(
                        'Done',
                        style: TextStyle(
                          fontSize: AppTheme.fontSizeMd,
                          fontWeight: AppTheme.fontWeightSemibold,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Class Options
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: flightClasses.length,
                  itemBuilder: (context, index) {
                    final classOption = flightClasses[index];
                    final isSelected = _selectedClass == classOption.type;
                    
                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            setState(() {
                              _selectedClass = classOption.type;
                            });
                          },
                          borderRadius: BorderRadius.circular(16),
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: isSelected 
                                  ? AppTheme.primaryColor.withOpacity(0.1)
                                  : AppTheme.backgroundColor,
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: isSelected 
                                    ? AppTheme.primaryColor 
                                    : AppTheme.borderColor,
                                width: isSelected ? 2 : 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  width: 48,
                                  height: 48,
                                  decoration: BoxDecoration(
                                    color: isSelected 
                                        ? AppTheme.primaryColor 
                                        : AppTheme.primaryColor.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    classOption.icon,
                                    color: isSelected 
                                        ? AppTheme.backgroundColor 
                                        : AppTheme.primaryColor,
                                    size: 24,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        classOption.name,
                                        style: TextStyle(
                                          fontSize: AppTheme.fontSizeMd,
                                          fontWeight: AppTheme.fontWeightSemibold,
                                          color: isSelected 
                                              ? AppTheme.primaryColor 
                                              : AppTheme.textPrimaryColor,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        classOption.description,
                                        style: TextStyle(
                                          fontSize: AppTheme.fontSizeSm,
                                          color: isSelected 
                                              ? AppTheme.primaryColor.withOpacity(0.8)
                                              : AppTheme.textSecondaryColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                if (isSelected)
                                  Container(
                                    width: 24,
                                    height: 24,
                                    decoration: const BoxDecoration(
                                      color: AppTheme.primaryColor,
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Icon(
                                      Icons.check,
                                      color: AppTheme.backgroundColor,
                                      size: 16,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleDone() {
    final selectedClassOption = flightClasses.firstWhere(
      (option) => option.type == _selectedClass,
    );
    widget.onSelectClass(_selectedClass, selectedClassOption.name);
    widget.onClose();
  }
}
