/// Test configuration and utilities for CultureConnect testing infrastructure
library test_config;

/// This file provides common test utilities, mocks, and configuration
/// that adapt to the existing production codebase without modifying it.

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'mocks/platform_channel_mocks.dart';

/// Test configuration constants
class TestConfig {
  // Performance test targets (matching production optimization goals)
  static const int maxMemoryUsageMB = 100;
  static const int targetFrameTimeMs = 16; // 60fps
  static const double targetCacheHitRate = 0.85; // 85%
  static const double targetCompressionRatio = 0.4; // 60% reduction
  static const int maxStartupTimeMs = 5000; // 5 seconds max startup time

  // Test timeouts
  static const Duration defaultTestTimeout = Duration(seconds: 30);
  static const Duration performanceTestTimeout = Duration(minutes: 2);
  static const Duration integrationTestTimeout = Duration(minutes: 5);

  // Test data sizes
  static const int smallDatasetSize = 10;
  static const int mediumDatasetSize = 100;
  static const int largeDatasetSize = 1000;

  // Cache test configuration
  static const int testCacheBatchSize = 5;
  static const Duration testCacheDelay = Duration(milliseconds: 50);
  static const int maxTestCacheItems = 100;
}

/// Test utilities for creating mock data that matches production patterns
class TestConfigDataFactory {
  /// Creates test experience data matching production structure
  static Map<String, dynamic> createExperienceData({
    String? id,
    String? title,
    String? location,
    String? imageUrl,
    double? rating,
    String? price,
    String? duration,
  }) {
    return {
      'id': id ?? 'test_exp_${DateTime.now().millisecondsSinceEpoch}',
      'title': title ?? 'Test Experience',
      'location': location ?? 'Test Location',
      'imageUrl': imageUrl ?? 'https://example.com/test-image.jpg',
      'rating': rating ?? 4.5,
      'price': price ?? '\$50',
      'duration': duration ?? '2 hours',
      'description': 'Test experience description',
      'category': 'test_category',
      'tags': ['test', 'experience', 'culture'],
      'created': DateTime.now().toIso8601String(),
      'updated': DateTime.now().toIso8601String(),
    };
  }

  /// Creates test booking data matching production structure
  static Map<String, dynamic> createBookingData({
    String? id,
    String? experienceId,
    String? userId,
    String? status,
  }) {
    return {
      'id': id ?? 'test_booking_${DateTime.now().millisecondsSinceEpoch}',
      'experienceId': experienceId ?? 'test_exp_123',
      'userId': userId ?? 'test_user_123',
      'status': status ?? 'confirmed',
      'bookingDate': DateTime.now().toIso8601String(),
      'participants': 2,
      'totalAmount': 100.0,
      'currency': 'USD',
      'created': DateTime.now().toIso8601String(),
    };
  }

  /// Creates test user behavior data for analytics
  static List<Map<String, dynamic>> createUserBehaviorData(int count) {
    return List.generate(
        count,
        (i) => {
              'action': [
                'view_experience',
                'book_experience',
                'rate_experience'
              ][i % 3],
              'timestamp':
                  DateTime.now().subtract(Duration(hours: i)).toIso8601String(),
              'experienceId': 'exp_${i % 20}',
              'userId': 'user_${i % 10}',
              'duration': 30 + (i % 120), // 30-150 seconds
              'metadata': {
                'device': 'test_device',
                'platform': 'test_platform',
                'version': '1.0.0',
              }
            });
  }

  /// Creates large compressible test data
  static Map<String, dynamic> createLargeCompressibleData() {
    return {
      'repeated_content':
          'This is repeated content for compression testing. ' * 100,
      'structured_data': List.generate(
          200,
          (i) => {
                'id': i,
                'name': 'Item $i',
                'description':
                    'Standard description that repeats for compression',
                'category': 'category_${i % 5}',
                'tags': ['tag1', 'tag2', 'tag3'],
                'metadata': {
                  'created': DateTime.now().toIso8601String(),
                  'version': '1.0.0',
                  'format': 'json',
                }
              }),
      'configuration': {
        'app_name': 'CultureConnect',
        'version': '1.0.0',
        'build': '100',
        'environment': 'test',
        'features': ['caching', 'compression', 'offline_mode'],
      }
    };
  }
}

/// Test utilities for performance measurement
class PerformanceTestUtils {
  /// Measures execution time of a function
  static Future<PerformanceResult<T>> measurePerformance<T>(
      Future<T> Function() operation,
      {String? operationName}) async {
    final stopwatch = Stopwatch()..start();

    try {
      final result = await operation();
      stopwatch.stop();

      return PerformanceResult<T>(
        result: result,
        executionTimeMs: stopwatch.elapsedMilliseconds,
        success: true,
        operationName: operationName ?? 'Unknown Operation',
      );
    } catch (error) {
      stopwatch.stop();

      return PerformanceResult<T>(
        result: null,
        executionTimeMs: stopwatch.elapsedMilliseconds,
        success: false,
        error: error,
        operationName: operationName ?? 'Unknown Operation',
      );
    }
  }

  /// Validates that operation meets performance targets
  static void validatePerformanceTarget(
    PerformanceResult result,
    int maxExecutionTimeMs,
  ) {
    expect(result.success, isTrue,
        reason: 'Operation ${result.operationName} failed: ${result.error}');
    expect(result.executionTimeMs, lessThan(maxExecutionTimeMs),
        reason:
            'Operation ${result.operationName} took ${result.executionTimeMs}ms, '
            'expected less than ${maxExecutionTimeMs}ms');
  }

  /// Simulates memory pressure for testing
  static List<Map<String, dynamic>> createMemoryPressureData(int sizeMB) {
    const itemSize = 1024; // Approximate size per item
    final itemCount = (sizeMB * 1024 * 1024) ~/ itemSize;

    return List.generate(
        itemCount,
        (i) => {
              'id': i,
              'data': 'x' * (itemSize ~/ 2), // Approximate half the item size
              'timestamp': DateTime.now().toIso8601String(),
            });
  }
}

/// Result of a performance measurement
class PerformanceResult<T> {
  final T? result;
  final int executionTimeMs;
  final bool success;
  final dynamic error;
  final String operationName;

  const PerformanceResult({
    required this.result,
    required this.executionTimeMs,
    required this.success,
    this.error,
    required this.operationName,
  });

  @override
  String toString() {
    return 'PerformanceResult(operation: $operationName, '
        'time: ${executionTimeMs}ms, success: $success)';
  }
}

/// Test utilities for widget testing
class WidgetTestUtils {
  /// Creates a test app wrapper for widget testing
  static Widget createTestApp({
    required Widget child,
    ThemeData? theme,
    Locale? locale,
  }) {
    return MaterialApp(
      theme: theme ?? ThemeData.light(),
      locale: locale,
      home: Scaffold(body: child),
      debugShowCheckedModeBanner: false,
    );
  }

  /// Pumps widget and waits for animations to settle
  static Future<void> pumpAndSettleWithTimeout(WidgetTester tester,
      {Duration timeout = const Duration(seconds: 10)}) async {
    await tester.pumpAndSettle(timeout);
  }

  /// Finds widget by type with better error messages
  static Finder findWidgetByType<T extends Widget>() {
    final finder = find.byType(T);
    expect(finder, findsAtLeastNWidgets(1),
        reason: 'Could not find widget of type $T');
    return finder;
  }
}

/// Mock data providers for testing services
class MockDataProvider {
  /// Provides mock SharedPreferences data
  static Map<String, Object> getMockPreferencesData() {
    return {
      'cached_experiences': jsonEncode([
        TestConfigDataFactory.createExperienceData(id: 'exp_1'),
        TestConfigDataFactory.createExperienceData(id: 'exp_2'),
      ]),
      'cached_bookings': jsonEncode([
        TestConfigDataFactory.createBookingData(id: 'booking_1'),
      ]),
      'last_updated_cached_experiences': DateTime.now().millisecondsSinceEpoch,
      'last_updated_cached_bookings': DateTime.now().millisecondsSinceEpoch,
    };
  }
}

/// Test assertions for performance validation
class PerformanceAssertions {
  /// Asserts cache performance meets targets
  static void assertCachePerformance(Map<String, dynamic> stats) {
    expect(stats, isA<Map<String, dynamic>>());
    expect(stats.containsKey('hit_rate'), isTrue);
    expect(stats.containsKey('cache_hits'), isTrue);
    expect(stats.containsKey('cache_misses'), isTrue);

    final hitRate = stats['hit_rate'] as double;
    expect(hitRate, greaterThanOrEqualTo(0.0));
    expect(hitRate, lessThanOrEqualTo(1.0));

    // Performance target validation
    if (stats['cache_hits'] + stats['cache_misses'] > 10) {
      expect(hitRate, greaterThan(TestConfig.targetCacheHitRate * 0.8),
          reason: 'Cache hit rate ${(hitRate * 100).toStringAsFixed(1)}% '
              'is below 80% of target ${(TestConfig.targetCacheHitRate * 100).toStringAsFixed(1)}%');
    }
  }

  /// Asserts memory usage is within targets
  static void assertMemoryUsage(int memoryUsageBytes) {
    final memoryUsageMB = memoryUsageBytes / (1024 * 1024);
    expect(memoryUsageMB, lessThan(TestConfig.maxMemoryUsageMB),
        reason: 'Memory usage ${memoryUsageMB.toStringAsFixed(1)}MB '
            'exceeds target ${TestConfig.maxMemoryUsageMB}MB');
  }

  /// Asserts frame time meets 60fps target
  static void assertFrameTime(int frameTimeMs) {
    expect(frameTimeMs, lessThan(TestConfig.targetFrameTimeMs * 2),
        reason: 'Frame time ${frameTimeMs}ms exceeds 60fps target '
            '(${TestConfig.targetFrameTimeMs}ms)');
  }
}

/// Test group utilities for organizing tests
class TestGroups {
  /// Runs a group of performance tests with proper setup/teardown
  static void performanceTestGroup(
    String description,
    void Function() body,
  ) {
    group(description, body);
  }

  /// Runs a group of integration tests with proper setup/teardown
  static void integrationTestGroup(
    String description,
    void Function() body,
  ) {
    group(description, body);
  }

  /// Setup platform channel mocks for tests that require native plugin dependencies
  ///
  /// This function initializes comprehensive mocking for all platform channels
  /// used by CultureConnect services without modifying production code.
  ///
  /// Call this in your test's setUp() method or main() function.
  static void setupPlatformChannelMocks() {
    // Ensure Flutter binding is initialized
    TestWidgetsFlutterBinding.ensureInitialized();

    // Initialize platform channel mocks
    PlatformChannelMocks.initialize();
  }

  /// Clean up platform channel mocks after tests
  ///
  /// Call this in your test's tearDown() method to ensure proper cleanup
  /// and prevent test interference.
  static void cleanupPlatformChannelMocks() {
    PlatformChannelMocks.cleanup();
  }

  /// Helper method to set custom SharedPreferences data for specific tests
  static void setMockSharedPreferencesData(String key, dynamic value) {
    PlatformChannelMocks.setSharedPreferencesData(key, value);
  }

  /// Helper method to set Firebase Auth state for specific tests
  static void setMockFirebaseAuthState({
    String? uid,
    String? email,
    bool isSignedIn = false,
  }) {
    PlatformChannelMocks.setFirebaseAuthState(
      uid: uid,
      email: email,
      isSignedIn: isSignedIn,
    );
  }
}
