{"protocolVersion":"0.1.1","runnerVersion":"1.25.8","pid":7997,"type":"start","time":0}
{"suite":{"id":0,"platform":"vm","path":"/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/cache_service_test.dart"},"type":"suite","time":0}
{"test":{"id":1,"name":"loading /Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/cache_service_test.dart","suiteID":0,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":1}
{"suite":{"id":2,"platform":"vm","path":"/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/sharing_service_test.dart"},"type":"suite","time":8}
{"test":{"id":3,"name":"loading /Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/sharing_service_test.dart","suiteID":2,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":8}
{"suite":{"id":4,"platform":"vm","path":"/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_backend_service_test.dart"},"type":"suite","time":8}
{"test":{"id":5,"name":"loading /Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_backend_service_test.dart","suiteID":4,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":8}
{"suite":{"id":6,"platform":"vm","path":"/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_voice_command_service_test.dart"},"type":"suite","time":9}
{"test":{"id":7,"name":"loading /Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_voice_command_service_test.dart","suiteID":6,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":9}
{"count":16,"time":13,"type":"allSuites"}
{"testID":1,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":7600}
{"group":{"id":8,"suiteID":0,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":9,"line":null,"column":null,"url":null},"type":"group","time":7606}
{"group":{"id":9,"suiteID":0,"parentID":8,"name":"CacheService","metadata":{"skip":false,"skipReason":null},"testCount":9,"line":19,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/cache_service_test.dart"},"type":"group","time":7607}
{"test":{"id":10,"name":"CacheService saveData should save data and timestamp","suiteID":0,"groupIDs":[8,9],"metadata":{"skip":false,"skipReason":null},"line":20,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/cache_service_test.dart"},"type":"testStart","time":7607}
{"testID":10,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7702}
{"test":{"id":11,"name":"CacheService getData should return null if key does not exist","suiteID":0,"groupIDs":[8,9],"metadata":{"skip":false,"skipReason":null},"line":37,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/cache_service_test.dart"},"type":"testStart","time":7703}
{"testID":11,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7711}
{"test":{"id":12,"name":"CacheService getData should return data if key exists and is not expired","suiteID":0,"groupIDs":[8,9],"metadata":{"skip":false,"skipReason":null},"line":48,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/cache_service_test.dart"},"type":"testStart","time":7712}
{"testID":12,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7721}
{"test":{"id":13,"name":"CacheService getData should return null if cache is expired","suiteID":0,"groupIDs":[8,9],"metadata":{"skip":false,"skipReason":null},"line":66,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/cache_service_test.dart"},"type":"testStart","time":7722}
{"testID":13,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7731}
{"test":{"id":14,"name":"CacheService clearCache should remove data and timestamp","suiteID":0,"groupIDs":[8,9],"metadata":{"skip":false,"skipReason":null},"line":89,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/cache_service_test.dart"},"type":"testStart","time":7731}
{"testID":14,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7737}
{"test":{"id":15,"name":"CacheService clearAllCache should remove all cache data","suiteID":0,"groupIDs":[8,9],"metadata":{"skip":false,"skipReason":null},"line":109,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/cache_service_test.dart"},"type":"testStart","time":7737}
{"testID":15,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7745}
{"test":{"id":16,"name":"CacheService isCacheAvailable should return false if key does not exist","suiteID":0,"groupIDs":[8,9],"metadata":{"skip":false,"skipReason":null},"line":132,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/cache_service_test.dart"},"type":"testStart","time":7746}
{"testID":16,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7752}
{"test":{"id":17,"name":"CacheService isCacheAvailable should return true if cache is not expired","suiteID":0,"groupIDs":[8,9],"metadata":{"skip":false,"skipReason":null},"line":144,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/cache_service_test.dart"},"type":"testStart","time":7752}
{"testID":17,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7759}
{"test":{"id":18,"name":"CacheService isCacheAvailable should return false if cache is expired","suiteID":0,"groupIDs":[8,9],"metadata":{"skip":false,"skipReason":null},"line":160,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/cache_service_test.dart"},"type":"testStart","time":7760}
{"testID":18,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7767}
{"suite":{"id":19,"platform":"vm","path":"/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/group_translation_service_test.dart"},"type":"suite","time":7811}
{"test":{"id":20,"name":"loading /Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/group_translation_service_test.dart","suiteID":19,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":7811}
{"testID":3,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":8606}
{"group":{"id":21,"suiteID":2,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":4,"line":null,"column":null,"url":null},"type":"group","time":8607}
{"test":{"id":22,"name":"(setUpAll)","suiteID":2,"groupIDs":[21],"metadata":{"skip":false,"skipReason":null},"line":20,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/sharing_service_test.dart"},"type":"testStart","time":8607}
{"testID":5,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":8843}
{"group":{"id":23,"suiteID":4,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":4,"line":null,"column":null,"url":null},"type":"group","time":8843}
{"group":{"id":24,"suiteID":4,"parentID":23,"name":"ARBackendService Tests","metadata":{"skip":false,"skipReason":null},"testCount":4,"line":17,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_backend_service_test.dart"},"type":"group","time":8843}
{"test":{"id":25,"name":"ARBackendService Tests ARBackendService should initialize without errors","suiteID":4,"groupIDs":[23,24],"metadata":{"skip":false,"skipReason":null},"line":18,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_backend_service_test.dart"},"type":"testStart","time":8843}
{"testID":22,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":8856}
{"group":{"id":26,"suiteID":2,"parentID":21,"name":"SharingService - Public Methods","metadata":{"skip":false,"skipReason":null},"testCount":4,"line":83,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/sharing_service_test.dart"},"type":"group","time":8857}
{"test":{"id":27,"name":"SharingService - Public Methods shareBooking should work without errors","suiteID":2,"groupIDs":[21,26],"metadata":{"skip":false,"skipReason":null},"line":84,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/sharing_service_test.dart"},"type":"testStart","time":8858}
{"testID":27,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":8900}
{"test":{"id":28,"name":"SharingService - Public Methods shareExperience should work without errors","suiteID":2,"groupIDs":[21,26],"metadata":{"skip":false,"skipReason":null},"line":94,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/sharing_service_test.dart"},"type":"testStart","time":8901}
{"testID":28,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":8956}
{"test":{"id":29,"name":"SharingService - Public Methods shareReview should work without errors","suiteID":2,"groupIDs":[21,26],"metadata":{"skip":false,"skipReason":null},"line":103,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/sharing_service_test.dart"},"type":"testStart","time":8957}
{"testID":29,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":8990}
{"test":{"id":30,"name":"SharingService - Public Methods SharingService should be singleton","suiteID":2,"groupIDs":[21,26],"metadata":{"skip":false,"skipReason":null},"line":114,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/sharing_service_test.dart"},"type":"testStart","time":8991}
{"testID":30,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9014}
{"test":{"id":31,"name":"(tearDownAll)","suiteID":2,"groupIDs":[21],"metadata":{"skip":false,"skipReason":null},"line":78,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/sharing_service_test.dart"},"type":"testStart","time":9015}
{"testID":31,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":9024}
{"suite":{"id":32,"platform":"vm","path":"/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/startup_optimization_service_test.dart"},"type":"suite","time":9050}
{"test":{"id":33,"name":"loading /Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/startup_optimization_service_test.dart","suiteID":32,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":9050}
{"testID":25,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9096}
{"test":{"id":34,"name":"ARBackendService Tests ARBackendService should be singleton","suiteID":4,"groupIDs":[23,24],"metadata":{"skip":false,"skipReason":null},"line":25,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_backend_service_test.dart"},"type":"testStart","time":9097}
{"testID":25,"error":"MissingPluginException(No implementation found for method getTemporaryDirectory on channel plugins.flutter.io/path_provider)","stackTrace":"package:flutter/src/services/platform_channel.dart 332:7  MethodChannel._invokeMethod\n===== asynchronous gap ===========================\ndart:async                                                _CustomZone.registerBinaryCallback\ntest/unit/services/ar_backend_service_test.dart 21:9      main.<fn>.<fn>.<fn>\npackage:matcher                                           expect\npackage:flutter_test/src/widget_tester.dart 480:18        expect\ntest/unit/services/ar_backend_service_test.dart 20:7      main.<fn>.<fn>\n","isFailure":false,"type":"error","time":9134}
{"testID":25,"error":"This test failed after it had already completed.\nMake sure to use a matching library which informs the test runner\nof pending async work.","stackTrace":"package:flutter/src/services/platform_channel.dart 332:7  MethodChannel._invokeMethod\n===== asynchronous gap ===========================\ndart:async                                                _CustomZone.registerBinaryCallback\ntest/unit/services/ar_backend_service_test.dart 21:9      main.<fn>.<fn>.<fn>\npackage:matcher                                           expect\npackage:flutter_test/src/widget_tester.dart 480:18        expect\ntest/unit/services/ar_backend_service_test.dart 20:7      main.<fn>.<fn>\n","isFailure":false,"type":"error","time":9136}
{"testID":34,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9136}
{"test":{"id":35,"name":"ARBackendService Tests getLandmarks should handle errors gracefully","suiteID":4,"groupIDs":[23,24],"metadata":{"skip":false,"skipReason":null},"line":34,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_backend_service_test.dart"},"type":"testStart","time":9136}
{"testID":35,"messageType":"print","message":"Error getting landmarks: DioException [bad response]: This exception was thrown because the response has a status code of 400 and RequestOptions.validateStatus was configured to throw for this status code.\nThe status code of 400 has the following meaning: \"Client error - the request contains bad syntax or cannot be fulfilled\"\nRead more about status codes at https://developer.mozilla.org/en-US/docs/Web/HTTP/Status\nIn order to resolve this exception you typically have either to verify and fix your request code or you have to fix the server code.\n","type":"print","time":9228}
{"testID":35,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9230}
{"test":{"id":36,"name":"ARBackendService Tests getARModel should handle errors gracefully","suiteID":4,"groupIDs":[23,24],"metadata":{"skip":false,"skipReason":null},"line":45,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_backend_service_test.dart"},"type":"testStart","time":9231}
{"testID":36,"messageType":"print","message":"Error getting AR model: DioException [bad response]: This exception was thrown because the response has a status code of 400 and RequestOptions.validateStatus was configured to throw for this status code.\nThe status code of 400 has the following meaning: \"Client error - the request contains bad syntax or cannot be fulfilled\"\nRead more about status codes at https://developer.mozilla.org/en-US/docs/Web/HTTP/Status\nIn order to resolve this exception you typically have either to verify and fix your request code or you have to fix the server code.\n","type":"print","time":9237}
{"testID":36,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9240}
{"suite":{"id":37,"platform":"vm","path":"/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/review_service_test.dart"},"type":"suite","time":9261}
{"test":{"id":38,"name":"loading /Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/review_service_test.dart","suiteID":37,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":9261}
{"testID":7,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":9578}
{"group":{"id":39,"suiteID":6,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":7,"line":null,"column":null,"url":null},"type":"group","time":9579}
{"test":{"id":40,"name":"(setUpAll)","suiteID":6,"groupIDs":[39],"metadata":{"skip":false,"skipReason":null},"line":6,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_voice_command_service_test.dart"},"type":"testStart","time":9579}
{"testID":40,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":9781}
{"group":{"id":41,"suiteID":6,"parentID":39,"name":"ARVoiceCommandService Tests","metadata":{"skip":false,"skipReason":null},"testCount":7,"line":26,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_voice_command_service_test.dart"},"type":"group","time":9781}
{"test":{"id":42,"name":"ARVoiceCommandService Tests initialize should setup voice commands","suiteID":6,"groupIDs":[39,41],"metadata":{"skip":false,"skipReason":null},"line":27,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_voice_command_service_test.dart"},"type":"testStart","time":9782}
{"testID":42,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9815}
{"test":{"id":43,"name":"ARVoiceCommandService Tests service should have correct initial state","suiteID":6,"groupIDs":[39,41],"metadata":{"skip":false,"skipReason":null},"line":35,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_voice_command_service_test.dart"},"type":"testStart","time":9816}
{"testID":43,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9826}
{"test":{"id":44,"name":"ARVoiceCommandService Tests registerCommand should register a command handler","suiteID":6,"groupIDs":[39,41],"metadata":{"skip":false,"skipReason":null},"line":43,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_voice_command_service_test.dart"},"type":"testStart","time":9828}
{"testID":44,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9835}
{"test":{"id":45,"name":"ARVoiceCommandService Tests availableCommands should contain expected commands","suiteID":6,"groupIDs":[39,41],"metadata":{"skip":false,"skipReason":null},"line":57,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_voice_command_service_test.dart"},"type":"testStart","time":9835}
{"testID":45,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9841}
{"test":{"id":46,"name":"ARVoiceCommandService Tests startListening should handle speech recognition","suiteID":6,"groupIDs":[39,41],"metadata":{"skip":false,"skipReason":null},"line":66,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_voice_command_service_test.dart"},"type":"testStart","time":9841}
{"testID":46,"messageType":"print","message":"Error starting speech recognition: type 'Null' is not a subtype of type 'bool'","type":"print","time":9856}
{"testID":46,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9857}
{"test":{"id":47,"name":"ARVoiceCommandService Tests stopListening should return a boolean","suiteID":6,"groupIDs":[39,41],"metadata":{"skip":false,"skipReason":null},"line":71,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_voice_command_service_test.dart"},"type":"testStart","time":9857}
{"testID":47,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9863}
{"test":{"id":48,"name":"ARVoiceCommandService Tests cancelListening should return a boolean","suiteID":6,"groupIDs":[39,41],"metadata":{"skip":false,"skipReason":null},"line":79,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_voice_command_service_test.dart"},"type":"testStart","time":9863}
{"testID":48,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9869}
{"test":{"id":49,"name":"(tearDownAll)","suiteID":6,"groupIDs":[39],"metadata":{"skip":false,"skipReason":null},"line":21,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_voice_command_service_test.dart"},"type":"testStart","time":9870}
{"testID":49,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":9879}
{"suite":{"id":50,"platform":"vm","path":"/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/auth_service_test.dart"},"type":"suite","time":9899}
{"test":{"id":51,"name":"loading /Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/auth_service_test.dart","suiteID":50,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":9899}
{"testID":20,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":10350}
{"group":{"id":52,"suiteID":19,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":5,"line":null,"column":null,"url":null},"type":"group","time":10350}
{"group":{"id":53,"suiteID":19,"parentID":52,"name":"Group Translation Model Tests","metadata":{"skip":false,"skipReason":null},"testCount":5,"line":6,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/group_translation_service_test.dart"},"type":"group","time":10350}
{"test":{"id":54,"name":"Group Translation Model Tests LanguageModel should be created with required parameters","suiteID":19,"groupIDs":[52,53],"metadata":{"skip":false,"skipReason":null},"line":7,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/group_translation_service_test.dart"},"type":"testStart","time":10350}
{"testID":54,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10428}
{"test":{"id":55,"name":"Group Translation Model Tests ParticipantLanguagePreference should be created correctly","suiteID":19,"groupIDs":[52,53],"metadata":{"skip":false,"skipReason":null},"line":22,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/group_translation_service_test.dart"},"type":"testStart","time":10429}
{"testID":55,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10439}
{"test":{"id":56,"name":"Group Translation Model Tests GroupTranslationSettings should be created correctly","suiteID":19,"groupIDs":[52,53],"metadata":{"skip":false,"skipReason":null},"line":45,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/group_translation_service_test.dart"},"type":"testStart","time":10439}
{"testID":56,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10455}
{"test":{"id":57,"name":"Group Translation Model Tests GroupMessageTranslation should be created correctly","suiteID":19,"groupIDs":[52,53],"metadata":{"skip":false,"skipReason":null},"line":75,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/group_translation_service_test.dart"},"type":"testStart","time":10456}
{"testID":57,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10479}
{"test":{"id":58,"name":"Group Translation Model Tests GroupMessageTranslation should add translations correctly","suiteID":19,"groupIDs":[52,53],"metadata":{"skip":false,"skipReason":null},"line":98,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/group_translation_service_test.dart"},"type":"testStart","time":10480}
{"testID":58,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10494}
{"suite":{"id":59,"platform":"vm","path":"/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/language_detection_service_test.dart"},"type":"suite","time":10514}
{"test":{"id":60,"name":"loading /Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/language_detection_service_test.dart","suiteID":59,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":10514}
{"testID":33,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":11354}
{"group":{"id":61,"suiteID":32,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":5,"line":null,"column":null,"url":null},"type":"group","time":11354}
{"test":{"id":62,"name":"(setUpAll)","suiteID":32,"groupIDs":[61],"metadata":{"skip":false,"skipReason":null},"line":15,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/startup_optimization_service_test.dart"},"type":"testStart","time":11355}
{"testID":62,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":11532}
{"group":{"id":63,"suiteID":32,"parentID":61,"name":"StartupOptimizationService Tests","metadata":{"skip":false,"skipReason":null},"testCount":5,"line":74,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/startup_optimization_service_test.dart"},"type":"group","time":11532}
{"test":{"id":64,"name":"StartupOptimizationService Tests initializeApp should complete successfully","suiteID":32,"groupIDs":[61,63],"metadata":{"skip":false,"skipReason":null},"line":75,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/startup_optimization_service_test.dart"},"type":"testStart","time":11532}
{"testID":64,"messageType":"print","message":"🚀 Starting app initialization","type":"print","time":11550}
{"testID":64,"messageType":"print","message":"✅ Preloaded asset: assets/images/splash.png (0 bytes)","type":"print","time":11570}
{"testID":64,"messageType":"print","message":"✅ SharedPreferences initialized in 20ms","type":"print","time":11572}
{"testID":64,"messageType":"print","message":"✅ Preloaded asset: assets/animations/splash_animation.json (0 bytes)","type":"print","time":11573}
{"testID":64,"messageType":"print","message":"✅ Critical assets preloaded in 9ms","type":"print","time":11574}
{"testID":64,"messageType":"print","message":"❌ Error initializing Firebase: PlatformException(channel-error, Unable to establish connection on channel., null, null)","type":"print","time":11583}
{"testID":64,"messageType":"print","message":"✅ App initialization completed in 48ms","type":"print","time":11593}
{"testID":64,"messageType":"print","message":"✅ Preloaded asset: assets/images/onboarding_1.png (0 bytes)","type":"print","time":11611}
{"testID":64,"messageType":"print","message":"Error calculating directory size: PathNotFoundException: Directory listing failed, path = '/mock/temp/path/' (OS Error: No such file or directory, errno = 2)","type":"print","time":11618}
{"testID":64,"messageType":"print","message":"✅ Preloaded asset: assets/images/onboarding_2.png (0 bytes)","type":"print","time":11621}
{"testID":64,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":11623}
{"test":{"id":65,"name":"StartupOptimizationService Tests initialized future should complete when initialization is done","suiteID":32,"groupIDs":[61,63],"metadata":{"skip":false,"skipReason":null},"line":84,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/startup_optimization_service_test.dart"},"type":"testStart","time":11623}
{"testID":64,"messageType":"print","message":"✅ Preloaded asset: assets/images/onboarding_3.png (0 bytes)","type":"print","time":11624}
{"testID":64,"messageType":"print","message":"✅ Non-critical assets preloaded in 28ms","type":"print","time":11625}
{"testID":65,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":11631}
{"test":{"id":66,"name":"StartupOptimizationService Tests initializeApp should handle errors gracefully","suiteID":32,"groupIDs":[61,63],"metadata":{"skip":false,"skipReason":null},"line":97,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/startup_optimization_service_test.dart"},"type":"testStart","time":11631}
{"testID":66,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":11637}
{"test":{"id":67,"name":"StartupOptimizationService Tests checkConnectivity should return a boolean value","suiteID":32,"groupIDs":[61,63],"metadata":{"skip":false,"skipReason":null},"line":118,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/startup_optimization_service_test.dart"},"type":"testStart","time":11638}
{"testID":67,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":11646}
{"test":{"id":68,"name":"StartupOptimizationService Tests unawaited should not block execution","suiteID":32,"groupIDs":[61,63],"metadata":{"skip":false,"skipReason":null},"line":126,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/startup_optimization_service_test.dart"},"type":"testStart","time":11646}
{"testID":68,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":11856}
{"test":{"id":69,"name":"(tearDownAll)","suiteID":32,"groupIDs":[61],"metadata":{"skip":false,"skipReason":null},"line":69,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/startup_optimization_service_test.dart"},"type":"testStart","time":11857}
{"testID":69,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":11865}
{"suite":{"id":70,"platform":"vm","path":"/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/map_cache_manager_test.dart"},"type":"suite","time":11890}
{"test":{"id":71,"name":"loading /Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/map_cache_manager_test.dart","suiteID":70,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":11890}
{"testID":38,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":12035}
{"group":{"id":72,"suiteID":37,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":3,"line":null,"column":null,"url":null},"type":"group","time":12035}
{"group":{"id":73,"suiteID":37,"parentID":72,"name":"Review Service Model Tests","metadata":{"skip":false,"skipReason":null},"testCount":3,"line":6,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/review_service_test.dart"},"type":"group","time":12035}
{"test":{"id":74,"name":"Review Service Model Tests Booking model should be created correctly","suiteID":37,"groupIDs":[72,73],"metadata":{"skip":false,"skipReason":null},"line":7,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/review_service_test.dart"},"type":"testStart","time":12035}
{"testID":74,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":12122}
{"test":{"id":75,"name":"Review Service Model Tests BookingStatus enum should have correct values","suiteID":37,"groupIDs":[72,73],"metadata":{"skip":false,"skipReason":null},"line":38,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/review_service_test.dart"},"type":"testStart","time":12123}
{"testID":75,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":12128}
{"test":{"id":76,"name":"Review Service Model Tests TimeSlot should be created correctly","suiteID":37,"groupIDs":[72,73],"metadata":{"skip":false,"skipReason":null},"line":46,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/review_service_test.dart"},"type":"testStart","time":12130}
{"testID":76,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":12135}
{"suite":{"id":77,"platform":"vm","path":"/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/location_service_test.dart"},"type":"suite","time":12153}
{"test":{"id":78,"name":"loading /Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/location_service_test.dart","suiteID":77,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":12154}
{"testID":51,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":13105}
{"group":{"id":79,"suiteID":50,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":5,"line":null,"column":null,"url":null},"type":"group","time":13106}
{"test":{"id":80,"name":"(setUpAll)","suiteID":50,"groupIDs":[79],"metadata":{"skip":false,"skipReason":null},"line":6,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/auth_service_test.dart"},"type":"testStart","time":13106}
{"testID":80,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":13297}
{"group":{"id":81,"suiteID":50,"parentID":79,"name":"AuthService Tests","metadata":{"skip":false,"skipReason":null},"testCount":5,"line":14,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/auth_service_test.dart"},"type":"group","time":13298}
{"test":{"id":82,"name":"AuthService Tests AuthService should be created successfully","suiteID":50,"groupIDs":[79,81],"metadata":{"skip":false,"skipReason":null},"line":15,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/auth_service_test.dart"},"type":"testStart","time":13298}
{"testID":82,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":13321}
{"test":{"id":83,"name":"AuthService Tests AuthService should handle sign in gracefully","suiteID":50,"groupIDs":[79,81],"metadata":{"skip":false,"skipReason":null},"line":20,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/auth_service_test.dart"},"type":"testStart","time":13321}
{"testID":83,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":13335}
{"test":{"id":84,"name":"AuthService Tests AuthService should handle registration gracefully","suiteID":50,"groupIDs":[79,81],"metadata":{"skip":false,"skipReason":null},"line":36,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/auth_service_test.dart"},"type":"testStart","time":13335}
{"testID":84,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":13340}
{"test":{"id":85,"name":"AuthService Tests AuthService should handle sign out gracefully","suiteID":50,"groupIDs":[79,81],"metadata":{"skip":false,"skipReason":null},"line":56,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/auth_service_test.dart"},"type":"testStart","time":13340}
{"testID":85,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":13348}
{"test":{"id":86,"name":"AuthService Tests AuthService singleton pattern should work","suiteID":50,"groupIDs":[79,81],"metadata":{"skip":false,"skipReason":null},"line":69,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/auth_service_test.dart"},"type":"testStart","time":13348}
{"testID":86,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":13353}
{"test":{"id":87,"name":"(tearDownAll)","suiteID":50,"groupIDs":[79],"metadata":{"skip":false,"skipReason":null},"line":77,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/auth_service_test.dart"},"type":"testStart","time":13353}
{"testID":87,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":13362}
{"suite":{"id":88,"platform":"vm","path":"/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_recording_service_test.dart"},"type":"suite","time":13380}
{"test":{"id":89,"name":"loading /Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_recording_service_test.dart","suiteID":88,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":13380}
{"testID":60,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":13918}
{"group":{"id":90,"suiteID":59,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":11,"line":null,"column":null,"url":null},"type":"group","time":13918}
{"group":{"id":91,"suiteID":59,"parentID":90,"name":"LanguageDetectionService Tests","metadata":{"skip":false,"skipReason":null},"testCount":11,"line":50,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/language_detection_service_test.dart"},"type":"group","time":13919}
{"test":{"id":92,"name":"LanguageDetectionService Tests supportedLanguages should return a list of supported languages","suiteID":59,"groupIDs":[90,91],"metadata":{"skip":false,"skipReason":null},"line":51,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/language_detection_service_test.dart"},"type":"testStart","time":13919}
{"testID":92,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":14002}
{"test":{"id":93,"name":"LanguageDetectionService Tests detectLanguage should return language from cache if available","suiteID":59,"groupIDs":[90,91],"metadata":{"skip":false,"skipReason":null},"line":62,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/language_detection_service_test.dart"},"type":"testStart","time":14002}
{"testID":93,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":14322}
{"test":{"id":94,"name":"LanguageDetectionService Tests detectLanguage should detect language and cache result if not in cache","suiteID":59,"groupIDs":[90,91],"metadata":{"skip":false,"skipReason":null},"line":82,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/language_detection_service_test.dart"},"type":"testStart","time":14323}
{"testID":94,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":14645}
{"test":{"id":95,"name":"LanguageDetectionService Tests detectLanguage should default to English for very short texts","suiteID":59,"groupIDs":[90,91],"metadata":{"skip":false,"skipReason":null},"line":102,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/language_detection_service_test.dart"},"type":"testStart","time":14645}
{"testID":95,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":14653}
{"test":{"id":96,"name":"LanguageDetectionService Tests detectLanguage should detect French text","suiteID":59,"groupIDs":[90,91],"metadata":{"skip":false,"skipReason":null},"line":117,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/language_detection_service_test.dart"},"type":"testStart","time":14653}
{"testID":96,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":14963}
{"test":{"id":97,"name":"LanguageDetectionService Tests detectLanguage should detect Spanish text","suiteID":59,"groupIDs":[90,91],"metadata":{"skip":false,"skipReason":null},"line":128,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/language_detection_service_test.dart"},"type":"testStart","time":14964}
{"testID":71,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":15087}
{"group":{"id":98,"suiteID":70,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":6,"line":null,"column":null,"url":null},"type":"group","time":15088}
{"group":{"id":99,"suiteID":70,"parentID":98,"name":"MapCacheManager Tests","metadata":{"skip":false,"skipReason":null},"testCount":6,"line":100,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/map_cache_manager_test.dart"},"type":"group","time":15088}
{"test":{"id":100,"name":"MapCacheManager Tests getCachedRegions should return list of cached regions","suiteID":70,"groupIDs":[98,99],"metadata":{"skip":false,"skipReason":null},"line":101,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/map_cache_manager_test.dart"},"type":"testStart","time":15088}
{"testID":100,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":15227}
{"test":{"id":101,"name":"MapCacheManager Tests deleteCachedRegion should remove region from list","suiteID":70,"groupIDs":[98,99],"metadata":{"skip":false,"skipReason":null},"line":114,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/map_cache_manager_test.dart"},"type":"testStart","time":15227}
{"testID":101,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":15239}
{"test":{"id":102,"name":"MapCacheManager Tests clearCache should remove all cached regions","suiteID":70,"groupIDs":[98,99],"metadata":{"skip":false,"skipReason":null},"line":129,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/map_cache_manager_test.dart"},"type":"testStart","time":15240}
{"testID":102,"error":"Expected: true\n  Actual: <false>\n","stackTrace":"package:matcher                                       expect\npackage:flutter_test/src/widget_tester.dart 480:18    expect\ntest/unit/services/map_cache_manager_test.dart 134:7  main.<fn>.<fn>\n","isFailure":true,"type":"error","time":15269}
{"testID":102,"result":"failure","skipped":false,"hidden":false,"type":"testDone","time":15270}
{"test":{"id":103,"name":"MapCacheManager Tests getCacheSize should return total size of cached files","suiteID":70,"groupIDs":[98,99],"metadata":{"skip":false,"skipReason":null},"line":141,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/map_cache_manager_test.dart"},"type":"testStart","time":15271}
{"testID":97,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":15272}
{"test":{"id":104,"name":"LanguageDetectionService Tests detectLanguage should detect Yoruba text","suiteID":59,"groupIDs":[90,91],"metadata":{"skip":false,"skipReason":null},"line":139,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/language_detection_service_test.dart"},"type":"testStart","time":15273}
{"testID":103,"error":"Expected: <1024>\n  Actual: <0>\n","stackTrace":"package:matcher                                       expect\npackage:flutter_test/src/widget_tester.dart 480:18    expect\ntest/unit/services/map_cache_manager_test.dart 146:7  main.<fn>.<fn>\n","isFailure":true,"type":"error","time":15286}
{"testID":103,"result":"failure","skipped":false,"hidden":false,"type":"testDone","time":15286}
{"test":{"id":105,"name":"MapCacheManager Tests cacheMapRegion should download and cache tiles","suiteID":70,"groupIDs":[98,99],"metadata":{"skip":false,"skipReason":null},"line":149,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/map_cache_manager_test.dart"},"type":"testStart","time":15287}
{"testID":105,"error":"Expected: true\n  Actual: <false>\n","stackTrace":"package:matcher                                       expect\npackage:flutter_test/src/widget_tester.dart 480:18    expect\ntest/unit/services/map_cache_manager_test.dart 175:7  main.<fn>.<fn>\n","isFailure":true,"type":"error","time":15301}
{"testID":105,"result":"failure","skipped":false,"hidden":false,"type":"testDone","time":15301}
{"test":{"id":106,"name":"MapCacheManager Tests _formatSize should format bytes correctly","suiteID":70,"groupIDs":[98,99],"metadata":{"skip":false,"skipReason":null},"line":182,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/map_cache_manager_test.dart"},"type":"testStart","time":15302}
{"testID":106,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":15317}
{"suite":{"id":107,"platform":"vm","path":"/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/notification_service_test.dart"},"type":"suite","time":15342}
{"test":{"id":108,"name":"loading /Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/notification_service_test.dart","suiteID":107,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":15342}
{"testID":104,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":15581}
{"test":{"id":109,"name":"LanguageDetectionService Tests detectLanguage should detect Igbo text","suiteID":59,"groupIDs":[90,91],"metadata":{"skip":false,"skipReason":null},"line":150,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/language_detection_service_test.dart"},"type":"testStart","time":15582}
{"testID":109,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":15889}
{"test":{"id":110,"name":"LanguageDetectionService Tests detectLanguage should detect Hausa text","suiteID":59,"groupIDs":[90,91],"metadata":{"skip":false,"skipReason":null},"line":161,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/language_detection_service_test.dart"},"type":"testStart","time":15889}
{"testID":78,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":15973}
{"group":{"id":111,"suiteID":77,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":13,"line":null,"column":null,"url":null},"type":"group","time":15973}
{"group":{"id":112,"suiteID":77,"parentID":111,"name":"LocationService Tests","metadata":{"skip":false,"skipReason":null},"testCount":13,"line":84,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/location_service_test.dart"},"type":"group","time":15973}
{"test":{"id":113,"name":"LocationService Tests getCurrentPosition should return a Position","suiteID":77,"groupIDs":[111,112],"metadata":{"skip":false,"skipReason":null},"line":85,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/location_service_test.dart"},"type":"testStart","time":15973}
{"testID":113,"error":"Binding has not yet been initialized.\nThe \"instance\" getter on the ServicesBinding binding mixin is only available once that binding has been initialized.\nTypically, this is done by calling \"WidgetsFlutterBinding.ensureInitialized()\" or \"runApp()\" (the latter calls the former). Typically this call is done in the \"void main()\" method. The \"ensureInitialized\" method is idempotent; calling it multiple times is not harmful. After calling that method, the \"instance\" getter will return the binding.\nIn a test, one can call \"TestWidgetsFlutterBinding.ensureInitialized()\" as the first line in the test's \"main()\" method to initialize the binding.\nIf ServicesBinding is a custom binding mixin, there must also be a custom binding class, like WidgetsFlutterBinding, but that mixes in the selected binding, and that is the class that must be constructed before using the \"instance\" getter.","stackTrace":"package:flutter/src/foundation/binding.dart 310:9                                              BindingBase.checkInstance.<fn>\npackage:flutter/src/foundation/binding.dart 391:6                                              BindingBase.checkInstance\npackage:flutter/src/services/binding.dart 69:54                                                ServicesBinding.instance\npackage:flutter/src/services/platform_channel.dart 158:25                                      _findBinaryMessenger\npackage:flutter/src/services/platform_channel.dart 293:56                                      MethodChannel.binaryMessenger\npackage:flutter/src/services/platform_channel.dart 327:15                                      MethodChannel._invokeMethod\npackage:flutter/src/services/platform_channel.dart 507:12                                      MethodChannel.invokeMethod\npackage:geolocator_platform_interface/src/implementations/method_channel_geolocator.dart 69:8  MethodChannelGeolocator.isLocationServiceEnabled\npackage:geolocator/geolocator.dart 43:35                                                       Geolocator.isLocationServiceEnabled\npackage:culture_connect/services/location_service.dart 834:39                                  LocationService.getCurrentPosition\ntest/unit/services/location_service_test.dart 90:46                                            main.<fn>.<fn>\n","isFailure":false,"type":"error","time":16153}
{"testID":113,"result":"error","skipped":false,"hidden":false,"type":"testDone","time":16155}
{"test":{"id":114,"name":"LocationService Tests calculateDistance should return correct distance","suiteID":77,"groupIDs":[111,112],"metadata":{"skip":false,"skipReason":null},"line":98,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/location_service_test.dart"},"type":"testStart","time":16155}
{"testID":114,"error":"Expected: <1000.0>\n  Actual: <14.189494970239046>\n","stackTrace":"package:matcher                                      expect\npackage:flutter_test/src/widget_tester.dart 480:18   expect\ntest/unit/services/location_service_test.dart 114:7  main.<fn>.<fn>\n","isFailure":true,"type":"error","time":16182}
{"testID":114,"result":"failure","skipped":false,"hidden":false,"type":"testDone","time":16182}
{"test":{"id":115,"name":"LocationService Tests formatDistance should format meters correctly","suiteID":77,"groupIDs":[111,112],"metadata":{"skip":false,"skipReason":null},"line":117,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/location_service_test.dart"},"type":"testStart","time":16182}
{"testID":115,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16188}
{"test":{"id":116,"name":"LocationService Tests formatDistance should format kilometers correctly","suiteID":77,"groupIDs":[111,112],"metadata":{"skip":false,"skipReason":null},"line":129,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/location_service_test.dart"},"type":"testStart","time":16189}
{"testID":116,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16193}
{"test":{"id":117,"name":"LocationService Tests getNearbyLandmarks should return landmarks within radius","suiteID":77,"groupIDs":[111,112],"metadata":{"skip":false,"skipReason":null},"line":141,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/location_service_test.dart"},"type":"testStart","time":16194}
{"testID":110,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16197}
{"test":{"id":118,"name":"LanguageDetectionService Tests detectLanguage should detect Swahili text","suiteID":59,"groupIDs":[90,91],"metadata":{"skip":false,"skipReason":null},"line":172,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/language_detection_service_test.dart"},"type":"testStart","time":16197}
{"testID":117,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16204}
{"test":{"id":119,"name":"LocationService Tests getRecommendedLandmarks should return sorted landmarks","suiteID":77,"groupIDs":[111,112],"metadata":{"skip":false,"skipReason":null},"line":170,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/location_service_test.dart"},"type":"testStart","time":16205}
{"testID":119,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16223}
{"test":{"id":120,"name":"LocationService Tests loadMapStyle should return style string for valid style name","suiteID":77,"groupIDs":[111,112],"metadata":{"skip":false,"skipReason":null},"line":205,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/location_service_test.dart"},"type":"testStart","time":16223}
{"testID":120,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16229}
{"test":{"id":121,"name":"LocationService Tests loadMapStyle should return null for invalid style name","suiteID":77,"groupIDs":[111,112],"metadata":{"skip":false,"skipReason":null},"line":218,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/location_service_test.dart"},"type":"testStart","time":16229}
{"testID":121,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16234}
{"test":{"id":122,"name":"LocationService Tests LocationService should handle route calculation gracefully","suiteID":77,"groupIDs":[111,112],"metadata":{"skip":false,"skipReason":null},"line":229,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/location_service_test.dart"},"type":"testStart","time":16234}
{"testID":122,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16241}
{"test":{"id":123,"name":"LocationService Tests LocationService should handle distance calculation gracefully","suiteID":77,"groupIDs":[111,112],"metadata":{"skip":false,"skipReason":null},"line":239,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/location_service_test.dart"},"type":"testStart","time":16241}
{"testID":123,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16247}
{"test":{"id":124,"name":"LocationService Tests estimateTravelTime should return correct time for walking","suiteID":77,"groupIDs":[111,112],"metadata":{"skip":false,"skipReason":null},"line":250,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/location_service_test.dart"},"type":"testStart","time":16247}
{"testID":124,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16252}
{"test":{"id":125,"name":"LocationService Tests estimateTravelTime should return correct time for cycling","suiteID":77,"groupIDs":[111,112],"metadata":{"skip":false,"skipReason":null},"line":264,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/location_service_test.dart"},"type":"testStart","time":16253}
{"testID":125,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16257}
{"test":{"id":126,"name":"LocationService Tests estimateTravelTime should return correct time for driving","suiteID":77,"groupIDs":[111,112],"metadata":{"skip":false,"skipReason":null},"line":277,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/location_service_test.dart"},"type":"testStart","time":16257}
{"testID":126,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16263}
{"suite":{"id":127,"platform":"vm","path":"/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_lazy_loading_service_test.dart"},"type":"suite","time":16280}
{"test":{"id":128,"name":"loading /Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_lazy_loading_service_test.dart","suiteID":127,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":16280}
{"testID":118,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16506}
{"test":{"id":129,"name":"LanguageDetectionService Tests clearCache should clear the detection cache","suiteID":59,"groupIDs":[90,91],"metadata":{"skip":false,"skipReason":null},"line":183,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/language_detection_service_test.dart"},"type":"testStart","time":16506}
{"testID":129,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16513}
{"suite":{"id":130,"platform":"vm","path":"/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/booking_service_test.dart"},"type":"suite","time":16533}
{"test":{"id":131,"name":"loading /Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/booking_service_test.dart","suiteID":130,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":16533}
{"testID":89,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":16613}
{"group":{"id":132,"suiteID":88,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":12,"line":null,"column":null,"url":null},"type":"group","time":16613}
{"group":{"id":133,"suiteID":88,"parentID":132,"name":"ARRecordingService Tests","metadata":{"skip":false,"skipReason":null},"testCount":12,"line":87,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_recording_service_test.dart"},"type":"group","time":16613}
{"test":{"id":134,"name":"ARRecordingService Tests initialize should setup recording service","suiteID":88,"groupIDs":[132,133],"metadata":{"skip":false,"skipReason":null},"line":88,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_recording_service_test.dart"},"type":"testStart","time":16613}
{"testID":134,"messageType":"print","message":"Error clearing temporary files: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":16716}
{"testID":134,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16729}
{"test":{"id":135,"name":"ARRecordingService Tests startRecording should start recording","suiteID":88,"groupIDs":[132,133],"metadata":{"skip":false,"skipReason":null},"line":104,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_recording_service_test.dart"},"type":"testStart","time":16729}
{"testID":135,"messageType":"print","message":"Error clearing temporary files: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":16735}
{"testID":135,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16740}
{"test":{"id":136,"name":"ARRecordingService Tests pauseRecording should pause recording","suiteID":88,"groupIDs":[132,133],"metadata":{"skip":false,"skipReason":null},"line":125,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_recording_service_test.dart"},"type":"testStart","time":16741}
{"testID":136,"messageType":"print","message":"Error clearing temporary files: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":16747}
{"testID":136,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16749}
{"test":{"id":137,"name":"ARRecordingService Tests resumeRecording should resume recording","suiteID":88,"groupIDs":[132,133],"metadata":{"skip":false,"skipReason":null},"line":146,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_recording_service_test.dart"},"type":"testStart","time":16749}
{"testID":137,"messageType":"print","message":"Error clearing temporary files: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":16755}
{"testID":137,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16756}
{"test":{"id":138,"name":"ARRecordingService Tests stopRecording should stop recording","suiteID":88,"groupIDs":[132,133],"metadata":{"skip":false,"skipReason":null},"line":168,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_recording_service_test.dart"},"type":"testStart","time":16757}
{"testID":138,"messageType":"print","message":"Error clearing temporary files: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":16762}
{"testID":138,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16764}
{"test":{"id":139,"name":"ARRecordingService Tests takeScreenshot should capture screenshot","suiteID":88,"groupIDs":[132,133],"metadata":{"skip":false,"skipReason":null},"line":190,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_recording_service_test.dart"},"type":"testStart","time":16764}
{"testID":139,"messageType":"print","message":"Error clearing temporary files: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":16770}
{"testID":139,"messageType":"print","message":"Error taking screenshot: MissingPluginException(No implementation found for method pickImage on channel plugins.flutter.io/image_picker)","type":"print","time":16779}
{"testID":139,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16780}
{"test":{"id":140,"name":"ARRecordingService Tests recordVideo should record video","suiteID":88,"groupIDs":[132,133],"metadata":{"skip":false,"skipReason":null},"line":204,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_recording_service_test.dart"},"type":"testStart","time":16781}
{"testID":140,"messageType":"print","message":"Error clearing temporary files: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":16787}
{"testID":140,"messageType":"print","message":"Error recording video: MissingPluginException(No implementation found for method pickVideo on channel plugins.flutter.io/image_picker)","type":"print","time":16789}
{"testID":140,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16791}
{"test":{"id":141,"name":"ARRecordingService Tests recordingDurationInSeconds should return current duration","suiteID":88,"groupIDs":[132,133],"metadata":{"skip":false,"skipReason":null},"line":218,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_recording_service_test.dart"},"type":"testStart","time":16791}
{"testID":141,"messageType":"print","message":"Error clearing temporary files: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":16797}
{"testID":141,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16800}
{"test":{"id":142,"name":"ARRecordingService Tests recordingDurationFormatted should return formatted duration","suiteID":88,"groupIDs":[132,133],"metadata":{"skip":false,"skipReason":null},"line":236,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_recording_service_test.dart"},"type":"testStart","time":16800}
{"testID":142,"messageType":"print","message":"Error clearing temporary files: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":16806}
{"testID":142,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16810}
{"test":{"id":143,"name":"ARRecordingService Tests shareRecording should share recording","suiteID":88,"groupIDs":[132,133],"metadata":{"skip":false,"skipReason":null},"line":254,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_recording_service_test.dart"},"type":"testStart","time":16811}
{"testID":143,"messageType":"print","message":"Error clearing temporary files: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":16818}
{"testID":143,"error":"Expected: <true>\n  Actual: <false>\n","stackTrace":"package:matcher                                          expect\npackage:flutter_test/src/widget_tester.dart 480:18       expect\ntest/unit/services/ar_recording_service_test.dart 271:7  main.<fn>.<fn>\n","isFailure":true,"type":"error","time":16853}
{"testID":143,"result":"failure","skipped":false,"hidden":false,"type":"testDone","time":16854}
{"test":{"id":144,"name":"ARRecordingService Tests saveRecording should save recording","suiteID":88,"groupIDs":[132,133],"metadata":{"skip":false,"skipReason":null},"line":274,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_recording_service_test.dart"},"type":"testStart","time":16854}
{"testID":144,"messageType":"print","message":"Error clearing temporary files: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":16862}
{"testID":144,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16864}
{"test":{"id":145,"name":"ARRecordingService Tests dispose should clean up resources","suiteID":88,"groupIDs":[132,133],"metadata":{"skip":false,"skipReason":null},"line":294,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_recording_service_test.dart"},"type":"testStart","time":16865}
{"testID":145,"messageType":"print","message":"Error clearing temporary files: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":16873}
{"testID":145,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16875}
{"suite":{"id":146,"platform":"vm","path":"/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_accessibility_service_test.dart"},"type":"suite","time":16894}
{"test":{"id":147,"name":"loading /Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_accessibility_service_test.dart","suiteID":146,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":16894}
{"testID":108,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":17896}
{"group":{"id":148,"suiteID":107,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":4,"line":null,"column":null,"url":null},"type":"group","time":17897}
{"test":{"id":149,"name":"(setUpAll)","suiteID":107,"groupIDs":[148],"metadata":{"skip":false,"skipReason":null},"line":6,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/notification_service_test.dart"},"type":"testStart","time":17897}
{"testID":149,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":18125}
{"group":{"id":150,"suiteID":107,"parentID":148,"name":"NotificationService Tests","metadata":{"skip":false,"skipReason":null},"testCount":4,"line":25,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/notification_service_test.dart"},"type":"group","time":18127}
{"test":{"id":151,"name":"NotificationService Tests NotificationService should be created successfully","suiteID":107,"groupIDs":[148,150],"metadata":{"skip":false,"skipReason":null},"line":26,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/notification_service_test.dart"},"type":"testStart","time":18127}
{"testID":151,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":18145}
{"test":{"id":152,"name":"NotificationService Tests NotificationService should be singleton","suiteID":107,"groupIDs":[148,150],"metadata":{"skip":false,"skipReason":null},"line":30,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/notification_service_test.dart"},"type":"testStart","time":18146}
{"testID":152,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":18150}
{"test":{"id":153,"name":"NotificationService Tests initialize should complete without error","suiteID":107,"groupIDs":[148,150],"metadata":{"skip":false,"skipReason":null},"line":39,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/notification_service_test.dart"},"type":"testStart","time":18150}
{"testID":153,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":18232}
{"test":{"id":154,"name":"NotificationService Tests requestPermissions should complete without error","suiteID":107,"groupIDs":[148,150],"metadata":{"skip":false,"skipReason":null},"line":46,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/notification_service_test.dart"},"type":"testStart","time":18232}
{"testID":154,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":18238}
{"test":{"id":155,"name":"(tearDownAll)","suiteID":107,"groupIDs":[148],"metadata":{"skip":false,"skipReason":null},"line":20,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/notification_service_test.dart"},"type":"testStart","time":18238}
{"testID":155,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":18247}
test/unit/services/ar_accessibility_service_test.dart:29:38: Error: No named parameter with the name 'preferences'.
    service = ARAccessibilityService(preferences: mockPreferences);
                                     ^^^^^^^^^^^
lib/services/ar_accessibility_service.dart:49:11: Context: Found this candidate, but the arguments don't match.
  factory ARAccessibilityService() => _instance;
          ^
test/unit/services/ar_accessibility_service_test.dart:42:22: Error: The getter 'isHighContrastModeEnabled' isn't defined for the class 'ARAccessibilityService'.
 - 'ARAccessibilityService' is from 'package:culture_connect/services/ar_accessibility_service.dart' ('lib/services/ar_accessibility_service.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isHighContrastModeEnabled'.
      expect(service.isHighContrastModeEnabled, true);
                     ^^^^^^^^^^^^^^^^^^^^^^^^^
test/unit/services/ar_accessibility_service_test.dart:51:36: Error: The getter 'isHighContrastModeEnabled' isn't defined for the class 'ARAccessibilityService'.
 - 'ARAccessibilityService' is from 'package:culture_connect/services/ar_accessibility_service.dart' ('lib/services/ar_accessibility_service.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isHighContrastModeEnabled'.
      final initialValue = service.isHighContrastModeEnabled;
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^
test/unit/services/ar_accessibility_service_test.dart:54:21: Error: The method 'toggleHighContrastMode' isn't defined for the class 'ARAccessibilityService'.
 - 'ARAccessibilityService' is from 'package:culture_connect/services/ar_accessibility_service.dart' ('lib/services/ar_accessibility_service.dart').
Try correcting the name to the name of an existing method, or defining a method named 'toggleHighContrastMode'.
      await service.toggleHighContrastMode();
                    ^^^^^^^^^^^^^^^^^^^^^^
test/unit/services/ar_accessibility_service_test.dart:57:22: Error: The getter 'isHighContrastModeEnabled' isn't defined for the class 'ARAccessibilityService'.
 - 'ARAccessibilityService' is from 'package:culture_connect/services/ar_accessibility_service.dart' ('lib/services/ar_accessibility_service.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isHighContrastModeEnabled'.
      expect(service.isHighContrastModeEnabled, !initialValue);
                     ^^^^^^^^^^^^^^^^^^^^^^^^^
test/unit/services/ar_accessibility_service_test.dart:68:33: Error: Too few positional arguments: 1 required, 0 given.
      service.toggleScreenReader();
                                ^
test/unit/services/ar_accessibility_service_test.dart:82:34: Error: Too few positional arguments: 1 required, 0 given.
      service.toggleReducedMotion();
                                 ^
{"testID":147,"error":"Failed to load \"/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_accessibility_service_test.dart\": Compilation failed for testPath=/Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_accessibility_service_test.dart","stackTrace":"","isFailure":false,"type":"error","time":18597}
{"testID":147,"result":"error","skipped":false,"hidden":false,"type":"testDone","time":18598}
{"testID":128,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":18797}
{"group":{"id":156,"suiteID":127,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":7,"line":null,"column":null,"url":null},"type":"group","time":18797}
{"group":{"id":157,"suiteID":127,"parentID":156,"name":"ARLazyLoadingService Tests","metadata":{"skip":false,"skipReason":null},"testCount":7,"line":110,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_lazy_loading_service_test.dart"},"type":"group","time":18797}
{"test":{"id":158,"name":"ARLazyLoadingService Tests initializeARFeatures should complete successfully","suiteID":127,"groupIDs":[156,157],"metadata":{"skip":false,"skipReason":null},"line":111,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_lazy_loading_service_test.dart"},"type":"testStart","time":18797}
{"testID":158,"messageType":"print","message":"🚀 Starting AR features initialization","type":"print","time":18879}
{"testID":158,"messageType":"print","message":"❌ Error loading AR models: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":18916}
{"testID":158,"messageType":"print","message":"❌ Error loading AR textures: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":18919}
{"testID":158,"messageType":"print","message":"❌ Error during AR features initialization: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":18920}
{"testID":158,"error":"Expected: <true>\n  Actual: <false>\n","stackTrace":"package:matcher                                             expect\npackage:flutter_test/src/widget_tester.dart 480:18          expect\ntest/unit/services/ar_lazy_loading_service_test.dart 116:7  main.<fn>.<fn>\n","isFailure":true,"type":"error","time":18960}
{"testID":158,"error":"FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","stackTrace":"dart:async                                                           _Completer.completeError\npackage:culture_connect/services/ar_lazy_loading_service.dart 60:35  ARLazyLoadingService.initializeARFeatures\n===== asynchronous gap ===========================\ndart:async                                                           _CustomZone.registerBinaryCallback\npackage:culture_connect/services/ar_lazy_loading_service.dart 43:7   ARLazyLoadingService.initializeARFeatures\ntest/unit/services/ar_lazy_loading_service_test.dart 113:36          main.<fn>.<fn>\n","isFailure":false,"type":"error","time":18967}
{"testID":158,"result":"error","skipped":false,"hidden":false,"type":"testDone","time":18974}
{"test":{"id":159,"name":"ARLazyLoadingService Tests arFeaturesLoadingFuture should complete when initialization is done","suiteID":127,"groupIDs":[156,157],"metadata":{"skip":false,"skipReason":null},"line":120,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_lazy_loading_service_test.dart"},"type":"testStart","time":18975}
{"testID":159,"messageType":"print","message":"🚀 Starting AR features initialization","type":"print","time":18983}
{"testID":159,"messageType":"print","message":"❌ Error loading AR models: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":18989}
{"testID":159,"messageType":"print","message":"❌ Error loading AR textures: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":18990}
{"testID":159,"messageType":"print","message":"❌ Error during AR features initialization: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":18991}
{"testID":159,"error":"Bad state: Future already completed","stackTrace":"dart:async                                                           _Completer.completeError\npackage:culture_connect/services/ar_lazy_loading_service.dart 60:35  ARLazyLoadingService.initializeARFeatures\n","isFailure":false,"type":"error","time":19002}
{"testID":159,"result":"error","skipped":false,"hidden":false,"type":"testDone","time":19003}
{"test":{"id":160,"name":"ARLazyLoadingService Tests initializeARFeatures should handle errors gracefully","suiteID":127,"groupIDs":[156,157],"metadata":{"skip":false,"skipReason":null},"line":133,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_lazy_loading_service_test.dart"},"type":"testStart","time":19003}
{"testID":160,"messageType":"print","message":"🚀 Starting AR features initialization","type":"print","time":19009}
{"testID":160,"messageType":"print","message":"❌ Error loading AR models: PlatformException(error, Exception: Mock error, null, null)","type":"print","time":19014}
{"testID":160,"messageType":"print","message":"❌ Error loading AR textures: PlatformException(error, Exception: Mock error, null, null)","type":"print","time":19014}
{"testID":160,"messageType":"print","message":"❌ Error during AR features initialization: PlatformException(error, Exception: Mock error, null, null)","type":"print","time":19016}
{"testID":160,"error":"initializeARFeatures should handle errors gracefully: Bad state: Future already completed","stackTrace":"package:matcher                                             fail\ntest/unit/services/ar_lazy_loading_service_test.dart 150:9  main.<fn>.<fn>\n","isFailure":true,"type":"error","time":19024}
{"testID":160,"result":"failure","skipped":false,"hidden":false,"type":"testDone","time":19025}
{"test":{"id":161,"name":"ARLazyLoadingService Tests getARModelFilePath should return null for non-existent model","suiteID":127,"groupIDs":[156,157],"metadata":{"skip":false,"skipReason":null},"line":158,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_lazy_loading_service_test.dart"},"type":"testStart","time":19025}
{"testID":161,"messageType":"print","message":"🚀 Starting AR features initialization","type":"print","time":19032}
{"testID":161,"messageType":"print","message":"❌ Error loading AR models: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":19038}
{"testID":161,"messageType":"print","message":"❌ Error loading AR textures: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":19038}
{"testID":161,"messageType":"print","message":"❌ Error during AR features initialization: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":19039}
{"testID":161,"error":"Bad state: Future already completed","stackTrace":"dart:async                                                           _Completer.completeError\npackage:culture_connect/services/ar_lazy_loading_service.dart 60:35  ARLazyLoadingService.initializeARFeatures\n","isFailure":false,"type":"error","time":19049}
{"testID":161,"result":"error","skipped":false,"hidden":false,"type":"testDone","time":19051}
{"test":{"id":162,"name":"ARLazyLoadingService Tests getARTextureFilePath should return null for non-existent texture","suiteID":127,"groupIDs":[156,157],"metadata":{"skip":false,"skipReason":null},"line":176,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_lazy_loading_service_test.dart"},"type":"testStart","time":19051}
{"testID":162,"messageType":"print","message":"🚀 Starting AR features initialization","type":"print","time":19059}
{"testID":162,"messageType":"print","message":"❌ Error loading AR models: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":19067}
{"testID":162,"messageType":"print","message":"❌ Error loading AR textures: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":19068}
{"testID":162,"messageType":"print","message":"❌ Error during AR features initialization: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":19070}
{"testID":162,"error":"Bad state: Future already completed","stackTrace":"dart:async                                                           _Completer.completeError\npackage:culture_connect/services/ar_lazy_loading_service.dart 60:35  ARLazyLoadingService.initializeARFeatures\n","isFailure":false,"type":"error","time":19078}
{"testID":162,"result":"error","skipped":false,"hidden":false,"type":"testDone","time":19080}
{"test":{"id":163,"name":"ARLazyLoadingService Tests getARModelFilePath should return path for existing model","suiteID":127,"groupIDs":[156,157],"metadata":{"skip":false,"skipReason":null},"line":195,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_lazy_loading_service_test.dart"},"type":"testStart","time":19080}
{"testID":163,"messageType":"print","message":"🚀 Starting AR features initialization","type":"print","time":19088}
{"testID":163,"messageType":"print","message":"❌ Error loading AR models: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":19094}
{"testID":163,"messageType":"print","message":"❌ Error loading AR textures: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":19096}
{"testID":163,"messageType":"print","message":"❌ Error during AR features initialization: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":19096}
{"testID":163,"error":"Bad state: Future already completed","stackTrace":"dart:async                                                           _Completer.completeError\npackage:culture_connect/services/ar_lazy_loading_service.dart 60:35  ARLazyLoadingService.initializeARFeatures\n","isFailure":false,"type":"error","time":19103}
{"testID":163,"result":"error","skipped":false,"hidden":false,"type":"testDone","time":19105}
{"test":{"id":164,"name":"ARLazyLoadingService Tests getARTextureFilePath should return path for existing texture","suiteID":127,"groupIDs":[156,157],"metadata":{"skip":false,"skipReason":null},"line":216,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/ar_lazy_loading_service_test.dart"},"type":"testStart","time":19105}
{"testID":164,"messageType":"print","message":"🚀 Starting AR features initialization","type":"print","time":19111}
{"testID":164,"messageType":"print","message":"❌ Error loading AR models: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":19118}
{"testID":164,"messageType":"print","message":"❌ Error loading AR textures: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":19119}
{"testID":164,"messageType":"print","message":"❌ Error during AR features initialization: FileSystemException: Creation failed, path = '/mock' (OS Error: Read-only file system, errno = 30)","type":"print","time":19119}
{"testID":164,"error":"Bad state: Future already completed","stackTrace":"dart:async                                                           _Completer.completeError\npackage:culture_connect/services/ar_lazy_loading_service.dart 60:35  ARLazyLoadingService.initializeARFeatures\n","isFailure":false,"type":"error","time":19125}
{"testID":164,"result":"error","skipped":false,"hidden":false,"type":"testDone","time":19127}
{"testID":131,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":19592}
{"group":{"id":165,"suiteID":130,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":6,"line":null,"column":null,"url":null},"type":"group","time":19592}
{"test":{"id":166,"name":"(setUpAll)","suiteID":130,"groupIDs":[165],"metadata":{"skip":false,"skipReason":null},"line":12,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/booking_service_test.dart"},"type":"testStart","time":19592}
{"testID":166,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":19748}
{"group":{"id":167,"suiteID":130,"parentID":165,"name":"BookingService - Core Booking Methods","metadata":{"skip":false,"skipReason":null},"testCount":6,"line":32,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/booking_service_test.dart"},"type":"group","time":19748}
{"test":{"id":168,"name":"BookingService - Core Booking Methods createBooking should create a new booking with correct properties","suiteID":130,"groupIDs":[165,167],"metadata":{"skip":false,"skipReason":null},"line":33,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/booking_service_test.dart"},"type":"testStart","time":19749}
{"testID":168,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":19766}
{"test":{"id":169,"name":"BookingService - Core Booking Methods calculateTotalPrice should return correct price","suiteID":130,"groupIDs":[165,167],"metadata":{"skip":false,"skipReason":null},"line":85,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/booking_service_test.dart"},"type":"testStart","time":19766}
{"testID":169,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":19771}
{"test":{"id":170,"name":"BookingService - Core Booking Methods updateBookingStatus should update booking status correctly","suiteID":130,"groupIDs":[165,167],"metadata":{"skip":false,"skipReason":null},"line":123,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/booking_service_test.dart"},"type":"testStart","time":19771}
{"testID":170,"messageType":"print","message":"Offline mode: Loading bookings from cache","type":"print","time":20285}
{"testID":170,"error":"Bad state: No element","stackTrace":"dart:collection                                      ListBase.firstWhere\ntest/unit/services/booking_service_test.dart 170:39  main.<fn>.<fn>\n","isFailure":false,"type":"error","time":20312}
{"testID":170,"result":"error","skipped":false,"hidden":false,"type":"testDone","time":20313}
{"test":{"id":171,"name":"BookingService - Core Booking Methods cancelBooking should change booking status to cancelled","suiteID":130,"groupIDs":[165,167],"metadata":{"skip":false,"skipReason":null},"line":174,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/booking_service_test.dart"},"type":"testStart","time":20313}
{"testID":171,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":21120}
{"test":{"id":172,"name":"BookingService - Core Booking Methods requestRefund should change booking status to refunded","suiteID":130,"groupIDs":[165,167],"metadata":{"skip":false,"skipReason":null},"line":217,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/booking_service_test.dart"},"type":"testStart","time":21120}
{"testID":172,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":22930}
{"test":{"id":173,"name":"BookingService - Core Booking Methods getUpcomingBookings should only return confirmed future bookings","suiteID":130,"groupIDs":[165,167],"metadata":{"skip":false,"skipReason":null},"line":263,"column":5,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/booking_service_test.dart"},"type":"testStart","time":22931}
{"testID":173,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":23941}
{"test":{"id":174,"name":"(tearDownAll)","suiteID":130,"groupIDs":[165],"metadata":{"skip":false,"skipReason":null},"line":27,"column":3,"url":"file:///Users/<USER>/Desktop/CurrentProject/cultureConnect/culture_connect/test/unit/services/booking_service_test.dart"},"type":"testStart","time":23942}
{"testID":174,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":23949}
{"success":false,"type":"done","time":23972}
