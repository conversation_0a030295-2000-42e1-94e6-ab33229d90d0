/// Payment Simulation Configuration
/// 
/// This configuration controls whether the app uses simulated payment flows
/// for testing purposes or the actual production payment system.
/// 
/// IMPORTANT: This is for TESTING ONLY. Before production deployment:
/// 1. Set ENABLE_PAYMENT_SIMULATION = false
/// 2. Remove all simulation-related code
/// 3. Restore full ProductionPaymentScreen integration

class PaymentSimulationConfig {
  /// Feature flag to enable/disable payment simulation
  /// 
  /// TODO: PRODUCTION DEPLOYMENT - Set this to false before production release
  /// TODO: PRODUCTION DEPLOYMENT - Remove this entire configuration system
  /// TODO: PRODUCTION DEPLOYMENT - Restore direct ProductionPaymentScreen usage
  static const bool ENABLE_PAYMENT_SIMULATION = true;
  
  /// Simulation timing configuration (in milliseconds)
  /// These values mimic the real payment flow timing for realistic testing
  static const int PAYMENT_INITIALIZATION_DELAY = 1500;
  static const int PAYMENT_PROCESSING_DELAY = 3000;
  static const int PAYMENT_SUCCESS_DELAY = 1000;
  
  /// Simulation success rate (for testing different scenarios)
  /// Set to 1.0 for always successful payments during testing
  static const double SIMULATION_SUCCESS_RATE = 1.0;
  
  /// Debug logging for simulation mode
  static const bool ENABLE_SIMULATION_LOGGING = true;
  
  /// Helper method to check if simulation is enabled
  static bool get isSimulationEnabled => ENABLE_PAYMENT_SIMULATION;
  
  /// Helper method to log simulation events
  static void logSimulation(String message) {
    if (ENABLE_SIMULATION_LOGGING) {
      print('[PAYMENT_SIMULATION] $message');
    }
  }
}
