// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/screens/travel/flight/rn_seat_selection_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Flight result model matching React Native implementation
class FlightResult {
  final String id;
  final String airline;
  final String airlineCode;
  final String flightNumber;
  final String logo;
  final String departure;
  final String arrival;
  final String departureCode;
  final String arrivalCode;
  final String duration;
  final int stops;
  final String? stopLocation;
  final int price;
  final int originalPrice;
  final List<String> amenities;
  final double rating;
  final String aircraft;
  final String baggage;
  final bool isRecommended;
  final String flightClass;

  const FlightResult({
    required this.id,
    required this.airline,
    required this.airlineCode,
    required this.flightNumber,
    required this.logo,
    required this.departure,
    required this.arrival,
    required this.departureCode,
    required this.arrivalCode,
    required this.duration,
    required this.stops,
    this.stopLocation,
    required this.price,
    required this.originalPrice,
    required this.amenities,
    required this.rating,
    required this.aircraft,
    required this.baggage,
    required this.isRecommended,
    required this.flightClass,
  });
}

/// Mock flight results data matching React Native
const List<FlightResult> mockFlightResults = [
  FlightResult(
    id: "1",
    airline: "Japan Airlines",
    airlineCode: "JAL",
    flightNumber: "JL 784",
    logo: "🇯🇵",
    departure: "8:30",
    arrival: "14:45",
    departureCode: "LAX",
    arrivalCode: "NRT",
    duration: "14h 15m",
    stops: 1,
    stopLocation: "Tokyo NRT",
    price: 450,
    originalPrice: 520,
    amenities: ["wifi", "meals", "entertainment"],
    rating: 4.8,
    aircraft: "Boeing 787",
    baggage: "2 x 23kg",
    isRecommended: false,
    flightClass: "Economy",
  ),
  FlightResult(
    id: "2",
    airline: "United Airlines",
    airlineCode: "UA",
    flightNumber: "UA 35",
    logo: "🇺🇸",
    departure: "11:20",
    arrival: "18:35",
    departureCode: "LAX",
    arrivalCode: "NRT",
    duration: "13h 15m",
    stops: 0,
    price: 520,
    originalPrice: 580,
    amenities: ["wifi", "meals", "entertainment", "extra-legroom"],
    rating: 4.6,
    aircraft: "Airbus A350",
    baggage: "2 x 23kg",
    isRecommended: true,
    flightClass: "Economy",
  ),
  FlightResult(
    id: "3",
    airline: "ANA",
    airlineCode: "NH",
    flightNumber: "NH 175",
    logo: "✈️",
    departure: "18:15",
    arrival: "23:30",
    departureCode: "LAX",
    arrivalCode: "NRT",
    duration: "13h 15m",
    stops: 0,
    price: 485,
    originalPrice: 545,
    amenities: ["wifi", "meals", "entertainment"],
    rating: 4.9,
    aircraft: "Boeing 777",
    baggage: "2 x 23kg",
    isRecommended: false,
    flightClass: "Economy",
  ),
];

/// Pixel-perfect React Native FlightResultsScreen replica
class RNFlightResultsScreen extends ConsumerStatefulWidget {
  final String from;
  final String to;

  const RNFlightResultsScreen({
    super.key,
    required this.from,
    required this.to,
  });

  @override
  ConsumerState<RNFlightResultsScreen> createState() =>
      _RNFlightResultsScreenState();
}

class _RNFlightResultsScreenState extends ConsumerState<RNFlightResultsScreen> {
  String? _selectedFlightId;
  String? _expandedFlightId;
  String _selectedFilter = 'Best Value';

  final List<String> _filters = [
    'Best Value',
    'Cheapest',
    'Fastest',
    'Non-stop'
  ];

  void _handleFlightSelect(String flightId) {
    setState(() {
      _selectedFlightId = flightId;
      _expandedFlightId = _expandedFlightId == flightId ? null : flightId;
    });
  }

  void _handleSelectFlight() {
    if (_selectedFlightId != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) =>
              RNSeatSelectionScreen(flightId: _selectedFlightId!),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Column(
        children: [
          // Header
          _buildHeader(),

          // Content with SafeArea
          Expanded(
            child: SafeArea(
              top: false,
              child: Column(
                children: [
                  // Results Header
                  _buildResultsHeader(),

                  // Filter Chips
                  _buildFilterChips(),

                  // Flight Results
                  Expanded(
                    child: _buildFlightResults(),
                  ),

                  // Select Flight Button
                  if (_selectedFlightId != null) _buildSelectButton(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      // Complete background coverage
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withOpacity(0.8),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
          child: Row(
            children: [
              Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(22),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back_ios),
                  color: Colors.white,
                  iconSize: 20,
                ),
              ),
              Expanded(
                child: Column(
                  children: [
                    const Text(
                      'Available Flights',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                        letterSpacing: -0.5,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${widget.from} → ${widget.to}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                        letterSpacing: -0.2,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 56), // Balance the back button
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildResultsHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '${mockFlightResults.length} flights found',
            style: const TextStyle(
              fontSize: AppTheme.fontSizeMd,
              fontWeight: AppTheme.fontWeightSemibold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const Text(
            'Sorted by best value',
            style: TextStyle(
              fontSize: AppTheme.fontSizeSm,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _filters.length,
        itemBuilder: (context, index) {
          final filter = _filters[index];
          final isSelected = _selectedFilter == filter;
          return Container(
            margin: const EdgeInsets.only(right: 12),
            child: FilterChip(
              label: Text(filter),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedFilter = filter;
                });
              },
              backgroundColor: AppTheme.backgroundColor,
              selectedColor: AppTheme.primaryColor,
              labelStyle: TextStyle(
                color: isSelected
                    ? AppTheme.backgroundColor
                    : AppTheme.textPrimaryColor,
                fontWeight: AppTheme.fontWeightMedium,
              ),
              side: BorderSide(
                color:
                    isSelected ? AppTheme.primaryColor : AppTheme.borderColor,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFlightResults() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: mockFlightResults.length,
      itemBuilder: (context, index) {
        final flight = mockFlightResults[index];
        return _buildFlightCard(flight);
      },
    );
  }

  Widget _buildSelectButton() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: Container(
        width: double.infinity,
        height: 56,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.primaryColor,
              AppTheme.primaryColor.withOpacity(0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryColor.withOpacity(0.4),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: _handleSelectFlight,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.transparent,
            foregroundColor: Colors.white,
            shadowColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            elevation: 0,
          ),
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.flight_takeoff,
                size: 20,
                color: Colors.white,
              ),
              SizedBox(width: 8),
              Text(
                'Select Flight',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  letterSpacing: -0.2,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFlightCard(FlightResult flight) {
    final isSelected = _selectedFlightId == flight.id;
    final isExpanded = _expandedFlightId == flight.id;

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Column(
        children: [
          GestureDetector(
            onTap: () => _handleFlightSelect(flight.id),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppTheme.backgroundColor,
                borderRadius: BorderRadius.circular(18),
                border: Border.all(
                  color: isSelected
                      ? AppTheme.primaryColor
                      : AppTheme.borderColor.withOpacity(0.3),
                  width: isSelected ? 2.5 : 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: isSelected
                        ? AppTheme.primaryColor.withOpacity(0.15)
                        : Colors.black.withOpacity(0.08),
                    blurRadius: isSelected ? 20 : 15,
                    offset: Offset(0, isSelected ? 6 : 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Enhanced Best Value Badge
                  if (flight.isRecommended)
                    Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  AppTheme.primaryColor,
                                  AppTheme.primaryColor.withOpacity(0.8),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: AppTheme.primaryColor.withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.star_rounded,
                                    size: 14, color: Colors.white),
                                SizedBox(width: 6),
                                Text(
                                  'Best Value',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                    letterSpacing: -0.1,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Spacer(),
                        ],
                      ),
                    ),

                  // Enhanced Airline Header
                  Row(
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              AppTheme.primaryColor.withOpacity(0.15),
                              AppTheme.primaryColor.withOpacity(0.05),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(14),
                          border: Border.all(
                            color: AppTheme.primaryColor.withOpacity(0.2),
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            flight.logo,
                            style: const TextStyle(fontSize: 22),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              flight.airline,
                              style: const TextStyle(
                                fontSize: 17,
                                fontWeight: FontWeight.w600,
                                color: AppTheme.textPrimaryColor,
                                letterSpacing: -0.3,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              flight.flightNumber,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: AppTheme.textSecondaryColor,
                                letterSpacing: -0.1,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          if (flight.originalPrice > flight.price) ...[
                            Text(
                              '\$${flight.originalPrice}',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: AppTheme.textSecondaryColor
                                    .withOpacity(0.7),
                                decoration: TextDecoration.lineThrough,
                                decorationColor: AppTheme.textSecondaryColor
                                    .withOpacity(0.5),
                              ),
                            ),
                            const SizedBox(height: 2),
                          ],
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: AppTheme.primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              '\$${flight.price}',
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w700,
                                color: AppTheme.primaryColor,
                                letterSpacing: -0.5,
                              ),
                            ),
                          ),
                          const SizedBox(height: 2),
                          const Text(
                            'per person',
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Flight Route
                  _buildFlightRoute(flight),

                  const SizedBox(height: 16),

                  // Flight Features
                  _buildFlightFeatures(flight),

                  const SizedBox(height: 16),

                  // Enhanced Expand Button
                  Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: AppTheme.primaryColor.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            isExpanded ? 'Hide Details' : 'View Details',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.primaryColor,
                              letterSpacing: -0.1,
                            ),
                          ),
                          const SizedBox(width: 6),
                          AnimatedRotation(
                            turns: isExpanded ? 0.5 : 0,
                            duration: const Duration(milliseconds: 200),
                            child: Icon(
                              Icons.keyboard_arrow_down,
                              color: AppTheme.primaryColor,
                              size: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Expanded Details
          if (isExpanded) _buildExpandedDetails(flight),
        ],
      ),
    );
  }

  Widget _buildFlightRoute(FlightResult flight) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.borderColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Departure
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  flight.departure,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: AppTheme.textPrimaryColor,
                    letterSpacing: -0.5,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  flight.departureCode,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textSecondaryColor,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),

          // Enhanced Route Visual
          Expanded(
            flex: 2,
            child: Column(
              children: [
                Stack(
                  alignment: Alignment.center,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor.withOpacity(0.1),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(Icons.flight_takeoff,
                              size: 14, color: AppTheme.primaryColor),
                        ),
                        Expanded(
                          child: Container(
                            height: 3,
                            margin: const EdgeInsets.symmetric(horizontal: 8),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  AppTheme.primaryColor,
                                  AppTheme.primaryColor.withOpacity(0.6),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor.withOpacity(0.1),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(Icons.flight_land,
                              size: 14, color: AppTheme.primaryColor),
                        ),
                      ],
                    ),
                    if (flight.stops > 0)
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: AppTheme.backgroundColor,
                            width: 2,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  flight.duration,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                    letterSpacing: -0.1,
                  ),
                ),
                const SizedBox(height: 2),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: flight.stops == 0
                        ? Colors.green.withOpacity(0.1)
                        : AppTheme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    flight.stops == 0
                        ? 'Direct'
                        : '${flight.stops} stop${flight.stops > 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      color: flight.stops == 0
                          ? Colors.green.shade700
                          : AppTheme.primaryColor,
                      letterSpacing: 0.2,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Arrival
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  flight.arrival,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: AppTheme.textPrimaryColor,
                    letterSpacing: -0.5,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  flight.arrivalCode,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textSecondaryColor,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFlightFeatures(FlightResult flight) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildFeatureItem(
              Icons.airline_seat_recline_normal, 'Class', flight.flightClass),
          _buildFeatureItem(
              Icons.airplanemode_active, 'Aircraft', flight.aircraft),
          _buildFeatureItem(Icons.star_rounded, 'Rating', '${flight.rating}'),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: AppTheme.borderColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 16,
            color: AppTheme.primaryColor.withOpacity(0.7),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: AppTheme.textSecondaryColor,
              letterSpacing: 0.2,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
              letterSpacing: -0.1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpandedDetails(FlightResult flight) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: const Color(
            0xFFF8F9FA), // Subtle off-white background for elegant appearance
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.flight_takeoff,
                  size: 18,
                  color: AppTheme.primaryColor,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Flight Details',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  color: AppTheme.textPrimaryColor,
                  letterSpacing: -0.4,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Flight Journey Section
          _buildFlightJourney(flight),

          // Divider
          Container(
            margin: const EdgeInsets.symmetric(vertical: 20),
            height: 1,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  AppTheme.borderColor.withOpacity(0.5),
                  Colors.transparent,
                ],
              ),
            ),
          ),

          // Flight Information Section
          _buildFlightInformation(flight),

          // Divider
          Container(
            margin: const EdgeInsets.symmetric(vertical: 20),
            height: 1,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  AppTheme.borderColor.withOpacity(0.5),
                  Colors.transparent,
                ],
              ),
            ),
          ),

          // Included Services Section
          _buildIncludedServices(flight),
        ],
      ),
    );
  }

  Widget _buildFlightJourney(FlightResult flight) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Flight Journey',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
            letterSpacing: -0.2,
          ),
        ),
        const SizedBox(height: 16),

        // Journey Timeline
        Row(
          children: [
            // Departure
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    flight.departure,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                      color: AppTheme.textPrimaryColor,
                      letterSpacing: -0.5,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    flight.departureCode,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryColor,
                      letterSpacing: 0.5,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'Los Angeles',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textSecondaryColor.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),

            // Flight Path Visual
            Expanded(
              flex: 2,
              child: Column(
                children: [
                  // Flight path with stops
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              color: AppTheme.primaryColor,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: AppTheme.backgroundColor,
                                width: 2,
                              ),
                            ),
                          ),
                          Expanded(
                            child: Container(
                              height: 3,
                              margin: const EdgeInsets.symmetric(horizontal: 8),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    AppTheme.primaryColor,
                                    AppTheme.primaryColor.withOpacity(0.6),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          ),
                          Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              color: AppTheme.primaryColor,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: AppTheme.backgroundColor,
                                width: 2,
                              ),
                            ),
                          ),
                        ],
                      ),
                      // Stop indicator (if applicable)
                      if (flight.stops > 0)
                        Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            color: Colors.orange,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: AppTheme.backgroundColor,
                              width: 3,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Duration and stops info
                  Text(
                    flight.duration,
                    style: const TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: flight.stops == 0
                          ? Colors.green.withOpacity(0.1)
                          : Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      flight.stops == 0
                          ? 'Direct'
                          : '${flight.stops} stop${flight.stops > 1 ? 's' : ''}',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: flight.stops == 0
                            ? Colors.green.shade700
                            : Colors.orange.shade700,
                        letterSpacing: 0.2,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Arrival
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    flight.arrival,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                      color: AppTheme.textPrimaryColor,
                      letterSpacing: -0.5,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    flight.arrivalCode,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryColor,
                      letterSpacing: 0.5,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'Tokyo',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textSecondaryColor.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        // Layover information (if applicable)
        if (flight.stops > 0 && flight.stopLocation != null) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.orange.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 16,
                  color: Colors.orange.shade700,
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Text(
                    'Layover in ${flight.stopLocation}',
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: Colors.orange.shade700,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildFlightInformation(FlightResult flight) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Flight Information',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
            letterSpacing: -0.2,
          ),
        ),
        const SizedBox(height: 16),

        // Information Grid
        Row(
          children: [
            Expanded(
              child: _buildInfoCard(
                Icons.airplanemode_active,
                'Aircraft',
                flight.aircraft,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildInfoCard(
                Icons.luggage,
                'Baggage',
                flight.baggage,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildInfoCard(
                Icons.schedule,
                'Duration',
                flight.duration,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildInfoCard(
                Icons.airline_seat_recline_normal,
                'Class',
                flight.flightClass,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoCard(IconData icon, String label, String value) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(14),
        border: Border.all(
          color: AppTheme.borderColor.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              size: 18,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: AppTheme.textSecondaryColor.withOpacity(0.8),
              letterSpacing: 0.2,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
              letterSpacing: -0.1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIncludedServices(FlightResult flight) {
    // Map amenities to service information
    final Map<String, Map<String, dynamic>> serviceMapping = {
      'wifi': {'icon': Icons.wifi, 'label': 'Free WiFi'},
      'meals': {'icon': Icons.restaurant, 'label': 'Meals Included'},
      'entertainment': {'icon': Icons.tv, 'label': 'In-flight Entertainment'},
      'extra-legroom': {
        'icon': Icons.airline_seat_legroom_extra,
        'label': 'Extra Legroom'
      },
      'power': {'icon': Icons.power, 'label': 'Power Outlets'},
      'blanket': {'icon': Icons.bed, 'label': 'Blanket & Pillow'},
    };

    // Filter available services based on flight amenities
    final availableServices = flight.amenities
        .where((amenity) => serviceMapping.containsKey(amenity))
        .map((amenity) => serviceMapping[amenity]!)
        .toList();

    // Add default services if none are mapped
    if (availableServices.isEmpty) {
      availableServices.addAll([
        {'icon': Icons.wifi, 'label': 'Free WiFi'},
        {'icon': Icons.restaurant, 'label': 'Meals Included'},
        {'icon': Icons.tv, 'label': 'In-flight Entertainment'},
      ]);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Included Services',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
            letterSpacing: -0.2,
          ),
        ),
        const SizedBox(height: 16),

        // Services List
        ...availableServices
            .map((service) => _buildServiceItem(
                  service['icon'] as IconData,
                  service['label'] as String,
                ))
            .toList(),
      ],
    );
  }

  Widget _buildServiceItem(IconData icon, String label) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(14),
        border: Border.all(
          color: Colors.green.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              size: 18,
              color: Colors.green.shade700,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimaryColor,
                letterSpacing: -0.1,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.check,
              size: 16,
              color: Colors.green.shade700,
            ),
          ),
        ],
      ),
    );
  }
}
