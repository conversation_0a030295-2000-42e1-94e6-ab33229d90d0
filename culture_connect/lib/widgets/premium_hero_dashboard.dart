import 'package:flutter/material.dart';
import 'package:culture_connect/models/premium_booking.dart';
import 'package:culture_connect/design_system/premium_design_tokens.dart';

/// Premium hero dashboard with stunning visual design and animated statistics
class PremiumHeroDashboard extends StatefulWidget {
  final PremiumBookingStats stats;
  final bool isLoading;
  final VoidCallback? onRefresh;

  const PremiumHeroDashboard({
    super.key,
    required this.stats,
    this.isLoading = false,
    this.onRefresh,
  });

  @override
  State<PremiumHeroDashboard> createState() => _PremiumHeroDashboardState();
}

class _PremiumHeroDashboardState extends State<PremiumHeroDashboard>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    _mainController = AnimationController(
      duration: PremiumDesignTokens.animationXSlow,
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.2, 0.8, curve: Curves.easeOutCubic),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.4, 1.0, curve: Curves.elasticOut),
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _mainController.forward();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _mainController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return _buildLoadingState();
    }

    return AnimatedBuilder(
      animation: _mainController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: _buildHeroContent(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return Container(
      height: 320, // Updated to match optimized compact hero content height
      margin: const EdgeInsets.all(PremiumDesignTokens.spacing16),
      decoration: BoxDecoration(
        gradient: PremiumDesignTokens.heroGradient,
        borderRadius: BorderRadius.circular(PremiumDesignTokens.radiusXXLarge),
        boxShadow: PremiumDesignTokens.shadowXLarge,
      ),
      child: const Center(
        child: CircularProgressIndicator(
          valueColor:
              AlwaysStoppedAnimation<Color>(PremiumDesignTokens.neutralWhite),
          strokeWidth: 3,
        ),
      ),
    );
  }

  Widget _buildHeroContent() {
    return Container(
      height:
          320, // Optimized compact design with adequate space for all content
      margin: const EdgeInsets.all(PremiumDesignTokens.spacing16),
      decoration: BoxDecoration(
        gradient: PremiumDesignTokens.heroGradient,
        borderRadius: BorderRadius.circular(PremiumDesignTokens.radiusXXLarge),
        boxShadow: PremiumDesignTokens.shadowXLarge,
      ),
      child: Stack(
        children: [
          _buildBackgroundPattern(),
          _buildContent(),
          _buildRefreshButton(),
        ],
      ),
    );
  }

  Widget _buildBackgroundPattern() {
    return Positioned.fill(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(PremiumDesignTokens.radiusXXLarge),
        child: Stack(
          children: [
            // Animated circles
            Positioned(
              top: -50,
              right: -50,
              child: AnimatedBuilder(
                animation: _pulseController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: 150,
                      height: 150,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: PremiumDesignTokens.neutralWhite
                            .withValues(alpha: 0.1),
                      ),
                    ),
                  );
                },
              ),
            ),
            Positioned(
              bottom: -30,
              left: -30,
              child: AnimatedBuilder(
                animation: _pulseController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: 2.0 - _pulseAnimation.value,
                    child: Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: PremiumDesignTokens.neutralWhite
                            .withValues(alpha: 0.08),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(PremiumDesignTokens.spacing20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: PremiumDesignTokens.spacing16),
          // Fixed height for stats grid to prevent overflow during refresh animations
          SizedBox(
            height:
                230, // Optimized for compact cards while ensuring no clipping
            child: _buildStatsGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Text(
      'Your Travel Journey',
      style: PremiumDesignTokens.displaySmall.copyWith(
        color: PremiumDesignTokens.neutralWhite,
        fontWeight: FontWeight.w800,
      ),
    );
  }

  Widget _buildStatsGrid() {
    return GridView.count(
      crossAxisCount: 2,
      mainAxisSpacing: PremiumDesignTokens.spacing12,
      crossAxisSpacing: PremiumDesignTokens.spacing12,
      childAspectRatio:
          1.4, // Optimized compact cards with adequate space for all content
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true, // Prevents overflow by using only required space
      children: [
        _buildStatCard(
          icon: Icons.flight_takeoff_rounded,
          value: widget.stats.completedTrips.toString(),
          label: 'Trips Completed',
          delay: 0,
        ),
        _buildStatCard(
          icon: Icons.public_rounded,
          value: widget.stats.countriesVisited.toString(),
          label: 'Countries Visited',
          delay: 100,
        ),
        _buildStatCard(
          icon: Icons.star_rounded,
          value: widget.stats.formattedAverageRating,
          label: 'Average Rating',
          delay: 200,
        ),
        _buildStatCard(
          icon: Icons.savings_rounded,
          value: widget.stats.formattedTotalSaved,
          label: 'Total Saved',
          delay: 300,
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String value,
    required String label,
    required int delay,
  }) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 800 + delay),
      tween: Tween<double>(begin: 0.0, end: 1.0),
      curve: Curves.elasticOut,
      builder: (context, animationValue, child) {
        return Transform.scale(
          scale: 0.5 + (0.5 * animationValue),
          child: Opacity(
            opacity: animationValue.clamp(0.0, 1.0),
            child: Container(
              padding: const EdgeInsets.all(10), // Optimized compact padding
              decoration: BoxDecoration(
                color: PremiumDesignTokens.neutralWhite.withValues(alpha: 0.15),
                borderRadius:
                    BorderRadius.circular(PremiumDesignTokens.radiusLarge),
                border: Border.all(
                  color:
                      PremiumDesignTokens.neutralWhite.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Icon(
                        icon,
                        color: PremiumDesignTokens.neutralWhite,
                        size: 22, // Optimized for compact card design
                      ),
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: PremiumDesignTokens.accentTeal,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: PremiumDesignTokens.accentTeal
                                  .withValues(alpha: 0.5),
                              blurRadius: 8,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6), // Optimized compact spacing
                  TweenAnimationBuilder<double>(
                    duration: Duration(milliseconds: 1200 + delay),
                    tween: Tween<double>(
                      begin: 0.0,
                      end: double.tryParse(
                              value.replaceAll(RegExp(r'[^\d.]'), '')) ??
                          0.0,
                    ),
                    curve: Curves.easeOutCubic,
                    builder: (context, animatedValue, child) {
                      String displayValue;
                      if (label.contains('Rating')) {
                        displayValue = animatedValue.toStringAsFixed(1);
                      } else if (label.contains('Saved')) {
                        displayValue = '\$${animatedValue.toInt()}';
                      } else {
                        displayValue = animatedValue.toInt().toString();
                      }

                      return Text(
                        displayValue,
                        style: PremiumDesignTokens.headlineLarge.copyWith(
                          color: PremiumDesignTokens.neutralWhite,
                          fontWeight: FontWeight.w800,
                          height: 1.0,
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 6), // Optimized compact spacing
                  Text(
                    label,
                    style: PremiumDesignTokens.bodySmall.copyWith(
                      color: PremiumDesignTokens.neutralWhite
                          .withValues(alpha: 0.8),
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRefreshButton() {
    if (widget.onRefresh == null) return const SizedBox.shrink();

    return Positioned(
      top: PremiumDesignTokens
          .spacing24, // Increased spacing for better visual separation
      right:
          PremiumDesignTokens.spacing24, // Increased spacing for cleaner layout
      child: GestureDetector(
        onTap: widget.onRefresh,
        child: const Icon(
          Icons.refresh_rounded,
          color: PremiumDesignTokens.neutralWhite,
          size: 20,
        ),
      ),
    );
  }
}
