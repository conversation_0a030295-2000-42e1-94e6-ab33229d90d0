import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:culture_connect/models/premium_booking.dart';
import 'package:culture_connect/design_system/premium_design_tokens.dart';

/// Hotel-specific booking layout with amenities, room details, check-in/out
class HotelBookingLayout extends StatefulWidget {
  final PremiumBooking booking;

  const HotelBookingLayout({
    super.key,
    required this.booking,
  });

  @override
  State<HotelBookingLayout> createState() => _HotelBookingLayoutState();
}

class _HotelBookingLayoutState extends State<HotelBookingLayout>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _startAnimations() {
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildCheckInOutSection(),
            _buildRoomDetailsSection(),
            _buildAmenitiesSection(),
            _buildHotelInfoSection(),
            _buildGallerySection(),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildCheckInOutSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.hotel,
                color: const Color(0xFF06B6D4),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Stay Details',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildCheckInOutCard(
                  icon: Icons.login,
                  title: 'Check-in',
                  date: 'Dec 15, 2024',
                  time: '3:00 PM',
                  color: const Color(0xFF10B981),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildCheckInOutCard(
                  icon: Icons.logout,
                  title: 'Check-out',
                  date: 'Dec 18, 2024',
                  time: '11:00 AM',
                  color: const Color(0xFFEF4444),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF06B6D4).withAlpha(26),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF06B6D4).withAlpha(51),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.nights_stay,
                  color: const Color(0xFF06B6D4),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '3 Nights Stay',
                        style: PremiumDesignTokens.bodyLarge.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${widget.booking.participants} ${widget.booking.participants == 1 ? 'Guest' : 'Guests'}',
                        style: PremiumDesignTokens.bodyMedium.copyWith(
                          color: PremiumDesignTokens.neutralGray600,
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    _handleEarlyCheckIn();
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF06B6D4),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'Early Check-in',
                      style: PremiumDesignTokens.labelMedium.copyWith(
                        color: PremiumDesignTokens.neutralWhite,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoomDetailsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.bed,
                color: const Color(0xFFF59E0B),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Room Details',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFF59E0B).withAlpha(26),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFFF59E0B).withAlpha(51),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: const Color(0xFFF59E0B),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: Text(
                          '512',
                          style: PremiumDesignTokens.headlineSmall.copyWith(
                            color: PremiumDesignTokens.neutralWhite,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Deluxe Ocean View Suite',
                            style: PremiumDesignTokens.bodyLarge.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'King Bed • 45 m² • Ocean View',
                            style: PremiumDesignTokens.bodyMedium.copyWith(
                              color: PremiumDesignTokens.neutralGray600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildRoomFeature(
                        icon: Icons.wifi,
                        label: 'Free WiFi',
                      ),
                    ),
                    Expanded(
                      child: _buildRoomFeature(
                        icon: Icons.ac_unit,
                        label: 'AC',
                      ),
                    ),
                    Expanded(
                      child: _buildRoomFeature(
                        icon: Icons.balcony,
                        label: 'Balcony',
                      ),
                    ),
                    Expanded(
                      child: _buildRoomFeature(
                        icon: Icons.room_service,
                        label: 'Room Service',
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmenitiesSection() {
    final amenities = [
      {
        'icon': Icons.pool,
        'name': 'Swimming Pool',
        'color': const Color(0xFF06B6D4)
      },
      {
        'icon': Icons.fitness_center,
        'name': 'Fitness Center',
        'color': const Color(0xFF10B981)
      },
      {
        'icon': Icons.spa,
        'name': 'Spa & Wellness',
        'color': const Color(0xFF8B5CF6)
      },
      {
        'icon': Icons.restaurant,
        'name': 'Restaurant',
        'color': const Color(0xFFEF4444)
      },
      {
        'icon': Icons.local_bar,
        'name': 'Bar & Lounge',
        'color': const Color(0xFFF59E0B)
      },
      {
        'icon': Icons.business_center,
        'name': 'Business Center',
        'color': const Color(0xFF6B7280)
      },
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.star,
                color: const Color(0xFF8B5CF6),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Hotel Amenities',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 1.2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: amenities.length,
            itemBuilder: (context, index) {
              final amenity = amenities[index];
              return _buildAmenityCard(
                icon: amenity['icon'] as IconData,
                name: amenity['name'] as String,
                color: amenity['color'] as Color,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildHotelInfoSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: const Color(0xFF10B981),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Hotel Information',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoRow(
            icon: Icons.location_on,
            title: 'Address',
            value: '123 Ocean Drive, Miami Beach, FL 33139',
            color: const Color(0xFFEF4444),
          ),
          const SizedBox(height: 12),
          _buildInfoRow(
            icon: Icons.phone,
            title: 'Phone',
            value: '+****************',
            color: const Color(0xFF06B6D4),
            onTap: _callHotel,
          ),
          const SizedBox(height: 12),
          _buildInfoRow(
            icon: Icons.email,
            title: 'Email',
            value: '<EMAIL>',
            color: const Color(0xFF8B5CF6),
            onTap: _emailHotel,
          ),
          const SizedBox(height: 12),
          _buildInfoRow(
            icon: Icons.star,
            title: 'Rating',
            value:
                '${widget.booking.rating} ⭐ (${widget.booking.reviewCount} reviews)',
            color: const Color(0xFFF59E0B),
          ),
        ],
      ),
    );
  }

  Widget _buildGallerySection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.photo_library,
                color: const Color(0xFFEF4444),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Photo Gallery',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  _viewAllPhotos();
                },
                child: Text(
                  'View All',
                  style: PremiumDesignTokens.labelMedium.copyWith(
                    color: const Color(0xFFEF4444),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: widget.booking.imageGallery.length,
              itemBuilder: (context, index) {
                return Container(
                  width: 160,
                  margin: EdgeInsets.only(
                      right: index < widget.booking.imageGallery.length - 1
                          ? 12
                          : 0),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    image: DecorationImage(
                      image: NetworkImage(widget.booking.imageGallery[index]),
                      fit: BoxFit.cover,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCheckInOutCard({
    required IconData icon,
    required String title,
    required String date,
    required String time,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withAlpha(51),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: PremiumDesignTokens.labelMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            date,
            style: PremiumDesignTokens.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            time,
            style: PremiumDesignTokens.bodySmall.copyWith(
              color: PremiumDesignTokens.neutralGray600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoomFeature({
    required IconData icon,
    required String label,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: const Color(0xFFF59E0B),
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: PremiumDesignTokens.labelSmall.copyWith(
            color: PremiumDesignTokens.neutralGray600,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildAmenityCard({
    required IconData icon,
    required String name,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withAlpha(51),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Flexible(
            child: Text(
              name,
              style: PremiumDesignTokens.labelSmall.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap != null
          ? () {
              HapticFeedback.lightImpact();
              onTap();
            }
          : null,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withAlpha(26),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color.withAlpha(51),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: color,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: PremiumDesignTokens.labelSmall.copyWith(
                      color: PremiumDesignTokens.neutralGray600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    value,
                    style: PremiumDesignTokens.bodyMedium.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            if (onTap != null)
              Icon(
                Icons.arrow_forward_ios,
                color: color,
                size: 16,
              ),
          ],
        ),
      ),
    );
  }

  void _handleEarlyCheckIn() {
    // TODO: Implement early check-in request
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Early check-in requested...'),
        backgroundColor: const Color(0xFF06B6D4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _callHotel() {
    // TODO: Implement phone call
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Calling hotel...'),
        backgroundColor: const Color(0xFF06B6D4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _emailHotel() {
    // TODO: Implement email
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Opening email...'),
        backgroundColor: const Color(0xFF8B5CF6),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _viewAllPhotos() {
    // TODO: Implement photo gallery
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Opening photo gallery...'),
        backgroundColor: const Color(0xFFEF4444),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
