import React from "react";
import { StyleSheet, Text, Pressable, ActivityIndicator, View } from "react-native";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: "primary" | "secondary" | "outline";
  size?: "small" | "medium" | "large";
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  fullWidth?: boolean;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = "primary",
  size = "medium",
  disabled = false,
  loading = false,
  icon,
  fullWidth = false,
}) => {
  const getContainerStyle = () => {
    return [
      styles.container,
      variant === "primary" && styles.primaryContainer,
      variant === "secondary" && styles.secondaryContainer,
      variant === "outline" && styles.outlineContainer,
      size === "small" && styles.smallContainer,
      size === "medium" && styles.mediumContainer,
      size === "large" && styles.largeContainer,
      fullWidth && styles.fullWidth,
      disabled && styles.disabledContainer,
    ].filter(Boolean);
  };
  
  const getTextStyle = () => {
    return [
      styles.text,
      variant === "primary" && styles.primaryText,
      variant === "secondary" && styles.secondaryText,
      variant === "outline" && styles.outlineText,
      size === "small" && styles.smallText,
      size === "large" && styles.largeText,
      disabled && styles.disabledText,
    ].filter(Boolean);
  };

  return (
    <Pressable
      style={getContainerStyle()}
      onPress={onPress}
      disabled={disabled || loading}
      android_ripple={{ color: "rgba(0,0,0,0.1)" }}
    >
      {loading ? (
        <ActivityIndicator
          color={variant === "outline" ? colors.primary : colors.white}
          size="small"
        />
      ) : (
        <View style={styles.contentContainer}>
          {icon && <View style={styles.iconContainer}>{icon}</View>}
          <Text style={getTextStyle()}>{title}</Text>
        </View>
      )}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  primaryContainer: {
    backgroundColor: "#1a1a2e",
  },
  secondaryContainer: {
    backgroundColor: colors.secondary,
  },
  outlineContainer: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: colors.primary,
  },
  mediumContainer: {
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.lg,
  },
  smallContainer: {
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.md,
  },
  largeContainer: {
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
  },
  fullWidth: {
    width: "100%",
  },
  disabledContainer: {
    opacity: 0.5,
  },
  contentContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  iconContainer: {
    marginRight: spacing.xs,
  },
  text: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium as any,
    textAlign: "center",
  },
  primaryText: {
    color: colors.white,
  },
  secondaryText: {
    color: colors.white,
  },
  outlineText: {
    color: colors.primary,
  },
  smallText: {
    fontSize: typography.sizes.sm,
  },
  largeText: {
    fontSize: typography.sizes.lg,
  },
  disabledText: {
    opacity: 0.8,
  },
});