import React, { useState } from "react";
import { StyleSheet, View, Text, ScrollView, Pressable, Animated } from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { ArrowLeft, Plane, Clock, Wifi, Coffee, Star, Shield, Zap, ChevronDown, ChevronUp, MapPin, Calendar, Users, PlaneTakeoff, PlaneLanding } from "lucide-react-native";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { Button } from "@/components/Button";

const flightResults = [
  {
    id: "1",
    airline: "Japan Airlines",
    airlineCode: "JAL",
    flightNumber: "JL 784",
    logo: "🇯🇵",
    departure: "8:30",
    arrival: "14:45",
    departureCode: "LAX",
    arrivalCode: "NRT",
    duration: "14h 15m",
    stops: 1,
    stopLocation: "Tokyo NRT",
    price: 450,
    originalPrice: 520,
    amenities: ["wifi", "meals", "entertainment"],
    rating: 4.8,
    aircraft: "Boeing 787",
    baggage: "2 x 23kg",
    isRecommended: false,
    class: "Economy",
  },
  {
    id: "2",
    airline: "United Airlines",
    airlineCode: "UA",
    flightNumber: "UA 35",
    logo: "🇺🇸",
    departure: "11:20",
    arrival: "18:35",
    departureCode: "LAX",
    arrivalCode: "NRT",
    duration: "13h 15m",
    stops: 0,
    stopLocation: null,
    price: 520,
    originalPrice: 580,
    amenities: ["wifi", "meals", "entertainment", "extra-legroom"],
    rating: 4.6,
    aircraft: "Airbus A350",
    baggage: "2 x 23kg",
    isRecommended: true,
    class: "Economy",
  },
  {
    id: "3",
    airline: "ANA",
    airlineCode: "NH",
    flightNumber: "NH 175",
    logo: "✈️",
    departure: "18:15",
    arrival: "23:30",
    departureCode: "LAX",
    arrivalCode: "NRT",
    duration: "13h 15m",
    stops: 0,
    stopLocation: null,
    price: 485,
    originalPrice: 545,
    amenities: ["wifi", "meals", "entertainment"],
    rating: 4.9,
    aircraft: "Boeing 777",
    baggage: "2 x 23kg",
    isRecommended: false,
    class: "Economy",
  },
];

export default function FlightResultsScreen() {
  const router = useRouter();
  const { from, to } = useLocalSearchParams();
  const [selectedFlight, setSelectedFlight] = useState<string | null>(null);
  const [expandedFlight, setExpandedFlight] = useState<string | null>(null);

  const handleFlightSelect = (flightId: string) => {
    setSelectedFlight(flightId);
    setExpandedFlight(expandedFlight === flightId ? null : flightId);
  };

  const handleSelectFlight = () => {
    if (selectedFlight) {
      router.push(`/booking/seat-selection?flightId=${selectedFlight}`);
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <View style={styles.header}>
        <Pressable style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.white} />
        </Pressable>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Available Flights</Text>
          <Text style={styles.headerSubtitle}>{from} → {to}</Text>
        </View>
        <View style={{ width: 24 }} />
      </View>

      <View style={styles.resultsHeader}>
        <Text style={styles.resultsCount}>3 flights found</Text>
        <Text style={styles.resultsSubtext}>Sorted by best value</Text>
      </View>

      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.filterScrollContent}>
          <Pressable style={[styles.filterChip, styles.filterChipActive]}>
            <Text style={[styles.filterText, styles.filterTextActive]}>Best Value</Text>
          </Pressable>
          <Pressable style={styles.filterChip}>
            <Text style={styles.filterText}>Cheapest</Text>
          </Pressable>
          <Pressable style={styles.filterChip}>
            <Text style={styles.filterText}>Fastest</Text>
          </Pressable>
          <Pressable style={styles.filterChip}>
            <Text style={styles.filterText}>Non-stop</Text>
          </Pressable>
        </ScrollView>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {flightResults.map((flight) => (
          <View key={flight.id} style={styles.flightCardContainer}>
            <Pressable
              style={[
                styles.flightCard,
                selectedFlight === flight.id && styles.flightCardSelected
              ]}
              onPress={() => handleFlightSelect(flight.id)}
            >
              {flight.isRecommended && (
                <View style={styles.recommendedBadge}>
                  <Star size={12} color={colors.white} fill={colors.white} />
                  <Text style={styles.recommendedText}>Best Value</Text>
                </View>
              )}

              {/* Main Flight Info */}
              <View style={styles.mainFlightInfo}>
                {/* Airline Header */}
                <View style={styles.airlineHeader}>
                  <View style={styles.airlineLogoContainer}>
                    <Text style={styles.airlineLogo}>{flight.logo}</Text>
                  </View>
                  <View style={styles.airlineNameContainer}>
                    <Text style={styles.airlineName}>{flight.airline}</Text>
                    <Text style={styles.flightNumber}>{flight.flightNumber}</Text>
                  </View>
                  <View style={styles.priceContainer}>
                    {flight.originalPrice > flight.price && (
                      <Text style={styles.originalPrice}>${flight.originalPrice}</Text>
                    )}
                    <Text style={styles.flightPrice}>${flight.price}</Text>
                  </View>
                </View>

                {/* Flight Route */}
                <View style={styles.routeContainer}>
                  <View style={styles.departureInfo}>
                    <Text style={styles.timeText}>{flight.departure}</Text>
                    <Text style={styles.airportCode}>{flight.departureCode}</Text>
                  </View>
                  
                  <View style={styles.routeMiddle}>
                    <View style={styles.routeVisual}>
                      <View style={styles.departureIcon}>
                        <PlaneTakeoff size={16} color="#4844b8" />
                      </View>
                      <View style={styles.routeLine} />
                      {flight.stops > 0 && (
                        <View style={styles.stopIndicator}>
                          <View style={styles.stopDot} />
                        </View>
                      )}
                      <View style={styles.arrivalIcon}>
                        <PlaneLanding size={16} color="#4844b8" />
                      </View>
                    </View>
                    <View style={styles.flightDetails}>
                      <Text style={styles.durationText}>{flight.duration}</Text>
                      <Text style={styles.stopsText}>
                        {flight.stops === 0 ? 'Direct' : `${flight.stops} stop${flight.stops > 1 ? 's' : ''}`}
                      </Text>
                    </View>
                  </View>
                  
                  <View style={styles.arrivalInfo}>
                    <Text style={styles.timeText}>{flight.arrival}</Text>
                    <Text style={styles.airportCode}>{flight.arrivalCode}</Text>
                  </View>
                </View>

                {/* Flight Features */}
                <View style={styles.featuresContainer}>
                  <View style={styles.featureItem}>
                    <Text style={styles.featureLabel}>Class</Text>
                    <Text style={styles.featureValue}>{flight.class}</Text>
                  </View>
                  <View style={styles.featureItem}>
                    <Text style={styles.featureLabel}>Aircraft</Text>
                    <Text style={styles.featureValue}>{flight.aircraft}</Text>
                  </View>
                  <View style={styles.featureItem}>
                    <Text style={styles.featureLabel}>Rating</Text>
                    <View style={styles.ratingContainer}>
                      <Star size={12} color={colors.accent} fill={colors.accent} />
                      <Text style={styles.ratingValue}>{flight.rating}</Text>
                    </View>
                  </View>
                </View>
              </View>

              {/* Expand Button */}
              <View style={styles.expandButton}>
                <Text style={styles.expandButtonText}>
                  {expandedFlight === flight.id ? 'Hide Details' : 'View Details'}
                </Text>
                {expandedFlight === flight.id ? (
                  <ChevronUp size={16} color="#4844b8" />
                ) : (
                  <ChevronDown size={16} color="#4844b8" />
                )}
              </View>
            </Pressable>

            {expandedFlight === flight.id && (
              <View style={styles.expandedDetails}>
                {/* Detailed Route Information */}
                <View style={styles.detailedRoute}>
                  <View style={styles.routeSegment}>
                    <View style={styles.segmentHeader}>
                      <View style={styles.segmentDot} />
                      <Text style={styles.segmentTitle}>Departure</Text>
                    </View>
                    <View style={styles.segmentDetails}>
                      <Text style={styles.segmentTime}>{flight.departure}</Text>
                      <Text style={styles.segmentLocation}>{flight.departureCode} - Los Angeles</Text>
                      <Text style={styles.segmentDate}>Today, Dec 15</Text>
                    </View>
                  </View>
                  
                  {flight.stops > 0 && (
                    <View style={styles.routeSegment}>
                      <View style={styles.segmentHeader}>
                        <View style={styles.segmentDot} />
                        <Text style={styles.segmentTitle}>Layover</Text>
                      </View>
                      <View style={styles.segmentDetails}>
                        <Text style={styles.segmentLocation}>{flight.stopLocation}</Text>
                        <Text style={styles.segmentDuration}>2h 30m layover</Text>
                      </View>
                    </View>
                  )}
                  
                  <View style={styles.routeSegment}>
                    <View style={styles.segmentHeader}>
                      <View style={styles.segmentDot} />
                      <Text style={styles.segmentTitle}>Arrival</Text>
                    </View>
                    <View style={styles.segmentDetails}>
                      <Text style={styles.segmentTime}>{flight.arrival}</Text>
                      <Text style={styles.segmentLocation}>{flight.arrivalCode} - Tokyo Narita</Text>
                      <Text style={styles.segmentDate}>Tomorrow, Dec 16</Text>
                    </View>
                  </View>
                </View>

                {/* Flight Information Grid */}
                <View style={styles.infoGrid}>
                  <View style={styles.infoRow}>
                    <View style={styles.infoItem}>
                      <Plane size={16} color="#4844b8" />
                      <View style={styles.infoTextContainer}>
                        <Text style={styles.infoLabel}>Aircraft</Text>
                        <Text style={styles.infoValue}>{flight.aircraft}</Text>
                      </View>
                    </View>
                    <View style={styles.infoItem}>
                      <Shield size={16} color="#4844b8" />
                      <View style={styles.infoTextContainer}>
                        <Text style={styles.infoLabel}>Baggage</Text>
                        <Text style={styles.infoValue}>{flight.baggage}</Text>
                      </View>
                    </View>
                  </View>
                  
                  <View style={styles.infoRow}>
                    <View style={styles.infoItem}>
                      <Clock size={16} color="#4844b8" />
                      <View style={styles.infoTextContainer}>
                        <Text style={styles.infoLabel}>Total Time</Text>
                        <Text style={styles.infoValue}>{flight.duration}</Text>
                      </View>
                    </View>
                    <View style={styles.infoItem}>
                      <Users size={16} color="#4844b8" />
                      <View style={styles.infoTextContainer}>
                        <Text style={styles.infoLabel}>Class</Text>
                        <Text style={styles.infoValue}>{flight.class}</Text>
                      </View>
                    </View>
                  </View>
                </View>
                
                {/* Amenities */}
                <View style={styles.amenitiesSection}>
                  <Text style={styles.amenitiesTitle}>Included Services</Text>
                  <View style={styles.amenitiesList}>
                    {flight.amenities.includes("wifi") && (
                      <View style={styles.amenityItem}>
                        <Wifi size={16} color={colors.success} />
                        <Text style={styles.amenityText}>Free WiFi</Text>
                      </View>
                    )}
                    {flight.amenities.includes("meals") && (
                      <View style={styles.amenityItem}>
                        <Coffee size={16} color={colors.success} />
                        <Text style={styles.amenityText}>Meals Included</Text>
                      </View>
                    )}
                    {flight.amenities.includes("entertainment") && (
                      <View style={styles.amenityItem}>
                        <Star size={16} color={colors.success} />
                        <Text style={styles.amenityText}>In-flight Entertainment</Text>
                      </View>
                    )}
                    {flight.amenities.includes("extra-legroom") && (
                      <View style={styles.amenityItem}>
                        <Users size={16} color={colors.success} />
                        <Text style={styles.amenityText}>Extra Legroom</Text>
                      </View>
                    )}
                  </View>
                </View>
                
                <View style={styles.selectFlightContainer}>
                  <Button
                    title="Select This Flight"
                    onPress={handleSelectFlight}
                    variant="primary"
                    fullWidth
                    size="large"
                  />
                </View>
              </View>
            )}
          </View>
        ))}
      </ScrollView>

      {selectedFlight && !expandedFlight && (
        <View style={styles.footer}>
          <Button
            title="Continue with Selected Flight"
            onPress={handleSelectFlight}
            variant="primary"
            fullWidth
            size="large"
          />
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundSecondary,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    backgroundColor: "#4844b8",
    paddingTop: spacing.xl,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  headerContent: {
    alignItems: "center",
  },
  headerTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  headerSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.white,
    opacity: 0.9,
  },
  resultsHeader: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  resultsCount: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  resultsSubtext: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  filterContainer: {
    backgroundColor: colors.white,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  filterScrollContent: {
    paddingHorizontal: spacing.lg,
  },
  filterChip: {
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 24,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    marginRight: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
  },
  filterChipActive: {
    backgroundColor: "#4844b8",
    borderColor: "#4844b8",
  },
  filterText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.text,
  },
  filterTextActive: {
    color: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
  },
  flightCardContainer: {
    marginBottom: spacing.lg,
  },
  flightCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.borderLight,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 3,
    position: "relative",
    overflow: "hidden",
  },
  flightCardSelected: {
    borderColor: "#4844b8",
    borderWidth: 2,
    shadowColor: "#4844b8",
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 6,
  },
  recommendedBadge: {
    position: "absolute",
    top: 12,
    right: 12,
    backgroundColor: colors.accent,
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    zIndex: 1,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  recommendedText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.semibold as any,
    color: colors.white,
    marginLeft: spacing.xs,
  },
  mainFlightInfo: {
    padding: spacing.lg,
  },
  airlineHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.lg,
  },
  airlineLogoContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
  },
  airlineLogo: {
    fontSize: 24,
  },
  airlineNameContainer: {
    flex: 1,
  },
  airlineName: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  flightNumber: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
  },
  priceContainer: {
    alignItems: "flex-end",
  },
  originalPrice: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    textDecorationLine: "line-through",
    marginBottom: spacing.xs,
  },
  flightPrice: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold as any,
    color: "#4844b8",
  },
  routeContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.lg,
    paddingVertical: spacing.md,
    minHeight: 80,
  },
  departureInfo: {
    alignItems: "flex-start",
    flex: 1,
    minWidth: 80,
  },
  arrivalInfo: {
    alignItems: "flex-end",
    flex: 1,
    minWidth: 80,
  },
  timeText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  airportCode: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    fontWeight: typography.weights.semibold as any,
  },
  routeMiddle: {
    flex: 2,
    alignItems: "center",
    paddingHorizontal: spacing.md,
    minWidth: 120,
  },
  routeVisual: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    marginBottom: spacing.sm,
  },
  departureIcon: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: "rgba(72, 68, 184, 0.1)",
    justifyContent: "center",
    alignItems: "center",
  },
  arrivalIcon: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: "rgba(72, 68, 184, 0.1)",
    justifyContent: "center",
    alignItems: "center",
  },
  routeLine: {
    flex: 1,
    height: 2,
    backgroundColor: "#4844b8",
    marginHorizontal: spacing.sm,
  },
  stopIndicator: {
    position: "absolute",
    left: "50%",
    marginLeft: -4,
  },
  stopDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.accent,
    borderWidth: 2,
    borderColor: colors.white,
  },
  flightDetails: {
    alignItems: "center",
  },
  durationText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  stopsText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
  },
  featuresContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
  },
  featureItem: {
    alignItems: "center",
    flex: 1,
  },
  featureLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  featureValue: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  ratingValue: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginLeft: spacing.xs,
  },
  expandButton: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: spacing.md,
    backgroundColor: colors.backgroundSecondary,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
  },
  expandButtonText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: "#4844b8",
    marginRight: spacing.sm,
  },
  expandedDetails: {
    backgroundColor: colors.white,
    borderRadius: 16,
    marginTop: spacing.sm,
    borderWidth: 1,
    borderColor: colors.borderLight,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 3,
    overflow: "hidden",
  },
  detailedRoute: {
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  routeSegment: {
    flexDirection: "row",
    marginBottom: spacing.lg,
  },
  segmentHeader: {
    flexDirection: "row",
    alignItems: "center",
    width: 100,
  },
  segmentDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: "#4844b8",
    marginRight: spacing.sm,
  },
  segmentTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  segmentDetails: {
    flex: 1,
    marginLeft: spacing.md,
  },
  segmentTime: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  segmentLocation: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  segmentDate: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  segmentDuration: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.accent,
    fontWeight: typography.weights.medium as any,
  },
  infoGrid: {
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  infoRow: {
    flexDirection: "row",
    marginBottom: spacing.lg,
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    marginRight: spacing.md,
  },
  infoTextContainer: {
    marginLeft: spacing.sm,
    flex: 1,
  },
  infoLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  infoValue: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  amenitiesSection: {
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  amenitiesTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.md,
  },
  amenitiesList: {
    gap: spacing.md,
  },
  amenityItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  amenityText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.text,
    marginLeft: spacing.md,
    fontWeight: typography.weights.medium as any,
  },
  selectFlightContainer: {
    padding: spacing.lg,
  },
  footer: {
    padding: spacing.lg,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
});