import React from "react";
import { StyleSheet, View, TextInput, Pressable } from "react-native";
import { Search, X, SlidersHorizontal } from "lucide-react-native";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";

interface SearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  onFilterPress?: () => void;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChangeText,
  placeholder = "Search destinations, activities...",
  onFilterPress,
}) => {
  const handleClear = () => {
    onChangeText("");
  };

  return (
    <View style={styles.container}>
      <Search size={20} color={colors.textLight} style={styles.icon} />
      <TextInput
        style={styles.input}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={colors.textLight}
      />
      {value.length > 0 && (
        <Pressable onPress={handleClear} hitSlop={10} style={styles.clearButton}>
          <X size={20} color={colors.textLight} />
        </Pressable>
      )}
      {onFilterPress && (
        <Pressable onPress={onFilterPress} hitSlop={10} style={styles.filterButton}>
          <SlidersHorizontal size={20} color={colors.primary} />
        </Pressable>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginBottom: spacing.md,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  icon: {
    marginRight: spacing.sm,
  },
  input: {
    flex: 1,
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.text,
    padding: 0,
  },
  clearButton: {
    marginLeft: spacing.sm,
  },
  filterButton: {
    marginLeft: spacing.sm,
    padding: spacing.xs,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 8,
  },
});