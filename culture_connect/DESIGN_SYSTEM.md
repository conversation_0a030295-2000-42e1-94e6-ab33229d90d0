# CultureConnect Design System

## Overview

The CultureConnect Design System is a comprehensive, modern design language built on Material Design 3 principles with enhanced visual appeal, performance optimization, and accessibility. This system provides a unified approach to creating beautiful, consistent, and performant user interfaces across the entire application.

## 🎨 Color Palette

### Primary Colors
```dart
// Modern Indigo - Primary brand color
static const Color primaryColor = Color(0xFF6366F1);
static const Color primaryLight = Color(0xFF818CF8);
static const Color primaryDark = Color(0xFF4F46E5);
```
**Usage**: Primary actions, navigation elements, key interactive components

### Secondary Colors
```dart
// Modern Cyan - Supporting brand color
static const Color secondaryColor = Color(0xFF06B6D4);
static const Color secondaryLight = Color(0xFF22D3EE);
static const Color secondaryDark = Color(0xFF0891B2);
```
**Usage**: Secondary actions, highlights, accent elements

### Accent Colors
```dart
// Modern Emerald - Success and positive actions
static const Color accentColor = Color(0xFF10B981);
static const Color accentLight = Color(0xFF34D399);
static const Color accentDark = Color(0xFF059669);
```
**Usage**: Success states, positive feedback, call-to-action elements

### Neutral Colors
```dart
// Clean, accessible neutrals
static const Color backgroundColor = Color(0xFFFAFAFA);
static const Color surfaceColor = Color(0xFFFFFFFF);
static const Color surfaceVariant = Color(0xFFF1F5F9);
static const Color outline = Color(0xFFE2E8F0);
static const Color outlineVariant = Color(0xFFF1F5F9);

static const Color textPrimaryColor = Color(0xFF0F172A);
static const Color textSecondaryColor = Color(0xFF64748B);
static const Color textTertiaryColor = Color(0xFF94A3B8);
```

### Status Colors
```dart
// Modern, accessible status colors
static const Color successColor = Color(0xFF10B981);
static const Color errorColor = Color(0xFFEF4444);
static const Color warningColor = Color(0xFFF59E0B);
static const Color infoColor = Color(0xFF3B82F6);
```

## 📐 Spacing System

Based on an 8px grid system for perfect visual rhythm:

```dart
static const double spacingXS = 4.0;      // 0.25rem
static const double spacingSmall = 8.0;   // 0.5rem
static const double spacingMedium = 16.0; // 1rem
static const double spacingLarge = 24.0;  // 1.5rem
static const double spacingXLarge = 32.0; // 2rem
static const double spacingXXLarge = 48.0; // 3rem
static const double spacingXXXLarge = 64.0; // 4rem
```

**Usage Guidelines**:
- `spacingXS`: Icon padding, fine adjustments
- `spacingSmall`: Button padding, small gaps
- `spacingMedium`: Standard component spacing, card padding
- `spacingLarge`: Section spacing, large component padding
- `spacingXLarge`: Page margins, major section spacing
- `spacingXXLarge`: Hero section spacing
- `spacingXXXLarge`: Page-level spacing

## 🔄 Border Radius System

Modern, consistent rounded corners:

```dart
static const double borderRadiusXS = 4.0;        // Small elements
static const double borderRadiusSmall = 8.0;     // Buttons, inputs
static const double borderRadiusMedium = 12.0;   // Cards, containers
static const double borderRadiusLarge = 16.0;    // Large cards, modals
static const double borderRadiusXLarge = 20.0;   // Hero elements
static const double borderRadiusXXLarge = 24.0;  // Special containers
static const double borderRadiusRounded = 999.0; // Pill-shaped buttons
```

## 🌟 Shadow System

Layered, subtle shadows for depth without performance impact:

```dart
// Extra Small - Subtle depth
static List<BoxShadow> get shadowXS => [
  BoxShadow(
    color: Colors.black.withAlpha(13), // 0.05 opacity
    blurRadius: 2,
    offset: const Offset(0, 1),
  ),
];

// Small - Standard elevation
static List<BoxShadow> get shadowSmall => [
  BoxShadow(
    color: Colors.black.withAlpha(26), // 0.1 opacity
    blurRadius: 4,
    offset: const Offset(0, 2),
  ),
  BoxShadow(
    color: Colors.black.withAlpha(13), // 0.05 opacity
    blurRadius: 2,
    offset: const Offset(0, 1),
  ),
];

// Medium - Cards, buttons
static List<BoxShadow> get shadowMedium => [
  BoxShadow(
    color: Colors.black.withAlpha(26), // 0.1 opacity
    blurRadius: 8,
    offset: const Offset(0, 4),
  ),
  BoxShadow(
    color: Colors.black.withAlpha(13), // 0.05 opacity
    blurRadius: 4,
    offset: const Offset(0, 2),
  ),
];

// Large - Modals, elevated content
static List<BoxShadow> get shadowLarge => [
  BoxShadow(
    color: Colors.black.withAlpha(38), // 0.15 opacity
    blurRadius: 16,
    offset: const Offset(0, 8),
  ),
  BoxShadow(
    color: Colors.black.withAlpha(26), // 0.1 opacity
    blurRadius: 8,
    offset: const Offset(0, 4),
  ),
];

// Extra Large - Hero elements
static List<BoxShadow> get shadowXLarge => [
  BoxShadow(
    color: Colors.black.withAlpha(51), // 0.2 opacity
    blurRadius: 24,
    offset: const Offset(0, 12),
  ),
  BoxShadow(
    color: Colors.black.withAlpha(38), // 0.15 opacity
    blurRadius: 16,
    offset: const Offset(0, 8),
  ),
];
```

## 🎭 Animation System

Modern, responsive animations that enhance UX:

### Animation Durations
```dart
static const Duration animationFast = Duration(milliseconds: 150);    // Quick interactions
static const Duration animationNormal = Duration(milliseconds: 250);  // Standard transitions
static const Duration animationSlow = Duration(milliseconds: 400);    // Complex animations
static const Duration animationSlower = Duration(milliseconds: 600);  // Hero transitions
```

### Animation Curves
```dart
static const Curve curveDefault = Curves.easeInOut;      // Standard curve
static const Curve curveEmphasized = Curves.easeInOutCubic; // Emphasized motion
static const Curve curveDecelerated = Curves.easeOut;   // Entering elements
static const Curve curveAccelerated = Curves.easeIn;    // Exiting elements
```

### Usage Guidelines
- **Fast (150ms)**: Button presses, hover effects, micro-interactions
- **Normal (250ms)**: Page transitions, modal appearances, standard animations
- **Slow (400ms)**: Complex state changes, multi-step animations
- **Slower (600ms)**: Hero transitions, celebration animations

## 🌈 Gradient System

Modern gradients for enhanced visual appeal:

```dart
// Primary gradient - Main brand elements
static const LinearGradient primaryGradient = LinearGradient(
  begin: Alignment.topLeft,
  end: Alignment.bottomRight,
  colors: [primaryColor, primaryDark],
);

// Secondary gradient - Supporting elements
static const LinearGradient secondaryGradient = LinearGradient(
  begin: Alignment.topLeft,
  end: Alignment.bottomRight,
  colors: [secondaryColor, secondaryDark],
);

// Card gradient - Subtle background enhancement
static const LinearGradient cardGradient = LinearGradient(
  begin: Alignment.topLeft,
  end: Alignment.bottomRight,
  colors: [Color(0xFFFFFFFF), Color(0xFFF8FAFC)],
);

// Hero gradient - Special sections
static const LinearGradient heroGradient = LinearGradient(
  begin: Alignment.topLeft,
  end: Alignment.bottomRight,
  colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
);
```

## 📱 Component Patterns

### Enhanced Button Usage
```dart
EnhancedAnimatedButton(
  onPressed: () => _handleAction(),
  backgroundColor: AppTheme.primaryColor,
  borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
  child: Text('Primary Action'),
)
```

### Enhanced Card Usage
```dart
EnhancedAnimatedCard(
  onTap: () => _navigateToDetails(),
  margin: EdgeInsets.all(AppTheme.spacingMedium),
  child: YourCardContent(),
)
```

### Page Transitions
```dart
// Slide transition
Navigator.push(
  context,
  EnhancedPageTransitions.slideFromRight(DestinationPage()),
);

// Fade with scale
Navigator.push(
  context,
  EnhancedPageTransitions.fadeWithScale(DestinationPage()),
);
```

## 🎯 Best Practices

### Performance Guidelines
- Always use `const` constructors where possible
- Implement proper animation disposal in `dispose()` methods
- Use `AnimatedBuilder` for complex animations
- Maintain 60fps target with animation optimizations

### Accessibility Guidelines
- Ensure minimum contrast ratios (4.5:1 for normal text, 3:1 for large text)
- Provide semantic labels for interactive elements
- Support reduced motion preferences
- Test with screen readers

### Consistency Guidelines
- Always use design system constants instead of hardcoded values
- Follow the 8px spacing grid system
- Use appropriate shadow levels for element hierarchy
- Maintain consistent animation timing across similar interactions

## 🔧 Implementation Examples

### Modern Container
```dart
Container(
  padding: EdgeInsets.all(AppTheme.spacingLarge),
  decoration: BoxDecoration(
    gradient: AppTheme.cardGradient,
    borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
    boxShadow: AppTheme.shadowMedium,
    border: Border.all(color: AppTheme.outline, width: 0.5),
  ),
  child: YourContent(),
)
```

### Animated List Item
```dart
EnhancedAnimatedCard(
  margin: EdgeInsets.symmetric(
    horizontal: AppTheme.spacingMedium,
    vertical: AppTheme.spacingSmall,
  ),
  onTap: () => _handleItemTap(),
  child: ListTile(
    title: Text('Item Title'),
    subtitle: Text('Item Description'),
  ),
)
```

## 📊 Performance Targets

- **Memory Usage**: <100MB
- **Frame Rate**: Consistent 60fps
- **Animation Performance**: <16ms per frame
- **Load Times**: <2s for page transitions

## 📝 Typography System

### Font Weights
```dart
FontWeight.w300  // Light - Subtle text, captions
FontWeight.w400  // Regular - Body text, descriptions
FontWeight.w500  // Medium - Emphasized text
FontWeight.w600  // Semi-bold - Headings, important text
FontWeight.w700  // Bold - Strong emphasis, titles
```

### Font Sizes
```dart
// Headings
fontSize: 32  // H1 - Page titles
fontSize: 28  // H2 - Section headers
fontSize: 24  // H3 - Subsection headers
fontSize: 20  // H4 - Component titles
fontSize: 18  // H5 - Card titles
fontSize: 16  // H6 - Small headings

// Body Text
fontSize: 16  // Body large - Primary content
fontSize: 14  // Body medium - Secondary content
fontSize: 12  // Body small - Captions, labels
fontSize: 10  // Caption - Fine print, metadata
```

## 🔧 Development Guidelines

### File Organization
```
lib/
├── theme/
│   └── app_theme.dart          # Main theme constants
├── widgets/
│   ├── animations/
│   │   ├── enhanced_button_animations.dart
│   │   └── enhanced_page_transitions.dart
│   └── common/
└── screens/
```

### Import Structure
```dart
// Always import theme first
import 'package:culture_connect/theme/app_theme.dart';

// Then other project imports
import 'package:culture_connect/widgets/...';
```

### Code Examples

#### Modern Button Implementation
```dart
ElevatedButton(
  onPressed: _handlePress,
  style: ElevatedButton.styleFrom(
    backgroundColor: AppTheme.primaryColor,
    foregroundColor: Colors.white,
    padding: EdgeInsets.symmetric(
      horizontal: AppTheme.spacingLarge,
      vertical: AppTheme.spacingMedium,
    ),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
    ),
    elevation: 0,
    shadowColor: AppTheme.primaryColor.withAlpha(77),
  ),
  child: Text('Action Button'),
)
```

#### Modern Card Implementation
```dart
Container(
  margin: EdgeInsets.all(AppTheme.spacingMedium),
  padding: EdgeInsets.all(AppTheme.spacingLarge),
  decoration: BoxDecoration(
    gradient: AppTheme.cardGradient,
    borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
    boxShadow: AppTheme.shadowMedium,
    border: Border.all(color: AppTheme.outline, width: 0.5),
  ),
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        'Card Title',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppTheme.textPrimaryColor,
        ),
      ),
      SizedBox(height: AppTheme.spacingSmall),
      Text(
        'Card description text',
        style: TextStyle(
          fontSize: 14,
          color: AppTheme.textSecondaryColor,
        ),
      ),
    ],
  ),
)
```

## 🎨 Visual Hierarchy

### Elevation Levels
1. **Level 0**: Background, base surface
2. **Level 1**: Cards, containers (shadowSmall)
3. **Level 2**: Buttons, interactive elements (shadowMedium)
4. **Level 3**: Modals, dialogs (shadowLarge)
5. **Level 4**: Tooltips, dropdowns (shadowXLarge)

### Color Usage Hierarchy
1. **Primary**: Main actions, navigation, key interactive elements
2. **Secondary**: Supporting actions, highlights, secondary navigation
3. **Accent**: Success states, positive feedback, CTAs
4. **Neutral**: Text, backgrounds, borders, dividers
5. **Status**: Error, warning, info, success states

## 🚀 Future Enhancements

- Dark mode support with automatic theme switching
- Advanced micro-interactions with haptic feedback
- Accessibility improvements with voice navigation
- Performance optimizations for older devices
- Component library expansion
- Design token automation
- Cross-platform consistency improvements

## 📞 Support & Contribution

### Getting Help
- Review this documentation first
- Check existing component implementations
- Consult with the design team for new patterns
- Test on multiple devices and screen sizes

### Contributing
- Follow the established patterns and conventions
- Maintain performance targets
- Update documentation for new components
- Ensure accessibility compliance
- Test animations on various devices

---

*This design system is continuously evolving. For questions or contributions, please refer to the development team guidelines.*
