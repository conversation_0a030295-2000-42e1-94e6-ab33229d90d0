# Auto-Lock Security Feature Documentation

## Overview

The Auto-Lock Security Feature provides comprehensive app security through automatic locking after periods of inactivity, biometric authentication, and user-configurable security preferences. This feature ensures user data protection while maintaining excellent user experience.

## Architecture

### Core Components

1. **SecuritySettings Model** (`lib/models/security_settings.dart`)
   - Data model for security preferences
   - Handles serialization/deserialization for persistent storage
   - Provides display text helpers for timeout options

2. **SecuritySettingsProvider** (`lib/providers/security_settings_provider.dart`)
   - Riverpod StateNotifier for security settings state management
   - Provides convenience methods for updating individual settings
   - Integrates with SharedPreferences for persistence

3. **AutoLockService** (`lib/services/auto_lock_service.dart`)
   - Core service managing inactivity detection and auto-lock functionality
   - Implements WidgetsBindingObserver for app lifecycle monitoring
   - Handles timer management and lock triggering

4. **LockScreen** (`lib/screens/lock_screen.dart`)
   - Lock screen UI with biometric authentication
   - Password input with validation fallback
   - Personalized user greeting and smooth animations

5. **SecuritySettingsScreen** (`lib/screens/settings/security_settings_screen.dart`)
   - User interface for configuring auto-lock preferences
   - Timeout selection dialog and biometric availability checking
   - Material Design 3 consistent UI

## Features

### Auto-Lock Functionality
- **Configurable Timeouts**: 30 seconds, 1 minute, 5 minutes, 15 minutes, or never
- **Inactivity Detection**: Monitors user interactions across the entire app
- **App Lifecycle Integration**: Automatically locks when app goes to background
- **Smart Timer Management**: Resets timer on user activity

### Authentication Methods
- **Biometric Authentication**: Fingerprint and face recognition support
- **Password Fallback**: Secure password input when biometrics unavailable
- **Availability Detection**: Automatically checks device biometric capabilities

### User Experience
- **Personalized Greeting**: Shows user's name on lock screen
- **Smooth Animations**: Material Design 3 transitions and effects
- **Accessibility Support**: Full accessibility compliance
- **Responsive Design**: Works seamlessly across all device sizes

## Implementation Details

### Security Settings Model

```dart
class SecuritySettings {
  final bool autoLockEnabled;
  final int autoLockTimeoutMinutes; // 0=30s, 1=1m, 5=5m, 15=15m, -1=never
  final bool biometricEnabled;
  
  // Display helpers
  String get timeoutDisplayText {
    switch (autoLockTimeoutMinutes) {
      case 0: return '30 seconds';
      case 1: return '1 minute';
      case 5: return '5 minutes';
      case 15: return '15 minutes';
      case -1: return 'Never';
      default: return '1 minute';
    }
  }
}
```

### Auto-Lock Service Integration

```dart
// In main_navigation.dart
return GestureDetector(
  onTap: () => autoLockService.recordActivity(),
  onPanDown: (_) => autoLockService.recordActivity(),
  child: Scaffold(
    // ... app content
  ),
);
```

### Biometric Authentication Flow

```dart
// Automatic biometric prompt on lock screen load
@override
void initState() {
  super.initState();
  WidgetsBinding.instance.addPostFrameCallback((_) {
    _attemptBiometricAuth();
  });
}
```

## Configuration

### User Settings Access
1. Navigate to Profile Screen
2. Tap "Security Settings" option
3. Configure auto-lock timeout
4. Enable/disable biometric authentication

### Timeout Options
- **30 seconds**: Maximum security for sensitive environments
- **1 minute**: Balanced security and convenience (default)
- **5 minutes**: Relaxed security for trusted environments
- **15 minutes**: Minimal security for development/testing
- **Never**: Disables auto-lock (not recommended for production)

## Security Considerations

### Data Protection
- All security preferences stored in encrypted SharedPreferences
- Biometric authentication uses device secure enclave
- No sensitive data cached in memory after lock

### Privacy Compliance
- Biometric data never leaves the device
- User consent required for biometric enrollment
- Full transparency in security settings UI

### Threat Mitigation
- **Unauthorized Access**: Auto-lock prevents access to unlocked devices
- **Shoulder Surfing**: Biometric authentication reduces password exposure
- **Device Theft**: Immediate lock on app backgrounding

## Testing

### Manual Testing Scenarios
1. **Auto-Lock Timer**: Verify lock triggers after configured timeout
2. **Biometric Auth**: Test fingerprint/face recognition unlock
3. **Password Fallback**: Verify password input when biometrics fail
4. **App Lifecycle**: Confirm lock on app backgrounding
5. **Settings Persistence**: Verify settings survive app restart

### Automated Testing
- Unit tests for SecuritySettings model
- Widget tests for LockScreen UI
- Integration tests for AutoLockService
- Provider tests for SecuritySettingsProvider

## Troubleshooting

### Common Issues

**Biometric Authentication Not Working**
- Check device biometric enrollment
- Verify app permissions for biometric access
- Test with device settings biometric verification

**Auto-Lock Not Triggering**
- Confirm auto-lock is enabled in settings
- Check timeout configuration
- Verify app lifecycle observer registration

**Settings Not Persisting**
- Check SharedPreferences initialization
- Verify provider state management
- Test settings screen save functionality

## Future Enhancements

### Planned Features
- **Multi-Factor Authentication**: SMS/Email verification options
- **Geofencing**: Location-based auto-lock policies
- **Time-Based Policies**: Different timeouts for different times of day
- **Enterprise Integration**: MDM policy enforcement

### Performance Optimizations
- **Battery Optimization**: Reduce timer frequency when appropriate
- **Memory Management**: Optimize lock screen resource usage
- **Background Processing**: Minimize CPU usage during inactivity detection

## Dependencies

### Required Packages
- `local_auth: ^2.1.7` - Biometric authentication
- `shared_preferences: ^2.2.2` - Settings persistence
- `flutter_riverpod: ^2.4.9` - State management

### Platform Requirements
- **iOS**: iOS 11.0+ for biometric authentication
- **Android**: API level 23+ for fingerprint, API level 28+ for face recognition

## Conclusion

The Auto-Lock Security Feature provides enterprise-grade security while maintaining excellent user experience. The implementation follows Material Design 3 guidelines, integrates seamlessly with existing authentication infrastructure, and provides comprehensive protection against unauthorized access.

For additional support or feature requests, please refer to the CultureConnect development team.
