import React from "react";
import { StyleSheet, View, Text, Pressable } from "react-native";
import { Shield } from "lucide-react-native";
import { Insurance } from "@/types";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";

interface InsuranceCardProps {
  insurance: Insurance;
  onPress: (insurance: Insurance) => void;
  isSelected?: boolean;
}

export const InsuranceCard: React.FC<InsuranceCardProps> = ({
  insurance,
  onPress,
  isSelected = false,
}) => {
  return (
    <Pressable
      style={[styles.container, isSelected && styles.selectedContainer]}
      onPress={() => onPress(insurance)}
      android_ripple={{ color: colors.border }}
    >
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Shield size={20} color={colors.white} />
        </View>
        <Text style={styles.type}>{insurance.type}</Text>
      </View>
      <Text style={styles.provider}>{insurance.provider}</Text>
      <Text style={styles.coverage}>{insurance.coverage}</Text>
      <View style={styles.footer}>
        <Text style={styles.duration}>{insurance.duration}</Text>
        <Text style={styles.price}>
          {insurance.currency}{insurance.price}
        </Text>
      </View>
      {isSelected && <View style={styles.selectedIndicator} />}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
    borderWidth: 2,
    borderColor: "transparent",
  },
  selectedContainer: {
    borderColor: colors.accent,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  iconContainer: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    padding: spacing.xs,
    marginRight: spacing.sm,
  },
  type: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    flex: 1,
  },
  provider: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textLight,
    marginBottom: spacing.sm,
  },
  coverage: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.text,
    marginBottom: spacing.md,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  duration: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textLight,
  },
  price: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.primary,
  },
  selectedIndicator: {
    position: "absolute",
    top: 0,
    right: 0,
    width: 0,
    height: 0,
    backgroundColor: "transparent",
    borderStyle: "solid",
    borderRightWidth: 40,
    borderTopWidth: 40,
    borderRightColor: "transparent",
    borderTopColor: colors.accent,
    borderTopRightRadius: 16,
  },
});