import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Pixel-perfect React Native CalendarModal replica
class RNCalendarModal extends StatefulWidget {
  final bool visible;
  final VoidCallback onClose;
  final Function(DateTime, DateTime?) onSelectDates;
  final String tripType;
  final DateTime? initialDepartDate;
  final DateTime? initialReturnDate;

  const RNCalendarModal({
    super.key,
    required this.visible,
    required this.onClose,
    required this.onSelectDates,
    required this.tripType,
    this.initialDepartDate,
    this.initialReturnDate,
  });

  @override
  State<RNCalendarModal> createState() => _RNCalendarModalState();
}

class _RNCalendarModalState extends State<RNCalendarModal> {
  DateTime? _selectedDepartDate;
  DateTime? _selectedReturnDate;
  DateTime _focusedDay = DateTime.now();
  bool _isSelectingReturn = false;

  @override
  void initState() {
    super.initState();
    _selectedDepartDate = widget.initialDepartDate;
    _selectedReturnDate = widget.initialReturnDate;
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.visible) return const SizedBox.shrink();

    return Material(
      color: Colors.black54,
      child: SafeArea(
        child: Container(
          color: AppTheme.backgroundColor,
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: AppTheme.backgroundColor,
                  border: Border(
                    bottom: BorderSide(color: AppTheme.borderColor, width: 1),
                  ),
                ),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: widget.onClose,
                      icon: const Icon(Icons.close),
                      color: AppTheme.textPrimaryColor,
                    ),
                    Expanded(
                      child: Text(
                        widget.tripType == 'round-trip'
                            ? 'Select Dates'
                            : 'Select Date',
                        style: const TextStyle(
                          fontSize: AppTheme.fontSizeLg,
                          fontWeight: AppTheme.fontWeightBold,
                          color: AppTheme.textPrimaryColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    TextButton(
                      onPressed: _handleDone,
                      child: const Text(
                        'Done',
                        style: TextStyle(
                          fontSize: AppTheme.fontSizeMd,
                          fontWeight: AppTheme.fontWeightSemibold,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Date Selection Info
              if (widget.tripType == 'round-trip')
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Expanded(
                        child: _buildDateInfo(
                          'Departure',
                          _selectedDepartDate,
                          !_isSelectingReturn,
                          () => setState(() => _isSelectingReturn = false),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildDateInfo(
                          'Return',
                          _selectedReturnDate,
                          _isSelectingReturn,
                          () => setState(() => _isSelectingReturn = true),
                        ),
                      ),
                    ],
                  ),
                ),

              // Calendar
              Expanded(
                child: TableCalendar<DateTime>(
                  firstDay: DateTime.now(),
                  lastDay: DateTime.now().add(const Duration(days: 365)),
                  focusedDay: _focusedDay,
                  calendarFormat: CalendarFormat.month,
                  startingDayOfWeek: StartingDayOfWeek.monday,
                  selectedDayPredicate: (day) {
                    if (widget.tripType == 'one-way') {
                      return isSameDay(_selectedDepartDate, day);
                    } else {
                      return isSameDay(_selectedDepartDate, day) ||
                          isSameDay(_selectedReturnDate, day);
                    }
                  },
                  // rangeSelectionPredicate: (start, end, day) {
                  //   if (widget.tripType == 'round-trip' &&
                  //       _selectedDepartDate != null &&
                  //       _selectedReturnDate != null) {
                  //     return day.isAfter(_selectedDepartDate!.subtract(const Duration(days: 1))) &&
                  //            day.isBefore(_selectedReturnDate!.add(const Duration(days: 1)));
                  //   }
                  //   return false;
                  // },
                  onDaySelected: _onDaySelected,
                  onPageChanged: (focusedDay) {
                    setState(() {
                      _focusedDay = focusedDay;
                    });
                  },
                  calendarStyle: CalendarStyle(
                    outsideDaysVisible: false,
                    weekendTextStyle:
                        const TextStyle(color: AppTheme.textPrimaryColor),
                    holidayTextStyle:
                        const TextStyle(color: AppTheme.textPrimaryColor),
                    selectedDecoration: const BoxDecoration(
                      color: AppTheme.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    todayDecoration: BoxDecoration(
                      color: AppTheme.primaryColor.withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                    rangeHighlightColor: AppTheme.primaryColor.withOpacity(0.2),
                    rangeStartDecoration: const BoxDecoration(
                      color: AppTheme.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    rangeEndDecoration: const BoxDecoration(
                      color: AppTheme.primaryColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                  headerStyle: const HeaderStyle(
                    formatButtonVisible: false,
                    titleCentered: true,
                    titleTextStyle: TextStyle(
                      fontSize: AppTheme.fontSizeLg,
                      fontWeight: AppTheme.fontWeightBold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDateInfo(
      String label, DateTime? date, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryColor.withOpacity(0.1)
              : AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : AppTheme.borderColor,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: AppTheme.fontSizeSm,
                color: isSelected
                    ? AppTheme.primaryColor
                    : AppTheme.textSecondaryColor,
                fontWeight: AppTheme.fontWeightMedium,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              date != null ? _formatDate(date) : 'Select',
              style: TextStyle(
                fontSize: AppTheme.fontSizeMd,
                fontWeight: AppTheme.fontWeightSemibold,
                color: isSelected
                    ? AppTheme.primaryColor
                    : AppTheme.textPrimaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    setState(() {
      _focusedDay = focusedDay;

      if (widget.tripType == 'one-way') {
        _selectedDepartDate = selectedDay;
      } else {
        if (!_isSelectingReturn) {
          _selectedDepartDate = selectedDay;
          _isSelectingReturn = true;
        } else {
          if (selectedDay.isBefore(_selectedDepartDate!)) {
            _selectedDepartDate = selectedDay;
            _selectedReturnDate = null;
            _isSelectingReturn = true;
          } else {
            _selectedReturnDate = selectedDay;
          }
        }
      }
    });
  }

  void _handleDone() {
    if (_selectedDepartDate != null) {
      widget.onSelectDates(_selectedDepartDate!, _selectedReturnDate);
      widget.onClose();
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day} ${_getMonthName(date.month)}';
  }

  String _getMonthName(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[month - 1];
  }
}
