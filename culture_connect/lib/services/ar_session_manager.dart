import 'package:flutter/material.dart';
import 'package:culture_connect/services/ar_framework_service.dart';
import 'package:culture_connect/services/ar_performance_service.dart' as perf;
import 'package:culture_connect/services/ar_error_recovery_service.dart';
import 'package:culture_connect/services/ar_offline_content_service.dart';
import 'dart:async';

/// AR Session Manager
/// Manages the complete lifecycle of AR sessions with proper state management
class ARSessionManager {
  static const String _logTag = 'ARSessionManager';

  // Service dependencies
  late final ARFrameworkService _frameworkService;
  late final perf.ARPerformanceService _performanceService;
  late final ARErrorRecoveryService _errorRecoveryService;
  late final AROfflineContentService _offlineContentService;

  // Session state
  ARSessionState _currentState = ARSessionState.idle;
  String? _currentSessionId;
  DateTime? _sessionStartTime;
  Timer? _sessionTimer;

  // Session configuration
  ARSessionConfiguration _configuration = const ARSessionConfiguration();

  // Session callbacks
  Function(ARSessionState state)? onStateChanged;
  Function(ARSessionEvent event)? onSessionEvent;
  Function(ARSessionMetrics metrics)? onMetricsUpdate;

  // Session metrics
  int _totalFrames = 0;
  int _droppedFrames = 0;
  Duration _totalSessionTime = Duration.zero;

  /// Initialize the AR session manager
  Future<void> initialize({
    ARSessionConfiguration? configuration,
  }) async {
    try {
      debugPrint('$_logTag: Initializing AR session manager');

      if (configuration != null) {
        _configuration = configuration;
      }

      // Initialize services
      _frameworkService = ARFrameworkService();
      _performanceService = perf.ARPerformanceService();
      _errorRecoveryService = ARErrorRecoveryService();
      _offlineContentService = AROfflineContentService();

      // Setup service callbacks
      _setupServiceCallbacks();

      // Initialize offline content service
      await _offlineContentService.initialize();

      _updateState(ARSessionState.ready);
      debugPrint('$_logTag: AR session manager initialized');
    } catch (e) {
      debugPrint('$_logTag: Error initializing AR session manager: $e');
      _updateState(ARSessionState.error);
      rethrow;
    }
  }

  /// Setup callbacks for all AR services
  void _setupServiceCallbacks() {
    // Performance service callbacks
    _performanceService.onPerformanceUpdate = (metrics) {
      _handlePerformanceUpdate(metrics);
    };

    _performanceService.onPerformanceWarning = (warning) {
      _handlePerformanceWarning(warning);
    };

    // Error recovery service callbacks
    _errorRecoveryService.onErrorOccurred = (error) {
      _handleError(error);
    };

    _errorRecoveryService.onRecoveryCompleted = (operationId, success) {
      _handleRecoveryCompleted(operationId, success);
    };

    // Framework service callbacks
    _frameworkService.onFPSUpdate = (fps) {
      _performanceService.updateFrameCount();
    };

    _frameworkService.onError = (error) {
      _handleFrameworkError(error);
    };
  }

  /// Start a new AR session
  Future<ARSessionResult> startSession(BuildContext context) async {
    if (_currentState != ARSessionState.ready) {
      return ARSessionResult(
        success: false,
        message:
            'Session manager not ready. Current state: ${_currentState.name}',
      );
    }

    try {
      debugPrint('$_logTag: Starting AR session');
      _updateState(ARSessionState.starting);

      // Generate session ID
      _currentSessionId = 'ar_session_${DateTime.now().millisecondsSinceEpoch}';
      _sessionStartTime = DateTime.now();

      // Initialize AR framework
      final initResult = await _frameworkService.initialize(context);
      if (!initResult.success) {
        _updateState(ARSessionState.error);
        return ARSessionResult(
          success: false,
          message: 'Failed to initialize AR framework: ${initResult.message}',
        );
      }

      // Start AR session
      final sessionStarted = await _frameworkService.startARSession();
      if (!sessionStarted) {
        _updateState(ARSessionState.error);
        return ARSessionResult(
          success: false,
          message: 'Failed to start AR session',
        );
      }

      // Start performance monitoring
      _performanceService.startMonitoring();

      // Start session timer
      _startSessionTimer();

      _updateState(ARSessionState.active);

      final event = ARSessionEvent(
        type: ARSessionEventType.sessionStarted,
        sessionId: _currentSessionId!,
        timestamp: DateTime.now(),
        message: 'AR session started successfully',
      );
      onSessionEvent?.call(event);

      debugPrint(
          '$_logTag: AR session started successfully: $_currentSessionId');

      return ARSessionResult(
        success: true,
        message: 'AR session started successfully',
        sessionId: _currentSessionId,
      );
    } catch (e) {
      debugPrint('$_logTag: Error starting AR session: $e');
      _updateState(ARSessionState.error);

      return ARSessionResult(
        success: false,
        message: 'Error starting AR session: $e',
      );
    }
  }

  /// Pause the current AR session
  Future<void> pauseSession() async {
    if (_currentState != ARSessionState.active) {
      debugPrint('$_logTag: Cannot pause session - not active');
      return;
    }

    try {
      debugPrint('$_logTag: Pausing AR session');
      _updateState(ARSessionState.paused);

      // Pause AR framework
      _frameworkService.stopARSession();

      // Pause performance monitoring
      _performanceService.stopMonitoring();

      // Pause session timer
      _sessionTimer?.cancel();

      final event = ARSessionEvent(
        type: ARSessionEventType.sessionPaused,
        sessionId: _currentSessionId!,
        timestamp: DateTime.now(),
        message: 'AR session paused',
      );
      onSessionEvent?.call(event);
    } catch (e) {
      debugPrint('$_logTag: Error pausing AR session: $e');
    }
  }

  /// Resume a paused AR session
  Future<void> resumeSession() async {
    if (_currentState != ARSessionState.paused) {
      debugPrint('$_logTag: Cannot resume session - not paused');
      return;
    }

    try {
      debugPrint('$_logTag: Resuming AR session');
      _updateState(ARSessionState.active);

      // Resume AR framework
      await _frameworkService.startARSession();

      // Resume performance monitoring
      _performanceService.startMonitoring();

      // Resume session timer
      _startSessionTimer();

      final event = ARSessionEvent(
        type: ARSessionEventType.sessionResumed,
        sessionId: _currentSessionId!,
        timestamp: DateTime.now(),
        message: 'AR session resumed',
      );
      onSessionEvent?.call(event);
    } catch (e) {
      debugPrint('$_logTag: Error resuming AR session: $e');
    }
  }

  /// Stop the current AR session
  Future<void> stopSession() async {
    if (_currentState == ARSessionState.idle ||
        _currentState == ARSessionState.ready) {
      debugPrint('$_logTag: No active session to stop');
      return;
    }

    try {
      debugPrint('$_logTag: Stopping AR session');
      _updateState(ARSessionState.stopping);

      // Stop AR framework
      _frameworkService.stopARSession();

      // Stop performance monitoring
      _performanceService.stopMonitoring();

      // Stop session timer
      _sessionTimer?.cancel();

      // Calculate session metrics
      final sessionMetrics = _calculateSessionMetrics();
      onMetricsUpdate?.call(sessionMetrics);

      final event = ARSessionEvent(
        type: ARSessionEventType.sessionEnded,
        sessionId: _currentSessionId!,
        timestamp: DateTime.now(),
        message: 'AR session ended',
        metrics: sessionMetrics,
      );
      onSessionEvent?.call(event);

      // Reset session state
      _resetSessionState();
      _updateState(ARSessionState.ready);

      debugPrint('$_logTag: AR session stopped successfully');
    } catch (e) {
      debugPrint('$_logTag: Error stopping AR session: $e');
      _updateState(ARSessionState.error);
    }
  }

  /// Handle app lifecycle changes
  void handleAppLifecycleState(AppLifecycleState state) {
    debugPrint('$_logTag: App lifecycle state changed: $state');

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        if (_currentState == ARSessionState.active) {
          pauseSession();
        }
        break;

      case AppLifecycleState.resumed:
        if (_currentState == ARSessionState.paused) {
          resumeSession();
        }
        break;

      case AppLifecycleState.detached:
        if (_currentState != ARSessionState.idle) {
          stopSession();
        }
        break;

      case AppLifecycleState.hidden:
        // Handle hidden state if needed
        break;
    }
  }

  /// Start session timer for metrics tracking
  void _startSessionTimer() {
    _sessionTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _totalSessionTime = DateTime.now().difference(_sessionStartTime!);

      // Update frame counts
      _totalFrames++;

      // Check for performance issues
      final performanceMetrics = _performanceService.getCurrentMetrics();
      if (performanceMetrics.currentFPS < 30) {
        _droppedFrames++;
      }
    });
  }

  /// Calculate session metrics
  ARSessionMetrics _calculateSessionMetrics() {
    final performanceMetrics = _performanceService.getCurrentMetrics();

    return ARSessionMetrics(
      sessionId: _currentSessionId!,
      duration: _totalSessionTime,
      totalFrames: _totalFrames,
      droppedFrames: _droppedFrames,
      averageFPS: performanceMetrics.averageFPS,
      peakMemoryUsageMB: performanceMetrics.peakMemoryUsageMB,
      errorCount: _errorRecoveryService.totalErrors,
    );
  }

  /// Reset session state
  void _resetSessionState() {
    _currentSessionId = null;
    _sessionStartTime = null;
    _totalFrames = 0;
    _droppedFrames = 0;
    _totalSessionTime = Duration.zero;
    _sessionTimer?.cancel();
    _sessionTimer = null;
  }

  /// Update session state and notify listeners
  void _updateState(ARSessionState newState) {
    if (_currentState != newState) {
      final previousState = _currentState;
      _currentState = newState;

      debugPrint(
          '$_logTag: State changed: ${previousState.name} -> ${newState.name}');
      onStateChanged?.call(newState);
    }
  }

  /// Handle performance updates
  void _handlePerformanceUpdate(perf.ARPerformanceMetrics metrics) {
    // Update session metrics based on performance data
    if (_currentState == ARSessionState.active) {
      // Could trigger optimizations or warnings
    }
  }

  /// Handle performance warnings
  void _handlePerformanceWarning(perf.ARPerformanceWarning warning) {
    final event = ARSessionEvent(
      type: ARSessionEventType.performanceWarning,
      sessionId: _currentSessionId ?? 'unknown',
      timestamp: DateTime.now(),
      message: warning.message,
    );
    onSessionEvent?.call(event);
  }

  /// Handle AR errors
  void _handleError(ARError error) {
    final event = ARSessionEvent(
      type: ARSessionEventType.error,
      sessionId: _currentSessionId ?? 'unknown',
      timestamp: DateTime.now(),
      message: error.message,
    );
    onSessionEvent?.call(event);
  }

  /// Handle recovery completion
  void _handleRecoveryCompleted(String operationId, bool success) {
    final event = ARSessionEvent(
      type: success
          ? ARSessionEventType.recoverySuccess
          : ARSessionEventType.recoveryFailed,
      sessionId: _currentSessionId ?? 'unknown',
      timestamp: DateTime.now(),
      message: success ? 'Recovery successful' : 'Recovery failed',
    );
    onSessionEvent?.call(event);
  }

  /// Handle framework errors
  void _handleFrameworkError(String error) {
    final arError = ARError(
      type: ARErrorType.unknown,
      message: error,
      operationId: 'framework_${DateTime.now().millisecondsSinceEpoch}',
    );

    _errorRecoveryService.handleError(arError);
  }

  /// Get current session information
  ARSessionInfo? getCurrentSessionInfo() {
    if (_currentSessionId == null) return null;

    return ARSessionInfo(
      sessionId: _currentSessionId!,
      state: _currentState,
      startTime: _sessionStartTime!,
      duration: _totalSessionTime,
      configuration: _configuration,
    );
  }

  /// Dispose of all resources
  void dispose() {
    stopSession();
    _frameworkService.dispose();
    _performanceService.dispose();
    _errorRecoveryService.dispose();
    _offlineContentService.dispose();

    debugPrint('$_logTag: AR session manager disposed');
  }

  // Getters
  ARSessionState get currentState => _currentState;
  String? get currentSessionId => _currentSessionId;
  bool get hasActiveSession => _currentState == ARSessionState.active;
  bool get isSessionPaused => _currentState == ARSessionState.paused;
}

/// AR Session configuration
class ARSessionConfiguration {
  final bool enablePerformanceMonitoring;
  final bool enableErrorRecovery;
  final bool enableOfflineContent;
  final Duration maxSessionDuration;
  final perf.ARPerformanceLevel targetPerformanceLevel;

  const ARSessionConfiguration({
    this.enablePerformanceMonitoring = true,
    this.enableErrorRecovery = true,
    this.enableOfflineContent = true,
    this.maxSessionDuration = const Duration(hours: 2),
    this.targetPerformanceLevel = perf.ARPerformanceLevel.medium,
  });
}

/// AR Session result
class ARSessionResult {
  final bool success;
  final String message;
  final String? sessionId;

  const ARSessionResult({
    required this.success,
    required this.message,
    this.sessionId,
  });

  @override
  String toString() {
    return 'ARSessionResult(success: $success, message: $message, sessionId: $sessionId)';
  }
}

/// AR Session information
class ARSessionInfo {
  final String sessionId;
  final ARSessionState state;
  final DateTime startTime;
  final Duration duration;
  final ARSessionConfiguration configuration;

  const ARSessionInfo({
    required this.sessionId,
    required this.state,
    required this.startTime,
    required this.duration,
    required this.configuration,
  });

  @override
  String toString() {
    return 'ARSessionInfo(id: $sessionId, state: ${state.name}, duration: ${duration.inSeconds}s)';
  }
}

/// AR Session event
class ARSessionEvent {
  final ARSessionEventType type;
  final String sessionId;
  final DateTime timestamp;
  final String message;
  final ARSessionMetrics? metrics;

  const ARSessionEvent({
    required this.type,
    required this.sessionId,
    required this.timestamp,
    required this.message,
    this.metrics,
  });

  @override
  String toString() {
    return 'ARSessionEvent(${type.name}: $message, session: $sessionId)';
  }
}

/// AR Session metrics
class ARSessionMetrics {
  final String sessionId;
  final Duration duration;
  final int totalFrames;
  final int droppedFrames;
  final double averageFPS;
  final int peakMemoryUsageMB;
  final int errorCount;

  const ARSessionMetrics({
    required this.sessionId,
    required this.duration,
    required this.totalFrames,
    required this.droppedFrames,
    required this.averageFPS,
    required this.peakMemoryUsageMB,
    required this.errorCount,
  });

  double get frameDropRate =>
      totalFrames > 0 ? droppedFrames / totalFrames : 0.0;

  @override
  String toString() {
    return 'ARSessionMetrics(duration: ${duration.inSeconds}s, fps: ${averageFPS.toStringAsFixed(1)}, memory: ${peakMemoryUsageMB}MB)';
  }
}

/// AR Session states
enum ARSessionState {
  idle,
  ready,
  starting,
  active,
  paused,
  stopping,
  error,
}

/// AR Session event types
enum ARSessionEventType {
  sessionStarted,
  sessionPaused,
  sessionResumed,
  sessionEnded,
  performanceWarning,
  error,
  recoverySuccess,
  recoveryFailed,
}
