import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  FlatList, 
  StyleSheet,
  Modal,
  Pressable,
  Dimensions
} from 'react-native';
import { Search, X, MapPin } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';
import { Airport, searchAirports } from '@/mocks/airports';

interface AirportDropdownProps {
  visible: boolean;
  onClose: () => void;
  onSelectAirport: (airport: Airport) => void;
  placeholder: string;
  initialValue?: string;
  title: string;
}

const { height: screenHeight } = Dimensions.get('window');

export const AirportDropdown: React.FC<AirportDropdownProps> = ({
  visible,
  onClose,
  onSelectAirport,
  placeholder,
  initialValue = '',
  title
}) => {
  const [searchQuery, setSearchQuery] = useState(initialValue);
  const [filteredAirports, setFilteredAirports] = useState<Airport[]>([]);

  useEffect(() => {
    if (visible) {
      setSearchQuery(initialValue);
      setFilteredAirports(searchAirports(initialValue));
    }
  }, [visible, initialValue]);

  useEffect(() => {
    setFilteredAirports(searchAirports(searchQuery));
  }, [searchQuery]);

  const handleSelectAirport = (airport: Airport) => {
    onSelectAirport(airport);
    onClose();
  };

  const renderAirportItem = ({ item }: { item: Airport }) => (
    <TouchableOpacity
      style={styles.airportItem}
      onPress={() => handleSelectAirport(item)}
      activeOpacity={0.7}
    >
      <View style={styles.airportIconContainer}>
        <MapPin size={16} color="#4C4DDC" />
      </View>
      <View style={styles.airportInfo}>
        <View style={styles.airportHeader}>
          <Text style={styles.airportCode}>{item.code}</Text>
          <Text style={styles.airportCity}>{item.city}</Text>
        </View>
        <Text style={styles.airportName} numberOfLines={1}>
          {item.name}
        </Text>
        <Text style={styles.airportCountry}>{item.country}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitle}>{title}</Text>
            <Text style={styles.headerSubtitle}>Search and select airport</Text>
          </View>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            activeOpacity={0.7}
          >
            <X size={20} color={colors.text} />
          </TouchableOpacity>
        </View>

        {/* Search Input */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Search size={18} color="#9CA3AF" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder={placeholder}
              placeholderTextColor="#9CA3AF"
              autoFocus
              autoCapitalize="none"
              autoCorrect={false}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity
                onPress={() => setSearchQuery('')}
                style={styles.clearButton}
                activeOpacity={0.7}
              >
                <X size={16} color="#9CA3AF" />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Results */}
        <View style={styles.resultsContainer}>
          {filteredAirports.length > 0 ? (
            <FlatList
              data={filteredAirports}
              renderItem={renderAirportItem}
              keyExtractor={(item) => item.code}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.listContent}
              ItemSeparatorComponent={() => <View style={styles.separator} />}
            />
          ) : (
            <View style={styles.noResultsContainer}>
              <MapPin size={48} color="#E5E7EB" />
              <Text style={styles.noResultsTitle}>No airports found</Text>
              <Text style={styles.noResultsSubtitle}>
                Try searching by city, airport name, or code
              </Text>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
    paddingBottom: spacing.lg,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontFamily: typography.fontFamily,
    fontSize: 20,
    fontWeight: '700' as any,
    color: colors.text,
    marginBottom: 2,
  },
  headerSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: 14,
    color: colors.textSecondary,
    fontWeight: '500' as any,
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F1F5F9',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: spacing.md,
  },
  searchContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    paddingHorizontal: 12,
    height: 48,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontFamily: typography.fontFamily,
    fontSize: 16,
    color: colors.text,
    padding: 0,
  },
  clearButton: {
    padding: 4,
    marginLeft: 8,
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  listContent: {
    paddingVertical: spacing.sm,
  },
  airportItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
    marginHorizontal: spacing.lg,
    marginVertical: 2,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  airportIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F0F4FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  airportInfo: {
    flex: 1,
  },
  airportHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  airportCode: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    fontWeight: '700' as any,
    color: '#4C4DDC',
    marginRight: spacing.sm,
  },
  airportCity: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    fontWeight: '600' as any,
    color: colors.text,
  },
  airportName: {
    fontFamily: typography.fontFamily,
    fontSize: 13,
    color: colors.textSecondary,
    marginBottom: 1,
    fontWeight: '500' as any,
  },
  airportCountry: {
    fontFamily: typography.fontFamily,
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '500' as any,
  },
  separator: {
    height: 1,
    backgroundColor: 'transparent',
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  noResultsTitle: {
    fontFamily: typography.fontFamily,
    fontSize: 18,
    fontWeight: '600' as any,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.xs,
  },
  noResultsSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
});