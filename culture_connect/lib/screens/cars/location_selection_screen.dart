import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/app_button.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'dart:async';

/// Location selection screen for car pickup location
/// Matches the design from locSelector.jpeg reference
class LocationSelectionScreen extends StatefulWidget {
  final String? initialLocation;
  final Function(String location, String address) onLocationSelected;

  const LocationSelectionScreen({
    super.key,
    this.initialLocation,
    required this.onLocationSelected,
  });

  @override
  State<LocationSelectionScreen> createState() =>
      _LocationSelectionScreenState();
}

class _LocationSelectionScreenState extends State<LocationSelectionScreen> {
  final TextEditingController _searchController = TextEditingController();
  String? _selectedLocation;
  String? _selectedAddress;
  List<LocationOption> _searchResults = [];
  bool _showSearchResults = false;

  // Map related variables
  Completer<GoogleMapController> _mapController = Completer();
  Set<Marker> _markers = {};
  LatLng _currentMapCenter =
      const LatLng(37.7749, -122.4194); // San Francisco default
  List<LocationSuggestion> _searchSuggestions = [];

  // Popular car pickup locations with car-themed icons
  final List<LocationOption> _popularLocations = [
    LocationOption(
      name: 'Downtown Airport',
      address: '123 Airport Blvd, Downtown',
      icon: Icons.local_taxi, // Car-themed: taxi for airport pickup
    ),
    LocationOption(
      name: 'City Center Plaza',
      address: '456 Main St, City Center',
      icon: Icons.directions_car, // Car-themed: car icon for city center
    ),
    LocationOption(
      name: 'Business District',
      address: '789 Corporate Ave, Business District',
      icon: Icons.car_rental, // Car-themed: car rental icon for business
    ),
    LocationOption(
      name: 'Shopping Mall',
      address: '321 Mall Dr, Shopping Center',
      icon: Icons.local_parking, // Car-themed: parking for shopping
    ),
    LocationOption(
      name: 'Train Station',
      address: '654 Railway St, Central Station',
      icon: Icons.commute, // Car-themed: commute icon for station pickup
    ),
  ];

  @override
  void initState() {
    super.initState();
    if (widget.initialLocation != null) {
      _searchController.text = widget.initialLocation!;
      _selectedLocation = widget.initialLocation;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _handleSearch(String query) {
    setState(() {
      if (query.isEmpty) {
        _searchResults = [];
        _searchSuggestions = [];
        _showSearchResults = false;
      } else {
        // Simulate API search results from popular locations
        _searchResults = _popularLocations
            .where((location) =>
                location.name.toLowerCase().contains(query.toLowerCase()) ||
                location.address.toLowerCase().contains(query.toLowerCase()))
            .toList();

        // Generate search suggestions (mock data - TODO: Replace with real API)
        _searchSuggestions = _generateSearchSuggestions(query);
        _showSearchResults = true;
      }
    });
  }

  List<LocationSuggestion> _generateSearchSuggestions(String query) {
    // TODO: Replace with real Google Places API or similar service
    // This is mock data for demonstration
    final mockSuggestions = [
      LocationSuggestion(
        name: '$query Airport Pickup',
        address:
            '🚗 Car Rental Counter • 123 Airport Blvd, ${query.toUpperCase()}',
        type: 'Airport',
        distance: '2.1 km',
        coordinates: LatLng(37.7749 + (query.length * 0.001), -122.4194),
      ),
      LocationSuggestion(
        name: '$query City Center Pickup',
        address:
            '🚗 Valet Service Available • 456 Main St, ${query.toUpperCase()} Center',
        type: 'City Center',
        distance: '1.5 km',
        coordinates: LatLng(37.7749 - (query.length * 0.001), -122.4194),
      ),
      LocationSuggestion(
        name: '$query Mall Pickup',
        address: '🚗 Free Parking • 789 Shopping Ave, ${query.toUpperCase()}',
        type: 'Shopping',
        distance: '3.2 km',
        coordinates: LatLng(37.7749, -122.4194 + (query.length * 0.001)),
      ),
    ];

    return mockSuggestions.take(3).toList();
  }

  void _selectLocation(LocationOption location) {
    setState(() {
      _selectedLocation = location.name;
      _selectedAddress = location.address;
      _searchController.text = location.name;
      _showSearchResults = false;
    });
  }

  void _handleConfirmSelection() {
    if (_selectedLocation != null && _selectedAddress != null) {
      widget.onLocationSelected(_selectedLocation!, _selectedAddress!);
      Navigator.pop(context);
    }
  }

  void _initializeMap() {
    // Initialize map with popular locations as markers
    _updateMapMarkers();
  }

  void _updateMapMarkers() {
    setState(() {
      _markers.clear();

      // Add markers for popular locations
      for (int i = 0; i < _popularLocations.length; i++) {
        final location = _popularLocations[i];
        _markers.add(
          Marker(
            markerId: MarkerId('popular_$i'),
            position: LatLng(
              37.7749 + (i * 0.01), // Mock coordinates
              -122.4194 + (i * 0.01),
            ),
            infoWindow: InfoWindow(
              title: location.name,
              snippet: '🚗 Car Pickup Available • ${location.address}',
            ),
            icon: BitmapDescriptor.defaultMarkerWithHue(
                BitmapDescriptor.hueOrange),
            onTap: () => _selectLocationFromMap(location),
          ),
        );
      }

      // Add marker for selected location if any
      if (_selectedLocation != null) {
        _markers.add(
          Marker(
            markerId: const MarkerId('selected'),
            position: _currentMapCenter,
            infoWindow: InfoWindow(
              title: _selectedLocation!,
              snippet: '🚗 Selected Car Pickup Location',
            ),
            icon:
                BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
          ),
        );
      }
    });
  }

  void _onMapTapped(LatLng position) {
    // TODO: Implement reverse geocoding to get address from coordinates
    // For now, create a car pickup location
    final location = LocationOption(
      name: 'Custom Pickup Location',
      address:
          'Lat: ${position.latitude.toStringAsFixed(4)}, Lng: ${position.longitude.toStringAsFixed(4)}',
      icon: Icons.directions_car, // Car-themed icon for custom pickup
    );
    _selectLocation(location);

    setState(() {
      _currentMapCenter = position;
    });
    _updateMapMarkers();
  }

  void _selectLocationFromMap(LocationOption location) {
    _selectLocation(location);
  }

  String? _getCustomMapStyle() {
    // Custom map style for premium appearance (Uber/Bolt-like)
    // TODO: Load from assets or API for more sophisticated styling
    return '''[
      {
        "featureType": "all",
        "elementType": "geometry",
        "stylers": [{"color": "#f5f5f5"}]
      },
      {
        "featureType": "road",
        "elementType": "geometry",
        "stylers": [{"color": "#ffffff"}]
      },
      {
        "featureType": "road",
        "elementType": "labels.text.fill",
        "stylers": [{"color": "#9ca5b3"}]
      },
      {
        "featureType": "water",
        "elementType": "geometry",
        "stylers": [{"color": "#c9c9c9"}]
      },
      {
        "featureType": "poi",
        "elementType": "geometry",
        "stylers": [{"color": "#eeeeee"}]
      }
    ]''';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.white,
        elevation: 0,
        leading: GestureDetector(
          onTap: () => Navigator.pop(context),
          child: const Icon(
            Icons.close,
            color: AppTheme.textPrimaryColor,
            size: 24,
          ),
        ),
        title: const Text(
          'Select Pickup Location',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        centerTitle: false,
      ),
      body: Column(
        children: [
          // Search input section
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingLg),
            color: AppTheme.white,
            child: Column(
              children: [
                // Search input field
                Container(
                  decoration: BoxDecoration(
                    color: AppTheme.backgroundSecondary,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppTheme.borderLight),
                  ),
                  child: TextField(
                    controller: _searchController,
                    onChanged: _handleSearch,
                    decoration: InputDecoration(
                      hintText: 'Search location...',
                      hintStyle: const TextStyle(
                        color: AppTheme.textSecondaryColor,
                        fontSize: 16,
                      ),
                      prefixIcon: const Icon(
                        Icons.search,
                        color: AppTheme.textSecondaryColor,
                        size: 20,
                      ),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? GestureDetector(
                              onTap: () {
                                _searchController.clear();
                                _handleSearch('');
                              },
                              child: const Icon(
                                Icons.clear,
                                color: AppTheme.textSecondaryColor,
                                size: 20,
                              ),
                            )
                          : const Icon(
                              Icons.my_location,
                              color: AppTheme.accentColor,
                              size: 20,
                            ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingMd,
                        vertical: AppTheme.spacingMd,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content area
          Expanded(
            child: _showSearchResults
                ? _buildSearchResults()
                : _buildMainContent(),
          ),

          // Bottom action button
          Container(
            padding: EdgeInsets.fromLTRB(
              AppTheme.spacingLg,
              AppTheme.spacingMd,
              AppTheme.spacingLg,
              AppTheme.spacingMd + MediaQuery.of(context).padding.bottom,
            ),
            decoration: const BoxDecoration(
              color: AppTheme.white,
              border: Border(
                top: BorderSide(color: AppTheme.borderLight, width: 1),
              ),
            ),
            child: AppButton(
              text: 'Select a Location',
              onPressed: _handleConfirmSelection,
              variant: ButtonVariant.filled,
              color: AppTheme.accentColor,
              height: 48,
              isDisabled: _selectedLocation == null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_searchResults.isEmpty && _searchSuggestions.isEmpty) {
      return const Center(
        child: Text(
          'No locations found',
          style: TextStyle(
            fontSize: 16,
            color: AppTheme.textSecondaryColor,
          ),
        ),
      );
    }

    return ListView(
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      children: [
        // Search suggestions section
        if (_searchSuggestions.isNotEmpty) ...[
          const Text(
            'Suggestions',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          ...(_searchSuggestions
              .map((suggestion) => _buildSuggestionTile(suggestion))),
          const SizedBox(height: AppTheme.spacingLg),
        ],

        // Search results from popular locations
        if (_searchResults.isNotEmpty) ...[
          const Text(
            'Popular Locations',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          ...(_searchResults.map((location) => _buildLocationTile(location))),
        ],
      ],
    );
  }

  Widget _buildSuggestionTile(LocationSuggestion suggestion) {
    return GestureDetector(
      onTap: () {
        final location = LocationOption(
          name: suggestion.name,
          address: suggestion.address,
          icon: _getIconForType(suggestion.type),
        );
        _selectLocation(location);

        // Update map center to suggestion coordinates
        setState(() {
          _currentMapCenter = suggestion.coordinates;
        });
        _updateMapMarkers();
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: AppTheme.spacingMd),
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        decoration: BoxDecoration(
          color: AppTheme.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppTheme.borderLight),
          boxShadow: [
            BoxShadow(
              color: AppTheme.shadowColor.withValues(alpha: 0.1),
              offset: const Offset(0, 1),
              blurRadius: 4,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppTheme.accentColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getIconForType(suggestion.type),
                color: AppTheme.accentColor,
                size: 20,
              ),
            ),
            const SizedBox(width: AppTheme.spacingMd),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    suggestion.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingXs),
                  Text(
                    suggestion.address,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.backgroundSecondary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    suggestion.type,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ),
                const SizedBox(height: AppTheme.spacingXs),
                Text(
                  suggestion.distance,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconForType(String type) {
    // Car-themed icons for pickup locations
    switch (type.toLowerCase()) {
      case 'airport':
        return Icons.local_taxi; // Car service to airport
      case 'city center':
        return Icons.directions_car; // Car for city center
      case 'shopping':
        return Icons.local_parking; // Parking for shopping
      case 'hotel':
        return Icons.car_rental; // Car rental at hotel
      case 'restaurant':
        return Icons.local_taxi; // Taxi/car service to restaurant
      default:
        return Icons.directions_car; // Default car icon
    }
  }

  Widget _buildMainContent() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Interactive Google Map
          Container(
            height: 200,
            margin: const EdgeInsets.all(AppTheme.spacingLg),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.shadowColor,
                  offset: const Offset(0, 2),
                  blurRadius: 8,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: GoogleMap(
                onMapCreated: (GoogleMapController controller) {
                  _mapController.complete(controller);
                  _initializeMap();
                },
                initialCameraPosition: CameraPosition(
                  target: _currentMapCenter,
                  zoom: 12.0,
                ),
                markers: _markers,
                onTap: _onMapTapped,
                style: _getCustomMapStyle(),
                zoomControlsEnabled: false,
                mapToolbarEnabled: false,
                myLocationButtonEnabled: false,
                compassEnabled: false,
              ),
            ),
          ),

          // Popular locations section
          Container(
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Popular Locations',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingMd),
                ...(_popularLocations
                    .map((location) => _buildLocationTile(location))),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationTile(LocationOption location) {
    final isSelected = _selectedLocation == location.name;

    return GestureDetector(
      onTap: () => _selectLocation(location),
      child: Container(
        margin: const EdgeInsets.only(bottom: AppTheme.spacingMd),
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.accentColor.withValues(alpha: 0.1)
              : AppTheme.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppTheme.accentColor : AppTheme.borderLight,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isSelected
                    ? AppTheme.accentColor
                    : AppTheme.backgroundSecondary,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                location.icon,
                color: isSelected ? AppTheme.white : AppTheme.accentColor,
                size: 20,
              ),
            ),
            const SizedBox(width: AppTheme.spacingMd),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    location.name,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isSelected
                          ? AppTheme.accentColor
                          : AppTheme.textPrimaryColor,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingXs),
                  Text(
                    location.address,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: AppTheme.accentColor,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }
}

class LocationOption {
  final String name;
  final String address;
  final IconData icon;

  LocationOption({
    required this.name,
    required this.address,
    required this.icon,
  });
}

class LocationSuggestion {
  final String name;
  final String address;
  final String type;
  final String distance;
  final LatLng coordinates;

  LocationSuggestion({
    required this.name,
    required this.address,
    required this.type,
    required this.distance,
    required this.coordinates,
  });
}
