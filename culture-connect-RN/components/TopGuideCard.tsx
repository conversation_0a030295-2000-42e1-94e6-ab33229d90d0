import React from 'react';
import { StyleSheet, View, Text, Image, Pressable } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Star, MapPin, Award, Shield } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';
import { TopGuide } from '@/mocks/guides';

interface TopGuideCardProps {
  guide: TopGuide;
  onPress: (guide: TopGuide) => void;
}

export function TopGuideCard({ guide, onPress }: TopGuideCardProps) {
  return (
    <Pressable
      style={styles.container}
      onPress={() => onPress(guide)}
      android_ripple={{ color: colors.primaryLight + '20' }}
    >
      <View style={styles.imageContainer}>
        <Image source={{ uri: guide.imageUrl }} style={styles.image} />
        {(guide.isVerified || guide.isCertified) && (
          <View style={styles.statusBadge}>
            {guide.isCertified ? (
              <View style={styles.certifiedBadge}>
                <Shield size={10} color={colors.white} />
              </View>
            ) : (
              <View style={styles.verifiedBadge}>
                <Award size={10} color={colors.white} />
              </View>
            )}
          </View>
        )}
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.7)']}
          style={styles.imageOverlay}
        />
      </View>
      
      <View style={styles.content}>
        <Text style={styles.name} numberOfLines={1}>
          {guide.name}
        </Text>
        
        <View style={styles.locationContainer}>
          <MapPin size={10} color={colors.textSecondary} />
          <Text style={styles.location} numberOfLines={1}>
            {guide.location}
          </Text>
        </View>
        
        <View style={styles.statsContainer}>
          <View style={styles.ratingContainer}>
            <Star size={12} color={colors.warning} fill={colors.warning} />
            <Text style={styles.rating}>{guide.rating}</Text>
          </View>
          
          <View style={styles.experienceContainer}>
            <Text style={styles.experience}>{guide.experience}+ years</Text>
          </View>
        </View>
        
        <View style={styles.specialtyContainer}>
          <Text style={styles.specialty} numberOfLines={1}>
            {guide.specialties[0]}
          </Text>
        </View>
        
        {(guide.isVerified || guide.isCertified) && (
          <View style={styles.statusContainer}>
            <View style={[
              styles.statusPill,
              guide.isCertified ? styles.certifiedPill : styles.verifiedPill
            ]}>
              {guide.isCertified ? (
                <>
                  <Shield size={8} color={colors.success} />
                  <Text style={[styles.statusText, { color: colors.success }]}>Certified</Text>
                </>
              ) : (
                <>
                  <Award size={8} color={colors.primary} />
                  <Text style={[styles.statusText, { color: colors.primary }]}>Verified</Text>
                </>
              )}
            </View>
          </View>
        )}
      </View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 20,
    marginRight: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
    overflow: 'hidden',
    width: 180,
    minHeight: 280,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  imageContainer: {
    position: 'relative',
  },
  image: {
    width: '100%',
    height: 140,
    resizeMode: 'cover',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 40,
  },
  statusBadge: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
  },
  certifiedBadge: {
    backgroundColor: colors.success,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 2,
    elevation: 2,
  },
  verifiedBadge: {
    backgroundColor: colors.primary,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 2,
    elevation: 2,
  },
  content: {
    padding: spacing.md,
  },
  name: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
    textAlign: 'center',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  location: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
    textAlign: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  rating: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginLeft: spacing.xs,
  },
  experienceContainer: {
    backgroundColor: colors.primaryLight + '20',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  experience: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium as any,
    color: colors.primary,
  },
  specialtyContainer: {
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 8,
    alignItems: 'center',
  },
  specialty: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  statusContainer: {
    alignItems: 'center',
    marginTop: spacing.xs,
  },
  statusPill: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: 6,
    gap: 2,
  },
  certifiedPill: {
    backgroundColor: colors.success + '15',
    borderWidth: 1,
    borderColor: colors.success + '30',
  },
  verifiedPill: {
    backgroundColor: colors.primary + '15',
    borderWidth: 1,
    borderColor: colors.primary + '30',
  },
  statusText: {
    fontFamily: typography.fontFamily,
    fontSize: 8,
    fontWeight: typography.weights.medium as any,
  },
});