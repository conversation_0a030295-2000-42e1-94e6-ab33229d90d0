# CultureConnect Android proguard rules
# Keep Flutter classes
-keep class io.flutter.** { *; }
-dontwarn io.flutter.**

# Keep Sceneform and ARCore classes referenced by reflection
-dontwarn com.google.ar.sceneform.animation.AnimationEngine
-dontwarn com.google.ar.sceneform.animation.AnimationLibraryLoader
-dontwarn com.google.ar.sceneform.assets.Loader
-dontwarn com.google.ar.sceneform.assets.ModelData
-dontwarn com.google.devtools.build.android.desugar.runtime.ThrowableExtension

# Keep MLKit optional language models (avoid R8 missing class errors)
-dontwarn com.google.mlkit.vision.text.chinese.**
-dontwarn com.google.mlkit.vision.text.devanagari.**
-dontwarn com.google.mlkit.vision.text.japanese.**
-dontwarn com.google.mlkit.vision.text.korean.**

# Stripe optional push provisioning (not used but referenced by SDK)
-dontwarn com.stripe.android.pushProvisioning.**

# Keep annotation classes
-keep class androidx.annotation.** { *; }
-dontwarn androidx.annotation.**

# Keep Kotlin metadata/stdlib
-keep class kotlin.** { *; }
-dontwarn kotlin.**

# Keep coroutines
-keep class kotlinx.coroutines.** { *; }
-dontwarn kotlinx.coroutines.**

