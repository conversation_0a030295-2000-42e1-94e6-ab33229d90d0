import React, { useState, useEffect, useRef } from "react";
import { StyleSheet, View, Text, ScrollView, FlatList, Pressable, RefreshControl, Modal, Image, Dimensions, Platform, PanResponder, Animated } from "react-native";
import { useRouter } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { Sparkles, Bell, Hotel, Plane, Car, UtensilsCrossed, FileText, Heart, MapPin, Briefcase, X, Check, MoreHorizontal, Clock, Crown } from "lucide-react-native";
import { LuxuryAuthModal } from "@/app/luxury/auth";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { SearchBar } from "@/components/SearchBar";
import { CategoryPill } from "@/components/CategoryPill";
import { DestinationCard } from "@/components/DestinationCard";
import { EventCard } from "@/components/EventCard";
import { AIRecommendationCard } from "@/components/AIRecommendationCard";
import { QuickActionButton } from "@/components/QuickActionButton";
import { PromotionalBanner } from "@/components/PromotionalBanner";
import { TopPlaceCard } from "@/components/TopPlaceCard";
import { TopGuideCard } from "@/components/TopGuideCard";
import { destinations } from "@/mocks/destinations";
import { events } from "@/mocks/events";
import { topPlaces, TopPlace } from "@/mocks/places";
import { topGuides, TopGuide } from "@/mocks/guides";
import { useUserStore } from "@/store/userStore";
import { useNotificationStore } from "@/store/notificationStore";
import { Destination, Event } from "@/types";
import { Notification } from "@/types/notification";

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const categories = [
  "All",
  "Popular",
  "Cultural",
  "Adventure",
  "Beach",
  "Mountain",
  "City",
  "Food",
];

const quickActions = [
  {
    title: "Hotels",
    icon: <Hotel size={24} color={colors.primary} />,
    backgroundColor: "#FFF5F5",
    action: "hotel",
  },
  {
    title: "Tours",
    icon: <MapPin size={24} color="#F59E0B" />,
    backgroundColor: "#FFFBEB",
    action: "tours",
  },
  {
    title: "Flights",
    icon: <Plane size={24} color={colors.secondary} />,
    backgroundColor: "#F0FDFA",
    action: "flights",
  },
  {
    title: "Cars",
    icon: <Car size={24} color={colors.accent} />,
    backgroundColor: "#FFF7ED",
    action: "cars",
  },
  {
    title: "Luxury",
    icon: <Crown size={24} color="#D946EF" />,
    backgroundColor: "#FAF5FF",
    action: "luxury",
  },
  {
    title: "Food",
    icon: <UtensilsCrossed size={24} color={colors.warning} />,
    backgroundColor: "#FFFBEB",
    action: "food",
  },
  {
    title: "Visa",
    icon: <FileText size={24} color="#8B5CF6" />,
    backgroundColor: "#F3F4F6",
    action: "visa",
  },
  {
    title: "Medical",
    icon: <Heart size={24} color="#EF4444" />,
    backgroundColor: "#FEF2F2",
    action: "medical",
  },
];

const getNotificationTypeColor = (type: string) => {
  switch (type) {
    case 'booking':
      return colors.primary;
    case 'promotion':
      return colors.accent;
    case 'reminder':
      return colors.secondary;
    case 'update':
      return '#8B5CF6';
    case 'alert':
      return colors.error;
    default:
      return colors.textSecondary;
  }
};

const getTimeAgo = (timestamp: Date) => {
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`;
  } else if (diffInMinutes < 1440) {
    return `${Math.floor(diffInMinutes / 60)}h ago`;
  } else {
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  }
};

export default function ExploreScreen() {
  const router = useRouter();
  const { user } = useUserStore();
  const {
    notifications,
    unreadCount,
    isOverlayVisible,
    displayedNotifications,
    currentPage,
    notificationsPerPage,
    loadNotifications,
    markAsRead,
    markAllAsRead,
    clearNotification,
    clearAllNotifications,
    showOverlay,
    hideOverlay,
    loadMoreNotifications
  } = useNotificationStore();

  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [refreshing, setRefreshing] = useState(false);
  const [filteredDestinations, setFilteredDestinations] = useState(destinations);
  const [showLuxuryAuth, setShowLuxuryAuth] = useState(false);

  // Gesture and animation states
  const overlayHeight = useRef(new Animated.Value(screenHeight * 0.5)).current;
  const [currentOverlayHeight, setCurrentOverlayHeight] = useState(screenHeight * 0.5);
  const minHeight = screenHeight * 0.3;
  const maxHeight = screenHeight * 0.85;

  // Pan responder for gesture handling
  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        return Math.abs(gestureState.dy) > 10;
      },
      onPanResponderGrant: () => {
        overlayHeight.setOffset(currentOverlayHeight);
      },
      onPanResponderMove: (evt, gestureState) => {
        // Invert the gesture - dragging up increases height
        const newHeight = currentOverlayHeight - gestureState.dy;
        if (newHeight >= minHeight && newHeight <= maxHeight) {
          overlayHeight.setValue(-gestureState.dy);
        }
      },
      onPanResponderRelease: (evt, gestureState) => {
        overlayHeight.flattenOffset();
        
        // Calculate the new height
        const newHeight = currentOverlayHeight - gestureState.dy;
        let finalHeight = newHeight;
        
        // Snap to boundaries if close
        if (newHeight < minHeight + 50) {
          finalHeight = minHeight;
        } else if (newHeight > maxHeight - 50) {
          finalHeight = maxHeight;
        }
        
        // Clamp to min/max
        finalHeight = Math.max(minHeight, Math.min(maxHeight, finalHeight));
        
        setCurrentOverlayHeight(finalHeight);
        
        Animated.spring(overlayHeight, {
          toValue: finalHeight,
          useNativeDriver: false,
          tension: 100,
          friction: 8,
        }).start();
      },
    })
  ).current;

  useEffect(() => {
    loadNotifications();
  }, []);

  useEffect(() => {
    if (selectedCategory === "All") {
      setFilteredDestinations(destinations);
    } else {
      setFilteredDestinations(
        destinations.filter((destination) =>
          destination.tags.some(
            (tag) => tag.toLowerCase() === selectedCategory.toLowerCase()
          )
        )
      );
    }
  }, [selectedCategory]);

  // Reset overlay height when opening
  useEffect(() => {
    if (isOverlayVisible) {
      const initialHeight = screenHeight * 0.5;
      setCurrentOverlayHeight(initialHeight);
      overlayHeight.setValue(initialHeight);
    }
  }, [isOverlayVisible]);

  const handleRefresh = () => {
    setRefreshing(true);
    loadNotifications();
    setTimeout(() => {
      setRefreshing(false);
    }, 1500);
  };

  const handleDestinationPress = (destination: Destination) => {
    router.push(`/experience/${destination.id}`);
  };

  const handleEventPress = (event: Event) => {
    router.push(`/event/${event.id}`);
  };

  const handleTopPlacePress = (place: TopPlace) => {
    // Navigate to place details
    router.push(`/destination/${place.id}`);
  };

  const handleTopGuidePress = (guide: TopGuide) => {
    // Navigate to guide swipe screen
    router.push(`/guides/swipe?guideId=${guide.id}`);
  };

  const handleAIRecommendationPress = () => {
    router.push(`/experience/${destinations[0].id}`);
  };

  const handleQuickActionPress = (action: string) => {
    switch (action) {
      case "hotel":
        // Navigate to hotel booking
        break;
      case "tours":
        // Navigate to tour packages
        break;
      case "flights":
        router.push("/booking/flight");
        break;
      case "cars":
        router.push("/cars");
        break;
      case "luxury":
        setShowLuxuryAuth(true);
        break;
      case "food":
        // Navigate to food experiences
        break;
      case "visa":
        // Navigate to visa services
        break;
      case "medical":
        // Navigate to medical services
        break;
    }
  };

  const handleFilterPress = () => {
    // Handle filter press
    console.log("Filter pressed");
  };

  const handlePromotionalBannerPress = () => {
    // Handle promotional banner press
    router.push(`/experience/${destinations[0].id}`);
  };

  const handleNotificationPress = () => {
    showOverlay();
  };

  const handleNotificationItemPress = (notification: Notification) => {
    if (!notification.isRead) {
      markAsRead(notification.id);
    }
    
    if (notification.actionUrl) {
      router.push(notification.actionUrl as any);
    }
    
    // Close overlay after navigation
    hideOverlay();
  };

  const handleClearNotification = (id: string) => {
    clearNotification(id);
  };

  const handleMarkAllAsRead = () => {
    markAllAsRead();
  };

  const handleClearAllNotifications = () => {
    clearAllNotifications();
  };

  const handleLoadMore = () => {
    const totalNotifications = notifications.length;
    const currentlyDisplayed = displayedNotifications.length;
    
    if (currentlyDisplayed < totalNotifications) {
      loadMoreNotifications();
    }
  };

  const hasMoreNotifications = displayedNotifications.length < notifications.length;

  const handleLuxuryAuthSuccess = () => {
    setShowLuxuryAuth(false);
    router.push("/luxury");
  };

  const handleLuxuryAuthClose = () => {
    setShowLuxuryAuth(false);
  };

  const renderNotificationItem = ({ item }: { item: Notification }) => (
    <Pressable
      style={[
        styles.notificationItem,
        !item.isRead && styles.unreadNotificationItem
      ]}
      onPress={() => handleNotificationItemPress(item)}
    >
      <View style={styles.notificationContent}>
        {item.imageUrl && (
          <Image source={{ uri: item.imageUrl }} style={styles.notificationImage} />
        )}
        <View style={styles.notificationTextContainer}>
          <View style={styles.notificationHeader}>
            <Text style={[styles.notificationTitle, !item.isRead && styles.unreadText]} numberOfLines={1}>
              {item.title}
            </Text>
            <View style={styles.notificationMeta}>
              <Clock size={12} color={colors.textSecondary} />
              <Text style={styles.notificationTime}>{getTimeAgo(item.timestamp)}</Text>
            </View>
          </View>
          <Text style={styles.notificationMessage} numberOfLines={2}>
            {item.message}
          </Text>
          <View style={styles.notificationFooter}>
            <View style={[styles.notificationTypeBadge, { backgroundColor: getNotificationTypeColor(item.type) + '20' }]}>
              <Text style={[styles.notificationTypeText, { color: getNotificationTypeColor(item.type) }]}>
                {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
              </Text>
            </View>
            {!item.isRead && <View style={styles.unreadDot} />}
          </View>
        </View>
        <Pressable
          style={styles.clearButton}
          onPress={(e) => {
            e.stopPropagation();
            handleClearNotification(item.id);
          }}
        >
          <X size={16} color={colors.textSecondary} />
        </Pressable>
      </View>
    </Pressable>
  );

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <Text style={styles.greeting}>Hello, {user?.name || "Guest"}</Text>
            <Pressable style={styles.iconButton} onPress={handleNotificationPress}>
              <Bell size={24} color={colors.text} />
              {unreadCount > 0 && (
                <View style={styles.notificationBadge}>
                  <Text style={styles.notificationBadgeText}>
                    {unreadCount > 9 ? '9+' : unreadCount}
                  </Text>
                </View>
              )}
            </Pressable>
          </View>
          <View style={styles.headerBottom}>
            <Text style={styles.mainTitle}>Let's Explore the World together</Text>
          </View>
        </View>

        <View style={styles.searchContainer}>
          <SearchBar
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search destination and explore"
            onFilterPress={handleFilterPress}
          />
        </View>

        <View style={styles.quickActionsContainer}>
          <Text style={styles.quickActionsTitle}>Quick Services</Text>
          <FlatList
            data={quickActions}
            renderItem={({ item }) => (
              <QuickActionButton
                title={item.title}
                icon={item.icon}
                backgroundColor={item.backgroundColor}
                onPress={() => handleQuickActionPress(item.action)}
              />
            )}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item.action}
            contentContainerStyle={styles.quickActionsList}
          />
        </View>

        <View style={styles.section}>
          <PromotionalBanner
            title="SAVE UP TO"
            subtitle="20% WITH Explorer+"
            imageUrl="https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
            onPress={handlePromotionalBannerPress}
          />
        </View>

        <View style={styles.categoriesContainer}>
          <Text style={styles.categoryTitle}>Explore by category</Text>
          <FlatList
            data={categories}
            renderItem={({ item }) => (
              <CategoryPill
                label={item}
                isSelected={selectedCategory === item}
                onPress={() => setSelectedCategory(item)}
              />
            )}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item}
            contentContainerStyle={styles.categoriesList}
          />
        </View>

        <View style={styles.section}>
          <AIRecommendationCard
            title="Your Perfect Weekend Getaway"
            description="Based on your preferences, we think you will love this cultural experience in Kyoto."
            imageUrl="https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80"
            onPress={handleAIRecommendationPress}
          />
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Trending Experiences</Text>
            <Pressable>
              <Text style={styles.seeAllText}>See All</Text>
            </Pressable>
          </View>
          {filteredDestinations.slice(0, 3).map((destination) => (
            <DestinationCard
              key={destination.id}
              destination={destination}
              onPress={handleDestinationPress}
            />
          ))}
        </View>

        {/* Top Places Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Top Places</Text>
            <Pressable>
              <Text style={styles.seeAllText}>See more</Text>
            </Pressable>
          </View>
          <FlatList
            data={topPlaces}
            renderItem={({ item }) => (
              <TopPlaceCard place={item} onPress={handleTopPlacePress} />
            )}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.topPlacesList}
            numColumns={1}
          />
        </View>

        {/* Top Guides Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Top Guides</Text>
            <Pressable onPress={() => router.push('/guides')}>
              <Text style={styles.seeAllText}>See more</Text>
            </Pressable>
          </View>
          <FlatList
            data={topGuides}
            renderItem={({ item }) => (
              <TopGuideCard guide={item} onPress={handleTopGuidePress} />
            )}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.guidesList}
          />
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Upcoming Events</Text>
            <Pressable>
              <Text style={styles.seeAllText}>See All</Text>
            </Pressable>
          </View>
          <FlatList
            data={events}
            renderItem={({ item }) => (
              <EventCard event={item} onPress={handleEventPress} />
            )}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.eventsList}
          />
        </View>

        <View style={styles.section}>
          <View style={styles.aiSuggestionHeader}>
            <Sparkles size={20} color={colors.primary} />
            <Text style={styles.aiSuggestionTitle}>
              AI-Powered Suggestions
            </Text>
          </View>
          <Text style={styles.aiSuggestionText}>
            Based on your interests in cultural experiences and food tourism, we
            recommend these destinations for your next trip.
          </Text>
          {destinations.slice(0, 2).map((destination) => (
            <DestinationCard
              key={destination.id}
              destination={destination}
              onPress={handleDestinationPress}
            />
          ))}
        </View>
      </ScrollView>

      {/* Enhanced Notification Overlay with Gesture Support */}
      <Modal
        visible={isOverlayVisible}
        transparent
        animationType="none"
        onRequestClose={hideOverlay}
        presentationStyle="overFullScreen"
      >
        <View style={styles.overlayBackdrop}>
          <Pressable style={styles.overlayBackdropTouchable} onPress={hideOverlay} />
          <Animated.View 
            style={[
              styles.notificationOverlay,
              {
                height: overlayHeight,
                maxHeight: maxHeight,
                minHeight: minHeight,
              }
            ]}
          >
            {/* Drag Handle */}
            <View style={styles.dragHandleContainer} {...panResponder.panHandlers}>
              <View style={styles.overlayHandle} />
              <Text style={styles.dragHintText}>Drag to expand</Text>
            </View>

            <View style={styles.overlayHeader}>
              <Text style={styles.overlayTitle}>Notifications</Text>
              <View style={styles.overlayActions}>
                {unreadCount > 0 && (
                  <Pressable style={styles.markAllReadButton} onPress={handleMarkAllAsRead}>
                    <Check size={16} color={colors.primary} />
                    <Text style={styles.markAllReadText}>Mark all read</Text>
                  </Pressable>
                )}
                <Pressable style={styles.closeButton} onPress={hideOverlay}>
                  <X size={24} color={colors.text} />
                </Pressable>
              </View>
            </View>

            {displayedNotifications.length === 0 ? (
              <View style={styles.emptyState}>
                <Bell size={48} color={colors.textLight} />
                <Text style={styles.emptyStateTitle}>No notifications</Text>
                <Text style={styles.emptyStateText}>You're all caught up!</Text>
              </View>
            ) : (
              <>
                <FlatList
                  data={displayedNotifications}
                  renderItem={renderNotificationItem}
                  keyExtractor={(item) => item.id}
                  showsVerticalScrollIndicator={true}
                  style={styles.notificationsList}
                  contentContainerStyle={styles.notificationsListContent}
                  bounces={false}
                />

                <View style={styles.overlayFooter}>
                  {hasMoreNotifications && (
                    <Pressable style={styles.loadMoreButton} onPress={handleLoadMore}>
                      <MoreHorizontal size={16} color={colors.primary} />
                      <Text style={styles.loadMoreText}>Load more</Text>
                    </Pressable>
                  )}
                  {displayedNotifications.length > 0 && (
                    <Pressable style={styles.clearAllButton} onPress={handleClearAllNotifications}>
                      <Text style={styles.clearAllText}>Clear all</Text>
                    </Pressable>
                  )}
                </View>
              </>
            )}
          </Animated.View>
        </View>
      </Modal>

      {/* Luxury Authentication Modal */}
      <LuxuryAuthModal
        visible={showLuxuryAuth}
        onClose={handleLuxuryAuthClose}
        onSuccess={handleLuxuryAuthSuccess}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.xl,
  },
  headerTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  headerBottom: {
    paddingRight: spacing.xl,
  },
  greeting: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
  },
  mainTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    lineHeight: typography.lineHeights.tight * typography.sizes.xxl,
  },
  iconButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.white,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.white,
  },
  notificationBadgeText: {
    color: colors.white,
    fontSize: 10,
    fontWeight: typography.weights.bold as any,
    textAlign: 'center',
  },
  searchContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  quickActionsContainer: {
    marginBottom: spacing.xl,
  },
  quickActionsTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.md,
  },
  quickActionsList: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
  },
  categoriesContainer: {
    marginBottom: spacing.xl,
  },
  categoryTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  categoriesList: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
  },
  section: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
  seeAllText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontWeight: typography.weights.medium as any,
  },
  eventsList: {
    paddingVertical: spacing.sm,
  },
  topPlacesList: {
    paddingVertical: spacing.sm,
  },
  guidesList: {
    paddingVertical: spacing.sm,
  },
  aiSuggestionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  aiSuggestionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  aiSuggestionText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.lg,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
  },
  // Enhanced Notification Overlay Styles
  overlayBackdrop: {
    flex: 1,
    backgroundColor: colors.overlay,
    justifyContent: 'flex-end',
  },
  overlayBackdropTouchable: {
    flex: 1,
  },
  notificationOverlay: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: colors.shadowDark,
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
    overflow: 'hidden',
  },
  dragHandleContainer: {
    paddingVertical: spacing.md,
    alignItems: 'center',
    backgroundColor: colors.backgroundSecondary,
  },
  overlayHandle: {
    width: 40,
    height: 4,
    backgroundColor: colors.borderLight,
    borderRadius: 2,
    marginBottom: spacing.xs,
  },
  dragHintText: {
    fontSize: typography.sizes.xs,
    color: colors.textLight,
    fontWeight: typography.weights.medium as any,
  },
  overlayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
    backgroundColor: colors.white,
  },
  overlayTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
  overlayActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  markAllReadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 8,
    backgroundColor: colors.primaryLight + '20',
  },
  markAllReadText: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontWeight: typography.weights.medium as any,
  },
  closeButton: {
    padding: spacing.xs,
  },
  notificationsList: {
    flex: 1,
  },
  notificationsListContent: {
    paddingBottom: spacing.lg,
  },
  notificationItem: {
    backgroundColor: colors.white,
    marginHorizontal: spacing.md,
    marginVertical: spacing.xs,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  unreadNotificationItem: {
    backgroundColor: colors.backgroundSecondary,
    borderColor: colors.primary + '30',
  },
  notificationContent: {
    flexDirection: 'row',
    padding: spacing.md,
    alignItems: 'flex-start',
  },
  notificationImage: {
    width: 48,
    height: 48,
    borderRadius: 8,
    marginRight: spacing.md,
  },
  notificationTextContainer: {
    flex: 1,
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.xs,
  },
  notificationTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    flex: 1,
    marginRight: spacing.sm,
  },
  unreadText: {
    color: colors.text,
  },
  notificationMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  notificationTime: {
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
  },
  notificationMessage: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.normal * typography.sizes.sm,
    marginBottom: spacing.sm,
  },
  notificationFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  notificationTypeBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  notificationTypeText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium as any,
    textTransform: 'capitalize',
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.primary,
  },
  clearButton: {
    padding: spacing.sm,
    marginLeft: spacing.sm,
  },
  overlayFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    backgroundColor: colors.white,
  },
  loadMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
    backgroundColor: colors.backgroundSecondary,
  },
  loadMoreText: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontWeight: typography.weights.medium as any,
  },
  clearAllButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  clearAllText: {
    fontSize: typography.sizes.sm,
    color: colors.error,
    fontWeight: typography.weights.medium as any,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xxl,
  },
  emptyStateTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.xs,
  },
  emptyStateText: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
});