import { Notification } from "@/types/notification";

export const notifications: Notification[] = [
  {
    id: "1",
    title: "Flight Booking Confirmed",
    message: "Your flight to Tokyo has been confirmed for March 15th. Check-in opens 24 hours before departure.",
    type: "booking",
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    isRead: false,
    imageUrl: "https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80",
    actionUrl: "/booking/flight-confirmation"
  },
  {
    id: "2",
    title: "Special Offer: 30% Off Hotels",
    message: "Limited time offer on premium hotels in your favorite destinations. Book now and save big!",
    type: "promotion",
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
    isRead: false,
    imageUrl: "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
  },
  {
    id: "3",
    title: "Trip Reminder",
    message: "Don't forget to pack your passport! Your trip to Bali starts in 3 days.",
    type: "reminder",
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
    isRead: true,
    imageUrl: "https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
  },
  {
    id: "4",
    title: "New AR Guide Available",
    message: "Explore Paris like never before with our new AR walking tour featuring historical landmarks.",
    type: "update",
    timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
    isRead: false,
    imageUrl: "https://images.unsplash.com/photo-1502602898536-47ad22581b52?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
  },
  {
    id: "5",
    title: "Weather Alert",
    message: "Heavy rain expected in your destination. Consider packing an umbrella for your upcoming trip.",
    type: "alert",
    timestamp: new Date(Date.now() - 18 * 60 * 60 * 1000), // 18 hours ago
    isRead: true,
    imageUrl: "https://images.unsplash.com/photo-1534274988757-a28bf1a57c17?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
  },
  {
    id: "6",
    title: "Kaia's Travel Tip",
    message: "Based on your preferences, I recommend visiting the local markets in Marrakech for authentic experiences.",
    type: "update",
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    isRead: false,
    imageUrl: "https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
  },
  {
    id: "7",
    title: "Booking Reminder",
    message: "Your hotel reservation expires in 2 hours. Complete your booking to secure your room.",
    type: "reminder",
    timestamp: new Date(Date.now() - 30 * 60 * 60 * 1000), // 30 hours ago
    isRead: true
  },
  {
    id: "8",
    title: "Flash Sale: Adventure Tours",
    message: "50% off on adventure tours this weekend only. Discover thrilling experiences at unbeatable prices.",
    type: "promotion",
    timestamp: new Date(Date.now() - 36 * 60 * 60 * 1000), // 36 hours ago
    isRead: false
  }
];