import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/models/ar_content.dart';
import 'dart:io';
import 'dart:convert';
import 'dart:async';

/// AR Offline Content Service
/// Manages caching and offline availability of AR content and models
class AROfflineContentService {
  static const String _logTag = 'AROfflineContentService';

  // Cache configuration
  static const int _maxCacheSizeMB = 500;
  static const int _maxCacheAgeDays = 30;
  static const String _cacheVersionKey = 'ar_cache_version';
  static const String _cacheMetadataKey = 'ar_cache_metadata';
  static const int _currentCacheVersion = 1;

  // Cache directories
  Directory? _modelsDirectory;
  Directory? _texturesDirectory;
  Directory? _metadataDirectory;

  // Cache state
  bool _isInitialized = false;
  final Map<String, ARCacheEntry> _cacheMetadata = {};
  int _currentCacheSizeMB = 0;

  // Progress callbacks
  Function(ARCacheProgress progress)? onCacheProgress;
  Function(String contentId, bool isAvailable)? onContentAvailabilityChanged;

  /// Initialize the offline content service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('$_logTag: Initializing offline content service');

      // Create cache directories
      await _createCacheDirectories();

      // Load cache metadata
      await _loadCacheMetadata();

      // Validate cache integrity
      await _validateCacheIntegrity();

      // Calculate current cache size
      await _calculateCacheSize();

      _isInitialized = true;
      debugPrint('$_logTag: Offline content service initialized');
      debugPrint('$_logTag: Current cache size: ${_currentCacheSizeMB}MB');
    } catch (e) {
      debugPrint('$_logTag: Error initializing offline content service: $e');
      rethrow;
    }
  }

  /// Create cache directories
  Future<void> _createCacheDirectories() async {
    final appDir = await getApplicationDocumentsDirectory();

    _modelsDirectory = Directory('${appDir.path}/ar_cache/models');
    _texturesDirectory = Directory('${appDir.path}/ar_cache/textures');
    _metadataDirectory = Directory('${appDir.path}/ar_cache/metadata');

    await _modelsDirectory!.create(recursive: true);
    await _texturesDirectory!.create(recursive: true);
    await _metadataDirectory!.create(recursive: true);

    debugPrint('$_logTag: Cache directories created');
  }

  /// Load cache metadata from persistent storage
  Future<void> _loadCacheMetadata() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check cache version
      final cacheVersion = prefs.getInt(_cacheVersionKey) ?? 0;
      if (cacheVersion != _currentCacheVersion) {
        debugPrint('$_logTag: Cache version mismatch, clearing cache');
        await clearCache();
        await prefs.setInt(_cacheVersionKey, _currentCacheVersion);
        return;
      }

      // Load metadata
      final metadataJson = prefs.getString(_cacheMetadataKey);
      if (metadataJson != null) {
        final metadata = json.decode(metadataJson) as Map<String, dynamic>;

        for (final entry in metadata.entries) {
          _cacheMetadata[entry.key] = ARCacheEntry.fromJson(entry.value);
        }

        debugPrint('$_logTag: Loaded ${_cacheMetadata.length} cache entries');
      }
    } catch (e) {
      debugPrint('$_logTag: Error loading cache metadata: $e');
      // Clear corrupted metadata
      _cacheMetadata.clear();
    }
  }

  /// Save cache metadata to persistent storage
  Future<void> _saveCacheMetadata() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final metadata = <String, dynamic>{};
      for (final entry in _cacheMetadata.entries) {
        metadata[entry.key] = entry.value.toJson();
      }

      await prefs.setString(_cacheMetadataKey, json.encode(metadata));
    } catch (e) {
      debugPrint('$_logTag: Error saving cache metadata: $e');
    }
  }

  /// Validate cache integrity
  Future<void> _validateCacheIntegrity() async {
    final invalidEntries = <String>[];

    for (final entry in _cacheMetadata.entries) {
      final cacheEntry = entry.value;

      // Check if files exist
      final modelFile =
          File('${_modelsDirectory!.path}/${cacheEntry.modelFileName}');
      final metadataFile =
          File('${_metadataDirectory!.path}/${entry.key}.json');

      if (!await modelFile.exists() || !await metadataFile.exists()) {
        debugPrint('$_logTag: Cache entry ${entry.key} has missing files');
        invalidEntries.add(entry.key);
        continue;
      }

      // Check if cache entry is expired
      final age = DateTime.now().difference(cacheEntry.cachedAt);
      if (age.inDays > _maxCacheAgeDays) {
        debugPrint('$_logTag: Cache entry ${entry.key} is expired');
        invalidEntries.add(entry.key);
        continue;
      }
    }

    // Remove invalid entries
    for (final contentId in invalidEntries) {
      await _removeCacheEntry(contentId);
    }

    if (invalidEntries.isNotEmpty) {
      await _saveCacheMetadata();
      debugPrint(
          '$_logTag: Removed ${invalidEntries.length} invalid cache entries');
    }
  }

  /// Calculate current cache size
  Future<void> _calculateCacheSize() async {
    int totalSize = 0;

    // Calculate models directory size
    if (await _modelsDirectory!.exists()) {
      await for (final entity in _modelsDirectory!.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }
    }

    // Calculate textures directory size
    if (await _texturesDirectory!.exists()) {
      await for (final entity in _texturesDirectory!.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }
    }

    _currentCacheSizeMB = (totalSize / (1024 * 1024)).round();
  }

  /// Cache AR content for offline use
  Future<bool> cacheARContent(ARContent content) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      debugPrint('$_logTag: Caching AR content: ${content.id}');

      // Check if already cached
      if (isContentCached(content.id)) {
        debugPrint('$_logTag: Content ${content.id} already cached');
        return true;
      }

      // Check cache size limits
      if (_currentCacheSizeMB >= _maxCacheSizeMB) {
        await _performCacheCleanup();
      }

      // Download and cache model
      final modelCached = await _cacheModel(content);
      if (!modelCached) {
        return false;
      }

      // Cache metadata
      await _cacheContentMetadata(content);

      // Update cache entry
      final cacheEntry = ARCacheEntry(
        contentId: content.id,
        modelFileName: '${content.id}.glb',
        thumbnailFileName: '${content.id}_thumb.jpg',
        cachedAt: DateTime.now(),
        sizeBytes: 0, // Will be updated after download
        version: '1.0',
      );

      _cacheMetadata[content.id] = cacheEntry;
      await _saveCacheMetadata();

      // Notify listeners
      onContentAvailabilityChanged?.call(content.id, true);

      debugPrint('$_logTag: Successfully cached AR content: ${content.id}');
      return true;
    } catch (e) {
      debugPrint('$_logTag: Error caching AR content ${content.id}: $e');
      return false;
    }
  }

  /// Cache AR model file
  Future<bool> _cacheModel(ARContent content) async {
    try {
      // TODO: Implement actual model download
      // This is a mock implementation

      final progress = ARCacheProgress(
        contentId: content.id,
        type: ARCacheProgressType.downloading,
        progress: 0.0,
        message: 'Starting download...',
      );
      onCacheProgress?.call(progress);

      // Simulate download progress
      for (int i = 0; i <= 100; i += 10) {
        await Future.delayed(const Duration(milliseconds: 100));

        final progressUpdate = ARCacheProgress(
          contentId: content.id,
          type: ARCacheProgressType.downloading,
          progress: i / 100.0,
          message: 'Downloading model... ${i}%',
        );
        onCacheProgress?.call(progressUpdate);
      }

      // Create mock model file
      final modelFile = File('${_modelsDirectory!.path}/${content.id}.glb');
      await modelFile.writeAsString('Mock AR model data for ${content.id}');

      final completeProgress = ARCacheProgress(
        contentId: content.id,
        type: ARCacheProgressType.complete,
        progress: 1.0,
        message: 'Download complete',
      );
      onCacheProgress?.call(completeProgress);

      return true;
    } catch (e) {
      debugPrint('$_logTag: Error caching model for ${content.id}: $e');

      final errorProgress = ARCacheProgress(
        contentId: content.id,
        type: ARCacheProgressType.error,
        progress: 0.0,
        message: 'Download failed: $e',
      );
      onCacheProgress?.call(errorProgress);

      return false;
    }
  }

  /// Cache AR content metadata
  Future<void> _cacheContentMetadata(ARContent content) async {
    final metadataFile = File('${_metadataDirectory!.path}/${content.id}.json');
    await metadataFile.writeAsString(json.encode(content.toJson()));
  }

  /// Check if content is cached
  bool isContentCached(String contentId) {
    return _cacheMetadata.containsKey(contentId);
  }

  /// Get cached AR content
  Future<ARContent?> getCachedContent(String contentId) async {
    if (!isContentCached(contentId)) {
      return null;
    }

    try {
      final metadataFile = File('${_metadataDirectory!.path}/$contentId.json');
      if (!await metadataFile.exists()) {
        return null;
      }

      final metadataJson = await metadataFile.readAsString();
      final metadata = json.decode(metadataJson);
      return ARContent.fromJson(metadata);
    } catch (e) {
      debugPrint('$_logTag: Error loading cached content $contentId: $e');
      return null;
    }
  }

  /// Get cached model file path
  String? getCachedModelPath(String contentId) {
    if (!isContentCached(contentId)) {
      return null;
    }

    final cacheEntry = _cacheMetadata[contentId]!;
    final modelPath = '${_modelsDirectory!.path}/${cacheEntry.modelFileName}';
    return modelPath;
  }

  /// Remove content from cache
  Future<bool> removeCachedContent(String contentId) async {
    try {
      await _removeCacheEntry(contentId);
      _cacheMetadata.remove(contentId);
      await _saveCacheMetadata();

      // Notify listeners
      onContentAvailabilityChanged?.call(contentId, false);

      debugPrint('$_logTag: Removed cached content: $contentId');
      return true;
    } catch (e) {
      debugPrint('$_logTag: Error removing cached content $contentId: $e');
      return false;
    }
  }

  /// Remove cache entry files
  Future<void> _removeCacheEntry(String contentId) async {
    final cacheEntry = _cacheMetadata[contentId];
    if (cacheEntry == null) return;

    // Remove model file
    final modelFile =
        File('${_modelsDirectory!.path}/${cacheEntry.modelFileName}');
    if (await modelFile.exists()) {
      await modelFile.delete();
    }

    // Remove thumbnail file
    final thumbnailFile =
        File('${_texturesDirectory!.path}/${cacheEntry.thumbnailFileName}');
    if (await thumbnailFile.exists()) {
      await thumbnailFile.delete();
    }

    // Remove metadata file
    final metadataFile = File('${_metadataDirectory!.path}/$contentId.json');
    if (await metadataFile.exists()) {
      await metadataFile.delete();
    }
  }

  /// Perform cache cleanup when size limit is reached
  Future<void> _performCacheCleanup() async {
    debugPrint('$_logTag: Performing cache cleanup');

    // Sort cache entries by last access time (oldest first)
    final sortedEntries = _cacheMetadata.entries.toList()
      ..sort((a, b) => a.value.cachedAt.compareTo(b.value.cachedAt));

    // Remove oldest entries until we're under the size limit
    for (final entry in sortedEntries) {
      if (_currentCacheSizeMB < _maxCacheSizeMB * 0.8) break;

      await removeCachedContent(entry.key);
      await _calculateCacheSize();
    }

    debugPrint(
        '$_logTag: Cache cleanup complete. New size: ${_currentCacheSizeMB}MB');
  }

  /// Clear all cached content
  Future<void> clearCache() async {
    try {
      debugPrint('$_logTag: Clearing all cached content');

      // Delete all cache directories
      if (await _modelsDirectory!.exists()) {
        await _modelsDirectory!.delete(recursive: true);
      }
      if (await _texturesDirectory!.exists()) {
        await _texturesDirectory!.delete(recursive: true);
      }
      if (await _metadataDirectory!.exists()) {
        await _metadataDirectory!.delete(recursive: true);
      }

      // Clear metadata
      _cacheMetadata.clear();
      _currentCacheSizeMB = 0;

      // Recreate directories
      await _createCacheDirectories();

      // Save empty metadata
      await _saveCacheMetadata();

      debugPrint('$_logTag: Cache cleared successfully');
    } catch (e) {
      debugPrint('$_logTag: Error clearing cache: $e');
    }
  }

  /// Get cache statistics
  ARCacheStatistics getCacheStatistics() {
    return ARCacheStatistics(
      totalEntries: _cacheMetadata.length,
      totalSizeMB: _currentCacheSizeMB,
      maxSizeMB: _maxCacheSizeMB,
      usagePercentage:
          (_currentCacheSizeMB / _maxCacheSizeMB * 100).clamp(0, 100),
    );
  }

  /// Get list of cached content IDs
  List<String> getCachedContentIds() {
    return _cacheMetadata.keys.toList();
  }

  /// Dispose of resources
  void dispose() {
    _cacheMetadata.clear();
    debugPrint('$_logTag: Offline content service disposed');
  }

  // Getters
  bool get isInitialized => _isInitialized;
  int get currentCacheSizeMB => _currentCacheSizeMB;
  int get maxCacheSizeMB => _maxCacheSizeMB;
  int get cachedContentCount => _cacheMetadata.length;
}

/// AR cache entry metadata
class ARCacheEntry {
  final String contentId;
  final String modelFileName;
  final String thumbnailFileName;
  final DateTime cachedAt;
  final int sizeBytes;
  final String version;

  const ARCacheEntry({
    required this.contentId,
    required this.modelFileName,
    required this.thumbnailFileName,
    required this.cachedAt,
    required this.sizeBytes,
    required this.version,
  });

  factory ARCacheEntry.fromJson(Map<String, dynamic> json) {
    return ARCacheEntry(
      contentId: json['contentId'],
      modelFileName: json['modelFileName'],
      thumbnailFileName: json['thumbnailFileName'],
      cachedAt: DateTime.parse(json['cachedAt']),
      sizeBytes: json['sizeBytes'],
      version: json['version'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'contentId': contentId,
      'modelFileName': modelFileName,
      'thumbnailFileName': thumbnailFileName,
      'cachedAt': cachedAt.toIso8601String(),
      'sizeBytes': sizeBytes,
      'version': version,
    };
  }
}

/// AR cache progress
class ARCacheProgress {
  final String contentId;
  final ARCacheProgressType type;
  final double progress;
  final String message;

  const ARCacheProgress({
    required this.contentId,
    required this.type,
    required this.progress,
    required this.message,
  });

  @override
  String toString() {
    return 'ARCacheProgress($contentId: ${(progress * 100).toStringAsFixed(1)}% - $message)';
  }
}

/// AR cache statistics
class ARCacheStatistics {
  final int totalEntries;
  final int totalSizeMB;
  final int maxSizeMB;
  final double usagePercentage;

  const ARCacheStatistics({
    required this.totalEntries,
    required this.totalSizeMB,
    required this.maxSizeMB,
    required this.usagePercentage,
  });

  @override
  String toString() {
    return 'ARCacheStatistics(entries: $totalEntries, size: ${totalSizeMB}MB/${maxSizeMB}MB, usage: ${usagePercentage.toStringAsFixed(1)}%)';
  }
}

/// Cache progress types
enum ARCacheProgressType {
  downloading,
  processing,
  complete,
  error,
}
