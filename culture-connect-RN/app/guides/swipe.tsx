import React, { useState, useRef } from 'react';
import { StyleSheet, View, Text, Image, Dimensions, PanResponder, Animated, Pressable, ScrollView, Platform } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft, Star, MapPin, Award, Shield, Heart, X, Share2, Globe, Calendar, Users, MessageCircle, Clock } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';
import { topGuides, TopGuide } from '@/mocks/guides';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const SWIPE_THRESHOLD = screenWidth * 0.25;
const IMAGE_HEIGHT = screenHeight * 0.55;

export default function GuideSwipeScreen() {
  const router = useRouter();
  const { guideId } = useLocalSearchParams();
  
  const [currentIndex, setCurrentIndex] = useState(() => {
    const index = topGuides.findIndex(guide => guide.id === guideId);
    return index >= 0 ? index : 0;
  });
  
  const [likedGuides, setLikedGuides] = useState<string[]>([]);
  const [rejectedGuides, setRejectedGuides] = useState<string[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isFavorite, setIsFavorite] = useState(false);
  
  const position = useRef(new Animated.ValueXY()).current;
  const rotate = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(1)).current;
  const scrollY = useRef(new Animated.Value(0)).current;
  
  const currentGuide = topGuides[currentIndex];
  const nextGuide = topGuides[currentIndex + 1];
  const allImages = currentGuide ? [currentGuide.imageUrl, ...currentGuide.gallery] : [];

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        return Math.abs(gestureState.dx) > 15 && Math.abs(gestureState.dy) < 80;
      },
      onPanResponderGrant: () => {
        position.setOffset({
          x: (position.x as any)._value,
          y: (position.y as any)._value,
        });
      },
      onPanResponderMove: (evt, gestureState) => {
        position.setValue({ x: gestureState.dx, y: 0 });
        rotate.setValue(gestureState.dx * 0.05);
      },
      onPanResponderRelease: (evt, gestureState) => {
        position.flattenOffset();
        
        if (gestureState.dx > SWIPE_THRESHOLD) {
          handleLike();
        } else if (gestureState.dx < -SWIPE_THRESHOLD) {
          handleReject();
        } else {
          Animated.parallel([
            Animated.spring(position, {
              toValue: { x: 0, y: 0 },
              useNativeDriver: false,
              tension: 100,
              friction: 8,
            }),
            Animated.spring(rotate, {
              toValue: 0,
              useNativeDriver: false,
              tension: 100,
              friction: 8,
            }),
          ]).start();
        }
      },
    })
  ).current;

  const handleLike = () => {
    if (!currentGuide) return;
    
    setLikedGuides(prev => [...prev, currentGuide.id]);
    animateCardOut(screenWidth * 1.5, () => {
      moveToNextGuide();
    });
  };

  const handleReject = () => {
    if (!currentGuide) return;
    
    setRejectedGuides(prev => [...prev, currentGuide.id]);
    animateCardOut(-screenWidth * 1.5, () => {
      moveToNextGuide();
    });
  };

  const animateCardOut = (toX: number, callback: () => void) => {
    Animated.parallel([
      Animated.timing(position, {
        toValue: { x: toX, y: -100 },
        duration: 300,
        useNativeDriver: false,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      }),
    ]).start(callback);
  };

  const moveToNextGuide = () => {
    if (currentIndex < topGuides.length - 1) {
      setCurrentIndex(prev => prev + 1);
    } else {
      setCurrentIndex(0);
    }
    setCurrentImageIndex(0);
    resetCard();
  };

  const resetCard = () => {
    position.setValue({ x: 0, y: 0 });
    rotate.setValue(0);
    opacity.setValue(1);
  };

  const getRotateStyle = () => {
    return rotate.interpolate({
      inputRange: [-200, 0, 200],
      outputRange: ['-15deg', '0deg', '15deg'],
    });
  };

  const getLikeOpacity = () => {
    return position.x.interpolate({
      inputRange: [0, 150],
      outputRange: [0, 1],
      extrapolate: 'clamp',
    });
  };

  const getRejectOpacity = () => {
    return position.x.interpolate({
      inputRange: [-150, 0],
      outputRange: [1, 0],
      extrapolate: 'clamp',
    });
  };

  const handleShare = () => {
    console.log('Share guide:', currentGuide?.name);
  };

  const handleFavorite = () => {
    setIsFavorite(!isFavorite);
  };

  const nextImage = () => {
    if (currentImageIndex < allImages.length - 1) {
      setCurrentImageIndex(prev => prev + 1);
    }
  };

  const prevImage = () => {
    if (currentImageIndex > 0) {
      setCurrentImageIndex(prev => prev - 1);
    }
  };

  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 150],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  const imageParallax = scrollY.interpolate({
    inputRange: [-100, 0, 100],
    outputRange: [50, 0, -50],
    extrapolate: 'clamp',
  });

  if (!currentGuide) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Pressable style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.text} />
          </Pressable>
          <Text style={styles.headerTitle}>Choose a Guide</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.emptyState}>
          <Text style={styles.emptyText}>No more guides available</Text>
          <Pressable style={styles.retryButton} onPress={() => router.back()}>
            <Text style={styles.retryButtonText}>Go Back</Text>
          </Pressable>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <View style={styles.container}>
      {/* Fixed Header */}
      <SafeAreaView edges={['top']} style={styles.headerContainer}>
        <View style={styles.header}>
          <Pressable style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.text} />
          </Pressable>
          <Text style={styles.headerTitle}>Choose a Guide</Text>
          <View style={styles.headerActions}>
            <Pressable style={styles.headerActionButton} onPress={handleShare}>
              <Share2 size={20} color={colors.text} />
            </Pressable>
          </View>
        </View>
      </SafeAreaView>

      {/* Main Card Container */}
      <View style={styles.cardContainer}>
        {/* Background Card */}
        {nextGuide && (
          <View style={[styles.card, styles.backgroundCard]}>
            <View style={styles.cardImageContainer}>
              <Image source={{ uri: nextGuide.imageUrl }} style={styles.cardImage} />
            </View>
          </View>
        )}

        {/* Active Card */}
        <Animated.View
          style={[
            styles.card,
            styles.activeCard,
            {
              transform: [
                { translateX: position.x },
                { translateY: position.y },
                { rotate: getRotateStyle() },
              ],
              opacity: opacity,
            },
          ]}
          {...(Platform.OS !== 'web' ? panResponder.panHandlers : {})}
        >
          {/* Swipe Overlays */}
          <Animated.View style={[styles.swipeOverlay, styles.likeOverlay, { opacity: getLikeOpacity() }]}>
            <View style={styles.swipeIndicator}>
              <Heart size={40} color={colors.success} fill={colors.success} />
              <Text style={[styles.swipeText, { color: colors.success }]}>LIKE</Text>
            </View>
          </Animated.View>

          <Animated.View style={[styles.swipeOverlay, styles.rejectOverlay, { opacity: getRejectOpacity() }]}>
            <View style={styles.swipeIndicator}>
              <X size={40} color={colors.error} />
              <Text style={[styles.swipeText, { color: colors.error }]}>PASS</Text>
            </View>
          </Animated.View>

          {/* Image Section */}
          <View style={styles.imageSection}>
            <Animated.View style={[styles.cardImageContainer, { transform: [{ translateY: imageParallax }] }]}>
              <Image 
                source={{ uri: allImages[currentImageIndex] || currentGuide.imageUrl }} 
                style={styles.cardImage} 
              />
              
              {/* Image Navigation */}
              {allImages.length > 1 && (
                <>
                  <Pressable 
                    style={[styles.imageNavButton, styles.prevButton]} 
                    onPress={prevImage}
                    disabled={currentImageIndex === 0}
                  >
                    <View style={[styles.imageNavIcon, currentImageIndex === 0 && styles.disabledNavIcon]}>
                      <Text style={styles.navArrow}>‹</Text>
                    </View>
                  </Pressable>
                  
                  <Pressable 
                    style={[styles.imageNavButton, styles.nextButton]} 
                    onPress={nextImage}
                    disabled={currentImageIndex === allImages.length - 1}
                  >
                    <View style={[styles.imageNavIcon, currentImageIndex === allImages.length - 1 && styles.disabledNavIcon]}>
                      <Text style={styles.navArrow}>›</Text>
                    </View>
                  </Pressable>
                </>
              )}
            </Animated.View>

            {/* Image Indicators */}
            {allImages.length > 1 && (
              <View style={styles.imageIndicators}>
                {allImages.map((_, index) => (
                  <View
                    key={index}
                    style={[
                      styles.indicator,
                      currentImageIndex === index && styles.activeIndicator
                    ]}
                  />
                ))}
              </View>
            )}

            {/* Status Badge */}
            {(currentGuide.isVerified || currentGuide.isCertified) && (
              <View style={styles.statusBadge}>
                {currentGuide.isCertified ? (
                  <>
                    <Shield size={14} color={colors.white} />
                    <Text style={styles.statusBadgeText}>Certified</Text>
                  </>
                ) : (
                  <>
                    <Award size={14} color={colors.white} />
                    <Text style={styles.statusBadgeText}>Verified</Text>
                  </>
                )}
              </View>
            )}

            {/* Progress Indicator */}
            <View style={styles.progressIndicator}>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill, 
                    { width: `${((currentIndex + 1) / topGuides.length) * 100}%` }
                  ]} 
                />
              </View>
              <Text style={styles.progressText}>
                {currentIndex + 1} / {topGuides.length}
              </Text>
            </View>
          </View>

          {/* Content Section */}
          <View style={styles.contentSection}>
            <ScrollView 
              style={styles.scrollContent}
              showsVerticalScrollIndicator={false}
              bounces={false}
              scrollEventThrottle={16}
              onScroll={Animated.event(
                [{ nativeEvent: { contentOffset: { y: scrollY } } }],
                { useNativeDriver: false }
              )}
            >
              {/* Guide Info Header */}
              <View style={styles.guideInfoHeader}>
                <View style={styles.guideBasicInfo}>
                  <Text style={styles.guideName}>{currentGuide.name}</Text>
                  <View style={styles.locationRow}>
                    <MapPin size={16} color={colors.textSecondary} />
                    <Text style={styles.guideLocation}>{currentGuide.location}</Text>
                  </View>
                  <View style={styles.ratingRow}>
                    <Star size={16} color={colors.warning} fill={colors.warning} />
                    <Text style={styles.ratingText}>{currentGuide.rating}</Text>
                    <Text style={styles.reviewCount}>({currentGuide.reviews.length} reviews)</Text>
                  </View>
                </View>
                <View style={styles.priceInfo}>
                  <Text style={styles.price}>${currentGuide.pricePerHour}</Text>
                  <Text style={styles.priceUnit}>per hour</Text>
                </View>
              </View>

              {/* Quick Stats */}
              <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                  <Calendar size={18} color={colors.accent} />
                  <Text style={styles.statValue}>{currentGuide.experience} years</Text>
                  <Text style={styles.statLabel}>Experience</Text>
                </View>
                <View style={styles.statItem}>
                  <Users size={18} color={colors.primary} />
                  <Text style={styles.statValue}>{currentGuide.totalTours}+</Text>
                  <Text style={styles.statLabel}>Tours</Text>
                </View>
                <View style={styles.statItem}>
                  <Clock size={18} color={colors.secondary} />
                  <Text style={styles.statValue}>{currentGuide.responseTime}</Text>
                  <Text style={styles.statLabel}>Response</Text>
                </View>
              </View>

              {/* About Section */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>About</Text>
                <Text style={styles.bioText}>{currentGuide.bio}</Text>
              </View>

              {/* Specialties */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Specialties</Text>
                <View style={styles.pillContainer}>
                  {currentGuide.specialties.map((specialty, index) => (
                    <View key={index} style={styles.pill}>
                      <Text style={styles.pillText}>{specialty}</Text>
                    </View>
                  ))}
                </View>
              </View>

              {/* Languages */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Languages</Text>
                <View style={styles.pillContainer}>
                  {currentGuide.languages.map((language, index) => (
                    <View key={index} style={[styles.pill, styles.languagePill]}>
                      <Globe size={12} color={colors.accent} />
                      <Text style={styles.pillText}>{language}</Text>
                    </View>
                  ))}
                </View>
              </View>

              {/* Reviews */}
              {currentGuide.reviews.length > 0 && (
                <View style={styles.section}>
                  <Text style={styles.sectionTitle}>Recent Reviews</Text>
                  {currentGuide.reviews.slice(0, 2).map((review) => (
                    <View key={review.id} style={styles.reviewCard}>
                      <View style={styles.reviewHeader}>
                        <Text style={styles.reviewerName}>{review.userName}</Text>
                        <View style={styles.reviewRating}>
                          <Star size={12} color={colors.warning} fill={colors.warning} />
                          <Text style={styles.reviewRatingText}>{review.rating}</Text>
                        </View>
                      </View>
                      <Text style={styles.reviewText}>"{review.comment}"</Text>
                    </View>
                  ))}
                </View>
              )}

              {/* Achievements */}
              {currentGuide.achievements.length > 0 && (
                <View style={styles.section}>
                  <Text style={styles.sectionTitle}>Achievements</Text>
                  <View style={styles.pillContainer}>
                    {currentGuide.achievements.map((achievement, index) => (
                      <View key={index} style={[styles.pill, styles.achievementPill]}>
                        <Award size={12} color={colors.warning} />
                        <Text style={styles.pillText}>{achievement}</Text>
                      </View>
                    ))}
                  </View>
                </View>
              )}

              <View style={styles.bottomPadding} />
            </ScrollView>
          </View>
        </Animated.View>
      </View>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <Pressable style={styles.rejectButton} onPress={handleReject}>
          <X size={24} color={colors.white} />
        </Pressable>
        
        <Pressable style={styles.likeButton} onPress={handleLike}>
          <Heart size={24} color={colors.white} fill={colors.white} />
        </Pressable>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundSecondary,
  },
  headerContainer: {
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
    zIndex: 100,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    height: 56,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    flex: 1,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    textAlign: 'center',
    marginHorizontal: spacing.md,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerActionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholder: {
    width: 40,
  },
  cardContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.md,
  },
  card: {
    width: screenWidth - (spacing.md * 2),
    height: screenHeight - 140,
    borderRadius: 20,
    backgroundColor: colors.white,
    shadowColor: colors.shadowDark,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 10,
    overflow: 'hidden',
    position: 'absolute',
  },
  backgroundCard: {
    transform: [{ scale: 0.95 }],
    opacity: 0.7,
    zIndex: 1,
  },
  activeCard: {
    zIndex: 2,
  },
  swipeOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  likeOverlay: {
    backgroundColor: colors.success + '15',
  },
  rejectOverlay: {
    backgroundColor: colors.error + '15',
  },
  swipeIndicator: {
    alignItems: 'center',
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    borderRadius: 20,
    shadowColor: colors.shadowDark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  swipeText: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    marginTop: spacing.sm,
    letterSpacing: 1,
  },
  imageSection: {
    height: IMAGE_HEIGHT,
    position: 'relative',
  },
  cardImageContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  cardImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  imageNavButton: {
    position: 'absolute',
    top: '50%',
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    transform: [{ translateY: -20 }],
  },
  prevButton: {
    left: spacing.md,
  },
  nextButton: {
    right: spacing.md,
  },
  imageNavIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledNavIcon: {
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
  },
  navArrow: {
    fontSize: 20,
    color: colors.white,
    fontWeight: 'bold',
  },
  imageIndicators: {
    position: 'absolute',
    bottom: spacing.lg,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: spacing.xs,
  },
  indicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  activeIndicator: {
    backgroundColor: colors.white,
    width: 18,
  },
  statusBadge: {
    position: 'absolute',
    top: spacing.lg,
    right: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    gap: spacing.xs,
  },
  statusBadgeText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.semibold as any,
    color: colors.white,
  },
  progressIndicator: {
    position: 'absolute',
    bottom: spacing.sm,
    left: spacing.lg,
    right: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  progressBar: {
    flex: 1,
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.white,
    borderRadius: 2,
  },
  progressText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.medium as any,
  },
  contentSection: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollContent: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  guideInfoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
    marginBottom: spacing.lg,
  },
  guideBasicInfo: {
    flex: 1,
  },
  guideName: {
    fontSize: 24,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    marginBottom: spacing.xs,
  },
  guideLocation: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  ratingText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  reviewCount: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  priceInfo: {
    alignItems: 'flex-end',
  },
  price: {
    fontSize: 28,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
  priceUnit: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
  },
  statsContainer: {
    flexDirection: 'row',
    gap: spacing.md,
    marginBottom: spacing.xl,
  },
  statItem: {
    flex: 1,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
    gap: spacing.xs,
  },
  statValue: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
  statLabel: {
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.md,
  },
  bioText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.md,
  },
  pillContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  pill: {
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  languagePill: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  achievementPill: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  pillText: {
    fontSize: typography.sizes.sm,
    color: colors.text,
    fontWeight: typography.weights.medium as any,
  },
  reviewCard: {
    backgroundColor: colors.backgroundSecondary,
    padding: spacing.lg,
    borderRadius: 12,
    marginBottom: spacing.sm,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  reviewerName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  reviewRating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  reviewRatingText: {
    fontSize: typography.sizes.sm,
    color: colors.text,
    fontWeight: typography.weights.medium as any,
  },
  reviewText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.md,
    fontStyle: 'italic',
  },
  bottomPadding: {
    height: 100,
  },
  actionButtons: {
    position: 'absolute',
    bottom: spacing.xl + 20,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: spacing.xl * 2,
    zIndex: 20,
  },
  rejectButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.error,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.shadowDark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  likeButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.success,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.shadowDark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  emptyText: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  retryButtonText: {
    fontSize: typography.sizes.md,
    color: colors.white,
    fontWeight: typography.weights.semibold as any,
  },
});