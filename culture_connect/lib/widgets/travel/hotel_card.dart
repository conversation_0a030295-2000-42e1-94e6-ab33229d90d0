import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:culture_connect/models/travel/hotel.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A card for displaying hotels with ExperienceCard-style design and hotel/short-let badges
class HotelCard extends StatefulWidget {
  /// The hotel to display
  final Hotel hotel;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Whether this is a short-let apartment (affects badge display)
  final bool isShortLet;

  /// Whether the card is displayed in grid mode (affects layout optimization)
  final bool isGridMode;

  /// Creates a new hotel card
  const HotelCard({
    super.key,
    required this.hotel,
    this.onTap,
    this.isShortLet = false,
    this.isGridMode = false,
  });

  @override
  State<HotelCard> createState() => _HotelCardState();
}

class _HotelCardState extends State<HotelCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleHoverEnter() {
    _animationController.forward();
  }

  void _handleHoverExit() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _handleHoverEnter(),
      onExit: (_) => _handleHoverExit(),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              margin: EdgeInsets.only(
                bottom: widget.isGridMode ? 8.0 : AppTheme.spacingMedium,
              ),
              constraints: widget.isGridMode
                  ? const BoxConstraints(
                      maxHeight: 275) // Reduced from 280 to 275
                  : null,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(8),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: widget.onTap,
                  borderRadius: BorderRadius.circular(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Image Section
                      _buildImageSection(),
                      // Content Section (flexible in grid mode)
                      widget.isGridMode
                          ? Flexible(child: _buildContentSection())
                          : _buildContentSection(),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildImageSection() {
    // Grid mode optimizations - reduce image height
    final imageHeight = widget.isGridMode ? 140.0 : 200.0;

    return Stack(
      children: [
        ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
          child: Stack(
            children: [
              CachedNetworkImage(
                imageUrl: widget.hotel.imageUrl,
                width: double.infinity,
                height: imageHeight,
                fit: BoxFit.cover,
                fadeInDuration: AppTheme.animationFast,
                // Performance optimizations
                memCacheWidth: 400,
                memCacheHeight: 300,
                maxWidthDiskCache: 800,
                maxHeightDiskCache: 600,
                placeholder: (context, url) => Container(
                  width: double.infinity,
                  height: imageHeight,
                  color: AppTheme.surfaceVariant,
                  child: const Icon(
                    Icons.image_outlined,
                    color: AppTheme.textSecondaryColor,
                    size: 32,
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  width: double.infinity,
                  height: imageHeight,
                  color: AppTheme.surfaceVariant,
                  child: const Icon(
                    Icons.broken_image_outlined,
                    color: AppTheme.textSecondaryColor,
                    size: 32,
                  ),
                ),
              ),

              // Hotel/Short-let badge
              Positioned(
                top: 12,
                left: 12,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: widget.isShortLet
                        ? AppTheme.secondaryColor
                        : AppTheme.primaryColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        widget.isShortLet ? Icons.apartment : Icons.hotel,
                        color: Colors.white,
                        size: 14,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        widget.isShortLet ? 'Short-let' : 'Hotel',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Star rating badge - text-based format
              Positioned(
                top: 12,
                right: 12,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: widget.isGridMode ? 6 : 8,
                    vertical: widget.isGridMode ? 3 : 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withAlpha(128),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    '${widget.hotel.starRating.value}-star',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: widget.isGridMode ? 11 : 12,
                      fontWeight: FontWeight.w600,
                      height: 1.0,
                    ),
                  ),
                ),
              ),

              // Sale badge
              if (widget.hotel.isOnSale)
                Positioned(
                  bottom: 12,
                  right: 12,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.errorColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '${widget.hotel.discountPercentage?.toStringAsFixed(0) ?? ''}% OFF',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContentSection() {
    // Grid mode optimizations - more aggressive spacing reduction
    final contentPadding = widget.isGridMode
        ? const EdgeInsets.all(10)
        : const EdgeInsets.all(16); // Reduced from 12 to 10
    final titleFontSize = widget.isGridMode ? 14.0 : 16.0;
    final titleMaxLines = widget.isGridMode ? 1 : 2;
    final verticalSpacing =
        widget.isGridMode ? 4.0 : 8.0; // Reduced from 6.0 to 4.0
    final smallVerticalSpacing =
        widget.isGridMode ? 1.0 : 4.0; // Reduced from 2.0 to 1.0

    return Padding(
      padding: contentPadding,
      child: ConstrainedBox(
        constraints: widget.isGridMode
            ? const BoxConstraints(
                maxHeight: 130) // Constrain content section height
            : const BoxConstraints(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title with height constraint
            Text(
              widget.hotel.name,
              style: TextStyle(
                fontSize: titleFontSize,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimaryColor,
                height: widget.isGridMode
                    ? 1.2
                    : null, // Constrain line height in grid mode
              ),
              maxLines: titleMaxLines,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: smallVerticalSpacing),

            // Location (more aggressive hiding in grid mode)
            if (!widget.isGridMode || widget.hotel.location.length <= 20)
              Row(
                children: [
                  const Icon(
                    Icons.location_on_outlined,
                    size: 16,
                    color: AppTheme.textSecondaryColor,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      widget.hotel.location,
                      style: TextStyle(
                        fontSize: widget.isGridMode ? 12.0 : 14.0,
                        color: AppTheme.textSecondaryColor,
                        height: widget.isGridMode
                            ? 1.1
                            : null, // Constrain line height
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),

            if (!widget.isGridMode || widget.hotel.location.length <= 20)
              SizedBox(height: verticalSpacing),

            // Rating and Reviews (optimized for grid mode)
            Row(
              children: [
                const Icon(
                  Icons.star,
                  size: 16,
                  color: AppTheme.warningColor,
                ),
                const SizedBox(width: 4),
                Text(
                  widget.hotel.rating.toStringAsFixed(1),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textPrimaryColor,
                    height:
                        widget.isGridMode ? 1.1 : null, // Constrain line height
                  ),
                ),
                if (!widget.isGridMode) ...[
                  const SizedBox(width: 4),
                  Flexible(
                    child: Text(
                      '(${widget.hotel.reviewCount} reviews)',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryColor,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ],
            ),

            SizedBox(height: verticalSpacing),

            // Price (optimized for grid mode)
            Row(
              children: [
                if (widget.hotel.isOnSale &&
                    widget.hotel.originalPrice != null &&
                    !widget.isGridMode) ...[
                  Text(
                    widget.hotel.formattedOriginalPrice!,
                    style: const TextStyle(
                      fontSize: 14,
                      decoration: TextDecoration.lineThrough,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Flexible(
                  child: Text(
                    widget.hotel.formattedPrice,
                    style: TextStyle(
                      fontSize: widget.isGridMode ? 16.0 : 18.0,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                      height: widget.isGridMode
                          ? 1.1
                          : null, // Constrain line height
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (!widget.isGridMode)
                  const Text(
                    '/night',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
