import 'package:flutter/foundation.dart';

/// Car model matching React Native implementation
@immutable
class Car {
  final String id;
  final String name;
  final String brand;
  final String model;
  final String category;
  final double pricePerDay;
  final double rating;
  final int reviewCount;
  final String imageUrl;
  final List<String> features;
  final String fuelType;
  final String transmission;
  final int seats;
  final int doors;
  final bool airConditioning;
  final bool available;
  final String location;
  final String description;

  const Car({
    required this.id,
    required this.name,
    required this.brand,
    required this.model,
    required this.category,
    required this.pricePerDay,
    required this.rating,
    required this.reviewCount,
    required this.imageUrl,
    required this.features,
    required this.fuelType,
    required this.transmission,
    required this.seats,
    required this.doors,
    required this.airConditioning,
    required this.available,
    required this.location,
    required this.description,
  });

  Car copyWith({
    String? id,
    String? name,
    String? brand,
    String? model,
    String? category,
    double? pricePerDay,
    double? rating,
    int? reviewCount,
    String? imageUrl,
    List<String>? features,
    String? fuelType,
    String? transmission,
    int? seats,
    int? doors,
    bool? airConditioning,
    bool? available,
    String? location,
    String? description,
  }) {
    return Car(
      id: id ?? this.id,
      name: name ?? this.name,
      brand: brand ?? this.brand,
      model: model ?? this.model,
      category: category ?? this.category,
      pricePerDay: pricePerDay ?? this.pricePerDay,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      imageUrl: imageUrl ?? this.imageUrl,
      features: features ?? this.features,
      fuelType: fuelType ?? this.fuelType,
      transmission: transmission ?? this.transmission,
      seats: seats ?? this.seats,
      doors: doors ?? this.doors,
      airConditioning: airConditioning ?? this.airConditioning,
      available: available ?? this.available,
      location: location ?? this.location,
      description: description ?? this.description,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Car &&
        other.id == id &&
        other.name == name &&
        other.brand == brand &&
        other.model == model &&
        other.category == category &&
        other.pricePerDay == pricePerDay &&
        other.rating == rating &&
        other.reviewCount == reviewCount &&
        other.imageUrl == imageUrl &&
        listEquals(other.features, features) &&
        other.fuelType == fuelType &&
        other.transmission == transmission &&
        other.seats == seats &&
        other.doors == doors &&
        other.airConditioning == airConditioning &&
        other.available == available &&
        other.location == location &&
        other.description == description;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      brand,
      model,
      category,
      pricePerDay,
      rating,
      reviewCount,
      imageUrl,
      Object.hashAll(features),
      fuelType,
      transmission,
      seats,
      doors,
      airConditioning,
      available,
      location,
      description,
    );
  }

  @override
  String toString() {
    return 'Car(id: $id, name: $name, brand: $brand, model: $model, category: $category, pricePerDay: $pricePerDay, rating: $rating, reviewCount: $reviewCount, imageUrl: $imageUrl, features: $features, fuelType: $fuelType, transmission: $transmission, seats: $seats, doors: $doors, airConditioning: $airConditioning, available: $available, location: $location, description: $description)';
  }
}

/// Car categories matching React Native implementation
class CarCategories {
  static const List<String> all = [
    'All',
    'SUV',
    'Hatchback',
    'Sedan',
    'Luxury',
    'Luxury Bus',
    'Electric',
  ];

  static const String suv = 'SUV';
  static const String hatchback = 'Hatchback';
  static const String sedan = 'Sedan';
  static const String luxury = 'Luxury';
  static const String luxuryBus = 'Luxury Bus';
  static const String electric = 'Electric';
}

/// Booking duration options
class BookingDuration {
  static const List<String> options = [
    '1 day',
    '3 days',
    '1 week',
    '1 month',
  ];

  static int getDaysFromDuration(String duration) {
    switch (duration) {
      case '1 day':
        return 1;
      case '3 days':
        return 3;
      case '1 week':
        return 7;
      case '1 month':
        return 30;
      default:
        return 1;
    }
  }
}

/// Insurance options for car rental
enum InsuranceType {
  basic,
  premium,
  comprehensive,
}

extension InsuranceTypeExtension on InsuranceType {
  String get name {
    switch (this) {
      case InsuranceType.basic:
        return 'Basic Coverage';
      case InsuranceType.premium:
        return 'Premium Coverage';
      case InsuranceType.comprehensive:
        return 'Comprehensive Coverage';
    }
  }

  String get description {
    switch (this) {
      case InsuranceType.basic:
        return 'Standard protection included';
      case InsuranceType.premium:
        return 'Enhanced protection with lower deductible';
      case InsuranceType.comprehensive:
        return 'Full protection with zero deductible';
    }
  }

  double get dailyCost {
    switch (this) {
      case InsuranceType.basic:
        return 0.0;
      case InsuranceType.premium:
        return 25.0;
      case InsuranceType.comprehensive:
        return 45.0;
    }
  }
}

/// Security add-on options
enum SecurityType {
  none,
  personal,
  full,
}

extension SecurityTypeExtension on SecurityType {
  String get name {
    switch (this) {
      case SecurityType.none:
        return 'No Security';
      case SecurityType.personal:
        return 'Personal Security';
      case SecurityType.full:
        return 'Full Security Detail';
    }
  }

  String get description {
    switch (this) {
      case SecurityType.none:
        return 'Standard rental without additional security';
      case SecurityType.personal:
        return 'Professional bodyguard without vehicle';
      case SecurityType.full:
        return 'Complete security team with escort vehicle';
    }
  }

  List<String> get features {
    switch (this) {
      case SecurityType.none:
        return [];
      case SecurityType.personal:
        return [
          'Personal protection',
          '24/7 availability',
          'Trained professional'
        ];
      case SecurityType.full:
        return [
          'Security team',
          'Escort vehicle',
          'Route planning',
          'Emergency response'
        ];
    }
  }

  double get dailyCost {
    switch (this) {
      case SecurityType.none:
        return 0.0;
      case SecurityType.personal:
        return 50.0;
      case SecurityType.full:
        return 120.0;
    }
  }
}
