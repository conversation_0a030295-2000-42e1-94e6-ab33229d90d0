import 'package:flutter/material.dart';
import 'package:culture_connect/theme/kaia_design_tokens.dart';
import 'package:culture_connect/widgets/ai_suggestions/ai_suggestion_card.dart';
import 'package:culture_connect/widgets/skeleton_loading.dart';
import 'package:culture_connect/screens/ai_suggestion_detail_screen.dart';

/// Comprehensive AI Suggestions screen that displays all AI-powered suggestions
/// <PERSON><PERSON> as the destination when users tap "See All" from AI Powered Suggestions
class AISuggestionsScreen extends StatefulWidget {
  const AISuggestionsScreen({super.key});

  @override
  State<AISuggestionsScreen> createState() => _AISuggestionsScreenState();
}

class _AISuggestionsScreenState extends State<AISuggestionsScreen>
    with TickerProviderStateMixin {
  bool _isLoading = true;
  bool _isGridView = true;
  String _searchQuery = '';
  String? _selectedCategory;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Categories for filtering
  final List<String> _categories = [
    'All',
    'Cultural',
    'Adventure',
    'Food & Drink',
    'Nature',
    'Historical',
    'Art & Museums',
    'Nightlife',
    'Wellness',
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _loadAISuggestions();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadAISuggestions() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate loading delay
    await Future.delayed(const Duration(milliseconds: 800));

    setState(() {
      _isLoading = false;
    });

    _animationController.forward();
  }

  List<Map<String, dynamic>> get _filteredSuggestions {
    return _mockAISuggestions.where((suggestion) {
      final matchesSearch = _searchQuery.isEmpty ||
          suggestion['title']
              .toString()
              .toLowerCase()
              .contains(_searchQuery.toLowerCase()) ||
          suggestion['location']
              .toString()
              .toLowerCase()
              .contains(_searchQuery.toLowerCase()) ||
          suggestion['matchReason']
              .toString()
              .toLowerCase()
              .contains(_searchQuery.toLowerCase());

      final matchesCategory = _selectedCategory == null ||
          _selectedCategory == 'All' ||
          suggestion['category'] == _selectedCategory;

      return matchesSearch && matchesCategory;
    }).toList();
  }

  void _handleAISuggestionPress(String suggestionId) {
    // Find the suggestion data
    final suggestion = _mockAISuggestions.firstWhere(
      (sug) => sug['id'] == suggestionId,
      orElse: () => _mockAISuggestions.first,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AISuggestionDetailScreen(
          id: suggestionId,
          title: suggestion['title'],
          description: suggestion['description'] ??
              'AI-powered suggestion tailored for your preferences.',
          imageUrl: suggestion['imageUrl'],
          aiConfidence: suggestion['aiConfidence'].toDouble(),
          duration: suggestion['duration'],
          price: suggestion['price'],
          tags: List<String>.from(suggestion['tags']),
          matchReason: suggestion['matchReason'],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: KaiaDesignTokens.neutralGray50,
      appBar: AppBar(
        backgroundColor: KaiaDesignTokens.neutralWhite,
        elevation: 0,
        title: Row(
          children: [
            const Icon(
              Icons.auto_awesome,
              size: 24,
              color: KaiaDesignTokens.primaryIndigo,
            ),
            const SizedBox(width: 8),
            Expanded(
              // Defensive overflow handling - allow text to shrink
              child: Text(
                'AI Powered Suggestions',
                style: const TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: KaiaDesignTokens.neutralGray900,
                ),
                maxLines: 1, // Prevent multi-line overflow
                overflow: TextOverflow.ellipsis, // Graceful text truncation
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(
              _isGridView ? Icons.view_list : Icons.grid_view,
              color: KaiaDesignTokens.neutralGray700,
            ),
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          _buildSearchAndFilterSection(),

          // Results Count
          _buildResultsCount(),

          // AI Suggestions Grid/List
          Expanded(
            child: _isLoading
                ? _buildLoadingState()
                : _filteredSuggestions.isEmpty
                    ? _buildEmptyState()
                    : FadeTransition(
                        opacity: _fadeAnimation,
                        child:
                            _isGridView ? _buildGridView() : _buildListView(),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilterSection() {
    return Container(
      color: KaiaDesignTokens.neutralWhite,
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
      child: Column(
        children: [
          // Search Bar
          Container(
            decoration: BoxDecoration(
              color: KaiaDesignTokens.neutralGray100,
              borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
              border: Border.all(
                color: KaiaDesignTokens.neutralGray200,
                width: 1,
              ),
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Search AI suggestions...',
                hintStyle: TextStyle(
                  color: KaiaDesignTokens.neutralGray500,
                  fontSize: 16,
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: KaiaDesignTokens.neutralGray500,
                  size: 20,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: KaiaDesignTokens.spacing16,
                  vertical: KaiaDesignTokens.spacing12,
                ),
              ),
            ),
          ),

          const SizedBox(height: KaiaDesignTokens.spacing16),

          // Category Filter
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = _selectedCategory == category ||
                    (_selectedCategory == null && category == 'All');

                return Padding(
                  padding: EdgeInsets.only(
                    right: index < _categories.length - 1
                        ? KaiaDesignTokens.spacing8
                        : 0,
                  ),
                  child: FilterChip(
                    label: Text(category),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = selected ? category : null;
                        if (category == 'All') _selectedCategory = null;
                      });
                    },
                    backgroundColor: KaiaDesignTokens.neutralWhite,
                    selectedColor:
                        KaiaDesignTokens.primaryIndigo.withOpacity(0.1),
                    checkmarkColor: KaiaDesignTokens.primaryIndigo,
                    labelStyle: TextStyle(
                      color: isSelected
                          ? KaiaDesignTokens.primaryIndigo
                          : KaiaDesignTokens.neutralGray700,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.w500,
                      fontSize: 14,
                    ),
                    side: BorderSide(
                      color: isSelected
                          ? KaiaDesignTokens.primaryIndigo
                          : KaiaDesignTokens.neutralGray300,
                      width: 1,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(KaiaDesignTokens.radiusMd),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsCount() {
    return Container(
      color: KaiaDesignTokens.neutralWhite,
      padding: const EdgeInsets.only(
        left: KaiaDesignTokens.spacing16,
        right: KaiaDesignTokens.spacing16,
        bottom: KaiaDesignTokens.spacing16,
      ),
      child: Row(
        children: [
          const Icon(
            Icons.auto_awesome,
            size: 16,
            color: KaiaDesignTokens.primaryIndigo,
          ),
          const SizedBox(width: 4),
          Flexible(
            // Defensive overflow handling - allow main text to shrink
            child: Text(
              '${_filteredSuggestions.length} AI-powered suggestions',
              style: const TextStyle(
                fontSize: 14,
                color: KaiaDesignTokens.neutralGray600,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1, // Prevent multi-line overflow
              overflow: TextOverflow.ellipsis, // Graceful text truncation
            ),
          ),
          if (_searchQuery.isNotEmpty || _selectedCategory != null) ...[
            const SizedBox(width: 8),
            Flexible(
              // Defensive overflow handling - allow filter text to shrink
              child: Text(
                '• Filtered',
                style: const TextStyle(
                  fontSize: 14,
                  color: KaiaDesignTokens.primaryIndigo,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1, // Prevent multi-line overflow
                overflow: TextOverflow.ellipsis, // Graceful text truncation
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Padding(
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
      child: _isGridView
          ? GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.75,
                crossAxisSpacing: KaiaDesignTokens.spacing16,
                mainAxisSpacing: KaiaDesignTokens.spacing16,
              ),
              itemCount: 6,
              itemBuilder: (context, index) {
                return const SkeletonLoading(
                  height: 270,
                  borderRadius: KaiaDesignTokens.radiusLg,
                );
              },
            )
          : ListView.builder(
              itemCount: 4,
              itemBuilder: (context, index) {
                return Padding(
                  padding:
                      const EdgeInsets.only(bottom: KaiaDesignTokens.spacing16),
                  child: const SkeletonLoading(
                    height: 120,
                    borderRadius: KaiaDesignTokens.radiusLg,
                  ),
                );
              },
            ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(KaiaDesignTokens.spacing32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: KaiaDesignTokens.primaryIndigo.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.auto_awesome,
                size: 40,
                color: KaiaDesignTokens.primaryIndigo,
              ),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing24),
            Text(
              'No AI suggestions found',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: KaiaDesignTokens.neutralGray900,
              ),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing8),
            Text(
              _searchQuery.isNotEmpty
                  ? 'Try adjusting your search terms or filters'
                  : 'Our AI is learning your preferences to provide better suggestions',
              style: TextStyle(
                fontSize: 16,
                color: KaiaDesignTokens.neutralGray600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: KaiaDesignTokens.spacing24),
            if (_searchQuery.isNotEmpty || _selectedCategory != null)
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _searchQuery = '';
                    _selectedCategory = null;
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: KaiaDesignTokens.primaryIndigo,
                  foregroundColor: KaiaDesignTokens.neutralWhite,
                  padding: const EdgeInsets.symmetric(
                    horizontal: KaiaDesignTokens.spacing24,
                    vertical: KaiaDesignTokens.spacing12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.circular(KaiaDesignTokens.radiusLg),
                  ),
                ),
                child: const Text(
                  'Clear Filters',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildGridView() {
    return RefreshIndicator(
      onRefresh: _loadAISuggestions,
      child: GridView.builder(
        padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.75,
          crossAxisSpacing: KaiaDesignTokens.spacing16,
          mainAxisSpacing: KaiaDesignTokens.spacing16,
        ),
        itemCount: _filteredSuggestions.length,
        itemBuilder: (context, index) {
          final suggestion = _filteredSuggestions[index];
          return AISuggestionCard(
            title: suggestion['title'],
            location: suggestion['location'],
            imageUrl: suggestion['imageUrl'],
            aiConfidence: suggestion['aiConfidence'].toDouble(),
            reviewCount: suggestion['reviewCount'],
            price: suggestion['price'],
            duration: suggestion['duration'],
            tags: List<String>.from(suggestion['tags']),
            category: suggestion['category'],
            matchReason: suggestion['matchReason'],
            isGridMode: true,
            onPressed: () => _handleAISuggestionPress(suggestion['id']),
            onFavoritePressed: () {
              // Handle favorite functionality
              // TODO: Implement favorite functionality
            },
          );
        },
      ),
    );
  }

  Widget _buildListView() {
    return RefreshIndicator(
      onRefresh: _loadAISuggestions,
      child: ListView.builder(
        padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
        itemCount: _filteredSuggestions.length,
        itemBuilder: (context, index) {
          final suggestion = _filteredSuggestions[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: KaiaDesignTokens.spacing16),
            child: AISuggestionCard(
              title: suggestion['title'],
              location: suggestion['location'],
              imageUrl: suggestion['imageUrl'],
              aiConfidence: suggestion['aiConfidence'].toDouble(),
              reviewCount: suggestion['reviewCount'],
              price: suggestion['price'],
              duration: suggestion['duration'],
              tags: List<String>.from(suggestion['tags']),
              category: suggestion['category'],
              matchReason: suggestion['matchReason'],
              isGridMode: false,
              onPressed: () => _handleAISuggestionPress(suggestion['id']),
              onFavoritePressed: () {
                // Handle favorite functionality
                // TODO: Implement favorite functionality
              },
            ),
          );
        },
      ),
    );
  }

  // Comprehensive mock data for AI-powered suggestions (20 diverse suggestions)
  final List<Map<String, dynamic>> _mockAISuggestions = [
    {
      'id': 'ai_001',
      'title': 'Perfect Weekend in Kyoto',
      'location': 'Kyoto, Japan',
      'category': 'Cultural',
      'aiConfidence': 95,
      'reviewCount': 0, // AI suggestions don't have traditional reviews
      'price': '¥12,500',
      'duration': '2 days',
      'imageUrl':
          'https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?w=800',
      'tags': ['Cultural', 'Temples', 'Traditional'],
      'matchReason': 'Based on your love for cultural sites and temples',
      'description':
          'AI-curated cultural journey through Kyoto\'s most sacred temples and traditional districts.',
    },
    {
      'id': 'ai_002',
      'title': 'Adrenaline Adventure Package',
      'location': 'Queenstown, New Zealand',
      'category': 'Adventure',
      'aiConfidence': 92,
      'reviewCount': 0,
      'price': 'NZ\$450',
      'duration': '1 day',
      'imageUrl':
          'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800',
      'tags': ['Adventure', 'Extreme', 'Bungee'],
      'matchReason': 'Matches your thrill-seeking adventure preferences',
      'description':
          'AI-selected extreme sports package including bungee jumping, skydiving, and jet boating.',
    },
    {
      'id': 'ai_003',
      'title': 'Culinary Discovery Tour',
      'location': 'Lyon, France',
      'category': 'Food & Drink',
      'aiConfidence': 88,
      'reviewCount': 0,
      'price': '€180',
      'duration': '6 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800',
      'tags': ['Culinary', 'French', 'Michelin'],
      'matchReason': 'Perfect for your gourmet food interests',
      'description':
          'AI-crafted gastronomic journey through Lyon\'s legendary bouchons and markets.',
    },
    {
      'id': 'ai_004',
      'title': 'Serengeti Wildlife Safari',
      'location': 'Serengeti, Tanzania',
      'category': 'Nature',
      'aiConfidence': 94,
      'reviewCount': 0,
      'price': '\$850',
      'duration': '3 days',
      'imageUrl':
          'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800',
      'tags': ['Wildlife', 'Safari', 'Photography'],
      'matchReason': 'Ideal for your wildlife photography passion',
      'description':
          'AI-optimized safari route for maximum wildlife encounters and photography opportunities.',
    },
    {
      'id': 'ai_005',
      'title': 'Ancient Rome Immersion',
      'location': 'Rome, Italy',
      'category': 'Historical',
      'aiConfidence': 91,
      'reviewCount': 0,
      'price': '€120',
      'duration': '4 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=800',
      'tags': ['Historical', 'Ancient', 'Archaeology'],
      'matchReason': 'Tailored for your ancient history fascination',
      'description':
          'AI-guided journey through Rome\'s most significant archaeological sites and hidden gems.',
    },
    {
      'id': 'ai_006',
      'title': 'Contemporary Art Exploration',
      'location': 'Berlin, Germany',
      'category': 'Art & Museums',
      'aiConfidence': 87,
      'reviewCount': 0,
      'price': '€95',
      'duration': '5 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=800',
      'tags': ['Contemporary', 'Street Art', 'Galleries'],
      'matchReason': 'Matches your modern art appreciation',
      'description':
          'AI-curated tour of Berlin\'s cutting-edge art scene and underground galleries.',
    },
    {
      'id': 'ai_007',
      'title': 'Rooftop Nightlife Circuit',
      'location': 'Bangkok, Thailand',
      'category': 'Nightlife',
      'aiConfidence': 89,
      'reviewCount': 0,
      'price': '฿2,500',
      'duration': '5 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=800',
      'tags': ['Nightlife', 'Rooftop', 'Cocktails'],
      'matchReason': 'Perfect for your nightlife and cocktail interests',
      'description':
          'AI-selected premium rooftop bars offering the best views and signature cocktails.',
    },
    {
      'id': 'ai_008',
      'title': 'Mindfulness Retreat Experience',
      'location': 'Ubud, Bali',
      'category': 'Wellness',
      'aiConfidence': 93,
      'reviewCount': 0,
      'price': '\$280',
      'duration': '8 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1506126613408-eca07ce68e71?w=800',
      'tags': ['Wellness', 'Meditation', 'Spa'],
      'matchReason': 'Designed for your wellness and relaxation needs',
      'description':
          'AI-personalized wellness journey combining yoga, meditation, and traditional Balinese healing.',
    },
    {
      'id': 'ai_009',
      'title': 'Arctic Northern Lights Hunt',
      'location': 'Tromsø, Norway',
      'category': 'Nature',
      'aiConfidence': 96,
      'reviewCount': 0,
      'price': 'NOK 1,200',
      'duration': '6 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1483347756197-71ef80e95f73?w=800',
      'tags': ['Aurora', 'Arctic', 'Photography'],
      'matchReason': 'Based on your interest in natural phenomena',
      'description':
          'AI-predicted optimal viewing locations and timing for Northern Lights photography.',
    },
    {
      'id': 'ai_010',
      'title': 'Flamenco Immersion Night',
      'location': 'Seville, Spain',
      'category': 'Cultural',
      'aiConfidence': 85,
      'reviewCount': 0,
      'price': '€75',
      'duration': '4 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1504893524553-b855bce32c67?w=800',
      'tags': ['Flamenco', 'Dance', 'Traditional'],
      'matchReason': 'Matches your passion for traditional dance forms',
      'description':
          'AI-selected authentic flamenco venues with intimate performances and dance lessons.',
    },
    {
      'id': 'ai_011',
      'title': 'Himalayan Trekking Adventure',
      'location': 'Annapurna, Nepal',
      'category': 'Adventure',
      'aiConfidence': 90,
      'reviewCount': 0,
      'price': '\$650',
      'duration': '5 days',
      'imageUrl':
          'https://images.unsplash.com/photo-1544735716-392fe2489ffa?w=800',
      'tags': ['Trekking', 'Mountains', 'Challenge'],
      'matchReason': 'Perfect for your mountain climbing experience',
      'description':
          'AI-optimized trekking route considering weather patterns and your fitness level.',
    },
    {
      'id': 'ai_012',
      'title': 'Wine & Vineyard Discovery',
      'location': 'Bordeaux, France',
      'category': 'Food & Drink',
      'aiConfidence': 86,
      'reviewCount': 0,
      'price': '€150',
      'duration': '7 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1506377247377-2a5b3b417ebb?w=800',
      'tags': ['Wine', 'Vineyard', 'Tasting'],
      'matchReason': 'Tailored for your wine appreciation knowledge',
      'description':
          'AI-curated wine tour featuring boutique vineyards and rare vintage tastings.',
    },
    {
      'id': 'ai_013',
      'title': 'Mayan Ruins Exploration',
      'location': 'Chichen Itza, Mexico',
      'category': 'Historical',
      'aiConfidence': 88,
      'reviewCount': 0,
      'price': 'MX\$1,800',
      'duration': '6 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1518638150340-f706e86654de?w=800',
      'tags': ['Mayan', 'Archaeology', 'UNESCO'],
      'matchReason': 'Based on your pre-Columbian history interests',
      'description':
          'AI-guided exploration of Mayan civilization with expert archaeological insights.',
    },
    {
      'id': 'ai_014',
      'title': 'Street Art & Graffiti Tour',
      'location': 'Melbourne, Australia',
      'category': 'Art & Museums',
      'aiConfidence': 84,
      'reviewCount': 0,
      'price': 'AU\$85',
      'duration': '3 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=800',
      'tags': ['Street Art', 'Urban', 'Alternative'],
      'matchReason': 'Matches your urban art and culture interests',
      'description':
          'AI-mapped route through Melbourne\'s most vibrant street art laneways and galleries.',
    },
    {
      'id': 'ai_015',
      'title': 'Jazz Club Crawl Experience',
      'location': 'New Orleans, USA',
      'category': 'Nightlife',
      'aiConfidence': 92,
      'reviewCount': 0,
      'price': '\$120',
      'duration': '5 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800',
      'tags': ['Jazz', 'Music', 'Historic'],
      'matchReason': 'Perfect for your jazz music appreciation',
      'description':
          'AI-selected authentic jazz venues featuring both legendary and emerging artists.',
    },
    {
      'id': 'ai_016',
      'title': 'Ayurveda Healing Journey',
      'location': 'Kerala, India',
      'category': 'Wellness',
      'aiConfidence': 91,
      'reviewCount': 0,
      'price': '₹8,500',
      'duration': '1 day',
      'imageUrl':
          'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=800',
      'tags': ['Ayurveda', 'Healing', 'Traditional'],
      'matchReason': 'Designed for your holistic wellness journey',
      'description':
          'AI-personalized Ayurvedic treatment plan based on your constitution and health goals.',
    },
    {
      'id': 'ai_017',
      'title': 'Patagonia Glacier Expedition',
      'location': 'El Calafate, Argentina',
      'category': 'Nature',
      'aiConfidence': 89,
      'reviewCount': 0,
      'price': 'AR\$15,000',
      'duration': '8 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800',
      'tags': ['Glacier', 'Patagonia', 'Expedition'],
      'matchReason': 'Based on your extreme landscape photography',
      'description':
          'AI-optimized glacier viewing experience with ice trekking and photography opportunities.',
    },
    {
      'id': 'ai_018',
      'title': 'Moroccan Souk Adventure',
      'location': 'Marrakech, Morocco',
      'category': 'Cultural',
      'aiConfidence': 87,
      'reviewCount': 0,
      'price': 'MAD 800',
      'duration': '4 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=800',
      'tags': ['Souk', 'Traditional', 'Shopping'],
      'matchReason': 'Tailored for your cultural immersion preferences',
      'description':
          'AI-guided navigation through Marrakech\'s labyrinthine souks with authentic experiences.',
    },
    {
      'id': 'ai_019',
      'title': 'Volcano Hiking Challenge',
      'location': 'Mount Etna, Italy',
      'category': 'Adventure',
      'aiConfidence': 93,
      'reviewCount': 0,
      'price': '€180',
      'duration': '6 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800',
      'tags': ['Volcano', 'Hiking', 'Geology'],
      'matchReason': 'Perfect for your geological interests and hiking skills',
      'description':
          'AI-planned volcanic expedition with safety optimization and geological insights.',
    },
    {
      'id': 'ai_020',
      'title': 'Craft Beer & Brewery Tour',
      'location': 'Portland, USA',
      'category': 'Food & Drink',
      'aiConfidence': 85,
      'reviewCount': 0,
      'price': '\$95',
      'duration': '4 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800',
      'tags': ['Craft Beer', 'Brewery', 'Local'],
      'matchReason': 'Based on your craft beer enthusiasm',
      'description':
          'AI-curated brewery tour featuring Portland\'s most innovative craft beer makers.',
    },
  ];
}
