import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/models/travel/timeline.dart';
import 'package:culture_connect/models/travel/timeline_event.dart';
import 'package:culture_connect/models/ar/ar_content_marker.dart';
import 'package:culture_connect/providers/ar/ar_content_providers.dart';
import 'package:culture_connect/widgets/travel/timeline/ar_timeline_event_card.dart';

void main() {
  // Sample timeline event with AR content
  final eventWithAR = TimelineEvent(
    id: 'event-1',
    title: 'Eiffel Tower Visit',
    description: 'Visit the iconic Eiffel Tower',
    eventDate: DateTime(2023, 6, 2),
    eventTime: const TimeOfDay(hour: 10, minute: 0),
    location: 'Eiffel Tower, Paris',
    coordinates: {'lat': 48.8584, 'lng': 2.2945},
    eventType: 'sightseeing',
    hasARContent: true,
    arContentId: 'ar-content-1',
  );

  // Sample timeline event without AR content
  final eventWithoutAR = TimelineEvent(
    id: 'event-2',
    title: 'Louvre Museum',
    description: 'Explore the famous Louvre Museum',
    eventDate: DateTime(2023, 6, 3),
    eventTime: const TimeOfDay(hour: 13, minute: 0),
    location: 'Louvre Museum, Paris',
    coordinates: {'lat': 48.8606, 'lng': 2.3376},
    eventType: 'museum',
    hasARContent: false,
  );

  // Sample AR content marker
  final sampleARContentMarker = ARContentMarker(
    id: 'ar-content-1',
    title: 'Eiffel Tower AR Experience',
    description: 'Explore the Eiffel Tower in augmented reality',
    contentType: ARContentType.model,
    contentUrl: 'https://example.com/ar/eiffel-tower.glb',
    thumbnailUrl: 'https://example.com/ar/eiffel-tower-thumb.jpg',
    location: 'Eiffel Tower, Paris',
    coordinates: {'lat': 48.8584, 'lng': 2.2945},
  );

  setUp(() {
    // Setup test data
  });

  testWidgets('ARTimelineEventCard displays event information correctly',
      (WidgetTester tester) async {
    // Arrange
    final widget = ProviderScope(
      child: MaterialApp(
        home: Scaffold(
          body: ARTimelineEventCard(
            event: eventWithAR,
            theme: TimelineTheme.standard,
          ),
        ),
      ),
    );

    // Act
    await tester.pumpWidget(widget);

    // Assert
    expect(find.text('Eiffel Tower Visit'), findsOneWidget);
    expect(find.text('10:00'), findsOneWidget);
    expect(find.text('Eiffel Tower, Paris'), findsOneWidget);
  });

  testWidgets(
      'ARTimelineEventCard displays AR badge for events with AR content',
      (WidgetTester tester) async {
    // Arrange
    final widget = ProviderScope(
      child: MaterialApp(
        home: Scaffold(
          body: ARTimelineEventCard(
            event: eventWithAR,
            theme: TimelineTheme.standard,
          ),
        ),
      ),
    );

    // Act
    await tester.pumpWidget(widget);

    // Assert
    expect(find.byIcon(Icons.view_in_ar), findsOneWidget);
  });

  testWidgets(
      'ARTimelineEventCard does not display AR badge for events without AR content',
      (WidgetTester tester) async {
    // Arrange
    final widget = ProviderScope(
      child: MaterialApp(
        home: Scaffold(
          body: ARTimelineEventCard(
            event: eventWithoutAR,
            theme: TimelineTheme.standard,
          ),
        ),
      ),
    );

    // Act
    await tester.pumpWidget(widget);

    // Assert
    expect(find.byIcon(Icons.view_in_ar), findsNothing);
  });

  testWidgets('ARTimelineEventCard calls onTap when tapped',
      (WidgetTester tester) async {
    // Arrange
    bool tapped = false;

    final widget = ProviderScope(
      child: MaterialApp(
        home: Scaffold(
          body: ARTimelineEventCard(
            event: eventWithAR,
            theme: TimelineTheme.standard,
            onTap: () => tapped = true,
          ),
        ),
      ),
    );

    // Act
    await tester.pumpWidget(widget);
    await tester.tap(find.byType(ARTimelineEventCard));

    // Assert
    expect(tapped, isTrue);
  });

  testWidgets(
      'ARTimelineEventCard calls onARContentTap when AR badge is tapped',
      (WidgetTester tester) async {
    // Arrange
    bool arTapped = false;

    final widget = ProviderScope(
      child: MaterialApp(
        home: Scaffold(
          body: ARTimelineEventCard(
            event: eventWithAR,
            theme: TimelineTheme.standard,
            onARContentTap: () => arTapped = true,
          ),
        ),
      ),
    );

    // Act
    await tester.pumpWidget(widget);

    // Find and tap the AR badge
    final arBadgeFinder = find.byIcon(Icons.view_in_ar);
    expect(arBadgeFinder, findsOneWidget);
    await tester.tap(arBadgeFinder);

    // Assert
    expect(arTapped, isTrue);
  });

  testWidgets(
      'ARTimelineEventCard expands to show description when "More" is tapped',
      (WidgetTester tester) async {
    // Arrange

    final widget = ProviderScope(
      overrides: [
        arContentMarkerProvider('ar-content-1')
            .overrideWithValue(AsyncValue.data(sampleARContentMarker)),
      ],
      child: MaterialApp(
        home: Scaffold(
          body: ARTimelineEventCard(
            event: eventWithAR,
            theme: TimelineTheme.standard,
          ),
        ),
      ),
    );

    // Act
    await tester.pumpWidget(widget);

    // Initially, the description should not be visible
    expect(find.text('Visit the iconic Eiffel Tower'), findsNothing);

    // Find and tap the "More" button
    final moreFinder = find.text('More');
    expect(moreFinder, findsOneWidget);
    await tester.tap(moreFinder);
    await tester.pumpAndSettle(); // Wait for animations to complete

    // Assert
    expect(find.text('Visit the iconic Eiffel Tower'), findsOneWidget);

    // The button should now say "Less"
    expect(find.text('Less'), findsOneWidget);
  });

  testWidgets('ARTimelineEventCard shows AR content preview when expanded',
      (WidgetTester tester) async {
    // Arrange

    final widget = ProviderScope(
      overrides: [
        arContentMarkerProvider('ar-content-1')
            .overrideWithValue(AsyncValue.data(sampleARContentMarker)),
      ],
      child: MaterialApp(
        home: Scaffold(
          body: ARTimelineEventCard(
            event: eventWithAR,
            theme: TimelineTheme.standard,
          ),
        ),
      ),
    );

    // Act
    await tester.pumpWidget(widget);

    // Find and tap the "More" button to expand
    final moreFinder = find.text('More');
    expect(moreFinder, findsOneWidget);
    await tester.tap(moreFinder);
    await tester.pumpAndSettle(); // Wait for animations to complete

    // Assert
    expect(find.text('Eiffel Tower AR Experience'), findsOneWidget);
    expect(find.text('3D Model'), findsOneWidget);
    expect(find.text('Preview'), findsOneWidget);
  });

  testWidgets(
      'ARTimelineEventCard shows loading indicator when AR content is loading',
      (WidgetTester tester) async {
    // Arrange
    final widget = ProviderScope(
      overrides: [
        arContentMarkerProvider('ar-content-1')
            .overrideWithValue(const AsyncValue.loading()),
      ],
      child: MaterialApp(
        home: Scaffold(
          body: ARTimelineEventCard(
            event: eventWithAR,
            theme: TimelineTheme.standard,
          ),
        ),
      ),
    );

    // Act
    await tester.pumpWidget(widget);

    // Find and tap the "More" button to expand
    final moreFinder = find.text('More');
    expect(moreFinder, findsOneWidget);
    await tester.tap(moreFinder);
    await tester.pumpAndSettle(); // Wait for animations to complete

    // Assert
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  });
}
