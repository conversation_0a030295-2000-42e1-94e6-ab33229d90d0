import 'package:flutter/material.dart';
import 'package:culture_connect/models/car.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/app_button.dart';
import 'package:culture_connect/screens/cars/cars_index_screen.dart';

/// Car booking confirmation screen with success animation and booking details
/// Matches React Native implementation with comprehensive booking summary
class CarConfirmationScreen extends StatefulWidget {
  final Car car;
  final Map<String, dynamic> bookingData;

  const CarConfirmationScreen({
    super.key,
    required this.car,
    required this.bookingData,
  });

  @override
  State<CarConfirmationScreen> createState() => _CarConfirmationScreenState();
}

class _CarConfirmationScreenState extends State<CarConfirmationScreen>
    with TickerProviderStateMixin {
  late AnimationController _successAnimationController;
  late AnimationController _contentAnimationController;
  late Animation<double> _checkmarkScale;
  late Animation<double> _checkmarkOpacity;
  late Animation<double> _contentOpacity;
  late Animation<Offset> _contentSlide;

  String? _bookingReference;

  @override
  void initState() {
    super.initState();

    // Generate booking reference
    _bookingReference = _generateBookingReference();

    // Success animation
    _successAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _checkmarkScale = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _successAnimationController,
        curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
      ),
    );

    _checkmarkOpacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _successAnimationController,
        curve: const Interval(0.0, 0.4, curve: Curves.easeOut),
      ),
    );

    // Content animation
    _contentAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _contentOpacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _contentAnimationController,
        curve: Curves.easeOut,
      ),
    );

    _contentSlide = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _contentAnimationController,
      curve: Curves.easeOut,
    ));

    // Start animations
    _startAnimations();
  }

  @override
  void dispose() {
    _successAnimationController.dispose();
    _contentAnimationController.dispose();
    super.dispose();
  }

  void _startAnimations() async {
    await Future.delayed(const Duration(milliseconds: 300));
    _successAnimationController.forward();

    await Future.delayed(const Duration(milliseconds: 400));
    _contentAnimationController.forward();
  }

  String _generateBookingReference() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final carId = widget.car.id;
    return 'CR${carId.padLeft(3, '0')}${timestamp.toString().substring(8)}';
  }

  void _handleBackToHome() {
    // Navigate to Cars Index Screen instead of app root
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(builder: (context) => const CarsIndexScreen()),
      (route) => route.isFirst,
    );
  }

  void _handleDownloadPDF() {
    // TODO: Implement PDF download
    debugPrint('Download PDF for booking: $_bookingReference');
  }

  void _handleShareBooking() {
    // TODO: Implement share functionality
    debugPrint('Share booking: $_bookingReference');
  }

  Widget _buildSuccessAnimation() {
    return AnimatedBuilder(
      animation: Listenable.merge([_checkmarkScale, _checkmarkOpacity]),
      builder: (context, child) {
        return Opacity(
          opacity: _checkmarkOpacity.value,
          child: Transform.scale(
            scale: _checkmarkScale.value,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppTheme.successColor,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.successColor.withValues(alpha: 0.3),
                    offset: const Offset(0, 8),
                    blurRadius: 24,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: const Icon(
                Icons.check,
                size: 60,
                color: AppTheme.white,
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.white,
        elevation: 0,
        automaticallyImplyLeading: false,
        title: const Text(
          'Booking Confirmed',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        centerTitle: true,
        bottom: const PreferredSize(
          preferredSize: Size.fromHeight(1),
          child: Divider(
            height: 1,
            color: AppTheme.borderLight,
          ),
        ),
      ),
      body: SlideTransition(
        position: _contentSlide,
        child: FadeTransition(
          opacity: _contentOpacity,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppTheme.spacingLg),
            child: Column(
              children: [
                const SizedBox(height: AppTheme.spacingXl),
                // Success animation
                _buildSuccessAnimation(),
                const SizedBox(height: AppTheme.spacingXl),
                // Success message
                const Text(
                  'Booking Confirmed!',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppTheme.spacingSm),
                const Text(
                  'Your car rental has been successfully booked.',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppTheme.textSecondaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppTheme.spacingXl),
                // Booking reference
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppTheme.spacingLg),
                  decoration: BoxDecoration(
                    color: AppTheme.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.shadowColor,
                        offset: const Offset(0, 4),
                        blurRadius: 12,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.confirmation_number,
                            size: 20,
                            color: AppTheme.accentColor,
                          ),
                          const SizedBox(width: AppTheme.spacingSm),
                          const Text(
                            'Booking Reference',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.textPrimaryColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: AppTheme.spacingMd),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(AppTheme.spacingMd),
                        decoration: BoxDecoration(
                          color: AppTheme.backgroundSecondary,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _bookingReference ?? '',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimaryColor,
                            letterSpacing: 1.0,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppTheme.spacingXl),
                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: AppButton(
                        text: 'Download PDF',
                        onPressed: _handleDownloadPDF,
                        variant: ButtonVariant.outlined,
                        height: 48,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingMd),
                    Expanded(
                      child: AppButton(
                        text: 'Share Booking',
                        onPressed: _handleShareBooking,
                        variant: ButtonVariant.filled,
                        height: 48,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppTheme.spacingLg),
                // Back to home
                SizedBox(
                  width: double.infinity,
                  child: AppButton(
                    text: 'Back to Home',
                    onPressed: _handleBackToHome,
                    variant: ButtonVariant.text,
                    height: 48,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
