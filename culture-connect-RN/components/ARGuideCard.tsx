import React from "react";
import { StyleSheet, View, Text, Pressable } from "react-native";
import { Image } from "expo-image";
import { MapPin, Navigation } from "lucide-react-native";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { LinearGradient } from "expo-linear-gradient";

interface ARGuideCardProps {
  title: string;
  distance: string;
  imageUrl: string;
  onPress: () => void;
}

export const ARGuideCard: React.FC<ARGuideCardProps> = ({
  title,
  distance,
  imageUrl,
  onPress,
}) => {
  return (
    <Pressable
      style={styles.container}
      onPress={onPress}
      android_ripple={{ color: colors.border }}
    >
      <Image
        source={{ uri: imageUrl }}
        style={styles.image}
        contentFit="cover"
        transition={300}
      />
      <LinearGradient
        colors={["transparent", "rgba(0,0,0,0.7)"]}
        style={styles.gradient}
      />
      <View style={styles.content}>
        <View style={styles.header}>
          <MapPin size={16} color={colors.white} />
          <Text style={styles.distance}>{distance}</Text>
        </View>
        <Text style={styles.title}>{title}</Text>
        <View style={styles.button}>
          <Navigation size={14} color={colors.white} />
          <Text style={styles.buttonText}>Start AR Guide</Text>
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 180,
    width: 280,
    borderRadius: 16,
    overflow: "hidden",
    marginRight: spacing.md,
  },
  image: {
    ...StyleSheet.absoluteFillObject,
  },
  gradient: {
    ...StyleSheet.absoluteFillObject,
  },
  content: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: spacing.md,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.xs,
  },
  distance: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.white,
    marginLeft: spacing.xs,
  },
  title: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    marginBottom: spacing.sm,
  },
  button: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.accent,
    borderRadius: 8,
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
    alignSelf: "flex-start",
  },
  buttonText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium as any,
    color: colors.white,
    marginLeft: spacing.xs,
  },
});