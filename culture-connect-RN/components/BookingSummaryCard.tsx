import React from "react";
import { StyleSheet, View, Text, Pressable } from "react-native";
import { ChevronDown, ChevronUp, Download } from "lucide-react-native";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";

interface BookingSummaryCardProps {
  title: string;
  subtitle: string;
  price: string;
  status: "booked" | "pending";
  details: string[];
  isExpanded: boolean;
  onToggleExpand: () => void;
  onDownload: () => void;
}

export const BookingSummaryCard: React.FC<BookingSummaryCardProps> = ({
  title,
  subtitle,
  price,
  status,
  details,
  isExpanded,
  onToggleExpand,
  onDownload,
}) => {
  return (
    <View style={styles.container}>
      <Pressable style={styles.header} onPress={onToggleExpand}>
        <View style={styles.headerContent}>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.subtitle}>{subtitle}</Text>
          <View style={styles.statusContainer}>
            <View style={[styles.statusDot, status === "booked" && styles.statusBooked]} />
            <Text style={styles.statusText}>
              {status === "booked" ? "Confirmed" : "Pending"}
            </Text>
          </View>
        </View>
        <View style={styles.priceContainer}>
          <Text style={styles.price}>{price}</Text>
          {isExpanded ? (
            <ChevronUp size={20} color={colors.textSecondary} />
          ) : (
            <ChevronDown size={20} color={colors.textSecondary} />
          )}
        </View>
      </Pressable>
      
      {isExpanded && (
        <View style={styles.expandedContent}>
          {details.map((detail, index) => (
            <Text key={index} style={styles.detail}>
              • {detail}
            </Text>
          ))}
          <Pressable style={styles.downloadButton} onPress={onDownload}>
            <Download size={16} color={colors.primary} />
            <Text style={styles.downloadText}>Download Receipt</Text>
          </Pressable>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 12,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: spacing.lg,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  subtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.warning,
    marginRight: spacing.xs,
  },
  statusBooked: {
    backgroundColor: colors.success,
  },
  statusText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
  },
  priceContainer: {
    alignItems: "flex-end",
  },
  price: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  expandedContent: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
  },
  detail: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
    lineHeight: typography.lineHeights.normal * typography.sizes.sm,
  },
  downloadButton: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: spacing.md,
    paddingVertical: spacing.sm,
  },
  downloadText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.primary,
    marginLeft: spacing.xs,
  },
});