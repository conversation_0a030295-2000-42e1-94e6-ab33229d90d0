import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/core/theme/app_theme.dart';
import 'package:culture_connect/screens/travel/hotel/hotel_list_screen_enhanced.dart';

/// Hotel/Apartment Booking Confirmation Screen with PDF download functionality
///
/// Features:
/// - Comprehensive booking summary display
/// - PDF generation with pixel-perfect accuracy
/// - QR code for booking verification
/// - Share functionality
/// - Secure access control
/// - Professional design matching CultureConnect's #6366F1 design system
class HotelBookingConfirmationScreen extends ConsumerStatefulWidget {
  final Booking booking;
  final String transactionId;
  final Map<String, dynamic> bookingDetails;
  final String userEmail;
  final String userName;
  final String? userPhone;

  const HotelBookingConfirmationScreen({
    super.key,
    required this.booking,
    required this.transactionId,
    required this.bookingDetails,
    required this.userEmail,
    required this.userName,
    this.userPhone,
  });

  @override
  ConsumerState<HotelBookingConfirmationScreen> createState() =>
      _HotelBookingConfirmationScreenState();
}

class _HotelBookingConfirmationScreenState
    extends ConsumerState<HotelBookingConfirmationScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isGeneratingPdf = false;
  String? _pdfPath;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _animationController.forward();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Column(
              children: [
                _buildHeader(),
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        _buildSuccessCard(),
                        const SizedBox(height: 16),
                        _buildBookingDetailsCard(),
                        const SizedBox(height: 16),
                        _buildPropertyDetailsCard(),
                        const SizedBox(height: 16),
                        _buildPricingCard(),
                        const SizedBox(height: 16),
                        _buildQRCodeCard(),
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ),
                _buildBottomActions(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => _navigateToHotels(),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Booking Confirmed',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF6366F1),
                  ),
                ),
                Text(
                  'Confirmation #${widget.booking.id.substring(0, 8).toUpperCase()}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareBooking,
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF6366F1).withAlpha(51), // 20% opacity
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51), // 20% opacity
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.check,
              size: 40,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Booking Successful!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your ${_isApartment() ? 'apartment' : 'hotel'} reservation has been confirmed',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withAlpha(230), // 90% opacity
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBookingDetailsCard() {
    final checkIn = widget.bookingDetails['checkIn'] as String? ?? 'N/A';
    final checkOut = widget.bookingDetails['checkOut'] as String? ?? 'N/A';
    final nights = widget.bookingDetails['nights'] as int? ?? 0;
    final guests = widget.bookingDetails['guests'] as int? ?? 0;

    return _buildEnhancedCard(
      title: 'Booking Details',
      icon: Icons.calendar_today,
      iconColor: const Color(0xFF10B981),
      child: Column(
        children: [
          _buildEnhancedDetailRow(
            'Check-in',
            checkIn,
            Icons.login,
            const Color(0xFF10B981),
          ),
          const SizedBox(height: 16),
          _buildEnhancedDetailRow(
            'Check-out',
            checkOut,
            Icons.logout,
            const Color(0xFFEF4444),
          ),
          const SizedBox(height: 16),
          _buildEnhancedDetailRow(
            'Duration',
            '$nights night${nights != 1 ? 's' : ''}',
            Icons.nights_stay,
            const Color(0xFF8B5CF6),
          ),
          const SizedBox(height: 16),
          _buildEnhancedDetailRow(
            'Guests',
            '$guests guest${guests != 1 ? 's' : ''}',
            Icons.people,
            const Color(0xFF06B6D4),
          ),
        ],
      ),
    );
  }

  Widget _buildPropertyDetailsCard() {
    final hotelName = widget.bookingDetails['hotelName'] as String? ?? 'N/A';
    final location = widget.bookingDetails['hotelLocation'] as String? ?? 'N/A';
    final roomType = widget.bookingDetails['roomType'] as String? ?? 'N/A';
    final starRating = widget.bookingDetails['starRating'] as int? ?? 0;

    return _buildEnhancedCard(
      title: 'Property Details',
      icon: _isApartment() ? Icons.apartment : Icons.hotel,
      iconColor: const Color(0xFF8B5CF6),
      child: Column(
        children: [
          _buildEnhancedDetailRow(
            'Property',
            hotelName,
            Icons.business,
            const Color(0xFF8B5CF6),
          ),
          const SizedBox(height: 16),
          _buildEnhancedDetailRow(
            'Location',
            location,
            Icons.location_on,
            const Color(0xFFEF4444),
          ),
          const SizedBox(height: 16),
          _buildEnhancedDetailRow(
            'Room Type',
            roomType,
            Icons.bed,
            const Color(0xFF06B6D4),
          ),
          const SizedBox(height: 16),
          _buildEnhancedDetailRow(
            'Rating',
            '$starRating star${starRating != 1 ? 's' : ''}',
            Icons.star,
            const Color(0xFFF59E0B),
          ),
        ],
      ),
    );
  }

  Widget _buildPricingCard() {
    final basePrice = widget.bookingDetails['basePrice'] as double? ?? 0.0;
    final totalPrice = widget.bookingDetails['totalPrice'] as double? ?? 0.0;
    final currency = widget.bookingDetails['currency'] as String? ?? 'USD';
    final nights = widget.bookingDetails['nights'] as int? ?? 1;

    return _buildEnhancedCard(
      title: 'Payment Summary',
      icon: Icons.receipt,
      iconColor: const Color(0xFF10B981),
      child: Column(
        children: [
          _buildPriceRow('Room Rate (per night)',
              '$currency ${basePrice.toStringAsFixed(2)}'),
          const SizedBox(height: 8),
          _buildPriceRow('Number of nights', '$nights'),
          const SizedBox(height: 8),
          _buildPriceRow('Subtotal',
              '$currency ${(basePrice * nights).toStringAsFixed(2)}'),
          const SizedBox(height: 8),
          const Divider(),
          const SizedBox(height: 8),
          _buildPriceRow(
            'Total Paid',
            '$currency ${totalPrice.toStringAsFixed(2)}',
            isTotal: true,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.green.withAlpha(26), // 10% opacity
                  Colors.green.withAlpha(13), // 5% opacity
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.green.withAlpha(51), // 20% opacity
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.withAlpha(51), // 20% opacity
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.check_circle,
                      color: Colors.green, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Payment Confirmed',
                        style: TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'Transaction: ${widget.transactionId}',
                        style: TextStyle(
                          color: Colors.green[700],
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQRCodeCard() {
    return _buildEnhancedCard(
      title: 'Booking Verification',
      icon: Icons.qr_code,
      iconColor: const Color(0xFF6366F1),
      child: Column(
        children: [
          Center(
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.white,
                    Colors.grey[50]!,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: const Color(0xFF6366F1).withAlpha(51), // 20% opacity
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF6366F1).withAlpha(26), // 10% opacity
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: QrImageView(
                data: 'BOOKING:${widget.booking.id}:${widget.transactionId}',
                version: QrVersions.auto,
                size: 140,
                backgroundColor: Colors.transparent,
              ),
            ),
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: const Color(0xFF6366F1).withAlpha(26), // 10% opacity
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF6366F1).withAlpha(51), // 20% opacity
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Text(
                  'Show this QR code at check-in',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: const Color(0xFF6366F1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'ID: ${widget.booking.id.substring(0, 8).toUpperCase()}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                      letterSpacing: 1.2,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isGeneratingPdf ? null : _generatePdf,
              icon: _isGeneratingPdf
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.download),
              label:
                  Text(_isGeneratingPdf ? 'Generating PDF...' : 'Download PDF'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF6366F1),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _navigateToHotels,
              icon: const Icon(Icons.hotel),
              label: const Text('Back to Hotels'),
              style: OutlinedButton.styleFrom(
                foregroundColor: const Color(0xFF6366F1),
                side: const BorderSide(color: Color(0xFF6366F1)),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods

  Widget _buildPriceRow(String label, String value, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
            color: isTotal ? Colors.black87 : Colors.grey[700],
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w600,
            color: isTotal ? const Color(0xFF6366F1) : Colors.black87,
          ),
        ),
      ],
    );
  }

  // Enhanced helper methods for improved visual design
  Widget _buildEnhancedCard({
    required String title,
    required IconData icon,
    required Widget child,
    Color? iconColor,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15), // 6% opacity
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withAlpha(8), // 3% opacity
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: (iconColor ?? const Color(0xFF6366F1))
                      .withAlpha(26), // 10% opacity
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: iconColor ?? const Color(0xFF6366F1),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                    letterSpacing: -0.5,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          child,
        ],
      ),
    );
  }

  Widget _buildEnhancedDetailRow(
      String label, String value, IconData icon, Color iconColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: iconColor.withAlpha(26), // 10% opacity
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 20,
              color: iconColor,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  bool _isApartment() {
    final roomType = widget.bookingDetails['roomType'] as String? ?? '';
    return roomType.toLowerCase().contains('apartment');
  }

  void _navigateToHotels() {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => const HotelListScreenEnhanced(),
      ),
      (route) => false,
    );
  }

  void _shareBooking() async {
    final hotelName =
        widget.bookingDetails['hotelName'] as String? ?? 'Property';
    final checkIn = widget.bookingDetails['checkIn'] as String? ?? 'N/A';
    final checkOut = widget.bookingDetails['checkOut'] as String? ?? 'N/A';
    final nights = widget.bookingDetails['nights'] as int? ?? 0;

    final shareText = '''
🏨 ${_isApartment() ? 'Apartment' : 'Hotel'} Booking Confirmed!

📍 $hotelName
📅 Check-in: $checkIn
📅 Check-out: $checkOut
🌙 $nights night${nights != 1 ? 's' : ''}

Booking ID: ${widget.booking.id.substring(0, 8).toUpperCase()}

Booked with CultureConnect - The Soul of Travel, Guided by AI
''';

    await Share.share(shareText);
  }

  Future<void> _generatePdf() async {
    setState(() => _isGeneratingPdf = true);
    HapticFeedback.mediumImpact();

    try {
      final pdf = pw.Document();

      // Use built-in fonts instead of loading from assets
      final fontRegular = pw.Font.helvetica();
      final fontBold = pw.Font.helveticaBold();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(20),
          build: (pw.Context context) =>
              _buildPdfContent(fontRegular, fontBold),
        ),
      );

      // Save PDF
      final directory = await getApplicationDocumentsDirectory();
      final file = File(
        '${directory.path}/hotel_booking_${widget.booking.id}_${DateTime.now().millisecondsSinceEpoch}.pdf',
      );
      await file.writeAsBytes(await pdf.save());

      setState(() {
        _isGeneratingPdf = false;
        _pdfPath = file.path;
      });

      // Show success and share PDF
      if (mounted) {
        HapticFeedback.lightImpact();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('PDF generated successfully!'),
            backgroundColor: Color(0xFF6366F1),
          ),
        );

        // Share the PDF file
        await Share.shareXFiles([XFile(file.path)]);
      }
    } catch (e) {
      setState(() => _isGeneratingPdf = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to generate PDF: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<pw.Widget> _buildPdfContent(pw.Font fontRegular, pw.Font fontBold) {
    final hotelName = widget.bookingDetails['hotelName'] as String? ?? 'N/A';
    final location = widget.bookingDetails['hotelLocation'] as String? ?? 'N/A';
    final checkIn = widget.bookingDetails['checkIn'] as String? ?? 'N/A';
    final checkOut = widget.bookingDetails['checkOut'] as String? ?? 'N/A';
    final nights = widget.bookingDetails['nights'] as int? ?? 0;
    final guests = widget.bookingDetails['guests'] as int? ?? 0;
    final roomType = widget.bookingDetails['roomType'] as String? ?? 'N/A';
    final totalPrice = widget.bookingDetails['totalPrice'] as double? ?? 0.0;
    final currency = widget.bookingDetails['currency'] as String? ?? 'USD';

    return [
      // Header
      pw.Container(
        padding: const pw.EdgeInsets.all(20),
        decoration: pw.BoxDecoration(
          color: PdfColor.fromHex('#6366F1'),
          borderRadius: pw.BorderRadius.circular(12),
        ),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              'Booking Confirmation',
              style: pw.TextStyle(
                font: fontBold,
                fontSize: 24,
                color: PdfColors.white,
              ),
            ),
            pw.SizedBox(height: 8),
            pw.Text(
              'Confirmation #${widget.booking.id.substring(0, 8).toUpperCase()}',
              style: pw.TextStyle(
                font: fontRegular,
                fontSize: 16,
                color: PdfColors.white,
              ),
            ),
          ],
        ),
      ),
      pw.SizedBox(height: 20),

      // Property Details
      pw.Text(
        'Property Details',
        style: pw.TextStyle(font: fontBold, fontSize: 18),
      ),
      pw.SizedBox(height: 10),
      pw.Text('Property: $hotelName',
          style: pw.TextStyle(font: fontRegular, fontSize: 14)),
      pw.Text('Location: $location',
          style: pw.TextStyle(font: fontRegular, fontSize: 14)),
      pw.Text('Room Type: $roomType',
          style: pw.TextStyle(font: fontRegular, fontSize: 14)),
      pw.SizedBox(height: 20),

      // Booking Details
      pw.Text(
        'Booking Details',
        style: pw.TextStyle(font: fontBold, fontSize: 18),
      ),
      pw.SizedBox(height: 10),
      pw.Text('Check-in: $checkIn',
          style: pw.TextStyle(font: fontRegular, fontSize: 14)),
      pw.Text('Check-out: $checkOut',
          style: pw.TextStyle(font: fontRegular, fontSize: 14)),
      pw.Text('Duration: $nights night${nights != 1 ? 's' : ''}',
          style: pw.TextStyle(font: fontRegular, fontSize: 14)),
      pw.Text('Guests: $guests guest${guests != 1 ? 's' : ''}',
          style: pw.TextStyle(font: fontRegular, fontSize: 14)),
      pw.SizedBox(height: 20),

      // Payment Summary
      pw.Text(
        'Payment Summary',
        style: pw.TextStyle(font: fontBold, fontSize: 18),
      ),
      pw.SizedBox(height: 10),
      pw.Text('Total Paid: $currency ${totalPrice.toStringAsFixed(2)}',
          style: pw.TextStyle(font: fontBold, fontSize: 16)),
      pw.Text('Transaction ID: ${widget.transactionId}',
          style: pw.TextStyle(font: fontRegular, fontSize: 14)),
      pw.SizedBox(height: 20),

      // Footer
      pw.Container(
        padding: const pw.EdgeInsets.all(16),
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: PdfColors.grey300),
          borderRadius: pw.BorderRadius.circular(8),
        ),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              'Important Information',
              style: pw.TextStyle(font: fontBold, fontSize: 14),
            ),
            pw.SizedBox(height: 8),
            pw.Text(
              '• Please bring a valid ID for check-in',
              style: pw.TextStyle(font: fontRegular, fontSize: 12),
            ),
            pw.Text(
              '• Show this confirmation at the property',
              style: pw.TextStyle(font: fontRegular, fontSize: 12),
            ),
            pw.Text(
              '• Contact property directly for special requests',
              style: pw.TextStyle(font: fontRegular, fontSize: 12),
            ),
          ],
        ),
      ),
    ];
  }
}
