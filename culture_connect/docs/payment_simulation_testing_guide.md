# Payment Simulation Testing Guide

## Overview

This guide explains how to use the payment simulation system for testing the complete flight booking flow without processing actual payments.

## Current Configuration

**Simulation Mode: ENABLED**
- Feature flag: `PaymentSimulationConfig.ENABLE_PAYMENT_SIMULATION = true`
- All flight bookings will use simulated payment processing
- No actual payment charges will occur

## Testing the Complete Flow

### 1. Flight Search to Booking
1. Open the app and navigate to the home screen
2. Tap the "Flights" button in Quick Services
3. Fill in flight search details (departure, destination, dates, passengers)
4. Tap "Search Flights"

### 2. Flight Selection
1. Browse available flights in the results screen
2. Tap on a flight to expand details
3. Select your preferred flight
4. Tap "Select Flight"

### 3. Seat Selection
1. View the interactive aircraft seat map
2. Tap on an available seat (green seats)
3. Note any additional costs for premium seats (exit rows)
4. Tap "Continue" to proceed to payment

### 4. Simulated Payment Processing
1. **Testing Mode Banner**: You'll see an orange banner indicating "TESTING MODE"
2. **Payment Initialization**: 1.5 second delay with pulsing payment icon
3. **Payment Processing**: 3 second progress bar simulation
4. **Payment Success**: 1 second success confirmation
5. **Automatic Navigation**: Redirects to boarding pass screen

### 5. Boarding Pass Generation
1. View the generated boarding pass with flight details
2. Tap "Download ticket" to simulate ticket download
3. Verify all booking information is correct

## Simulation Features

### Visual Indicators
- **Orange Testing Banner**: Clearly indicates simulation mode
- **Progress Animation**: Realistic payment processing visualization
- **Status Messages**: "Initializing secure payment..." → "Processing payment..." → "Payment successful!"
- **Booking Details**: Shows actual booking information being processed

### Timing Configuration
- **Initialization**: 1.5 seconds (mimics payment setup)
- **Processing**: 3 seconds with progress bar (mimics actual payment time)
- **Success**: 1 second confirmation (mimics payment completion)

### Data Flow
- All booking data flows through the same models as production
- Seat selection, pricing, and booking details are preserved
- Navigation follows identical paths to production flow

## Switching to Production Mode

### For Production Deployment

**⚠️ CRITICAL: Before production release, follow these steps:**

1. **Update Configuration**
   ```dart
   // In lib/config/payment_simulation_config.dart
   static const bool ENABLE_PAYMENT_SIMULATION = false;
   ```

2. **Update Seat Selection Screen**
   ```dart
   // In lib/screens/travel/flight/rn_seat_selection_screen.dart
   // Replace PaymentRouterService call with:
   Navigator.of(context).push(
     MaterialPageRoute(
       builder: (context) => ProductionPaymentScreen(
         booking: flightBooking,
         userEmail: userEmail, // Get from user provider
         userName: userName,   // Get from user provider
         userPhone: userPhone, // Get from user provider
       ),
     ),
   );
   ```

3. **Remove Simulation Files**
   - Delete `lib/config/payment_simulation_config.dart`
   - Delete `lib/screens/payment/simulated_payment_screen.dart`
   - Delete `lib/services/payment_router_service.dart`

4. **Update Imports**
   - Remove PaymentRouterService import from seat selection screen
   - Add ProductionPaymentScreen import back to seat selection screen

### Quick Production Switch (Temporary)

For quick testing of production mode without removing files:

```dart
// In lib/config/payment_simulation_config.dart
static const bool ENABLE_PAYMENT_SIMULATION = false; // Set to false
```

This will route all payments through ProductionPaymentScreen while keeping simulation infrastructure intact.

## Troubleshooting

### Common Issues

1. **Payment Screen Not Appearing**
   - Check that `ENABLE_PAYMENT_SIMULATION = true`
   - Verify PaymentRouterService is imported in seat selection screen

2. **Navigation Issues**
   - Ensure RNFlightConfirmationScreen is properly imported
   - Check that booking object contains flight-specific data

3. **Simulation Not Working**
   - Check console logs for `[PAYMENT_SIMULATION]` messages
   - Verify PaymentSimulationConfig.ENABLE_SIMULATION_LOGGING = true

### Debug Logging

Enable detailed logging by checking console output for:
```
[PAYMENT_SIMULATION] Starting payment simulation for booking: flight_xxx
[PAYMENT_SIMULATION] Payment simulation flow started
[PAYMENT_SIMULATION] Payment simulation completed successfully
[PAYMENT_SIMULATION] Navigating to flight confirmation screen
```

## Testing Scenarios

### Successful Payment Flow
- Default configuration simulates 100% successful payments
- Tests complete booking journey from search to boarding pass

### Different Seat Types
- Test regular economy seats (no extra cost)
- Test exit row seats (additional $25 fee)
- Verify pricing calculations in simulation

### Booking Data Validation
- Verify all flight details appear correctly in simulation
- Check that seat selection is preserved through payment flow
- Confirm booking ID generation and tracking

## Security Notes

### Simulation Safety
- No actual payment processing occurs in simulation mode
- No real payment credentials are collected or transmitted
- All payment data is mocked and temporary

### Production Safety
- ProductionPaymentScreen remains completely intact
- No changes to actual payment processing logic
- Simulation system is completely isolated from production payment infrastructure

## Performance Testing

### Memory Usage
- Simulation mode should maintain same memory targets as production
- Monitor for memory leaks during repeated booking flows

### Timing Accuracy
- Simulation timing should closely match real payment processing times
- Adjust timing constants in PaymentSimulationConfig if needed

### User Experience
- Simulation should feel identical to real payment processing
- Visual feedback should be clear and professional

## Conclusion

The payment simulation system provides a safe, realistic way to test the complete flight booking flow. It preserves all production payment infrastructure while enabling comprehensive testing without financial transactions.

Remember to disable simulation mode and remove simulation files before production deployment.
