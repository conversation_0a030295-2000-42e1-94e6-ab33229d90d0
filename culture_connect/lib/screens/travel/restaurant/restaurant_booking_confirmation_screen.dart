import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

// Project imports
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/travel/restaurant.dart';
import 'package:culture_connect/models/travel/restaurant_reservation.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/screens/main_navigation.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/restaurant_pdf_service.dart';

/// Restaurant Booking Confirmation Screen
///
/// Displays complete reservation details after successful payment including:
/// - Restaurant information and reservation details
/// - Payment information and deposit details
/// - PDF download functionality with exact visual fidelity
/// - Navigation back to home screen
///
/// Features:
/// - Premium aesthetic matching CultureConnect design system
/// - Defensive overflow handling with Flexible/Expanded/TextOverflow.ellipsis
/// - Payment simulation system integration
/// - Performance targets: <100MB memory, 60fps
class RestaurantBookingConfirmationScreen extends ConsumerStatefulWidget {
  final Booking booking;
  final String transactionId;
  final Map<String, dynamic> bookingDetails;
  final String userEmail;
  final String userName;
  final String? userPhone;

  const RestaurantBookingConfirmationScreen({
    super.key,
    required this.booking,
    required this.transactionId,
    required this.bookingDetails,
    required this.userEmail,
    required this.userName,
    this.userPhone,
  });

  @override
  ConsumerState<RestaurantBookingConfirmationScreen> createState() =>
      _RestaurantBookingConfirmationScreenState();
}

class _RestaurantBookingConfirmationScreenState
    extends ConsumerState<RestaurantBookingConfirmationScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final LoggingService _logger = LoggingService();
  bool _isGeneratingPdf = false;

  // Extract reservation and restaurant from booking details
  RestaurantReservation? get _reservation =>
      widget.bookingDetails['reservation'] as RestaurantReservation?;
  Restaurant? get _restaurant =>
      widget.bookingDetails['restaurant'] as Restaurant?;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Start success animation
    _animationController.forward();

    _logger.info('RestaurantBookingConfirmationScreen',
        'Initialized with booking: ${widget.booking.id}');
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Defensive null handling
    if (_reservation == null || _restaurant == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Booking Confirmed'),
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Text(
            'Error loading reservation details',
            style: TextStyle(fontSize: 16),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Reservation Confirmed'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Success header
            _buildSuccessHeader(theme),

            const SizedBox(height: AppTheme.spacingLg),

            // Restaurant and reservation details
            _buildReservationDetailsCard(theme),

            const SizedBox(height: AppTheme.spacingMd),

            // Payment information
            _buildPaymentDetailsCard(theme),

            const SizedBox(height: AppTheme.spacingLg),

            // Action buttons
            _buildActionButtons(theme),

            const SizedBox(height: AppTheme.spacingMd),

            // Back to home button
            _buildBackToHomeButton(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildSuccessHeader(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppTheme.shadowLight,
      ),
      child: Column(
        children: [
          // Success icon with animation
          ScaleTransition(
            scale: Tween<double>(begin: 0.0, end: 1.0).animate(
              CurvedAnimation(
                parent: _animationController,
                curve: Curves.elasticOut,
              ),
            ),
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.green.shade100,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.check_circle,
                size: 50,
                color: Colors.green.shade600,
              ),
            ),
          ),

          const SizedBox(height: AppTheme.spacingMd),

          Text(
            'Reservation Confirmed!',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppTheme.spacingSm),

          Text(
            'Your table at ${_restaurant!.name} has been reserved',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppTheme.spacingSm),

          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingMd,
              vertical: AppTheme.spacingSm,
            ),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'Confirmation sent to ${widget.userEmail}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReservationDetailsCard(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppTheme.shadowLight,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.restaurant,
                color: AppTheme.primaryColor,
                size: 24,
              ),
              const SizedBox(width: AppTheme.spacingSm),
              Expanded(
                child: Text(
                  'Reservation Details',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMd),

          // Restaurant name and info
          _buildDetailRow(
            'Restaurant',
            _restaurant!.name,
            Icons.restaurant_menu,
          ),

          _buildDetailRow(
            'Location',
            _restaurant!.location,
            Icons.location_on,
          ),

          const Divider(height: AppTheme.spacingLg),

          // Reservation details
          _buildDetailRow(
            'Date',
            DateFormat('EEEE, MMMM d, yyyy').format(_reservation!.date),
            Icons.calendar_today,
          ),

          _buildDetailRow(
            'Time',
            '${_reservation!.timeSlot.startTime} - ${_reservation!.timeSlot.endTime}',
            Icons.access_time,
          ),

          _buildDetailRow(
            'Party Size',
            '${_reservation!.partySize} ${_reservation!.partySize == 1 ? 'Guest' : 'Guests'}',
            Icons.people,
          ),

          _buildDetailRow(
            'Reservation ID',
            _reservation!.id.substring(0, 8).toUpperCase(),
            Icons.confirmation_number,
          ),

          if (_reservation!.specialRequests.isNotEmpty) ...[
            const Divider(height: AppTheme.spacingLg),
            _buildDetailRow(
              'Special Requests',
              _reservation!.specialRequests,
              Icons.note,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPaymentDetailsCard(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppTheme.shadowLight,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.payment,
                color: AppTheme.primaryColor,
                size: 24,
              ),
              const SizedBox(width: AppTheme.spacingSm),
              Expanded(
                child: Text(
                  'Payment Information',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          _buildDetailRow(
            'Transaction ID',
            widget.transactionId,
            Icons.receipt,
          ),
          _buildDetailRow(
            'Amount Paid',
            '\$${widget.booking.totalAmount.toStringAsFixed(2)}',
            Icons.attach_money,
          ),
          if (_reservation!.depositRequired) ...[
            _buildDetailRow(
              'Deposit Type',
              _restaurant!.restaurantType.toString().contains('tier1')
                  ? 'Required Deposit (Tier 1)'
                  : 'Required Deposit (Tier 2)',
              Icons.security,
            ),
          ],
          _buildDetailRow(
            'Payment Status',
            'Confirmed',
            Icons.check_circle,
            valueColor: Colors.green.shade600,
          ),
          _buildDetailRow(
            'Payment Date',
            DateFormat('MMM d, yyyy \'at\' h:mm a').format(DateTime.now()),
            Icons.schedule,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon,
      {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingSm),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 20,
            color: AppTheme.textSecondaryColor,
          ),
          const SizedBox(width: AppTheme.spacingSm),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: valueColor ?? AppTheme.textPrimaryColor,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.end,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ThemeData theme) {
    return Column(
      children: [
        // PDF Download button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _isGeneratingPdf ? null : _downloadPdf,
            icon: _isGeneratingPdf
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.download, size: 20),
            label: Text(_isGeneratingPdf
                ? 'Generating PDF...'
                : 'Download Confirmation PDF'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingMd),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
          ),
        ),

        const SizedBox(height: AppTheme.spacingSm),

        // Share button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _shareReservation,
            icon: const Icon(Icons.share, size: 20),
            label: const Text('Share Reservation'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.primaryColor,
              side: BorderSide(color: AppTheme.primaryColor),
              padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingMd),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBackToHomeButton(ThemeData theme) {
    return SizedBox(
      width: double.infinity,
      child: TextButton.icon(
        onPressed: _navigateToHome,
        icon: const Icon(Icons.home, size: 20),
        label: const Text('Back to Home Screen'),
        style: TextButton.styleFrom(
          foregroundColor: AppTheme.textSecondaryColor,
          padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingMd),
        ),
      ),
    );
  }

  // Action methods
  void _downloadPdf() async {
    setState(() => _isGeneratingPdf = true);

    try {
      HapticFeedback.lightImpact();

      _logger.info('RestaurantBookingConfirmationScreen',
          'Starting PDF generation for booking: ${widget.booking.id}');

      // Generate PDF using RestaurantPdfService with exact visual fidelity
      final pdfFile =
          await RestaurantPdfService.generateRestaurantReservationPdf(
        booking: widget.booking,
        reservation: _reservation!,
        restaurant: _restaurant!,
        transactionId: widget.transactionId,
        userEmail: widget.userEmail,
        userName: widget.userName,
        userPhone: widget.userPhone,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('PDF saved to: ${pdfFile.path}'),
            backgroundColor: AppTheme.primaryColor,
            action: SnackBarAction(
              label: 'Open',
              textColor: Colors.white,
              onPressed: () {
                // TODO: Open PDF with system default app
                // This would typically use url_launcher or similar
                _logger.info('RestaurantBookingConfirmationScreen',
                    'PDF open requested: ${pdfFile.path}');
              },
            ),
          ),
        );
      }

      _logger.info('RestaurantBookingConfirmationScreen',
          'PDF generated successfully: ${pdfFile.path}');
    } catch (e, stackTrace) {
      _logger.error('RestaurantBookingConfirmationScreen',
          'PDF download failed: $e', stackTrace);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to download PDF: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isGeneratingPdf = false);
      }
    }
  }

  void _shareReservation() {
    HapticFeedback.lightImpact();

    // TODO: Implement native sharing functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality will be implemented'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );

    _logger.info('RestaurantBookingConfirmationScreen',
        'Share requested for booking: ${widget.booking.id}');
  }

  void _navigateToHome() {
    HapticFeedback.lightImpact();

    _logger.info(
        'RestaurantBookingConfirmationScreen', 'Navigating to home screen');

    // Navigate to home screen (MainNavigation) and clear navigation stack
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const MainNavigation()),
      (route) => false,
    );
  }
}
