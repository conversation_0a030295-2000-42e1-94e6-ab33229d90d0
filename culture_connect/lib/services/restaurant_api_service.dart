import 'dart:convert';
// Package imports
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;

// Project imports
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/models/travel/restaurant_reservation.dart';
import 'package:culture_connect/models/location/geo_location.dart';
import 'package:culture_connect/services/cache_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Provider for the restaurant API service
final restaurantApiServiceProvider = Provider<RestaurantApiService>((ref) {
  return RestaurantApiService(
    LoggingService(),
    CacheService(),
  );
});

/// Service for making API requests related to restaurants
class RestaurantApiService {
  /// Base URL for the API
  final String _baseUrl = 'https://api.cultureconnect.com';

  /// HTTP client with connection pooling
  static final http.Client _sharedClient = http.Client();

  /// Logging service for error reporting
  final LoggingService _loggingService;

  /// Cache service for offline support
  final CacheService _cacheService;

  /// Connectivity instance for checking network status
  final Connectivity _connectivity = Connectivity();

  /// Cache keys
  static const String _restaurantsCacheKey = 'restaurants';
  static const String _restaurantDetailsCacheKey = 'restaurant_details_';
  static const String _restaurantReservationsCacheKey =
      'restaurant_reservations_';

  /// Creates a new restaurant API service
  RestaurantApiService(this._loggingService, this._cacheService);

  /// Get all restaurants with caching and error handling
  Future<List<Restaurant>> getRestaurants({
    String? cuisineType,
    double? minRating,
    double? maxPrice,
    String? location,
  }) async {
    const endpoint = '/restaurants';
    final cacheKey = _buildCacheKey(_restaurantsCacheKey, {
      'cuisineType': cuisineType,
      'minRating': minRating,
      'maxPrice': maxPrice,
      'location': location,
    });

    try {
      // Check if we're online
      final online = await _isOnline();

      // If we're offline, try to get cached data
      if (!online) {
        final cachedData = await _cacheService.getData(cacheKey);
        if (cachedData != null) {
          _loggingService.debug(
              'RestaurantApiService', 'Using cached restaurants data');
          // For now, return mock data since we don't have JSON serialization
          // TODO: Implement proper JSON serialization for Restaurant model
          return _getMockRestaurants();
        } else {
          _loggingService.debug(
              'RestaurantApiService', 'No cached restaurants data available');
          // Return mock data when offline and no cache
          return _getMockRestaurants();
        }
      }

      // Build query parameters
      final queryParams = <String, String>{};
      if (cuisineType != null) queryParams['cuisine'] = cuisineType;
      if (minRating != null) queryParams['min_rating'] = minRating.toString();
      if (maxPrice != null) queryParams['max_price'] = maxPrice.toString();
      if (location != null) queryParams['location'] = location;

      final uri =
          Uri.parse('$_baseUrl$endpoint').replace(queryParameters: queryParams);

      // TODO: Replace with actual API call when backend is ready
      _loggingService.info('RestaurantApiService', 'Making API call to: $uri');

      // For now, simulate API delay and return mock data
      await Future.delayed(const Duration(milliseconds: 500));
      final mockData = _getMockRestaurants();

      // Cache the response
      // TODO: Implement proper JSON serialization for Restaurant model
      // For now, skip caching since we don't have JSON serialization
      // await _cacheService.saveData(cacheKey, jsonData);

      return mockData;

      /* TODO: Uncomment when backend API is ready
      final response = await _sharedClient.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        final restaurants = data.map((json) => Restaurant.fromJson(json)).toList();

        // Cache the response
        await _cacheService.saveData(cacheKey, response.body);

        return restaurants;
      } else {
        throw Exception('Failed to load restaurants: ${response.statusCode}');
      }
      */
    } catch (e, stackTrace) {
      _loggingService.error(
          'RestaurantApiService', 'Error getting restaurants', e, stackTrace);

      // Return mock data on error
      return _getMockRestaurants();
    }
  }

  /// Get restaurant details by ID
  Future<Restaurant> getRestaurantById(String id) async {
    final endpoint = '/restaurants/$id';
    final cacheKey = '$_restaurantDetailsCacheKey$id';

    try {
      // Check if we're online
      final online = await _isOnline();

      // If we're offline, try to get cached data
      if (!online) {
        final cachedData = await _cacheService.getData(cacheKey);
        if (cachedData != null) {
          _loggingService.debug('RestaurantApiService',
              'Using cached restaurant data for ID: $id');
          // For now, return mock data since we don't have JSON serialization
          // TODO: Implement proper JSON serialization for Restaurant model
          return _getMockRestaurantById(id);
        } else {
          // Try to find the restaurant in the cached restaurants list
          final cachedRestaurants =
              await _cacheService.getData(_restaurantsCacheKey);
          if (cachedRestaurants != null) {
            final List<dynamic> data = cachedRestaurants is String
                ? jsonDecode(cachedRestaurants)
                : cachedRestaurants;
            // For now, return mock data since we don't have JSON serialization
            // TODO: Implement proper JSON serialization for Restaurant model
            return _getMockRestaurantById(id);
          }

          // Return mock data when offline and no cache
          return _getMockRestaurantById(id);
        }
      }

      // TODO: Replace with actual API call when backend is ready
      _loggingService.info(
          'RestaurantApiService', 'Making API call to: $_baseUrl$endpoint');

      // For now, simulate API delay and return mock data
      await Future.delayed(const Duration(milliseconds: 300));
      final mockRestaurant = _getMockRestaurantById(id);

      // Cache the response
      // TODO: Implement proper JSON serialization for Restaurant model
      // For now, skip caching since we don't have JSON serialization
      // await _cacheService.saveData(cacheKey, mockRestaurant.toJson());

      return mockRestaurant;

      /* TODO: Uncomment when backend API is ready
      final response = await _sharedClient.get(
        Uri.parse('$_baseUrl$endpoint'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        final restaurant = Restaurant.fromJson(json);

        // Cache the response
        await _cacheService.saveData(cacheKey, response.body);

        return restaurant;
      } else if (response.statusCode == 404) {
        throw Exception('Restaurant not found');
      } else {
        throw Exception('Failed to load restaurant: ${response.statusCode}');
      }
      */
    } catch (e, stackTrace) {
      _loggingService.error('RestaurantApiService',
          'Error getting restaurant by ID: $id', e, stackTrace);

      // Return mock data on error
      return _getMockRestaurantById(id);
    }
  }

  /// Create a restaurant reservation
  Future<RestaurantReservation> createReservation(
      RestaurantReservation reservation) async {
    const endpoint = '/restaurants/reservations';

    try {
      // Check if we're online
      final online = await _isOnline();
      if (!online) {
        throw Exception(
            'No internet connection. Please try again when online.');
      }

      // TODO: Replace with actual API call when backend is ready
      _loggingService.info('RestaurantApiService',
          'Creating reservation for restaurant: ${reservation.restaurantId}');

      // For now, simulate API delay and return the reservation with confirmed status
      await Future.delayed(const Duration(milliseconds: 800));
      final confirmedReservation = reservation.copyWith(
        status: ReservationStatus.confirmed,
        isConfirmedByRestaurant: true,
      );

      return confirmedReservation;

      /* TODO: Uncomment when backend API is ready
      final response = await _sharedClient.post(
        Uri.parse('$_baseUrl$endpoint'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(reservation.toJson()),
      ).timeout(const Duration(seconds: 15));

      if (response.statusCode == 201) {
        final json = jsonDecode(response.body);
        return RestaurantReservation.fromJson(json);
      } else {
        throw Exception('Failed to create reservation: ${response.statusCode}');
      }
      */
    } catch (e, stackTrace) {
      _loggingService.error(
          'RestaurantApiService', 'Error creating reservation', e, stackTrace);
      rethrow;
    }
  }

  /// Check if device is online
  Future<bool> _isOnline() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      _loggingService.warning(
          'RestaurantApiService', 'Error checking connectivity: $e');
      return true; // Assume online if check fails
    }
  }

  /// Build cache key with parameters
  String _buildCacheKey(String baseKey, Map<String, dynamic> params) {
    final filteredParams = params.entries
        .where((entry) => entry.value != null)
        .map((entry) => '${entry.key}=${entry.value}')
        .join('&');
    return filteredParams.isEmpty ? baseKey : '${baseKey}_$filteredParams';
  }

  /// Get mock restaurants data (fallback)
  List<Restaurant> _getMockRestaurants() {
    // Use the existing mock data from the provider
    return _getSampleRestaurants();
  }

  /// Get mock restaurant by ID (fallback)
  Restaurant _getMockRestaurantById(String id) {
    final restaurants = _getMockRestaurants();
    try {
      return restaurants.firstWhere((restaurant) => restaurant.id == id);
    } catch (e) {
      // Return first restaurant if ID not found
      return restaurants.first;
    }
  }

  /// Get sample restaurants data (simplified for API service)
  List<Restaurant> _getSampleRestaurants() {
    // Return a simple list of restaurants for testing
    // In production, this would be replaced by actual API calls
    return [
      Restaurant(
        id: 'restaurant1',
        name: 'The Fancy Bistro',
        description:
            'An elegant bistro offering a fusion of French and Italian cuisine.',
        price: 150.0,
        currency: '\$',
        rating: 4.7,
        reviewCount: 128,
        imageUrl:
            'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
        additionalImages: [],
        provider: 'CultureConnect Dining',
        location: '123 Gourmet Street, Paris, France',
        coordinates: const GeoLocation(latitude: 48.8566, longitude: 2.3522),
        isAvailable: true,
        isFeatured: true,
        isOnSale: false,
        tags: ['Romantic', 'Fine Dining'],
        amenities: ['Free Wi-Fi', 'Valet Parking'],
        cancellationPolicy:
            'Free cancellation up to 24 hours before your reservation.',
        createdAt: DateTime.now().subtract(const Duration(days: 365)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
        restaurantType: RestaurantType.bistro,
        cuisineTypes: [CuisineType.french, CuisineType.italian],
        menuItems: [],
        openingHours: {
          'Monday': '5:00 PM - 10:00 PM',
          'Tuesday': '5:00 PM - 10:00 PM',
          'Wednesday': '5:00 PM - 10:00 PM',
          'Thursday': '5:00 PM - 10:00 PM',
          'Friday': '5:00 PM - 11:00 PM',
          'Saturday': '5:00 PM - 11:00 PM',
          'Sunday': 'Closed',
        },
        hasOutdoorSeating: true,
        hasBar: true,
        hasLiveMusic: false,
        hasKidsMenu: false,
        hasVegetarianOptions: true,
        hasVeganOptions: false,
        hasGlutenFreeOptions: true,
        hasHalalOptions: false,
        hasKosherOptions: false,
        hasDressCode: true,
        dressCodeDescription: 'Smart casual attire preferred',
        requiresReservation: true,
        hasParking: false,
        hasValetParking: true,
        isWheelchairAccessible: true,
        hasWifi: true,
        acceptsCreditCards: true,
        hasView: false,
        viewDescription: null,
      ),
    ];
  }

  /// Dispose resources
  void dispose() {
    // Note: Shared client is managed globally
  }
}
