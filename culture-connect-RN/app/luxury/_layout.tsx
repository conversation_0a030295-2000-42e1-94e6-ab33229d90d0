import { Stack } from 'expo-router';

export default function LuxuryLayout() {
  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: '#1a1a1a',
        },
        headerTintColor: '#D946EF',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: 'Luxury Services',
        }}
      />
      <Stack.Screen
        name="service/[id]"
        options={{
          title: 'Service Details',
        }}
      />
      <Stack.Screen
        name="auth"
        options={{
          presentation: 'modal',
          headerShown: false,
        }}
      />
    </Stack>
  );
}