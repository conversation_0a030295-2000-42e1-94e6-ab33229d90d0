import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// AR Tutorial Details Screen
class ARTutorialDetailsScreen extends StatefulWidget {
  final String title;
  final String description;
  final double progress;
  final bool isCompleted;
  final IconData icon;

  const ARTutorialDetailsScreen({
    super.key,
    required this.title,
    required this.description,
    required this.progress,
    required this.isCompleted,
    this.icon = Icons.play_circle_outline,
  });

  @override
  State<ARTutorialDetailsScreen> createState() =>
      _ARTutorialDetailsScreenState();
}

class _ARTutorialDetailsScreenState extends State<ARTutorialDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeroSection(),
            const SizedBox(height: AppTheme.spacingLg),
            _buildContentSections(),
            const SizedBox(height: AppTheme.spacingLg),
            _buildActionButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeroSection() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: AppTheme.shadowMedium,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(widget.icon, size: 40, color: AppTheme.primaryColor),
              const SizedBox(width: AppTheme.spacingMd),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.title,
                      style: const TextStyle(
                          fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    Text('${(widget.progress * 100).toInt()}% Complete'),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingMd,
                  vertical: AppTheme.spacingSm,
                ),
                decoration: BoxDecoration(
                  color: widget.isCompleted
                      ? AppTheme.primaryColor.withAlpha(26)
                      : AppTheme.accentColor.withAlpha(26),
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Text(
                  widget.isCompleted ? 'Completed' : 'In Progress',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: widget.isCompleted
                        ? AppTheme.primaryColor
                        : AppTheme.accentColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          LinearProgressIndicator(
            value: widget.progress,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(
              widget.isCompleted ? AppTheme.primaryColor : AppTheme.accentColor,
            ),
            minHeight: 8,
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Text(widget.description),
        ],
      ),
    );
  }

  Widget _buildContentSections() {
    return Column(
      children: [
        _buildInfoCard(
            'Learning Objectives', Icons.checklist, _getLearningObjectives()),
        const SizedBox(height: AppTheme.spacingMd),
        _buildInfoCard(
            'Tutorial Details', Icons.info_outline, _getTutorialDetails()),
        const SizedBox(height: AppTheme.spacingMd),
        _buildInfoCard('Prerequisites', Icons.school, _getPrerequisites()),
      ],
    );
  }

  Widget _buildActionButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _handleStartTutorial,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingMd),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(widget.isCompleted ? Icons.replay : Icons.play_arrow),
            const SizedBox(width: AppTheme.spacingSm),
            Text(
              widget.isCompleted ? 'Review Tutorial' : 'Start Tutorial',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard(String title, IconData icon, List<String> items) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: AppTheme.shadowLight,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 20, color: AppTheme.primaryColor),
              const SizedBox(width: AppTheme.spacingSm),
              Text(title,
                  style: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.w600)),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          ...items
              .map((item) => Padding(
                    padding: const EdgeInsets.only(bottom: AppTheme.spacingSm),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 6,
                          height: 6,
                          margin: const EdgeInsets.only(
                              top: 6, right: AppTheme.spacingSm),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                        Expanded(child: Text(item)),
                      ],
                    ),
                  ))
              .toList(),
        ],
      ),
    );
  }

  List<String> _getLearningObjectives() {
    switch (widget.title) {
      case 'AR Basics Tutorial':
        return [
          'Understand AR concepts',
          'Learn AR navigation',
          'Master AR gestures'
        ];
      case 'Camera Controls':
        return [
          'Master camera positioning',
          'Learn camera settings',
          'Practice capture techniques'
        ];
      case 'Location-Based AR':
        return [
          'Discover nearby POIs',
          'Learn GPS-based AR',
          'Explore landmarks'
        ];
      case 'AR Translation':
        return [
          'Use real-time translation',
          'Learn supported languages',
          'Practice translation'
        ];
      case 'Cultural Insights':
        return [
          'Access cultural information',
          'Learn historical contexts',
          'Explore traditions'
        ];
      default:
        return ['Complete tutorial objectives', 'Practice learned skills'];
    }
  }

  List<String> _getTutorialDetails() {
    switch (widget.title) {
      case 'AR Basics Tutorial':
        return [
          'Duration: 15-20 minutes',
          'Difficulty: Beginner',
          'Interactive exercises'
        ];
      case 'Camera Controls':
        return [
          'Duration: 10-15 minutes',
          'Difficulty: Intermediate',
          'Hands-on practice'
        ];
      case 'Location-Based AR':
        return [
          'Duration: 20-25 minutes',
          'Difficulty: Intermediate',
          'Outdoor recommended'
        ];
      case 'AR Translation':
        return [
          'Duration: 12-18 minutes',
          'Difficulty: Beginner',
          'Multiple languages'
        ];
      case 'Cultural Insights':
        return [
          'Duration: 25-30 minutes',
          'Difficulty: Beginner',
          'Rich multimedia'
        ];
      default:
        return [
          'Duration: 15-20 minutes',
          'Difficulty: Beginner to Intermediate'
        ];
    }
  }

  List<String> _getPrerequisites() {
    switch (widget.title) {
      case 'AR Basics Tutorial':
        return [
          'AR-capable smartphone',
          'Stable internet',
          'Well-lit environment'
        ];
      case 'Camera Controls':
        return [
          'Completed AR Basics',
          'Camera permissions',
          'Basic AR knowledge'
        ];
      case 'Location-Based AR':
        return [
          'Location services enabled',
          'GPS permissions',
          'Outdoor environment'
        ];
      case 'AR Translation':
        return ['Camera permissions', 'Internet connection', 'Text samples'];
      case 'Cultural Insights':
        return ['Basic AR knowledge', 'Audio permissions', 'Cultural interest'];
      default:
        return [
          'AR-capable device',
          'Required permissions',
          'Internet connection'
        ];
    }
  }

  void _handleStartTutorial() {
    HapticFeedback.mediumImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(widget.isCompleted
            ? 'Starting ${widget.title} review...'
            : 'Starting ${widget.title}...'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
    Navigator.pop(context);
  }
}
