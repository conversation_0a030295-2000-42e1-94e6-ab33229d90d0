import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:culture_connect/theme/kaia_design_tokens.dart';
import 'package:culture_connect/data/mock_guides_data.dart';
import 'package:culture_connect/models/guide.dart';
import 'package:culture_connect/widgets/guides/guide_card.dart';
import 'package:culture_connect/widgets/guides/company_guide_card.dart';
import 'package:culture_connect/widgets/guides/premium_top_guide_card.dart';
import 'package:culture_connect/screens/guide_details_screen.dart';

/// Comprehensive Guides listing screen with top section and vertical list
/// Follows CultureConnect design system with premium aesthetics
class GuidesScreen extends StatefulWidget {
  const GuidesScreen({super.key});

  @override
  State<GuidesScreen> createState() => _GuidesScreenState();
}

class _GuidesScreenState extends State<GuidesScreen> {
  final ScrollController _scrollController = ScrollController();
  String _selectedFilter = 'All';

  final List<String> _filters = [
    'All',
    'Individual',
    'Companies',
    'Certified',
    'Top Rated',
    'Cultural',
    'Adventure',
    'Food',
  ];

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  List<Guide> get _filteredGuides {
    final allGuides = MockGuidesData.allGuides;

    switch (_selectedFilter) {
      case 'Individual':
        return allGuides
            .where((guide) => guide.guideType == GuideType.individual)
            .toList();
      case 'Companies':
        return allGuides
            .where((guide) => guide.guideType == GuideType.company)
            .toList();
      case 'Certified':
        return allGuides.where((guide) => guide.isCertified).toList();
      case 'Top Rated':
        return allGuides.where((guide) => (guide.rating ?? 0) >= 4.7).toList();
      case 'Cultural':
        return MockGuidesData.getGuidesBySpecialty('cultural');
      case 'Adventure':
        return MockGuidesData.getGuidesBySpecialty('adventure');
      case 'Food':
        return MockGuidesData.getGuidesBySpecialty('food');
      default:
        return allGuides;
    }
  }

  void _handleGuidePress(Guide guide) {
    HapticFeedback.lightImpact();
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GuideDetailsScreen(guide: guide),
      ),
    );
  }

  void _handleFilterPress(String filter) {
    HapticFeedback.selectionClick();
    setState(() {
      _selectedFilter = filter;
    });
  }

  @override
  Widget build(BuildContext context) {
    final topGuides = MockGuidesData.topGuides;
    final filteredGuides = _filteredGuides;

    return Scaffold(
      backgroundColor: KaiaDesignTokens.neutralWhite,
      appBar: AppBar(
        backgroundColor: KaiaDesignTokens.neutralWhite,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: KaiaDesignTokens.neutralGray800,
            size: 20,
          ),
          onPressed: () {
            HapticFeedback.lightImpact();
            Navigator.pop(context);
          },
        ),
        title: Text(
          'Guides',
          style: TextStyle(
            color: KaiaDesignTokens.neutralGray800,
            fontSize: KaiaDesignTokens.fontSize2Xl,
            fontWeight: KaiaDesignTokens.fontWeightBold,
          ),
        ),
        centerTitle: true,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // Top Guides Section
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(
                    KaiaDesignTokens.spacing16,
                    KaiaDesignTokens.spacing8,
                    KaiaDesignTokens.spacing16,
                    KaiaDesignTokens.spacing12,
                  ),
                  child: Text(
                    'Top Guides',
                    style: TextStyle(
                      color: KaiaDesignTokens.neutralGray800,
                      fontSize: KaiaDesignTokens.fontSizeXl,
                      fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                    ),
                  ),
                ),
                SizedBox(
                  height:
                      315, // Increased by 5px to accommodate taller specialty tag containers
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(
                      horizontal: KaiaDesignTokens.spacing8,
                    ),
                    itemCount: topGuides.length,
                    itemBuilder: (context, index) {
                      final guide = topGuides[index];
                      return PremiumTopGuideCard(
                        guide: guide,
                        onPressed: () => _handleGuidePress(guide),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),

          // Filter Section
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.fromLTRB(
                0,
                KaiaDesignTokens.spacing24,
                0,
                KaiaDesignTokens.spacing16,
              ),
              height: 50,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(
                  horizontal: KaiaDesignTokens.spacing16,
                ),
                itemCount: _filters.length,
                itemBuilder: (context, index) {
                  final filter = _filters[index];
                  final isSelected = filter == _selectedFilter;

                  return Container(
                    margin: const EdgeInsets.only(
                        right: KaiaDesignTokens.spacing12),
                    child: FilterChip(
                      label: Text(
                        filter,
                        style: TextStyle(
                          color: isSelected
                              ? KaiaDesignTokens.neutralWhite
                              : KaiaDesignTokens.neutralGray600,
                          fontSize: KaiaDesignTokens.fontSizeSm,
                          fontWeight: isSelected
                              ? KaiaDesignTokens.fontWeightMedium
                              : KaiaDesignTokens.fontWeightRegular,
                        ),
                      ),
                      selected: isSelected,
                      onSelected: (_) => _handleFilterPress(filter),
                      backgroundColor: KaiaDesignTokens.neutralGray100,
                      selectedColor: KaiaDesignTokens.primaryIndigo,
                      checkmarkColor: KaiaDesignTokens.neutralWhite,
                      side: BorderSide.none,
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(KaiaDesignTokens.radiusLg),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: KaiaDesignTokens.spacing16,
                        vertical: KaiaDesignTokens.spacing8,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          // All Guides Section Header
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(
                KaiaDesignTokens.spacing16,
                KaiaDesignTokens.spacing8,
                KaiaDesignTokens.spacing16,
                KaiaDesignTokens.spacing16,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'All Guides',
                    style: TextStyle(
                      color: KaiaDesignTokens.neutralGray800,
                      fontSize: KaiaDesignTokens.fontSizeXl,
                      fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                    ),
                  ),
                  Text(
                    '${filteredGuides.length} guides',
                    style: TextStyle(
                      color: KaiaDesignTokens.neutralGray500,
                      fontSize: KaiaDesignTokens.fontSizeSm,
                      fontWeight: KaiaDesignTokens.fontWeightRegular,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Guides List
          SliverPadding(
            padding: const EdgeInsets.symmetric(
              horizontal: KaiaDesignTokens.spacing16,
            ),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final guide = filteredGuides[index];

                  return Container(
                    margin: const EdgeInsets.only(
                        bottom: KaiaDesignTokens.spacing16),
                    child: guide.guideType == GuideType.company
                        ? CompanyGuideCard(
                            guide: guide,
                            onPressed: () => _handleGuidePress(guide),
                          )
                        : GuideCard(
                            guide: guide,
                            onPressed: () => _handleGuidePress(guide),
                          ),
                  );
                },
                childCount: filteredGuides.length,
              ),
            ),
          ),

          // Bottom Padding
          const SliverToBoxAdapter(
            child: SizedBox(height: KaiaDesignTokens.spacing32),
          ),
        ],
      ),
    );
  }
}
