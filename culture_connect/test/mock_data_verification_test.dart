import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/services/booking_service.dart';

void main() {
  group('Mock Booking Data Verification', () {
    late BookingService bookingService;

    setUp(() {
      bookingService = BookingService();
    });

    test('should return comprehensive mock booking data', () async {
      // Get mock bookings
      final bookings = await bookingService.getUserBookings();

      // Verify we have the expected number of bookings
      expect(bookings.length, equals(12));

      // Verify all booking statuses are represented
      final statuses = bookings.map((b) => b.status).toSet();
      expect(statuses.contains(BookingStatus.confirmed), isTrue);
      expect(statuses.contains(BookingStatus.pending), isTrue);
      expect(statuses.contains(BookingStatus.completed), isTrue);
      expect(statuses.contains(BookingStatus.cancelled), isTrue);
      expect(statuses.contains(BookingStatus.refunded), isTrue);

      // Verify price range diversity
      final prices = bookings.map((b) => b.totalAmount).toList();
      expect(prices.any((price) => price < 50), isTrue); // Budget options
      expect(prices.any((price) => price > 500), isTrue); // Premium options

      // Verify participant count diversity
      final participantCounts = bookings.map((b) => b.participantCount).toSet();
      expect(participantCounts.contains(1), isTrue); // Solo
      expect(participantCounts.contains(2), isTrue); // Couple
      expect(participantCounts.any((count) => count >= 4), isTrue); // Groups

      // Verify special requirements variety
      final withRequirements = bookings.where((b) => b.specialRequirements.isNotEmpty).length;
      final withoutRequirements = bookings.where((b) => b.specialRequirements.isEmpty).length;
      expect(withRequirements, greaterThan(0));
      expect(withoutRequirements, greaterThan(0));

      // Verify date distribution (past, present, future)
      final now = DateTime.now();
      final pastBookings = bookings.where((b) => b.date.isBefore(now)).length;
      final futureBookings = bookings.where((b) => b.date.isAfter(now)).length;
      expect(pastBookings, greaterThan(0));
      expect(futureBookings, greaterThan(0));
    });

    test('should have specific test bookings for edge cases', () async {
      final bookings = await bookingService.getUserBookings();

      // Find the edge case booking with very long title
      final longTitleBooking = bookings.firstWhere(
        (b) => b.experienceId.contains('Comprehensive Cultural Immersion'),
        orElse: () => throw Exception('Long title booking not found'),
      );
      expect(longTitleBooking.experienceId.length, greaterThan(100));
      expect(longTitleBooking.specialRequirements.length, greaterThan(200));

      // Find budget booking
      final budgetBooking = bookings.firstWhere(
        (b) => b.totalAmount < 30,
        orElse: () => throw Exception('Budget booking not found'),
      );
      expect(budgetBooking.totalAmount, equals(25.0));

      // Find premium booking
      final premiumBooking = bookings.firstWhere(
        (b) => b.totalAmount > 700,
        orElse: () => throw Exception('Premium booking not found'),
      );
      expect(premiumBooking.totalAmount, equals(720.0));
    });

    test('should have proper booking ID format', () async {
      final bookings = await bookingService.getUserBookings();

      for (final booking in bookings) {
        // Verify booking ID format (BK001, BK002, etc.)
        expect(booking.id, matches(RegExp(r'^BK\d{3}$')));
        
        // Verify required fields are not empty
        expect(booking.experienceId, isNotEmpty);
        expect(booking.participantCount, greaterThan(0));
        expect(booking.totalAmount, greaterThan(0));
        
        // Verify date consistency
        expect(booking.createdAt, isNotNull);
        expect(booking.updatedAt, isNotNull);
        expect(booking.timeSlot.startTime, isNotNull);
        expect(booking.timeSlot.endTime, isNotNull);
        
        // Verify time slot logic
        expect(booking.timeSlot.endTime.isAfter(booking.timeSlot.startTime), isTrue);
      }
    });

    test('should distribute bookings correctly across tabs', () async {
      final bookings = await bookingService.getUserBookings();
      final now = DateTime.now();

      // Upcoming bookings (confirmed + pending, future dates)
      final upcomingBookings = bookings.where((b) =>
          (b.status == BookingStatus.confirmed || b.status == BookingStatus.pending) &&
          b.date.isAfter(now)).toList();

      // Pending bookings
      final pendingBookings = bookings.where((b) => b.status == BookingStatus.pending).toList();

      // Past bookings (completed, cancelled, refunded, or past dates)
      final pastBookings = bookings.where((b) =>
          b.status == BookingStatus.completed ||
          b.status == BookingStatus.cancelled ||
          b.status == BookingStatus.refunded ||
          b.date.isBefore(now)).toList();

      // Verify we have bookings in each category
      expect(upcomingBookings.length, greaterThan(0));
      expect(pendingBookings.length, greaterThan(0));
      expect(pastBookings.length, greaterThan(0));

      // Verify specific counts based on our mock data
      expect(pendingBookings.length, equals(2)); // BK004, BK005
      expect(bookings.where((b) => b.status == BookingStatus.confirmed).length, equals(6));
      expect(bookings.where((b) => b.status == BookingStatus.completed).length, equals(2));
      expect(bookings.where((b) => b.status == BookingStatus.cancelled).length, equals(1));
      expect(bookings.where((b) => b.status == BookingStatus.refunded).length, equals(1));
    });

    test('should have action buttons for confirmed bookings only', () async {
      final bookings = await bookingService.getUserBookings();

      final confirmedBookings = bookings.where((b) => b.status == BookingStatus.confirmed).toList();
      final nonConfirmedBookings = bookings.where((b) => b.status != BookingStatus.confirmed).toList();

      // Confirmed bookings should show action buttons
      expect(confirmedBookings.length, equals(6));
      
      // Non-confirmed bookings should not show action buttons
      expect(nonConfirmedBookings.length, equals(6));
      
      // Verify specific confirmed booking IDs
      final confirmedIds = confirmedBookings.map((b) => b.id).toSet();
      expect(confirmedIds.contains('BK001'), isTrue);
      expect(confirmedIds.contains('BK002'), isTrue);
      expect(confirmedIds.contains('BK003'), isTrue);
      expect(confirmedIds.contains('BK010'), isTrue);
      expect(confirmedIds.contains('BK011'), isTrue);
      expect(confirmedIds.contains('BK012'), isTrue);
    });
  });
}
