import React from "react";
import { StyleSheet, View, Text, Pressable } from "react-native";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { Calendar, CheckCircle, XCircle, Clock, MapPin, Star, ChevronRight } from "lucide-react-native";
import { Booking } from "@/types";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";

interface BookingCardProps {
  booking: Booking;
  onPress: (booking: Booking) => void;
}

export const BookingCard: React.FC<BookingCardProps> = ({
  booking,
  onPress,
}) => {
  const getStatusIcon = () => {
    switch (booking.status) {
      case "upcoming":
        return <Clock size={12} color={colors.info} />;
      case "completed":
        return <CheckCircle size={12} color={colors.success} />;
      case "cancelled":
        return <XCircle size={12} color={colors.error} />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    switch (booking.status) {
      case "upcoming":
        return "Upcoming";
      case "completed":
        return "Completed";
      case "cancelled":
        return "Cancelled";
      default:
        return "";
    }
  };

  const getStatusColor = () => {
    switch (booking.status) {
      case "upcoming":
        return colors.info;
      case "completed":
        return colors.success;
      case "cancelled":
        return colors.error;
      default:
        return colors.textLight;
    }
  };

  return (
    <Pressable
      style={styles.container}
      onPress={() => onPress(booking)}
      android_ripple={{ color: colors.border }}
    >
      <View style={styles.imageContainer}>
        <Image
          source={{ uri: booking.imageUrl }}
          style={styles.image}
          contentFit="cover"
          transition={300}
        />
        <LinearGradient
          colors={["transparent", "rgba(0,0,0,0.3)"]}
          style={styles.imageGradient}
        />
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor() + '20' }]}>
          {getStatusIcon()}
          <Text style={[styles.statusBadgeText, { color: getStatusColor() }]}>
            {getStatusText()}
          </Text>
        </View>
      </View>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title} numberOfLines={1}>{booking.title}</Text>
          <ChevronRight size={16} color={colors.textLight} />
        </View>
        <View style={styles.infoRow}>
          <Calendar size={14} color={colors.textSecondary} />
          <Text style={styles.date}>{booking.date}</Text>
        </View>
        <View style={styles.metaRow}>
          <View style={styles.metaItem}>
            <MapPin size={12} color={colors.textLight} />
            <Text style={styles.metaText}>Destination</Text>
          </View>
          <View style={styles.metaItem}>
            <Star size={12} color="#FFD700" fill="#FFD700" />
            <Text style={styles.metaText}>4.8</Text>
          </View>
        </View>
        <View style={styles.footer}>
          <Text style={styles.confirmationCode}>
            #{booking.confirmationCode}
          </Text>
          <View style={styles.priceContainer}>
            <Text style={styles.priceLabel}>Total</Text>
            <Text style={styles.price}>${booking.price}</Text>
          </View>
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 20,
    overflow: "hidden",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  imageContainer: {
    position: "relative",
  },
  image: {
    height: 160,
    width: "100%",
  },
  imageGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  statusBadge: {
    position: "absolute",
    top: spacing.md,
    right: spacing.md,
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 16,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    backgroundColor: "rgba(255, 255, 255, 0.9)",
  },
  statusBadgeText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.semibold as any,
    marginLeft: spacing.xs,
  },
  content: {
    padding: spacing.lg,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  title: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    flex: 1,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  date: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  metaRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.md,
    gap: spacing.md,
  },
  metaItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  metaText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textLight,
    marginLeft: spacing.xs,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
  },
  confirmationCode: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textLight,
    fontWeight: typography.weights.medium as any,
  },
  priceContainer: {
    alignItems: "flex-end",
  },
  priceLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    marginBottom: 2,
  },
  price: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.primary,
  },
});