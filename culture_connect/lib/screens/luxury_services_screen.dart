import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Luxury Services Screen - Premium travel services
/// Matches React Native luxury services functionality
class LuxuryServicesScreen extends ConsumerStatefulWidget {
  const LuxuryServicesScreen({super.key});

  @override
  ConsumerState<LuxuryServicesScreen> createState() =>
      _LuxuryServicesScreenState();
}

class _LuxuryServicesScreenState extends ConsumerState<LuxuryServicesScreen> {
  String _selectedCategory = 'all';

  final List<LuxuryCategory> _categories = const [
    LuxuryCategory(
        id: 'all', title: 'All Services', icon: Icons.diamond_outlined),
    LuxuryCategory(
        id: 'jets', title: 'Private Jets', icon: Icons.flight_outlined),
    LuxuryCategory(
        id: 'helicopters', title: 'Helicopters', icon: Icons.flight_outlined),
    LuxuryCategory(
        id: 'cars', title: 'Luxury Cars', icon: Icons.directions_car_outlined),
    LuxuryCategory(id: 'yachts', title: 'Yachts', icon: Icons.sailing_outlined),
  ];

  final List<LuxuryService> _services = const [
    LuxuryService(
      id: '1',
      title: 'Private Jet Charter',
      description:
          'Exclusive private jet services for ultimate comfort and privacy',
      category: 'jets',
      price: 'From \$5,000/hour',
      rating: 4.9,
      imageUrl:
          'https://images.unsplash.com/photo-1540962351504-03099e0a754b?w=400',
      features: ['24/7 Concierge', 'Gourmet Catering', 'Wi-Fi', 'Privacy'],
    ),
    LuxuryService(
      id: '2',
      title: 'Helicopter Tours',
      description: 'Scenic helicopter tours with professional pilots',
      category: 'helicopters',
      price: 'From \$800/hour',
      rating: 4.8,
      imageUrl:
          'https://images.unsplash.com/photo-1544717684-6e5e4c2b6c3b?w=400',
      features: [
        'Professional Pilot',
        'Scenic Routes',
        'Photography',
        'Safety First'
      ],
    ),
    LuxuryService(
      id: '3',
      title: 'Luxury Car Rental',
      description: 'Premium vehicles for discerning travelers',
      category: 'cars',
      price: 'From \$300/day',
      rating: 4.7,
      imageUrl:
          'https://images.unsplash.com/photo-1544717684-6e5e4c2b6c3b?w=400',
      features: [
        'Premium Brands',
        'Chauffeur Available',
        'Insurance',
        'Delivery'
      ],
    ),
    LuxuryService(
      id: '4',
      title: 'Yacht Charter',
      description: 'Exclusive yacht charters for unforgettable experiences',
      category: 'yachts',
      price: 'From \$2,000/day',
      rating: 4.9,
      imageUrl:
          'https://images.unsplash.com/photo-1544717684-6e5e4c2b6c3b?w=400',
      features: [
        'Professional Crew',
        'Gourmet Dining',
        'Water Sports',
        'Luxury Amenities'
      ],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),

            // Categories
            _buildCategories(),

            // Services List
            Expanded(
              child: _buildServicesList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        boxShadow: AppTheme.shadowLight,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.arrow_back, color: Colors.white),
              ),
              const Expanded(
                child: Text(
                  'Luxury Services',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(width: 48), // Balance the back button
            ],
          ),
          const SizedBox(height: AppTheme.spacingSm),
          const Text(
            'Premium travel experiences for discerning travelers',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCategories() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingSm),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingMd),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = _selectedCategory == category.id;

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedCategory = category.id;
              });
            },
            child: Container(
              margin: EdgeInsets.only(
                right: index < _categories.length - 1 ? AppTheme.spacingMd : 0,
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingMd,
                vertical: AppTheme.spacingSm,
              ),
              decoration: BoxDecoration(
                color:
                    isSelected ? AppTheme.primaryColor : AppTheme.surfaceColor,
                borderRadius:
                    BorderRadius.circular(AppTheme.borderRadiusMedium),
                boxShadow:
                    isSelected ? AppTheme.shadowMedium : AppTheme.shadowLight,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    category.icon,
                    size: 20,
                    color:
                        isSelected ? Colors.white : AppTheme.textPrimaryColor,
                  ),
                  const SizedBox(width: AppTheme.spacingSm),
                  Text(
                    category.title,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color:
                          isSelected ? Colors.white : AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildServicesList() {
    final filteredServices = _selectedCategory == 'all'
        ? _services
        : _services
            .where((service) => service.category == _selectedCategory)
            .toList();

    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      itemCount: filteredServices.length,
      itemBuilder: (context, index) {
        final service = filteredServices[index];
        return _buildServiceCard(service);
      },
    );
  }

  Widget _buildServiceCard(LuxuryService service) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        boxShadow: AppTheme.shadowLight,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Service Image
          Container(
            height: 200,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha(50),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppTheme.borderRadiusMedium),
                topRight: Radius.circular(AppTheme.borderRadiusMedium),
              ),
            ),
            child: Center(
              child: Icon(
                _getCategoryIcon(service.category),
                size: 64,
                color: AppTheme.primaryColor,
              ),
            ),
          ),

          // Service Details
          Padding(
            padding: const EdgeInsets.all(AppTheme.spacingMd),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        service.title,
                        style: TextStyle(
                          fontSize: AppTheme.fontSizeLg,
                          fontWeight: AppTheme.fontWeightBold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ),
                    Row(
                      children: [
                        const Icon(Icons.star, color: Colors.amber, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          service.rating.toString(),
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: AppTheme.spacingSm),
                Text(
                  service.description,
                  style: TextStyle(
                    color: AppTheme.textSecondaryColor,
                    fontSize: AppTheme.fontSizeMd,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingMd),

                // Features
                Wrap(
                  spacing: AppTheme.spacingSm,
                  runSpacing: AppTheme.spacingSm,
                  children: service.features
                      .map((feature) => Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppTheme.spacingSm,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.primaryColor.withAlpha(26),
                              borderRadius: BorderRadius.circular(
                                  AppTheme.borderRadiusSmall),
                            ),
                            child: Text(
                              feature,
                              style: TextStyle(
                                fontSize: 12,
                                color: AppTheme.primaryColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ))
                      .toList(),
                ),

                const SizedBox(height: AppTheme.spacingMd),

                // Price and Book Button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      service.price,
                      style: TextStyle(
                        fontSize: AppTheme.fontSizeLg,
                        fontWeight: AppTheme.fontWeightBold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () => _bookService(service),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                              AppTheme.borderRadiusMedium),
                        ),
                      ),
                      child: const Text('Book Now'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'jets':
        return Icons.flight_outlined;
      case 'helicopters':
        return Icons.flight_outlined;
      case 'cars':
        return Icons.directions_car_outlined;
      case 'yachts':
        return Icons.sailing_outlined;
      default:
        return Icons.diamond_outlined;
    }
  }

  void _bookService(LuxuryService service) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Book ${service.title}'),
        content: Text(
            'Booking functionality for ${service.title} will be implemented soon.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

class LuxuryCategory {
  final String id;
  final String title;
  final IconData icon;

  const LuxuryCategory({
    required this.id,
    required this.title,
    required this.icon,
  });
}

class LuxuryService {
  final String id;
  final String title;
  final String description;
  final String category;
  final String price;
  final double rating;
  final String imageUrl;
  final List<String> features;

  const LuxuryService({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.price,
    required this.rating,
    required this.imageUrl,
    required this.features,
  });
}
