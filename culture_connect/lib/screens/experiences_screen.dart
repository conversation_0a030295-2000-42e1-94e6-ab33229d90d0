import 'package:flutter/material.dart';
import 'package:culture_connect/theme/kaia_design_tokens.dart';
import 'package:culture_connect/widgets/experiences/experience_card.dart';
import 'package:culture_connect/widgets/skeleton_loading.dart';
import 'package:culture_connect/screens/trending_experience_detail_screen.dart';

/// Comprehensive Experiences screen that displays all available experiences
/// Serves as the destination when users tap "See All" from Trending Experiences
class ExperiencesScreen extends StatefulWidget {
  const ExperiencesScreen({super.key});

  @override
  State<ExperiencesScreen> createState() => _ExperiencesScreenState();
}

class _ExperiencesScreenState extends State<ExperiencesScreen>
    with TickerProviderStateMixin {
  bool _isLoading = true;
  bool _isGridView = true;
  String _searchQuery = '';
  String? _selectedCategory;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Categories for filtering
  final List<String> _categories = [
    'All',
    'Cultural',
    'Adventure',
    'Food & Drink',
    'Nature',
    'Historical',
    'Art & Museums',
    'Nightlife',
    'Wellness',
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _loadExperiences();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadExperiences() async {
    setState(() => _isLoading = true);

    // Simulate loading delay
    await Future.delayed(const Duration(milliseconds: 800));

    if (mounted) {
      setState(() => _isLoading = false);
      _animationController.forward();
    }
  }

  List<Map<String, dynamic>> get _filteredExperiences {
    var experiences = _mockExperiences;

    // Filter by category
    if (_selectedCategory != null && _selectedCategory != 'All') {
      experiences = experiences
          .where((exp) => exp['category'] == _selectedCategory)
          .toList();
    }

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      experiences = experiences
          .where((exp) =>
              exp['title'].toLowerCase().contains(_searchQuery.toLowerCase()) ||
              exp['location']
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              exp['category']
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()))
          .toList();
    }

    return experiences;
  }

  void _handleExperiencePress(String experienceId) {
    // Find the experience data
    final experience = _mockExperiences.firstWhere(
      (exp) => exp['id'] == experienceId,
      orElse: () => _mockExperiences.first,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TrendingExperienceDetailScreen(
          id: experienceId,
          title: experience['title'],
          location: experience['location'],
          rating: experience['rating'].toDouble(),
          reviewCount: experience['reviewCount'],
          price: experience['price'],
          duration: experience['duration'],
          imageUrl: experience['imageUrl'],
          tags: List<String>.from(experience['tags']),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: KaiaDesignTokens.neutralGray50,
      appBar: AppBar(
        backgroundColor: KaiaDesignTokens.neutralWhite,
        elevation: 0,
        title: const Text(
          'All Experiences',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: KaiaDesignTokens.neutralGray900,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _isGridView ? Icons.view_list : Icons.grid_view,
              color: KaiaDesignTokens.neutralGray700,
            ),
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          _buildSearchAndFilterSection(),

          // Results Count
          _buildResultsCount(),

          // Experiences Grid/List
          Expanded(
            child: _isLoading
                ? _buildLoadingState()
                : _filteredExperiences.isEmpty
                    ? _buildEmptyState()
                    : FadeTransition(
                        opacity: _fadeAnimation,
                        child:
                            _isGridView ? _buildGridView() : _buildListView(),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilterSection() {
    return Container(
      color: KaiaDesignTokens.neutralWhite,
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
      child: Column(
        children: [
          // Search Bar
          Container(
            decoration: BoxDecoration(
              color: KaiaDesignTokens.neutralGray100,
              borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
              border: Border.all(
                color: KaiaDesignTokens.neutralGray200,
                width: 1,
              ),
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: const InputDecoration(
                hintText: 'Search experiences...',
                prefixIcon: Icon(
                  Icons.search,
                  color: KaiaDesignTokens.neutralGray500,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: KaiaDesignTokens.spacing16,
                  vertical: KaiaDesignTokens.spacing12,
                ),
              ),
            ),
          ),

          const SizedBox(height: KaiaDesignTokens.spacing16),

          // Category Filter Pills
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = _selectedCategory == category ||
                    (_selectedCategory == null && category == 'All');

                return Padding(
                  padding: EdgeInsets.only(
                    right: index < _categories.length - 1
                        ? KaiaDesignTokens.spacing8
                        : 0,
                  ),
                  child: FilterChip(
                    label: Text(category),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = selected ? category : null;
                        if (category == 'All') _selectedCategory = null;
                      });
                    },
                    backgroundColor: KaiaDesignTokens.neutralWhite,
                    selectedColor: KaiaDesignTokens.primaryIndigo.withAlpha(26),
                    checkmarkColor: KaiaDesignTokens.primaryIndigo,
                    labelStyle: TextStyle(
                      color: isSelected
                          ? KaiaDesignTokens.primaryIndigo
                          : KaiaDesignTokens.neutralGray700,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.w500,
                    ),
                    side: BorderSide(
                      color: isSelected
                          ? KaiaDesignTokens.primaryIndigo
                          : KaiaDesignTokens.neutralGray300,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsCount() {
    final count = _filteredExperiences.length;
    return Container(
      color: KaiaDesignTokens.neutralWhite,
      padding: const EdgeInsets.fromLTRB(
        KaiaDesignTokens.spacing16,
        0,
        KaiaDesignTokens.spacing16,
        KaiaDesignTokens.spacing16,
      ),
      child: Row(
        children: [
          Text(
            '$count experience${count != 1 ? 's' : ''} found',
            style: const TextStyle(
              fontSize: 14,
              color: KaiaDesignTokens.neutralGray600,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return _isGridView
        ? GridView.builder(
            padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.75,
              crossAxisSpacing: KaiaDesignTokens.spacing16,
              mainAxisSpacing: KaiaDesignTokens.spacing16,
            ),
            itemCount: 6,
            itemBuilder: (context, index) => const SkeletonCard(
              width: double.infinity,
              height: 280,
              hasImage: true,
              imageHeight: 160,
            ),
          )
        : ListView.builder(
            padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
            itemCount: 4,
            itemBuilder: (context, index) => const Padding(
              padding: EdgeInsets.only(bottom: KaiaDesignTokens.spacing16),
              child: SkeletonCard(
                width: double.infinity,
                height: 280,
                hasImage: true,
                imageHeight: 160,
              ),
            ),
          );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.explore_off,
            size: 64,
            color: KaiaDesignTokens.neutralGray400,
          ),
          SizedBox(height: KaiaDesignTokens.spacing16),
          Text(
            'No experiences found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: KaiaDesignTokens.neutralGray700,
            ),
          ),
          SizedBox(height: KaiaDesignTokens.spacing8),
          Text(
            'Try adjusting your search or filters',
            style: TextStyle(
              fontSize: 14,
              color: KaiaDesignTokens.neutralGray500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGridView() {
    return RefreshIndicator(
      onRefresh: _loadExperiences,
      child: GridView.builder(
        padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.75,
          crossAxisSpacing: KaiaDesignTokens.spacing16,
          mainAxisSpacing: KaiaDesignTokens.spacing16,
        ),
        itemCount: _filteredExperiences.length,
        itemBuilder: (context, index) {
          final experience = _filteredExperiences[index];
          return ExperienceCard(
            title: experience['title'],
            location: experience['location'],
            imageUrl: experience['imageUrl'],
            rating: experience['rating'].toDouble(),
            reviewCount: experience['reviewCount'],
            price: experience['price'],
            duration: experience['duration'],
            tags: List<String>.from(experience['tags']),
            category: experience['category'],
            isGridMode: true,
            onPressed: () => _handleExperiencePress(experience['id']),
            onFavoritePressed: () {
              // Handle favorite functionality
              // TODO: Implement favorite functionality
            },
          );
        },
      ),
    );
  }

  Widget _buildListView() {
    return RefreshIndicator(
      onRefresh: _loadExperiences,
      child: ListView.builder(
        padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
        itemCount: _filteredExperiences.length,
        itemBuilder: (context, index) {
          final experience = _filteredExperiences[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: KaiaDesignTokens.spacing16),
            child: ExperienceCard(
              title: experience['title'],
              location: experience['location'],
              imageUrl: experience['imageUrl'],
              rating: experience['rating'].toDouble(),
              reviewCount: experience['reviewCount'],
              price: experience['price'],
              duration: experience['duration'],
              tags: List<String>.from(experience['tags']),
              category: experience['category'],
              isGridMode: false,
              onPressed: () => _handleExperiencePress(experience['id']),
              onFavoritePressed: () {
                // Handle favorite functionality
                // TODO: Implement favorite functionality
              },
            ),
          );
        },
      ),
    );
  }

  // Comprehensive mock data for 20 diverse experiences
  static const List<Map<String, dynamic>> _mockExperiences = [
    {
      'id': 'exp_001',
      'title': 'Traditional Tea Ceremony Experience',
      'location': 'Kyoto, Japan',
      'category': 'Cultural',
      'rating': 4.9,
      'reviewCount': 342,
      'price': '\$85',
      'duration': '2 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=800',
      'tags': ['Cultural', 'Traditional', 'Authentic'],
      'description':
          'Immerse yourself in the ancient art of Japanese tea ceremony with a master tea practitioner.',
    },
    {
      'id': 'exp_002',
      'title': 'Sunrise Hot Air Balloon Adventure',
      'location': 'Cappadocia, Turkey',
      'category': 'Adventure',
      'rating': 4.8,
      'reviewCount': 1256,
      'price': '\$180',
      'duration': '3 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800',
      'tags': ['Adventure', 'Scenic', 'Sunrise'],
      'description':
          'Soar above the fairy chimneys and unique rock formations of Cappadocia at sunrise.',
    },
    {
      'id': 'exp_003',
      'title': 'Authentic Pasta Making Class',
      'location': 'Rome, Italy',
      'category': 'Food & Drink',
      'rating': 4.7,
      'reviewCount': 892,
      'price': '\$95',
      'duration': '3 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800',
      'tags': ['Cooking', 'Italian', 'Hands-on'],
      'description':
          'Learn to make traditional Italian pasta from scratch with a local chef in Rome.',
    },
    {
      'id': 'exp_004',
      'title': 'Amazon Rainforest Canopy Walk',
      'location': 'Iquitos, Peru',
      'category': 'Nature',
      'rating': 4.6,
      'reviewCount': 567,
      'price': '\$120',
      'duration': '4 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800',
      'tags': ['Nature', 'Wildlife', 'Adventure'],
      'description':
          'Walk among the treetops and discover the incredible biodiversity of the Amazon rainforest.',
    },
    {
      'id': 'exp_005',
      'title': 'Ancient Pyramid Exploration',
      'location': 'Giza, Egypt',
      'category': 'Historical',
      'rating': 4.5,
      'reviewCount': 2134,
      'price': '\$75',
      'duration': '5 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=800',
      'tags': ['Historical', 'Ancient', 'UNESCO'],
      'description':
          'Explore the last remaining wonder of the ancient world with an expert Egyptologist guide.',
    },
    {
      'id': 'exp_006',
      'title': 'Louvre Private Art Tour',
      'location': 'Paris, France',
      'category': 'Art & Museums',
      'rating': 4.8,
      'reviewCount': 756,
      'price': '\$150',
      'duration': '3 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=800',
      'tags': ['Art', 'Museum', 'Private'],
      'description':
          'Skip the lines and explore the world\'s most famous museum with an art historian.',
    },
    {
      'id': 'exp_007',
      'title': 'Rooftop Bar Crawl Experience',
      'location': 'Bangkok, Thailand',
      'category': 'Nightlife',
      'rating': 4.4,
      'reviewCount': 623,
      'price': '\$65',
      'duration': '4 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=800',
      'tags': ['Nightlife', 'Rooftop', 'Social'],
      'description':
          'Experience Bangkok\'s vibrant nightlife scene from the city\'s best rooftop bars.',
    },
    {
      'id': 'exp_008',
      'title': 'Himalayan Yoga Retreat',
      'location': 'Rishikesh, India',
      'category': 'Wellness',
      'rating': 4.9,
      'reviewCount': 445,
      'price': '\$200',
      'duration': '6 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1506126613408-eca07ce68e71?w=800',
      'tags': ['Wellness', 'Yoga', 'Meditation'],
      'description':
          'Find inner peace with a transformative yoga session in the foothills of the Himalayas.',
    },
    {
      'id': 'exp_009',
      'title': 'Northern Lights Photography',
      'location': 'Reykjavik, Iceland',
      'category': 'Nature',
      'rating': 4.7,
      'reviewCount': 892,
      'price': '\$220',
      'duration': '8 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1531366936337-7c912a4589a7?w=800',
      'tags': ['Nature', 'Photography', 'Aurora'],
      'description':
          'Capture the magical Northern Lights with professional photography guidance.',
    },
    {
      'id': 'exp_010',
      'title': 'Machu Picchu Sunrise Hike',
      'location': 'Cusco, Peru',
      'category': 'Adventure',
      'rating': 4.6,
      'reviewCount': 1134,
      'price': '\$160',
      'duration': '12 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1587595431973-160d0d94add1?w=800',
      'tags': ['Adventure', 'Hiking', 'Historical'],
      'description':
          'Witness the sunrise over the ancient Incan citadel of Machu Picchu.',
    },
    {
      'id': 'exp_011',
      'title': 'Flamenco Dance Workshop',
      'location': 'Seville, Spain',
      'category': 'Cultural',
      'rating': 4.5,
      'reviewCount': 378,
      'price': '\$70',
      'duration': '2 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1504609813442-a8924e83f76e?w=800',
      'tags': ['Cultural', 'Dance', 'Traditional'],
      'description':
          'Learn the passionate art of flamenco dancing from professional instructors.',
    },
    {
      'id': 'exp_012',
      'title': 'Wine Tasting in Tuscany',
      'location': 'Florence, Italy',
      'category': 'Food & Drink',
      'rating': 4.8,
      'reviewCount': 967,
      'price': '\$130',
      'duration': '5 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1506377247377-2a5b3b417ebb?w=800',
      'tags': ['Wine', 'Tuscany', 'Countryside'],
      'description':
          'Discover the finest wines of Tuscany with visits to family-owned vineyards.',
    },
    {
      'id': 'exp_013',
      'title': 'Great Wall Hiking Adventure',
      'location': 'Beijing, China',
      'category': 'Historical',
      'rating': 4.4,
      'reviewCount': 1567,
      'price': '\$90',
      'duration': '6 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1508804185872-d7badad00f7d?w=800',
      'tags': ['Historical', 'Hiking', 'UNESCO'],
      'description':
          'Hike along the less crowded sections of the Great Wall of China.',
    },
    {
      'id': 'exp_014',
      'title': 'Street Art Walking Tour',
      'location': 'Berlin, Germany',
      'category': 'Art & Museums',
      'rating': 4.6,
      'reviewCount': 534,
      'price': '\$45',
      'duration': '3 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=800',
      'tags': ['Art', 'Street Art', 'Walking'],
      'description':
          'Explore Berlin\'s vibrant street art scene and learn about its cultural significance.',
    },
    {
      'id': 'exp_015',
      'title': 'Cocktail Making Masterclass',
      'location': 'New York, USA',
      'category': 'Nightlife',
      'rating': 4.7,
      'reviewCount': 723,
      'price': '\$85',
      'duration': '2 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1551538827-9c037cb4f32a?w=800',
      'tags': ['Cocktails', 'Mixology', 'NYC'],
      'description':
          'Master the art of cocktail making with NYC\'s top bartenders.',
    },
    {
      'id': 'exp_016',
      'title': 'Thermal Springs Relaxation',
      'location': 'Blue Lagoon, Iceland',
      'category': 'Wellness',
      'rating': 4.5,
      'reviewCount': 2145,
      'price': '\$110',
      'duration': '4 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800',
      'tags': ['Wellness', 'Thermal', 'Relaxation'],
      'description':
          'Relax in the mineral-rich geothermal waters of Iceland\'s Blue Lagoon.',
    },
    {
      'id': 'exp_017',
      'title': 'Safari Wildlife Experience',
      'location': 'Serengeti, Tanzania',
      'category': 'Nature',
      'rating': 4.9,
      'reviewCount': 834,
      'price': '\$350',
      'duration': '8 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1516426122078-c23e76319801?w=800',
      'tags': ['Nature', 'Wildlife', 'Safari'],
      'description':
          'Witness the incredible wildlife of the Serengeti on a guided safari adventure.',
    },
    {
      'id': 'exp_018',
      'title': 'Bungee Jumping Extreme',
      'location': 'Queenstown, New Zealand',
      'category': 'Adventure',
      'rating': 4.8,
      'reviewCount': 1456,
      'price': '\$195',
      'duration': '2 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1544197150-b99a580bb7a8?w=800',
      'tags': ['Adventure', 'Extreme', 'Adrenaline'],
      'description':
          'Take the ultimate leap of faith with the world\'s first commercial bungee jump.',
    },
    {
      'id': 'exp_019',
      'title': 'Samurai Sword Experience',
      'location': 'Tokyo, Japan',
      'category': 'Cultural',
      'rating': 4.6,
      'reviewCount': 456,
      'price': '\$120',
      'duration': '3 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1528360983277-13d401cdc186?w=800',
      'tags': ['Cultural', 'Samurai', 'Traditional'],
      'description':
          'Learn the ancient art of the samurai and forge your own katana blade.',
    },
    {
      'id': 'exp_020',
      'title': 'Michelin Star Dining',
      'location': 'Copenhagen, Denmark',
      'category': 'Food & Drink',
      'rating': 4.9,
      'reviewCount': 234,
      'price': '\$280',
      'duration': '3 hours',
      'imageUrl':
          'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=800',
      'tags': ['Fine Dining', 'Michelin', 'Nordic'],
      'description':
          'Experience innovative Nordic cuisine at a world-renowned Michelin-starred restaurant.',
    },
  ];
}
