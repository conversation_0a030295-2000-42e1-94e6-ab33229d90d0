import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/widgets/payment/credit_card_form.dart';

void main() {
  group('CreditCardForm Widget Tests', () {
    testWidgets('renders form fields', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CreditCardForm(
              onSubmit: (
                {required String cardNumber,
                required String cardHolderName,
                required int expiryMonth,
                required int expiryYear,
                required String cvv,
                required bool setAsDefault}) {},
              isLoading: false,
            ),
          ),
        ),
      );

      // Verify that all form fields are displayed
      expect(find.text('Card Number'), findsOneWidget);
      expect(find.text('Card Holder Name'), findsOneWidget);
      expect(find.text('Expiry Date'), findsOneWidget);
      expect(find.text('CVV'), findsOneWidget);
      expect(find.text('Set as default payment method'), findsOneWidget);
      expect(find.text('Add Card'), findsOneWidget);
    });

    testWidgets('validates card number', (WidgetTester tester) async {
      // Variables to track form submission
      bool formSubmitted = false;
      String submittedCardNumber = '';

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CreditCardForm(
              onSubmit: (
                {required String cardNumber,
                required String cardHolderName,
                required int expiryMonth,
                required int expiryYear,
                required String cvv,
                required bool setAsDefault}) {
                formSubmitted = true;
                submittedCardNumber = cardNumber;
              },
              isLoading: false,
            ),
          ),
        ),
      );

      // Fill in the form with an invalid card number
      await tester.enterText(
          find.widgetWithText(TextFormField, 'Card Number'), '1234 5678 9012');
      await tester.enterText(
          find.widgetWithText(TextFormField, 'Card Holder Name'), 'John Doe');
      await tester.enterText(
          find.widgetWithText(TextFormField, 'Expiry Date'), '12/25');
      await tester.enterText(find.widgetWithText(TextFormField, 'CVV'), '123');

      // Submit the form
      await tester.tap(find.text('Add Card'));
      await tester.pump();

      // Verify that the form was not submitted due to validation error
      expect(formSubmitted, isFalse);

      // Fill in the form with a valid card number (Visa test card)
      await tester.enterText(find.widgetWithText(TextFormField, 'Card Number'),
          '4242 4242 4242 4242');
      
      // Submit the form
      await tester.tap(find.text('Add Card'));
      await tester.pump();

      // Verify that the form was submitted with the correct card number
      expect(formSubmitted, isTrue);
      expect(submittedCardNumber, '****************');
    });

    testWidgets('validates expiry date', (WidgetTester tester) async {
      // Variables to track form submission
      bool formSubmitted = false;
      int submittedExpiryMonth = 0;
      int submittedExpiryYear = 0;

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CreditCardForm(
              onSubmit: (
                {required String cardNumber,
                required String cardHolderName,
                required int expiryMonth,
                required int expiryYear,
                required String cvv,
                required bool setAsDefault}) {
                formSubmitted = true;
                submittedExpiryMonth = expiryMonth;
                submittedExpiryYear = expiryYear;
              },
              isLoading: false,
            ),
          ),
        ),
      );

      // Fill in the form with valid data except for an expired date
      await tester.enterText(find.widgetWithText(TextFormField, 'Card Number'),
          '4242 4242 4242 4242');
      await tester.enterText(
          find.widgetWithText(TextFormField, 'Card Holder Name'), 'John Doe');
      await tester.enterText(
          find.widgetWithText(TextFormField, 'Expiry Date'), '01/20'); // Expired
      await tester.enterText(find.widgetWithText(TextFormField, 'CVV'), '123');

      // Submit the form
      await tester.tap(find.text('Add Card'));
      await tester.pump();

      // Verify that the form was not submitted due to validation error
      expect(formSubmitted, isFalse);

      // Fill in the form with a valid future date
      final now = DateTime.now();
      final futureYear = now.year + 2;
      final futureYearShort = futureYear % 100; // Last 2 digits
      
      await tester.enterText(find.widgetWithText(TextFormField, 'Expiry Date'),
          '12/$futureYearShort');

      // Submit the form
      await tester.tap(find.text('Add Card'));
      await tester.pump();

      // Verify that the form was submitted with the correct expiry date
      expect(formSubmitted, isTrue);
      expect(submittedExpiryMonth, 12);
      expect(submittedExpiryYear, 2000 + futureYearShort);
    });

    testWidgets('shows loading indicator when isLoading is true',
        (WidgetTester tester) async {
      // Build the widget with isLoading set to true
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CreditCardForm(
              onSubmit: (
                {required String cardNumber,
                required String cardHolderName,
                required int expiryMonth,
                required int expiryYear,
                required String cvv,
                required bool setAsDefault}) {},
              isLoading: true,
            ),
          ),
        ),
      );

      // Verify that the loading indicator is displayed
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Add Card'), findsNothing);
    });
  });
}
