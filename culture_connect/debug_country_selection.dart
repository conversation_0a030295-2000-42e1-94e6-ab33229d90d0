import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:culture_connect/config/environment_config.dart';
import 'package:culture_connect/services/travel/visa/travel_buddy_api_service.dart';

/// Debug script to test country selection functionality
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Load environment variables
    await dotenv.load(fileName: ".env");
    print('✅ Environment variables loaded');

    // Validate environment
    EnvironmentConfig.validateEnvironment();
    print('✅ Environment validation passed');

    // Test API key access
    final apiKey = EnvironmentConfig.rapidApiKey;
    print('✅ RapidAPI key loaded: ${apiKey.substring(0, 8)}...');

    // Test TravelBuddyApiService
    final service = TravelBuddyApiService();
    print('✅ TravelBuddyApiService initialized');

    // Test country loading
    print('🔄 Loading countries...');
    final countries = await service.getSupportedCountries();
    print('✅ Loaded ${countries.length} countries');

    if (countries.isNotEmpty) {
      print('📍 First 5 countries:');
      for (int i = 0; i < 5 && i < countries.length; i++) {
        final country = countries[i];
        print('   ${country.flag} ${country.name} (${country.code})');
      }
    }

    print('\n🎉 Country selection debug completed successfully!');
    print('Expected behavior:');
    print('1. Navigate to VISA Requirements Screen via Quick Services → Visa');
    print('2. Tap on country input fields');
    print('3. See dropdown with ${countries.length} countries');
    print('4. Select countries and see button enable');
    print('\nDebug Console Messages to Watch For:');
    print('✅ CountrySelectorWidget: Initializing and loading countries...');
    print('✅ CountrySelectorWidget: Loaded ${countries.length} countries');
    print(
        '✅ CountrySelectorWidget: Field tapped. Countries: ${countries.length}');
    print('✅ CountrySelectorWidget: Country selected: [Country Name] ([Code])');
    print('✅ VisaRequirementsScreen: Passport country selected: [Code]');
    print('✅ VisaRequirementsScreen: Button state debug: Can search: true');
  } catch (e, stackTrace) {
    print('❌ Debug test failed: $e');
    print('Stack trace: $stackTrace');
  }
}
