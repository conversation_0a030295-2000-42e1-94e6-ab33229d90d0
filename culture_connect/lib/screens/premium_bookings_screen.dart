import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/premium_booking.dart';
import 'package:culture_connect/data/premium_mock_data.dart';

import 'package:culture_connect/widgets/premium_booking_card.dart';
import 'package:culture_connect/design_system/premium_design_tokens.dart';
import 'package:culture_connect/screens/booking/adaptive_booking_details_screen.dart';

/// Premium booking service provider
final premiumBookingServiceProvider =
    Provider<PremiumBookingService>((ref) => PremiumBookingService());

/// Provider for booking statistics
final premiumBookingStatsProvider =
    FutureProvider<PremiumBookingStats>((ref) async {
  final service = ref.watch(premiumBookingServiceProvider);
  return service.getBookingStats();
});

/// Provider for all bookings
final premiumAllBookingsProvider =
    FutureProvider<List<PremiumBooking>>((ref) async {
  final service = ref.watch(premiumBookingServiceProvider);
  return service.getAllBookings();
});

/// Provider for filtered bookings
final premiumFilteredBookingsProvider =
    Provider.family<AsyncValue<List<PremiumBooking>>, String>((ref, filter) {
  final bookingsAsync = ref.watch(premiumAllBookingsProvider);

  return bookingsAsync.when(
    data: (bookings) {
      List<PremiumBooking> filtered;
      switch (filter) {
        case 'upcoming':
          filtered = bookings
              .where((b) =>
                  b.status == PremiumBookingStatus.upcoming ||
                  b.status == PremiumBookingStatus.confirmed ||
                  b.status == PremiumBookingStatus.pending)
              .toList();
          break;
        case 'completed':
          filtered = bookings
              .where((b) => b.status == PremiumBookingStatus.completed)
              .toList();
          break;
        case 'cancelled':
          filtered = bookings
              .where((b) => b.status == PremiumBookingStatus.cancelled)
              .toList();
          break;
        default:
          filtered = bookings;
      }
      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

/// Premium booking screen with stunning visual design
class PremiumBookingsScreen extends ConsumerStatefulWidget {
  const PremiumBookingsScreen({super.key});

  @override
  ConsumerState<PremiumBookingsScreen> createState() =>
      _PremiumBookingsScreenState();
}

class _PremiumBookingsScreenState extends ConsumerState<PremiumBookingsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _searchController;
  late Animation<double> _searchAnimation;
  late ScrollController _scrollController; // Added: For better scroll control

  bool _isRefreshing = false;
  bool _isSearchVisible = false;
  String _searchQuery = '';
  final TextEditingController _searchTextController = TextEditingController();

  @override
  void initState() {
    super.initState();

    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(_handleTabChange);

    _scrollController = ScrollController(); // Initialize scroll controller

    _searchController = AnimationController(
      duration: PremiumDesignTokens.animationMedium,
      vsync: this,
    );

    _searchAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _searchController,
      curve: PremiumDesignTokens.curveEaseOut,
    ));
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _scrollController.dispose(); // Dispose scroll controller
    _searchTextController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) return;
    // Trigger rebuild to update animated indicator position
    setState(() {});
  }

  Future<void> _handleRefresh() async {
    setState(() => _isRefreshing = true);

    // Invalidate providers to trigger refresh
    ref.invalidate(premiumBookingStatsProvider);
    ref.invalidate(premiumAllBookingsProvider);

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1200));

    setState(() => _isRefreshing = false);
  }

  /// Toggle search bar visibility with animation
  void _toggleSearch() {
    setState(() {
      _isSearchVisible = !_isSearchVisible;
    });

    if (_isSearchVisible) {
      _searchController.forward();
    } else {
      _searchController.reverse();
      _searchTextController.clear();
      _searchQuery = '';
    }
  }

  /// Handle search query changes
  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
  }

  /// Filter bookings based on search query
  List<PremiumBooking> _filterBookings(List<PremiumBooking> bookings) {
    if (_searchQuery.isEmpty) return bookings;

    final lowercaseQuery = _searchQuery.toLowerCase();
    return bookings.where((booking) {
      return booking.title.toLowerCase().contains(lowercaseQuery) ||
          booking.subtitle.toLowerCase().contains(lowercaseQuery) ||
          _formatDate(booking.date).toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  /// Header matching the reference design with back button, title, and search icon
  Widget _buildReferenceHeader() {
    return Container(
      height: 100,
      // Transparent background to show gradient
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            // Back button (circular white background)
            Container(
              width: 40,
              height: 40,
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(
                  Icons.arrow_back,
                  color: Colors.black,
                  size: 20,
                ),
                padding: EdgeInsets.zero,
              ),
            ),
            // Title
            Expanded(
              child: Text(
                'Upcoming Appointments',
                textAlign: TextAlign.center,
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            // Search button (circular white background)
            Container(
              width: 40,
              height: 40,
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: _toggleSearch,
                icon: Icon(
                  _isSearchVisible ? Icons.close : Icons.search,
                  color: Colors.black,
                  size: 20,
                ),
                padding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        toolbarHeight: 0, // Hide the AppBar but keep status bar handling
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      ),
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFF0F9FF), // Very light blue at top
              Color(0xFFFAFAFA), // Light gray in middle
              Color(0xFFF8FAFC), // Off-white at bottom
            ],
            stops: [0.0, 0.4, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildReferenceHeader(), // Header with proper safe area handling
              Expanded(
                child: RefreshIndicator(
                  onRefresh: _handleRefresh,
                  color: PremiumDesignTokens.primaryBlue,
                  backgroundColor: PremiumDesignTokens.neutralWhite,
                  child: ListView(
                    controller: _scrollController,
                    physics: const BouncingScrollPhysics(),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    children: [
                      // Animated search bar
                      AnimatedBuilder(
                        animation: _searchAnimation,
                        builder: (context, child) {
                          return SizeTransition(
                            sizeFactor: _searchAnimation,
                            child: _buildSearchBar(),
                          );
                        },
                      ),

                      // Statistics summary card
                      _buildStatisticsCard(),
                      const SizedBox(height: 16),

                      // Bookings list
                      _buildReferenceBookingsList(), // New simplified list matching reference
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Simplified bookings list matching the reference design
  Widget _buildReferenceBookingsList() {
    return Consumer(
      builder: (context, ref, child) {
        final bookingsAsync =
            ref.watch(premiumFilteredBookingsProvider('upcoming'));

        return bookingsAsync.when(
          data: (bookings) {
            // Filter for upcoming bookings to match "Upcoming Appointments"
            final upcomingBookings = bookings
                .where((booking) =>
                    booking.status == PremiumBookingStatus.upcoming ||
                    booking.status == PremiumBookingStatus.confirmed ||
                    booking.status == PremiumBookingStatus.pending)
                .toList();

            // Apply search filtering
            final filteredBookings = _filterBookings(upcomingBookings);

            if (filteredBookings.isEmpty) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Text(
                    _searchQuery.isNotEmpty
                        ? 'No appointments found for "$_searchQuery"'
                        : 'No upcoming appointments',
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              );
            }

            return Column(
              children: filteredBookings.map((booking) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: _buildReferenceBookingCard(booking),
                );
              }).toList(),
            );
          },
          loading: () => const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: CircularProgressIndicator(),
            ),
          ),
          error: (error, stack) => Center(
            child: Padding(
              padding: const EdgeInsets.all(32),
              child: Text(
                'Error loading appointments: $error',
                style: const TextStyle(color: Colors.red),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Build booking card matching the reference design exactly
  Widget _buildReferenceBookingCard(PremiumBooking booking) {
    return GestureDetector(
      onTap: () => _handleBookingTap(booking),
      child: Container(
        padding:
            const EdgeInsets.all(20), // Increased padding to match reference
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius:
              BorderRadius.circular(20), // Increased radius for softer look
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.06), // Softer shadow
              blurRadius: 12, // More blur for elegant shadow
              offset: const Offset(0, 4), // Slightly more offset
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          children: [
            // Top row with profile image, name/specialty, and status badge
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Profile image (circular) - matching reference size
                Container(
                  width: 64, // Slightly larger to match reference
                  height: 64,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    image: DecorationImage(
                      image: NetworkImage(booking.imageUrl),
                      fit: BoxFit.cover,
                    ),
                    // Add subtle border for professional look
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.1),
                      width: 1,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                // Name and specialty section
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Doctor name - larger and bolder to match reference
                      Text(
                        booking.title,
                        style: const TextStyle(
                          fontSize: 20, // Increased from 18
                          fontWeight: FontWeight.w700, // Bolder weight
                          color: Color(0xFF1A1A1A), // Slightly warmer black
                          letterSpacing: -0.2, // Tighter letter spacing
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      // Specialty/subtitle - matching reference color and size
                      Text(
                        booking.subtitle,
                        style: const TextStyle(
                          fontSize: 15, // Slightly larger
                          fontWeight: FontWeight.w400,
                          color: Color(0xFF6B7280), // More refined gray
                          letterSpacing: -0.1,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                // Status badge - positioned in top right
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 14, // Slightly wider
                    vertical: 6, // Slightly taller
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusBadgeColor(booking.status),
                    borderRadius: BorderRadius.circular(16), // More rounded
                  ),
                  child: Text(
                    _getStatusText(booking.status),
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600, // Slightly bolder
                      color: Colors.white,
                      letterSpacing: 0.2,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(
                height: 20), // Space between top section and date/time

            // Bottom row with date and time - matching reference layout
            Row(
              children: [
                // Date section with icon
                Expanded(
                  flex: 1,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(
                          0xFFF1F5F9), // Slightly darker gray for better contrast
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.calendar_today_outlined,
                          size: 16,
                          color: const Color(0xFF6B7280), // Matching gray
                        ),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            _formatDate(booking.date),
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF374151), // Darker gray for text
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // Time section with icon
                Expanded(
                  flex: 1,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(
                          0xFFF1F5F9), // Slightly darker gray for better contrast
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.access_time_outlined,
                          size: 16,
                          color: const Color(0xFF6B7280), // Matching gray
                        ),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            _formatTime(booking.date),
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF374151), // Darker gray for text
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Get status badge color matching reference design
  Color _getStatusBadgeColor(PremiumBookingStatus status) {
    switch (status) {
      case PremiumBookingStatus.confirmed:
      case PremiumBookingStatus.upcoming:
        return const Color(0xFF10B981); // Emerald green for confirmed
      case PremiumBookingStatus.pending:
        return const Color(
            0xFFF59E0B); // Amber/orange for pending (matches reference)
      case PremiumBookingStatus.cancelled:
        return const Color(0xFFEF4444); // Red for cancelled
      case PremiumBookingStatus.completed:
        return const Color(0xFF3B82F6); // Blue for completed
    }
  }

  /// Get status text matching reference design
  String _getStatusText(PremiumBookingStatus status) {
    switch (status) {
      case PremiumBookingStatus.confirmed:
      case PremiumBookingStatus.upcoming:
        return 'Confirmed';
      case PremiumBookingStatus.pending:
        return 'Pending';
      case PremiumBookingStatus.cancelled:
        return 'Cancelled';
      case PremiumBookingStatus.completed:
        return 'Completed';
    }
  }

  /// Format date to match reference design (e.g., "22 July, 2025")
  String _formatDate(DateTime date) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return '${date.day} ${months[date.month - 1]}, ${date.year}';
  }

  /// Format time to match reference design (e.g., "8:30 - 9:30 pm")
  String _formatTime(DateTime date) {
    final hour = date.hour;
    final minute = date.minute;
    final period = hour >= 12 ? 'pm' : 'am';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    final endHour = displayHour + 1;

    return '${displayHour}:${minute.toString().padLeft(2, '0')} - ${endHour}:${minute.toString().padLeft(2, '0')} $period';
  }

  /// Build animated search bar matching booking card design
  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white, // Pure white for better contrast against gradient
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.15),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(
            Icons.search,
            size: 20,
            color: Color(0xFF6B7280),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              controller: _searchTextController,
              onChanged: _onSearchChanged,
              decoration: const InputDecoration(
                hintText: 'Search appointments...',
                hintStyle: TextStyle(
                  color: Color(0xFF9CA3AF),
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
              style: const TextStyle(
                color: Color(0xFF374151),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (_searchQuery.isNotEmpty)
            GestureDetector(
              onTap: () {
                _searchTextController.clear();
                _onSearchChanged('');
              },
              child: const Icon(
                Icons.clear,
                size: 20,
                color: Color(0xFF6B7280),
              ),
            ),
        ],
      ),
    );
  }

  /// Build statistics summary card matching booking card design
  Widget _buildStatisticsCard() {
    return Consumer(
      builder: (context, ref, child) {
        final allBookingsAsync = ref.watch(premiumAllBookingsProvider);

        return allBookingsAsync.when(
          data: (bookings) {
            // Calculate statistics
            final totalBookings = bookings.length;
            final confirmedBookings = bookings
                .where((b) =>
                    b.status == PremiumBookingStatus.confirmed ||
                    b.status == PremiumBookingStatus.upcoming)
                .length;
            final pendingBookings = bookings
                .where((b) => b.status == PremiumBookingStatus.pending)
                .length;
            final cancelledBookings = bookings
                .where((b) => b.status == PremiumBookingStatus.cancelled)
                .length;

            return Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.06),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Total bookings
                  Expanded(
                    child: _buildStatItem(
                      Icons.calendar_month_outlined,
                      totalBookings.toString(),
                      'Total',
                      const Color(0xFF3B82F6), // Blue
                    ),
                  ),
                  Container(
                    width: 1,
                    height: 40,
                    color: const Color(0xFFF1F5F9),
                  ),
                  // Confirmed bookings
                  Expanded(
                    child: _buildStatItem(
                      Icons.check_circle_outline,
                      confirmedBookings.toString(),
                      'Confirmed',
                      const Color(0xFF10B981), // Emerald green
                    ),
                  ),
                  Container(
                    width: 1,
                    height: 40,
                    color: const Color(0xFFF1F5F9),
                  ),
                  // Pending bookings
                  Expanded(
                    child: _buildStatItem(
                      Icons.schedule_outlined,
                      pendingBookings.toString(),
                      'Pending',
                      const Color(0xFFF59E0B), // Amber
                    ),
                  ),
                  Container(
                    width: 1,
                    height: 40,
                    color: const Color(0xFFF1F5F9),
                  ),
                  // Cancelled bookings
                  Expanded(
                    child: _buildStatItem(
                      Icons.cancel_outlined,
                      cancelledBookings.toString(),
                      'Cancelled',
                      const Color(0xFFEF4444), // Red
                    ),
                  ),
                ],
              ),
            );
          },
          loading: () => Container(
            height: 80,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.06),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: const Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF3B82F6)),
              ),
            ),
          ),
          error: (error, stack) => const SizedBox.shrink(),
        );
      },
    );
  }

  /// Build individual statistic item
  Widget _buildStatItem(
      IconData icon, String value, String label, Color color) {
    return Column(
      children: [
        Icon(
          icon,
          size: 24,
          color: color,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w700,
            color: Color(0xFF1A1A1A),
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Color(0xFF6B7280),
          ),
        ),
      ],
    );
  }

  Widget _buildBookingListForTab(String filter) {
    return Consumer(
      builder: (context, ref, child) {
        final filteredBookingsAsync =
            ref.watch(premiumFilteredBookingsProvider(filter));

        return filteredBookingsAsync.when(
          data: (bookings) {
            if (bookings.isEmpty) {
              return _buildEmptyState(filter);
            }

            return ListView.builder(
              padding: const EdgeInsets.fromLTRB(
                PremiumDesignTokens.spacing12,
                PremiumDesignTokens.spacing8,
                PremiumDesignTokens.spacing12,
                PremiumDesignTokens.spacing24,
              ),
              itemCount: bookings.length,
              physics: const BouncingScrollPhysics(),
              itemBuilder: (context, index) {
                final booking = bookings[index];

                return TweenAnimationBuilder<double>(
                  duration: Duration(milliseconds: 200 + (index * 50)),
                  tween: Tween<double>(begin: 0.0, end: 1.0),
                  curve: Curves.easeOutCubic,
                  builder: (context, animationValue, child) {
                    return Transform.translate(
                      offset: Offset(0, 30 * (1 - animationValue)),
                      child: Opacity(
                        opacity: animationValue.clamp(0.0, 1.0),
                        child: Container(
                          margin: const EdgeInsets.only(
                              bottom: PremiumDesignTokens.spacing12),
                          child: PremiumBookingCard(
                            booking: booking,
                            width: double.infinity,
                            height: 200,
                            onTap: () => _handleBookingTap(booking),
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
            );
          },
          loading: () => const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                  PremiumDesignTokens.primaryBlue),
              strokeWidth: 3,
            ),
          ),
          error: (error, stack) => _buildErrorState(error.toString()),
        );
      },
    );
  }

  Widget _buildEmptyState(String filter) {
    String title;
    String message;
    IconData icon;

    switch (filter) {
      case 'upcoming':
        title = 'No Upcoming Adventures';
        message =
            'Your next amazing journey is just a booking away. Start exploring!';
        icon = Icons.schedule_rounded;
        break;
      case 'completed':
        title = 'No Completed Journeys';
        message =
            'Your travel memories will appear here once you complete your adventures.';
        icon = Icons.done_all_rounded;
        break;
      case 'cancelled':
        title = 'No Cancelled Bookings';
        message = 'Great! You haven\'t cancelled any adventures recently.';
        icon = Icons.cancel_rounded;
        break;
      default:
        title = 'No Bookings Found';
        message = 'Try adjusting your search or explore new destinations.';
        icon = Icons.search_off_rounded;
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(PremiumDesignTokens.spacing32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    PremiumDesignTokens.primaryBlue.withValues(alpha: 0.2),
                    PremiumDesignTokens.accentTeal.withValues(alpha: 0.1),
                  ],
                ),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 48,
                color: PremiumDesignTokens.primaryBlue,
              ),
            ),
            const SizedBox(height: PremiumDesignTokens.spacing24),
            Text(
              title,
              style: PremiumDesignTokens.headlineLarge.copyWith(
                color: PremiumDesignTokens.neutralGray900,
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: PremiumDesignTokens.spacing12),
            Text(
              message,
              style: PremiumDesignTokens.bodyLarge.copyWith(
                color: PremiumDesignTokens.neutralGray600,
                height: 1.6,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(PremiumDesignTokens.spacing32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: PremiumDesignTokens.statusError.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.error_outline_rounded,
                size: 48,
                color: PremiumDesignTokens.statusError,
              ),
            ),
            const SizedBox(height: PremiumDesignTokens.spacing24),
            Text(
              'Something went wrong',
              style: PremiumDesignTokens.headlineLarge.copyWith(
                color: PremiumDesignTokens.statusError,
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: PremiumDesignTokens.spacing12),
            Text(
              'Unable to load your bookings. Please try again.',
              style: PremiumDesignTokens.bodyLarge.copyWith(
                color: PremiumDesignTokens.neutralGray600,
                height: 1.6,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: PremiumDesignTokens.spacing24),
            ElevatedButton(
              onPressed: _handleRefresh,
              style: ElevatedButton.styleFrom(
                backgroundColor: PremiumDesignTokens.primaryBlue,
                padding: const EdgeInsets.symmetric(
                  horizontal: PremiumDesignTokens.spacing24,
                  vertical: PremiumDesignTokens.spacing16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius:
                      BorderRadius.circular(PremiumDesignTokens.radiusMedium),
                ),
                elevation: 4,
              ),
              child: Text(
                'Try Again',
                style: PremiumDesignTokens.labelLarge.copyWith(
                  color: PremiumDesignTokens.neutralWhite,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Handler methods
  void _handleBookingTap(PremiumBooking booking) {
    // Navigate to adaptive booking details screen
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AdaptiveBookingDetailsScreen(booking: booking),
      ),
    );
  }
}

/// Premium booking service for data management
class PremiumBookingService {
  Future<List<PremiumBooking>> getAllBookings() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));
    return PremiumMockData.getAllBookings();
  }

  Future<PremiumBookingStats> getBookingStats() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 600));
    return PremiumMockData.getBookingStats();
  }

  Future<List<PremiumBooking>> getBookingsByStatus(
      PremiumBookingStatus status) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return PremiumMockData.getBookingsByStatus(status);
  }

  Future<PremiumBooking?> getBookingById(String id) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return PremiumMockData.getBookingById(id);
  }
}
