import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:culture_connect/theme/kaia_design_tokens.dart';
import 'package:culture_connect/models/guide.dart';

/// Comprehensive guide details screen with complete profile information
/// Follows CultureConnect design system with premium aesthetics
class GuideDetailsScreen extends StatefulWidget {
  final Guide guide;

  const GuideDetailsScreen({
    super.key,
    required this.guide,
  });

  @override
  State<GuideDetailsScreen> createState() => _GuideDetailsScreenState();
}

class _GuideDetailsScreenState extends State<GuideDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _handleBookingPress() {
    HapticFeedback.lightImpact();
    // TODO: Navigate to booking screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Booking ${widget.guide.name}...'),
        backgroundColor: KaiaDesignTokens.primaryIndigo,
      ),
    );
  }

  void _handleContactPress() {
    HapticFeedback.lightImpact();
    // TODO: Open contact options
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Contacting ${widget.guide.name}...'),
        backgroundColor: KaiaDesignTokens.secondaryCyan,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final guide = widget.guide;
    final isCompany = guide.guideType == GuideType.company;

    return Scaffold(
      backgroundColor: KaiaDesignTokens.neutralWhite,
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // App Bar with Hero Image
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            backgroundColor: KaiaDesignTokens.neutralWhite,
            leading: IconButton(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: KaiaDesignTokens.neutralWhite.withOpacity(0.9),
                  shape: BoxShape.circle,
                  boxShadow: KaiaDesignTokens.shadowSm,
                ),
                child: Icon(
                  Icons.arrow_back_ios,
                  color: KaiaDesignTokens.neutralGray800,
                  size: 18,
                ),
              ),
              onPressed: () {
                HapticFeedback.lightImpact();
                Navigator.pop(context);
              },
            ),
            actions: [
              IconButton(
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: KaiaDesignTokens.neutralWhite.withOpacity(0.9),
                    shape: BoxShape.circle,
                    boxShadow: KaiaDesignTokens.shadowSm,
                  ),
                  child: Icon(
                    Icons.share_outlined,
                    color: KaiaDesignTokens.neutralGray800,
                    size: 18,
                  ),
                ),
                onPressed: () {
                  HapticFeedback.lightImpact();
                  // TODO: Share guide profile
                },
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: BoxDecoration(
                  color: KaiaDesignTokens.neutralGray100,
                ),
                child: guide.profileImageUrl != null
                    ? Image.network(
                        guide.profileImageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: KaiaDesignTokens.neutralGray100,
                            child: Icon(
                              isCompany ? Icons.business : Icons.person,
                              color: KaiaDesignTokens.neutralGray400,
                              size: 80,
                            ),
                          );
                        },
                      )
                    : Container(
                        color: KaiaDesignTokens.neutralGray100,
                        child: Icon(
                          isCompany ? Icons.business : Icons.person,
                          color: KaiaDesignTokens.neutralGray400,
                          size: 80,
                        ),
                      ),
              ),
            ),
            systemOverlayStyle: SystemUiOverlayStyle.light,
          ),

          // Profile Header
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name and Status
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          guide.name,
                          style: TextStyle(
                            color: KaiaDesignTokens.neutralGray800,
                            fontSize: KaiaDesignTokens.fontSize2Xl,
                            fontWeight: KaiaDesignTokens.fontWeightBold,
                          ),
                        ),
                      ),
                      if (isCompany)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: KaiaDesignTokens.spacing8,
                            vertical: KaiaDesignTokens.spacing4,
                          ),
                          decoration: BoxDecoration(
                            color: KaiaDesignTokens.accentEmerald,
                            borderRadius: BorderRadius.circular(
                                KaiaDesignTokens.radiusSm),
                          ),
                          child: Text(
                            'COMPANY',
                            style: TextStyle(
                              color: KaiaDesignTokens.neutralWhite,
                              fontSize: KaiaDesignTokens.fontSizeXs,
                              fontWeight: KaiaDesignTokens.fontWeightBold,
                            ),
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: KaiaDesignTokens.spacing8),

                  // Location and Rating
                  Row(
                    children: [
                      if (guide.location != null) ...[
                        Icon(
                          Icons.location_on_outlined,
                          color: KaiaDesignTokens.neutralGray500,
                          size: 16,
                        ),
                        const SizedBox(width: KaiaDesignTokens.spacing4),
                        Flexible(
                          child: Text(
                            guide.location!,
                            style: TextStyle(
                              color: KaiaDesignTokens.neutralGray600,
                              fontSize: KaiaDesignTokens.fontSizeMd,
                              fontWeight: KaiaDesignTokens.fontWeightRegular,
                            ),
                          ),
                        ),
                        const SizedBox(width: KaiaDesignTokens.spacing16),
                      ],
                      if (guide.rating != null) ...[
                        Icon(
                          Icons.star,
                          color: KaiaDesignTokens.warningAmber,
                          size: 18,
                        ),
                        const SizedBox(width: KaiaDesignTokens.spacing4),
                        Text(
                          guide.rating!.toStringAsFixed(1),
                          style: TextStyle(
                            color: KaiaDesignTokens.neutralGray700,
                            fontSize: KaiaDesignTokens.fontSizeMd,
                            fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                          ),
                        ),
                        if (guide.reviewCount != null) ...[
                          const SizedBox(width: KaiaDesignTokens.spacing4),
                          Text(
                            '(${guide.reviewCount} reviews)',
                            style: TextStyle(
                              color: KaiaDesignTokens.neutralGray500,
                              fontSize: KaiaDesignTokens.fontSizeMd,
                              fontWeight: KaiaDesignTokens.fontWeightRegular,
                            ),
                          ),
                        ],
                      ],
                    ],
                  ),

                  const SizedBox(height: KaiaDesignTokens.spacing16),

                  // Quick Stats
                  Container(
                    padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
                    decoration: BoxDecoration(
                      color: KaiaDesignTokens.neutralGray50,
                      borderRadius:
                          BorderRadius.circular(KaiaDesignTokens.radiusLg),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatItem(
                          'Experience',
                          '${guide.yearsOfExperience} years',
                          Icons.timeline,
                        ),
                        _buildStatItem(
                          'Tours',
                          '${guide.totalTours}',
                          Icons.tour,
                        ),
                        if (isCompany && guide.teamSize != null)
                          _buildStatItem(
                            'Team',
                            '${guide.teamSize}',
                            Icons.group,
                          )
                        else
                          _buildStatItem(
                            'Languages',
                            '${guide.languages.length}',
                            Icons.language,
                          ),
                      ],
                    ),
                  ),

                  const SizedBox(height: KaiaDesignTokens.spacing24),

                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: ElevatedButton(
                          onPressed: _handleBookingPress,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: KaiaDesignTokens.primaryIndigo,
                            foregroundColor: KaiaDesignTokens.neutralWhite,
                            padding: const EdgeInsets.symmetric(
                              vertical: KaiaDesignTokens.spacing16,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                  KaiaDesignTokens.radiusLg),
                            ),
                            elevation: 0,
                          ),
                          child: Text(
                            isCompany ? 'Get Quote' : 'Book Now',
                            style: TextStyle(
                              fontSize: KaiaDesignTokens.fontSizeMd,
                              fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: KaiaDesignTokens.spacing12),
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _handleContactPress,
                          style: OutlinedButton.styleFrom(
                            foregroundColor: KaiaDesignTokens.primaryIndigo,
                            side: BorderSide(
                              color: KaiaDesignTokens.primaryIndigo,
                              width: 1.5,
                            ),
                            padding: const EdgeInsets.symmetric(
                              vertical: KaiaDesignTokens.spacing16,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                  KaiaDesignTokens.radiusLg),
                            ),
                          ),
                          child: Text(
                            'Contact',
                            style: TextStyle(
                              fontSize: KaiaDesignTokens.fontSizeMd,
                              fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Tab Bar
          SliverPersistentHeader(
            pinned: true,
            delegate: _StickyTabBarDelegate(
              TabBar(
                controller: _tabController,
                labelColor: KaiaDesignTokens.primaryIndigo,
                unselectedLabelColor: KaiaDesignTokens.neutralGray500,
                indicatorColor: KaiaDesignTokens.primaryIndigo,
                indicatorWeight: 3,
                labelStyle: TextStyle(
                  fontSize: KaiaDesignTokens.fontSizeSm,
                  fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                ),
                unselectedLabelStyle: TextStyle(
                  fontSize: KaiaDesignTokens.fontSizeSm,
                  fontWeight: KaiaDesignTokens.fontWeightRegular,
                ),
                tabs: [
                  const Tab(text: 'About'),
                  const Tab(text: 'Services'),
                  const Tab(text: 'Reviews'),
                  Tab(text: isCompany ? 'Packages' : 'Gallery'),
                ],
              ),
            ),
          ),

          // Tab Content
          SliverFillRemaining(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAboutTab(),
                _buildServicesTab(),
                _buildReviewsTab(),
                isCompany ? _buildPackagesTab() : _buildGalleryTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: KaiaDesignTokens.primaryIndigo,
          size: 24,
        ),
        const SizedBox(height: KaiaDesignTokens.spacing4),
        Text(
          value,
          style: TextStyle(
            color: KaiaDesignTokens.neutralGray800,
            fontSize: KaiaDesignTokens.fontSizeLg,
            fontWeight: KaiaDesignTokens.fontWeightBold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: KaiaDesignTokens.neutralGray500,
            fontSize: KaiaDesignTokens.fontSizeXs,
            fontWeight: KaiaDesignTokens.fontWeightRegular,
          ),
        ),
      ],
    );
  }

  Widget _buildAboutTab() {
    final guide = widget.guide;
    final isCompany = guide.guideType == GuideType.company;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Bio Section
          if (guide.bio != null) ...[
            Text(
              'About',
              style: TextStyle(
                color: KaiaDesignTokens.neutralGray800,
                fontSize: KaiaDesignTokens.fontSizeLg,
                fontWeight: KaiaDesignTokens.fontWeightSemiBold,
              ),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing8),
            Text(
              guide.bio!,
              style: TextStyle(
                color: KaiaDesignTokens.neutralGray600,
                fontSize: KaiaDesignTokens.fontSizeMd,
                fontWeight: KaiaDesignTokens.fontWeightRegular,
                height: 1.5,
              ),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing24),
          ],

          // Languages Section
          if (guide.languages.isNotEmpty) ...[
            Text(
              'Languages',
              style: TextStyle(
                color: KaiaDesignTokens.neutralGray800,
                fontSize: KaiaDesignTokens.fontSizeLg,
                fontWeight: KaiaDesignTokens.fontWeightSemiBold,
              ),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing8),
            Wrap(
              spacing: KaiaDesignTokens.spacing8,
              runSpacing: KaiaDesignTokens.spacing6,
              children: guide.languages.map((language) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: KaiaDesignTokens.spacing12,
                    vertical: KaiaDesignTokens.spacing6,
                  ),
                  decoration: BoxDecoration(
                    color:
                        KaiaDesignTokens.primaryIndigo.withValues(alpha: 0.1),
                    borderRadius:
                        BorderRadius.circular(KaiaDesignTokens.radiusSm),
                  ),
                  child: Text(
                    language,
                    style: TextStyle(
                      color: KaiaDesignTokens.primaryIndigo,
                      fontSize: KaiaDesignTokens.fontSizeSm,
                      fontWeight: KaiaDesignTokens.fontWeightMedium,
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing24),
          ],

          // Certifications Section
          if (guide.certifications.isNotEmpty) ...[
            Text(
              'Certifications',
              style: TextStyle(
                color: KaiaDesignTokens.neutralGray800,
                fontSize: KaiaDesignTokens.fontSizeLg,
                fontWeight: KaiaDesignTokens.fontWeightSemiBold,
              ),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing12),
            ...guide.certifications.map((cert) {
              return Container(
                margin:
                    const EdgeInsets.only(bottom: KaiaDesignTokens.spacing8),
                padding: const EdgeInsets.all(KaiaDesignTokens.spacing12),
                decoration: BoxDecoration(
                  color: KaiaDesignTokens.successGreen.withValues(alpha: 0.1),
                  borderRadius:
                      BorderRadius.circular(KaiaDesignTokens.radiusSm),
                  border: Border.all(
                    color: KaiaDesignTokens.successGreen.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.verified,
                      color: KaiaDesignTokens.successGreen,
                      size: 20,
                    ),
                    const SizedBox(width: KaiaDesignTokens.spacing8),
                    Expanded(
                      child: Text(
                        cert,
                        style: TextStyle(
                          color: KaiaDesignTokens.neutralGray700,
                          fontSize: KaiaDesignTokens.fontSizeSm,
                          fontWeight: KaiaDesignTokens.fontWeightMedium,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
            const SizedBox(height: KaiaDesignTokens.spacing24),
          ],

          // Achievements Section
          if (guide.achievements.isNotEmpty) ...[
            Text(
              'Achievements',
              style: TextStyle(
                color: KaiaDesignTokens.neutralGray800,
                fontSize: KaiaDesignTokens.fontSizeLg,
                fontWeight: KaiaDesignTokens.fontWeightSemiBold,
              ),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing12),
            ...guide.achievements.map((achievement) {
              return Container(
                margin:
                    const EdgeInsets.only(bottom: KaiaDesignTokens.spacing8),
                padding: const EdgeInsets.all(KaiaDesignTokens.spacing12),
                decoration: BoxDecoration(
                  color: KaiaDesignTokens.warningAmber.withValues(alpha: 0.1),
                  borderRadius:
                      BorderRadius.circular(KaiaDesignTokens.radiusSm),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.emoji_events,
                      color: KaiaDesignTokens.warningAmber,
                      size: 20,
                    ),
                    const SizedBox(width: KaiaDesignTokens.spacing8),
                    Expanded(
                      child: Text(
                        achievement,
                        style: TextStyle(
                          color: KaiaDesignTokens.neutralGray700,
                          fontSize: KaiaDesignTokens.fontSizeSm,
                          fontWeight: KaiaDesignTokens.fontWeightMedium,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ],
      ),
    );
  }

  Widget _buildServicesTab() {
    final guide = widget.guide;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Specialties Section
          if (guide.specialties.isNotEmpty) ...[
            Text(
              'Specialties',
              style: TextStyle(
                color: KaiaDesignTokens.neutralGray800,
                fontSize: KaiaDesignTokens.fontSizeLg,
                fontWeight: KaiaDesignTokens.fontWeightSemiBold,
              ),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing12),
            Wrap(
              spacing: KaiaDesignTokens.spacing8,
              runSpacing: KaiaDesignTokens.spacing8,
              children: guide.specialties.map((specialty) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: KaiaDesignTokens.spacing12,
                    vertical: KaiaDesignTokens.spacing8,
                  ),
                  decoration: BoxDecoration(
                    color:
                        KaiaDesignTokens.secondaryCyan.withValues(alpha: 0.1),
                    borderRadius:
                        BorderRadius.circular(KaiaDesignTokens.radiusSm),
                  ),
                  child: Text(
                    specialty,
                    style: TextStyle(
                      color: KaiaDesignTokens.secondaryCyan,
                      fontSize: KaiaDesignTokens.fontSizeSm,
                      fontWeight: KaiaDesignTokens.fontWeightMedium,
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing24),
          ],

          // Service Highlights Section
          if (guide.serviceHighlights.isNotEmpty) ...[
            Text(
              'Service Highlights',
              style: TextStyle(
                color: KaiaDesignTokens.neutralGray800,
                fontSize: KaiaDesignTokens.fontSizeLg,
                fontWeight: KaiaDesignTokens.fontWeightSemiBold,
              ),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing12),
            ...guide.serviceHighlights.map((service) {
              return Container(
                margin:
                    const EdgeInsets.only(bottom: KaiaDesignTokens.spacing8),
                padding: const EdgeInsets.all(KaiaDesignTokens.spacing12),
                decoration: BoxDecoration(
                  color: KaiaDesignTokens.neutralGray50,
                  borderRadius:
                      BorderRadius.circular(KaiaDesignTokens.radiusSm),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: KaiaDesignTokens.successGreen,
                      size: 20,
                    ),
                    const SizedBox(width: KaiaDesignTokens.spacing8),
                    Expanded(
                      child: Text(
                        service,
                        style: TextStyle(
                          color: KaiaDesignTokens.neutralGray700,
                          fontSize: KaiaDesignTokens.fontSizeSm,
                          fontWeight: KaiaDesignTokens.fontWeightMedium,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
            const SizedBox(height: KaiaDesignTokens.spacing24),
          ],

          // Pricing Section
          if (guide.priceRange.isNotEmpty) ...[
            Text(
              'Pricing',
              style: TextStyle(
                color: KaiaDesignTokens.neutralGray800,
                fontSize: KaiaDesignTokens.fontSizeLg,
                fontWeight: KaiaDesignTokens.fontWeightSemiBold,
              ),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
              decoration: BoxDecoration(
                color: KaiaDesignTokens.primaryIndigo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    guide.priceRange,
                    style: TextStyle(
                      color: KaiaDesignTokens.primaryIndigo,
                      fontSize: KaiaDesignTokens.fontSizeXl,
                      fontWeight: KaiaDesignTokens.fontWeightBold,
                    ),
                  ),
                  const SizedBox(height: KaiaDesignTokens.spacing4),
                  Text(
                    'Response time: ${guide.responseTime}',
                    style: TextStyle(
                      color: KaiaDesignTokens.neutralGray600,
                      fontSize: KaiaDesignTokens.fontSizeSm,
                      fontWeight: KaiaDesignTokens.fontWeightRegular,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing24),
          ],

          // Availability Section
          if (guide.availability.isNotEmpty) ...[
            Text(
              'Availability',
              style: TextStyle(
                color: KaiaDesignTokens.neutralGray800,
                fontSize: KaiaDesignTokens.fontSizeLg,
                fontWeight: KaiaDesignTokens.fontWeightSemiBold,
              ),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing12),
            Wrap(
              spacing: KaiaDesignTokens.spacing8,
              runSpacing: KaiaDesignTokens.spacing6,
              children: guide.availability.map((time) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: KaiaDesignTokens.spacing12,
                    vertical: KaiaDesignTokens.spacing6,
                  ),
                  decoration: BoxDecoration(
                    color:
                        KaiaDesignTokens.accentEmerald.withValues(alpha: 0.1),
                    borderRadius:
                        BorderRadius.circular(KaiaDesignTokens.radiusSm),
                  ),
                  child: Text(
                    time,
                    style: TextStyle(
                      color: KaiaDesignTokens.accentEmerald,
                      fontSize: KaiaDesignTokens.fontSizeSm,
                      fontWeight: KaiaDesignTokens.fontWeightMedium,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildReviewsTab() {
    final guide = widget.guide;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (guide.reviews.isNotEmpty) ...[
            Text(
              'Customer Reviews',
              style: TextStyle(
                color: KaiaDesignTokens.neutralGray800,
                fontSize: KaiaDesignTokens.fontSizeLg,
                fontWeight: KaiaDesignTokens.fontWeightSemiBold,
              ),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing16),
            ...guide.reviews.map((review) {
              return Container(
                margin:
                    const EdgeInsets.only(bottom: KaiaDesignTokens.spacing16),
                padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
                decoration: BoxDecoration(
                  color: KaiaDesignTokens.neutralWhite,
                  borderRadius:
                      BorderRadius.circular(KaiaDesignTokens.radiusLg),
                  boxShadow: KaiaDesignTokens.shadowSm,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 20,
                          backgroundColor: KaiaDesignTokens.primaryIndigo
                              .withValues(alpha: 0.1),
                          child: Text(
                            review.userName.substring(0, 1).toUpperCase(),
                            style: TextStyle(
                              color: KaiaDesignTokens.primaryIndigo,
                              fontSize: KaiaDesignTokens.fontSizeMd,
                              fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                            ),
                          ),
                        ),
                        const SizedBox(width: KaiaDesignTokens.spacing12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                review.userName,
                                style: TextStyle(
                                  color: KaiaDesignTokens.neutralGray800,
                                  fontSize: KaiaDesignTokens.fontSizeMd,
                                  fontWeight:
                                      KaiaDesignTokens.fontWeightSemiBold,
                                ),
                              ),
                              const SizedBox(height: KaiaDesignTokens.spacing2),
                              Row(
                                children: [
                                  ...List.generate(5, (index) {
                                    return Icon(
                                      index < review.rating.floor()
                                          ? Icons.star
                                          : Icons.star_border,
                                      color: KaiaDesignTokens.warningAmber,
                                      size: 16,
                                    );
                                  }),
                                  const SizedBox(
                                      width: KaiaDesignTokens.spacing8),
                                  Text(
                                    review.date,
                                    style: TextStyle(
                                      color: KaiaDesignTokens.neutralGray500,
                                      fontSize: KaiaDesignTokens.fontSizeXs,
                                      fontWeight:
                                          KaiaDesignTokens.fontWeightRegular,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: KaiaDesignTokens.spacing12),
                    Text(
                      review.comment,
                      style: TextStyle(
                        color: KaiaDesignTokens.neutralGray600,
                        fontSize: KaiaDesignTokens.fontSizeSm,
                        fontWeight: KaiaDesignTokens.fontWeightRegular,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ] else ...[
            Center(
              child: Column(
                children: [
                  const SizedBox(height: KaiaDesignTokens.spacing48),
                  Icon(
                    Icons.rate_review_outlined,
                    color: KaiaDesignTokens.neutralGray400,
                    size: 64,
                  ),
                  const SizedBox(height: KaiaDesignTokens.spacing16),
                  Text(
                    'No reviews yet',
                    style: TextStyle(
                      color: KaiaDesignTokens.neutralGray500,
                      fontSize: KaiaDesignTokens.fontSizeLg,
                      fontWeight: KaiaDesignTokens.fontWeightMedium,
                    ),
                  ),
                  const SizedBox(height: KaiaDesignTokens.spacing8),
                  Text(
                    'Be the first to leave a review!',
                    style: TextStyle(
                      color: KaiaDesignTokens.neutralGray400,
                      fontSize: KaiaDesignTokens.fontSizeSm,
                      fontWeight: KaiaDesignTokens.fontWeightRegular,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildGalleryTab() {
    final guide = widget.guide;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (guide.gallery.isNotEmpty) ...[
            Text(
              'Photo Gallery',
              style: TextStyle(
                color: KaiaDesignTokens.neutralGray800,
                fontSize: KaiaDesignTokens.fontSizeLg,
                fontWeight: KaiaDesignTokens.fontWeightSemiBold,
              ),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing16),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: KaiaDesignTokens.spacing12,
                mainAxisSpacing: KaiaDesignTokens.spacing12,
                childAspectRatio: 1.0,
              ),
              itemCount: guide.gallery.length,
              itemBuilder: (context, index) {
                final imageUrl = guide.gallery[index];
                return Container(
                  decoration: BoxDecoration(
                    borderRadius:
                        BorderRadius.circular(KaiaDesignTokens.radiusLg),
                    boxShadow: KaiaDesignTokens.shadowSm,
                  ),
                  child: ClipRRect(
                    borderRadius:
                        BorderRadius.circular(KaiaDesignTokens.radiusLg),
                    child: Image.network(
                      imageUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: KaiaDesignTokens.neutralGray100,
                          child: Icon(
                            Icons.image_outlined,
                            color: KaiaDesignTokens.neutralGray400,
                            size: 40,
                          ),
                        );
                      },
                    ),
                  ),
                );
              },
            ),
          ] else ...[
            Center(
              child: Column(
                children: [
                  const SizedBox(height: KaiaDesignTokens.spacing48),
                  Icon(
                    Icons.photo_library_outlined,
                    color: KaiaDesignTokens.neutralGray400,
                    size: 64,
                  ),
                  const SizedBox(height: KaiaDesignTokens.spacing16),
                  Text(
                    'No photos available',
                    style: TextStyle(
                      color: KaiaDesignTokens.neutralGray500,
                      fontSize: KaiaDesignTokens.fontSizeLg,
                      fontWeight: KaiaDesignTokens.fontWeightMedium,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPackagesTab() {
    final guide = widget.guide;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (guide.packages != null && guide.packages!.isNotEmpty) ...[
            Text(
              'Tour Packages',
              style: TextStyle(
                color: KaiaDesignTokens.neutralGray800,
                fontSize: KaiaDesignTokens.fontSizeLg,
                fontWeight: KaiaDesignTokens.fontWeightSemiBold,
              ),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing16),
            ...guide.packages!.map((package) {
              return Container(
                margin:
                    const EdgeInsets.only(bottom: KaiaDesignTokens.spacing16),
                decoration: BoxDecoration(
                  color: KaiaDesignTokens.neutralWhite,
                  borderRadius:
                      BorderRadius.circular(KaiaDesignTokens.radiusLg),
                  boxShadow: KaiaDesignTokens.shadowMd,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Package Image
                    if (package.imageUrl != null)
                      Container(
                        height: 200,
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(KaiaDesignTokens.radiusLg),
                            topRight:
                                Radius.circular(KaiaDesignTokens.radiusLg),
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(KaiaDesignTokens.radiusLg),
                            topRight:
                                Radius.circular(KaiaDesignTokens.radiusLg),
                          ),
                          child: Image.network(
                            package.imageUrl!,
                            width: double.infinity,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: KaiaDesignTokens.neutralGray100,
                                child: Icon(
                                  Icons.tour_outlined,
                                  color: KaiaDesignTokens.neutralGray400,
                                  size: 60,
                                ),
                              );
                            },
                          ),
                        ),
                      ),

                    // Package Content
                    Padding(
                      padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  package.name,
                                  style: TextStyle(
                                    color: KaiaDesignTokens.neutralGray800,
                                    fontSize: KaiaDesignTokens.fontSizeLg,
                                    fontWeight:
                                        KaiaDesignTokens.fontWeightSemiBold,
                                  ),
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: KaiaDesignTokens.spacing8,
                                  vertical: KaiaDesignTokens.spacing4,
                                ),
                                decoration: BoxDecoration(
                                  color: KaiaDesignTokens.accentEmerald
                                      .withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(
                                      KaiaDesignTokens.radiusSm),
                                ),
                                child: Text(
                                  '\$${package.price.toStringAsFixed(0)}',
                                  style: TextStyle(
                                    color: KaiaDesignTokens.accentEmerald,
                                    fontSize: KaiaDesignTokens.fontSizeSm,
                                    fontWeight: KaiaDesignTokens.fontWeightBold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: KaiaDesignTokens.spacing8),
                          Text(
                            package.description,
                            style: TextStyle(
                              color: KaiaDesignTokens.neutralGray600,
                              fontSize: KaiaDesignTokens.fontSizeSm,
                              fontWeight: KaiaDesignTokens.fontWeightRegular,
                              height: 1.4,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }),
          ] else ...[
            Center(
              child: Column(
                children: [
                  const SizedBox(height: KaiaDesignTokens.spacing48),
                  Icon(
                    Icons.card_travel_outlined,
                    color: KaiaDesignTokens.neutralGray400,
                    size: 64,
                  ),
                  const SizedBox(height: KaiaDesignTokens.spacing16),
                  Text(
                    'No packages available',
                    style: TextStyle(
                      color: KaiaDesignTokens.neutralGray500,
                      fontSize: KaiaDesignTokens.fontSizeLg,
                      fontWeight: KaiaDesignTokens.fontWeightMedium,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Sticky tab bar delegate for the guide details screen
class _StickyTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  _StickyTabBarDelegate(this.tabBar);

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: KaiaDesignTokens.neutralWhite,
      child: tabBar,
    );
  }

  @override
  bool shouldRebuild(_StickyTabBarDelegate oldDelegate) {
    return tabBar != oldDelegate.tabBar;
  }
}
