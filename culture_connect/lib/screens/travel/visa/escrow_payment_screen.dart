// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/visa/escrow_payment_models.dart';
import 'package:culture_connect/models/travel/visa/visa_models.dart'
    as visa_models;
import 'package:culture_connect/models/travel/document/visa_service_provider.dart'
    as vsp;
import 'package:culture_connect/services/travel/visa/escrow_payment_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// Screen for managing escrow payments
class EscrowPaymentScreen extends ConsumerStatefulWidget {
  const EscrowPaymentScreen({
    super.key,
    this.escrowPayment,
    this.booking,
    this.provider,
  });

  final EscrowPayment? escrowPayment;
  final visa_models.VisaServiceBooking? booking;
  final vsp.VisaServiceProvider? provider;

  @override
  ConsumerState<EscrowPaymentScreen> createState() =>
      _EscrowPaymentScreenState();
}

class _EscrowPaymentScreenState extends ConsumerState<EscrowPaymentScreen> {
  EscrowPayment? _escrowPayment;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _escrowPayment = widget.escrowPayment;
    if (_escrowPayment == null && widget.booking != null) {
      _initializeEscrowPayment();
    }
  }

  Future<void> _initializeEscrowPayment() async {
    if (widget.booking == null || widget.provider == null) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final escrowService = ref.read(escrowPaymentServiceProvider);
      final result = await escrowService.initializeEscrowPayment(
        context: context,
        booking: widget.booking!,
        provider: widget.provider!,
        clientId: 'current_user_id', // TODO: Get from auth service
        clientEmail: '<EMAIL>', // TODO: Get from auth service
        clientName: 'Current User', // TODO: Get from auth service
      );

      if (result.success && result.escrowPayment != null) {
        setState(() {
          _escrowPayment = result.escrowPayment;
        });
      } else {
        setState(() {
          _errorMessage =
              result.errorMessage ?? 'Failed to initialize escrow payment';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error initializing escrow payment: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        title: Text(
          'Escrow Payment',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: AppTheme.fontWeightBold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: AppTheme.spacingMd),
            child: IconButton(
              onPressed: _showEscrowInfo,
              icon: Container(
                padding: const EdgeInsets.all(AppTheme.spacingXs),
                decoration: BoxDecoration(
                  color: AppTheme.infoColor.withValues(alpha: 0.1),
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: const Icon(
                  Icons.info_outline,
                  color: AppTheme.infoColor,
                  size: 20,
                ),
              ),
              tooltip: 'How escrow works',
            ),
          ),
        ],
      ),
      body: _buildBody(theme),
    );
  }

  Widget _buildBody(ThemeData theme) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: AppTheme.primaryColor,
            ),
            SizedBox(height: AppTheme.spacingMd),
            Text(
              'Setting up secure escrow payment...',
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
                fontWeight: AppTheme.fontWeightMedium,
              ),
            ),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return ErrorView(
        error: _errorMessage!,
        onRetry: _initializeEscrowPayment,
      );
    }

    if (_escrowPayment == null) {
      return _buildEmptyState(theme);
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildEscrowHeader(theme),
          const SizedBox(height: AppTheme.spacingLg),
          _buildPaymentSummary(theme),
          const SizedBox(height: AppTheme.spacingLg),
          _buildMilestoneProgress(theme),
          const SizedBox(height: AppTheme.spacingLg),
          _buildReleaseConditions(theme),
          const SizedBox(height: AppTheme.spacingLg),
          _buildActionButtons(theme),
          const SizedBox(height: AppTheme.spacingXl),
        ],
      ),
    );
  }

  Widget _buildEscrowHeader(ThemeData theme) {
    final status = _escrowPayment!.status;
    final statusColor = _getStatusColor(status);
    final statusText = _getStatusText(status);

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            statusColor.withValues(alpha: 0.1),
            statusColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        border: Border.all(
          color: statusColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingSm),
                decoration: BoxDecoration(
                  color: statusColor,
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Icon(
                  _getStatusIcon(status),
                  color: AppTheme.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMd),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Escrow Payment',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: AppTheme.fontWeightBold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    Text(
                      statusText,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: statusColor,
                        fontWeight: AppTheme.fontWeightSemibold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Row(
            children: [
              _buildInfoItem('Reference', _escrowPayment!.escrowReference),
              const SizedBox(width: AppTheme.spacingLg),
              _buildInfoItem('Amount',
                  '${_escrowPayment!.currency} ${_escrowPayment!.amount.toStringAsFixed(2)}'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: AppTheme.fontSizeSm,
            color: AppTheme.textTertiaryColor,
            fontWeight: AppTheme.fontWeightMedium,
          ),
        ),
        const SizedBox(height: AppTheme.spacingXs),
        Text(
          value,
          style: const TextStyle(
            fontSize: AppTheme.fontSizeMd,
            color: AppTheme.textPrimaryColor,
            fontWeight: AppTheme.fontWeightSemibold,
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentSummary(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Summary',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: AppTheme.fontWeightBold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          _buildSummaryRow('Service Amount',
              '${_escrowPayment!.currency} ${_escrowPayment!.amount.toStringAsFixed(2)}'),
          _buildSummaryRow('Escrow Fee', '${_escrowPayment!.currency} 0.00'),
          const Divider(height: AppTheme.spacingLg),
          _buildSummaryRow(
            'Total Held in Escrow',
            '${_escrowPayment!.currency} ${_escrowPayment!.amount.toStringAsFixed(2)}',
            isTotal: true,
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingSm),
            decoration: BoxDecoration(
              color: AppTheme.infoColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.security,
                  color: AppTheme.infoColor,
                  size: 16,
                ),
                const SizedBox(width: AppTheme.spacingSm),
                Expanded(
                  child: Text(
                    'Funds are securely held until service completion',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.infoColor,
                      fontWeight: AppTheme.fontWeightMedium,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingXs),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? AppTheme.fontSizeMd : AppTheme.fontSizeSm,
              color: isTotal
                  ? AppTheme.textPrimaryColor
                  : AppTheme.textSecondaryColor,
              fontWeight:
                  isTotal ? AppTheme.fontWeightBold : AppTheme.fontWeightMedium,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? AppTheme.fontSizeMd : AppTheme.fontSizeSm,
              color: AppTheme.textPrimaryColor,
              fontWeight: isTotal
                  ? AppTheme.fontWeightBold
                  : AppTheme.fontWeightSemibold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMilestoneProgress(ThemeData theme) {
    final milestones = _escrowPayment!.milestones;
    final completedCount = milestones.where((m) => m.isCompleted).length;
    final progress =
        milestones.isNotEmpty ? completedCount / milestones.length : 0.0;

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Service Progress',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: AppTheme.fontWeightBold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              Text(
                '$completedCount/${milestones.length} completed',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: AppTheme.fontWeightSemibold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: AppTheme.borderColor,
            valueColor:
                const AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          ...milestones
              .map((milestone) => _buildMilestoneItem(milestone, theme)),
        ],
      ),
    );
  }

  Widget _buildMilestoneItem(EscrowMilestone milestone, ThemeData theme) {
    final isCompleted = milestone.isCompleted;
    final iconColor =
        isCompleted ? AppTheme.successColor : AppTheme.borderColor;
    final textColor =
        isCompleted ? AppTheme.textPrimaryColor : AppTheme.textSecondaryColor;

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingSm),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: isCompleted ? AppTheme.successColor : Colors.transparent,
              border: Border.all(
                color: iconColor,
                width: 2,
              ),
              shape: BoxShape.circle,
            ),
            child: isCompleted
                ? const Icon(
                    Icons.check,
                    color: AppTheme.white,
                    size: 16,
                  )
                : null,
          ),
          const SizedBox(width: AppTheme.spacingMd),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  milestone.name,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: textColor,
                    fontWeight: AppTheme.fontWeightSemibold,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingXs),
                Text(
                  milestone.description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: AppTheme.textTertiaryColor,
                    height: AppTheme.lineHeightRelaxed,
                  ),
                ),
                if (!isCompleted) ...[
                  const SizedBox(height: AppTheme.spacingXs),
                  Text(
                    'Expected: ${_formatDate(milestone.expectedDate)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.textTertiaryColor,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReleaseConditions(ThemeData theme) {
    final releasablePercentage = _escrowPayment!.releasablePercentage;
    final canRelease = _escrowPayment!.canRelease;

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Release Conditions',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: AppTheme.fontWeightBold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingSm),
            decoration: BoxDecoration(
              color: canRelease
                  ? AppTheme.successColor.withValues(alpha: 0.1)
                  : AppTheme.warningColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
            ),
            child: Row(
              children: [
                Icon(
                  canRelease ? Icons.check_circle : Icons.schedule,
                  color: canRelease
                      ? AppTheme.successColor
                      : AppTheme.warningColor,
                  size: 20,
                ),
                const SizedBox(width: AppTheme.spacingSm),
                Expanded(
                  child: Text(
                    canRelease
                        ? 'Ready for release (${releasablePercentage.toStringAsFixed(0)}% conditions met)'
                        : 'Awaiting conditions (${releasablePercentage.toStringAsFixed(0)}% conditions met)',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: canRelease
                          ? AppTheme.successColor
                          : AppTheme.warningColor,
                      fontWeight: AppTheme.fontWeightSemibold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ThemeData theme) {
    final canRelease = _escrowPayment!.canRelease;
    final status = _escrowPayment!.status;

    return Column(
      children: [
        if (status == EscrowPaymentStatus.fundsHeld ||
            status == EscrowPaymentStatus.serviceInProgress) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: canRelease ? _approveServiceCompletion : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.successColor,
                foregroundColor: AppTheme.white,
                padding:
                    const EdgeInsets.symmetric(vertical: AppTheme.spacingMd),
                shape: RoundedRectangleBorder(
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusLarge),
                ),
              ),
              child: const Text(
                'Approve & Release Funds',
                style: TextStyle(
                  fontWeight: AppTheme.fontWeightSemibold,
                  fontSize: AppTheme.fontSizeMd,
                ),
              ),
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: _requestRefund,
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.errorColor,
                side: const BorderSide(color: AppTheme.errorColor),
                padding:
                    const EdgeInsets.symmetric(vertical: AppTheme.spacingMd),
                shape: RoundedRectangleBorder(
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusLarge),
                ),
              ),
              child: const Text(
                'Request Refund',
                style: TextStyle(
                  fontWeight: AppTheme.fontWeightSemibold,
                  fontSize: AppTheme.fontSizeMd,
                ),
              ),
            ),
          ),
        ],
        if (status == EscrowPaymentStatus.disputed) ...[
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMd),
            decoration: BoxDecoration(
              color: AppTheme.warningColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.gavel,
                  color: AppTheme.warningColor,
                  size: 24,
                ),
                const SizedBox(width: AppTheme.spacingMd),
                Expanded(
                  child: Text(
                    'This payment is under dispute review. We will contact you within 48 hours.',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.warningColor,
                      fontWeight: AppTheme.fontWeightMedium,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
        if (status == EscrowPaymentStatus.fundsReleased) ...[
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMd),
            decoration: BoxDecoration(
              color: AppTheme.successColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: AppTheme.successColor,
                  size: 24,
                ),
                const SizedBox(width: AppTheme.spacingMd),
                Expanded(
                  child: Text(
                    'Funds have been successfully released to the service provider.',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.successColor,
                      fontWeight: AppTheme.fontWeightMedium,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingXl),
            decoration: BoxDecoration(
              color: AppTheme.borderColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.account_balance_wallet_outlined,
              size: 64,
              color: AppTheme.borderColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingLg),
          Text(
            'No Escrow Payment',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: AppTheme.fontWeightBold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingSm),
          Text(
            'No escrow payment information available.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper methods
  Color _getStatusColor(EscrowPaymentStatus status) {
    switch (status) {
      case EscrowPaymentStatus.initializing:
        return AppTheme.infoColor;
      case EscrowPaymentStatus.fundsHeld:
      case EscrowPaymentStatus.serviceInProgress:
        return AppTheme.warningColor;
      case EscrowPaymentStatus.awaitingApproval:
        return AppTheme.primaryColor;
      case EscrowPaymentStatus.approved:
      case EscrowPaymentStatus.fundsReleased:
        return AppTheme.successColor;
      case EscrowPaymentStatus.refunded:
        return AppTheme.infoColor;
      case EscrowPaymentStatus.disputed:
      case EscrowPaymentStatus.cancelled:
      case EscrowPaymentStatus.expired:
        return AppTheme.errorColor;
    }
  }

  String _getStatusText(EscrowPaymentStatus status) {
    switch (status) {
      case EscrowPaymentStatus.initializing:
        return 'Initializing Payment';
      case EscrowPaymentStatus.fundsHeld:
        return 'Funds Secured in Escrow';
      case EscrowPaymentStatus.serviceInProgress:
        return 'Service in Progress';
      case EscrowPaymentStatus.awaitingApproval:
        return 'Awaiting Your Approval';
      case EscrowPaymentStatus.approved:
        return 'Service Approved';
      case EscrowPaymentStatus.fundsReleased:
        return 'Funds Released';
      case EscrowPaymentStatus.refunded:
        return 'Refunded';
      case EscrowPaymentStatus.disputed:
        return 'Under Dispute';
      case EscrowPaymentStatus.cancelled:
        return 'Cancelled';
      case EscrowPaymentStatus.expired:
        return 'Expired';
    }
  }

  IconData _getStatusIcon(EscrowPaymentStatus status) {
    switch (status) {
      case EscrowPaymentStatus.initializing:
        return Icons.hourglass_empty;
      case EscrowPaymentStatus.fundsHeld:
        return Icons.lock;
      case EscrowPaymentStatus.serviceInProgress:
        return Icons.work;
      case EscrowPaymentStatus.awaitingApproval:
        return Icons.pending_actions;
      case EscrowPaymentStatus.approved:
      case EscrowPaymentStatus.fundsReleased:
        return Icons.check_circle;
      case EscrowPaymentStatus.refunded:
        return Icons.undo;
      case EscrowPaymentStatus.disputed:
        return Icons.gavel;
      case EscrowPaymentStatus.cancelled:
      case EscrowPaymentStatus.expired:
        return Icons.cancel;
    }
  }

  bool _isConditionMet(EscrowReleaseCondition condition) {
    switch (condition) {
      case EscrowReleaseCondition.clientApproval:
        return _escrowPayment!.clientApproved == true;
      case EscrowReleaseCondition.serviceCompletion:
        return _escrowPayment!.providerConfirmedCompletion == true;
      case EscrowReleaseCondition.timeBasedRelease:
        return DateTime.now().isAfter(_escrowPayment!.autoReleaseDate);
      case EscrowReleaseCondition.milestoneCompletion:
        return _escrowPayment!.milestones.every((m) => m.isCompleted);
      case EscrowReleaseCondition.documentSubmission:
      case EscrowReleaseCondition.embassySubmission:
        return false; // Would be determined by external service status
    }
  }

  String _getConditionText(EscrowReleaseCondition condition) {
    switch (condition) {
      case EscrowReleaseCondition.clientApproval:
        return 'Client approval of service completion';
      case EscrowReleaseCondition.serviceCompletion:
        return 'Service provider confirms completion';
      case EscrowReleaseCondition.timeBasedRelease:
        return 'Automatic release after 30 days';
      case EscrowReleaseCondition.milestoneCompletion:
        return 'All service milestones completed';
      case EscrowReleaseCondition.documentSubmission:
        return 'Documents submitted to embassy';
      case EscrowReleaseCondition.embassySubmission:
        return 'Application submitted to embassy';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _approveServiceCompletion() async {
    if (_escrowPayment == null) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Approve Service Completion'),
        content: const Text(
          'Are you satisfied with the service provided? This will release the funds to the service provider.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.successColor,
            ),
            child: const Text('Approve & Release'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      setState(() {
        _isLoading = true;
      });

      try {
        final escrowService = ref.read(escrowPaymentServiceProvider);
        final result = await escrowService.approveServiceCompletion(
          escrowId: _escrowPayment!.id,
          clientId: 'current_user_id', // TODO: Get from auth service
        );

        if (result.success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.message ?? 'Service approved successfully'),
              backgroundColor: AppTheme.successColor,
            ),
          );
          // Refresh escrow payment data
          // TODO: Implement refresh logic
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.errorMessage ?? 'Failed to approve service'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: $e'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _requestRefund() async {
    if (_escrowPayment == null) return;

    final result = await showDialog<Map<String, String>?>(
      context: context,
      builder: (context) => _RefundRequestDialog(),
    );

    if (result != null && mounted) {
      setState(() {
        _isLoading = true;
      });

      try {
        final escrowService = ref.read(escrowPaymentServiceProvider);
        final refundResult = await escrowService.requestRefund(
          escrowId: _escrowPayment!.id,
          clientId: 'current_user_id', // TODO: Get from auth service
          reason: result['reason']!,
          description: result['description'],
        );

        if (refundResult.success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(refundResult.message ?? 'Refund request submitted'),
              backgroundColor: AppTheme.infoColor,
            ),
          );
          // Refresh escrow payment data
          // TODO: Implement refresh logic
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(refundResult.errorMessage ??
                  'Failed to submit refund request'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: $e'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _showEscrowInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('How Escrow Works'),
        content: const SingleChildScrollView(
          child: Text(
            'Escrow payments protect both you and the service provider:\n\n'
            '1. Your payment is held securely until service completion\n'
            '2. The provider cannot access funds until you approve\n'
            '3. Funds are automatically released after 30 days if no disputes\n'
            '4. You can request a refund if unsatisfied with the service\n'
            '5. All disputes are reviewed by our team within 48 hours\n\n'
            'This ensures you only pay for satisfactory service while protecting providers from payment disputes.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}

/// Dialog for requesting a refund
class _RefundRequestDialog extends StatefulWidget {
  @override
  State<_RefundRequestDialog> createState() => _RefundRequestDialogState();
}

class _RefundRequestDialogState extends State<_RefundRequestDialog> {
  final _reasonController = TextEditingController();
  final _descriptionController = TextEditingController();
  String _selectedReason = 'Service not completed';

  final List<String> _refundReasons = [
    'Service not completed',
    'Poor service quality',
    'Consultant unresponsive',
    'Incorrect information provided',
    'Service not as described',
    'Other',
  ];

  @override
  void dispose() {
    _reasonController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Request Refund'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Reason for refund:'),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _selectedReason,
              items: _refundReasons.map((reason) {
                return DropdownMenuItem(
                  value: reason,
                  child: Text(reason),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedReason = value!;
                });
              },
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
            ),
            const SizedBox(height: 16),
            const Text('Additional details (optional):'),
            const SizedBox(height: 8),
            TextField(
              controller: _descriptionController,
              maxLines: 3,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Provide more details about your refund request...',
                contentPadding: EdgeInsets.all(12),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop({
              'reason': _selectedReason,
              'description': _descriptionController.text.trim().isEmpty
                  ? null
                  : _descriptionController.text.trim(),
            });
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.errorColor,
          ),
          child: const Text('Submit Request'),
        ),
      ],
    );
  }
}
