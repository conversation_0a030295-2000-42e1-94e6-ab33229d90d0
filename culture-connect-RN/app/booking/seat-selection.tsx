import React, { useState } from "react";
import { StyleSheet, View, Text, ScrollView, Pressable, Dimensions, Platform } from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { ArrowLeft, Plane, PlaneTakeoff, PlaneLanding, Users, Crown, Check } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { Button } from "@/components/Button";

const { width: screenWidth } = Dimensions.get('window');

const seatMap = [
  // First Class
  ["1A", "1B", "", "1C", "1D"],
  ["2A", "2B", "", "2C", "2D"],
  ["", "", "", "", ""], // Divider
  // Business Class
  ["3A", "3B", "", "3C", "3D", "3E", "3F"],
  ["4A", "4B", "", "4C", "4D", "4E", "4F"],
  ["5A", "5B", "", "5C", "5D", "5E", "5F"],
  ["", "", "", "", "", "", ""], // Divider
  // Premium Economy
  ["6A", "6B", "6C", "", "6D", "6E", "6F"],
  ["7A", "7B", "7C", "", "7D", "7E", "7F"],
  ["8A", "8B", "8C", "", "8D", "8E", "8F"],
  ["", "", "", "", "", "", ""], // Divider
  // Economy
  ["9A", "9B", "9C", "", "9D", "9E", "9F"],
  ["10A", "10B", "10C", "", "10D", "10E", "10F"],
  ["11A", "11B", "11C", "", "11D", "11E", "11F"],
  ["12A", "12B", "12C", "", "12D", "12E", "12F"],
  ["13A", "13B", "13C", "", "13D", "13E", "13F"],
  ["14A", "14B", "14C", "", "14D", "14E", "14F"],
  ["15A", "15B", "15C", "", "15D", "15E", "15F"],
  ["16A", "16B", "16C", "", "16D", "16E", "16F"],
  ["17A", "17B", "17C", "", "17D", "17E", "17F"],
  ["18A", "18B", "18C", "", "18D", "18E", "18F"],
  ["19A", "19B", "19C", "", "19D", "19E", "19F"],
  ["20A", "20B", "20C", "", "20D", "20E", "20F"],
];

const occupiedSeats = ["1A", "2C", "3B", "4D", "6A", "7E", "9C", "10F", "12B", "15D", "18A", "19E"];
const firstClassSeats = ["1A", "1B", "1C", "1D", "2A", "2B", "2C", "2D"];
const businessSeats = ["3A", "3B", "3C", "3D", "3E", "3F", "4A", "4B", "4C", "4D", "4E", "4F", "5A", "5B", "5C", "5D", "5E", "5F"];
const premiumEconomySeats = ["6A", "6B", "6C", "6D", "6E", "6F", "7A", "7B", "7C", "7D", "7E", "7F", "8A", "8B", "8C", "8D", "8E", "8F"];

const getSeatClass = (seat: string) => {
  if (firstClassSeats.includes(seat)) return 'first';
  if (businessSeats.includes(seat)) return 'business';
  if (premiumEconomySeats.includes(seat)) return 'premium';
  return 'economy';
};

const getSeatPrice = (seat: string) => {
  const seatClass = getSeatClass(seat);
  switch (seatClass) {
    case 'first': return 150;
    case 'business': return 100;
    case 'premium': return 50;
    default: return 25;
  }
};

const getSeatClassLabel = (seatClass: string) => {
  switch (seatClass) {
    case 'first': return 'First Class';
    case 'business': return 'Business';
    case 'premium': return 'Premium Economy';
    default: return 'Economy';
  }
};

export default function SeatSelectionScreen() {
  const router = useRouter();
  const { flightId } = useLocalSearchParams();
  const [selectedSeats, setSelectedSeats] = useState<string[]>([]);

  const handleSeatSelect = (seat: string) => {
    if (occupiedSeats.includes(seat)) return;
    
    if (selectedSeats.includes(seat)) {
      setSelectedSeats(prev => prev.filter(s => s !== seat));
    } else {
      setSelectedSeats(prev => [...prev, seat]);
    }
  };

  const getSeatStyle = (seat: string) => {
    const baseStyle = [styles.seat];
    
    if (occupiedSeats.includes(seat)) {
      return [...baseStyle, styles.seatOccupied];
    }
    if (selectedSeats.includes(seat)) {
      return [...baseStyle, styles.seatSelected];
    }
    
    const seatClass = getSeatClass(seat);
    switch (seatClass) {
      case 'first':
        return [...baseStyle, styles.seatFirst];
      case 'business':
        return [...baseStyle, styles.seatBusiness];
      case 'premium':
        return [...baseStyle, styles.seatPremium];
      default:
        return [...baseStyle, styles.seatAvailable];
    }
  };

  const getSeatTextStyle = (seat: string) => {
    if (occupiedSeats.includes(seat)) {
      return [styles.seatText, styles.seatTextOccupied];
    }
    if (selectedSeats.includes(seat)) {
      return [styles.seatText, styles.seatTextSelected];
    }
    return [styles.seatText, styles.seatTextDefault];
  };

  const handleContinue = () => {
    const seatsParam = selectedSeats.length > 0 ? `&seats=${selectedSeats.join(',')}` : '';
    router.push(`/booking/payment?flightId=${flightId}${seatsParam}`);
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#4844b8', '#6366f1']}
        style={styles.headerGradient}
      >
        <SafeAreaView edges={['top']}>
          <View style={styles.header}>
            <Pressable style={styles.backButton} onPress={() => router.back()}>
              <ArrowLeft size={24} color={colors.white} />
            </Pressable>
            <Text style={styles.headerTitle}>Select Your Seats</Text>
            <View style={{ width: 24 }} />
          </View>

          <View style={styles.flightInfo}>
            <View style={styles.flightRoute}>
              <View style={styles.routeContainer}>
                <View style={styles.airportContainer}>
                  <PlaneTakeoff size={20} color={colors.white} />
                  <Text style={styles.airportCode}>NYC</Text>
                </View>
                <View style={styles.flightLine}>
                  <View style={styles.flightDot} />
                  <View style={styles.flightPath} />
                  <View style={styles.flightDot} />
                </View>
                <View style={styles.airportContainer}>
                  <PlaneLanding size={20} color={colors.white} />
                  <Text style={styles.airportCode}>NRT</Text>
                </View>
              </View>
              <Text style={styles.flightDetails}>Japan Airlines • Aug 15, 2025 • Boeing 777-300ER</Text>
            </View>
          </View>
        </SafeAreaView>
      </LinearGradient>

      <View style={styles.legendContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.legend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendSeat, styles.seatAvailable]} />
            <Text style={styles.legendText}>Available</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendSeat, styles.seatFirst]} />
            <Text style={styles.legendText}>First Class</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendSeat, styles.seatBusiness]} />
            <Text style={styles.legendText}>Business</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendSeat, styles.seatPremium]} />
            <Text style={styles.legendText}>Premium</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendSeat, styles.seatSelected]} />
            <Text style={styles.legendText}>Selected</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendSeat, styles.seatOccupied]} />
            <Text style={styles.legendText}>Occupied</Text>
          </View>
        </ScrollView>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.aircraftContainer}>
          <LinearGradient
            colors={['#f8fafc', '#f1f5f9']}
            style={styles.aircraftBody}
          >
            <View style={styles.aircraftNose}>
              <Plane size={28} color="#4844b8" />
            </View>
            
            <View style={styles.seatMap}>
              {seatMap.map((row, rowIndex) => {
                const isEmptyRow = row.every(seat => seat === "");
                if (isEmptyRow) {
                  return (
                    <View key={rowIndex} style={styles.sectionDivider}>
                      <View style={styles.dividerLine} />
                    </View>
                  );
                }
                
                return (
                  <View key={rowIndex} style={styles.seatRow}>
                    <View style={styles.rowNumber}>
                      <Text style={styles.rowNumberText}>{row.find(s => s)?.replace(/[A-Z]/g, '') || ''}</Text>
                    </View>
                    <View style={styles.seatsContainer}>
                      {row.map((seat, seatIndex) => (
                        <View key={seatIndex} style={styles.seatContainer}>
                          {seat ? (
                            <Pressable
                              style={getSeatStyle(seat)}
                              onPress={() => handleSeatSelect(seat)}
                              disabled={occupiedSeats.includes(seat)}
                            >
                              {selectedSeats.includes(seat) && (
                                <View style={styles.selectedIndicator}>
                                  <Check size={12} color={colors.white} />
                                </View>
                              )}
                              {occupiedSeats.includes(seat) && (
                                <Users size={12} color={colors.white} />
                              )}
                              {firstClassSeats.includes(seat) && !occupiedSeats.includes(seat) && !selectedSeats.includes(seat) && (
                                <Crown size={10} color="#fbbf24" />
                              )}
                              <Text style={getSeatTextStyle(seat)}>
                                {seat.replace(/[0-9]/g, '')}
                              </Text>
                            </Pressable>
                          ) : (
                            <View style={styles.seatSpacer} />
                          )}
                        </View>
                      ))}
                    </View>
                    <View style={styles.rowNumber}>
                      <Text style={styles.rowNumberText}>{row.find(s => s)?.replace(/[A-Z]/g, '') || ''}</Text>
                    </View>
                  </View>
                );
              })}
            </View>
          </LinearGradient>
        </View>

        {selectedSeats.length > 0 && (
          <View style={styles.selectionSummary}>
            <LinearGradient
              colors={['#ffffff', '#f8fafc']}
              style={styles.summaryGradient}
            >
              <View style={styles.summaryHeader}>
                <Text style={styles.summaryTitle}>Selected Seats</Text>
                <View style={styles.summaryBadge}>
                  <Text style={styles.summaryBadgeText}>{selectedSeats.length}</Text>
                </View>
              </View>
              <View style={styles.selectedSeatsList}>
                {selectedSeats.map((seat, index) => {
                  const seatClass = getSeatClass(seat);
                  const price = getSeatPrice(seat);
                  return (
                    <View key={index} style={styles.selectedSeatItem}>
                      <View style={styles.selectedSeatInfo}>
                        <View style={styles.selectedSeatNumber}>
                          <Text style={styles.selectedSeatText}>{seat}</Text>
                        </View>
                        <View>
                          <Text style={styles.selectedSeatClass}>{getSeatClassLabel(seatClass)}</Text>
                          <Text style={styles.selectedSeatPosition}>
                            {seat.includes('A') || seat.includes('F') ? 'Window' : 
                             seat.includes('B') || seat.includes('E') ? 'Middle' : 'Aisle'}
                          </Text>
                        </View>
                      </View>
                      <Text style={styles.selectedSeatPrice}>${price}</Text>
                    </View>
                  );
                })}
              </View>
            </LinearGradient>
          </View>
        )}
        
        <View style={{ height: 120 }} />
      </ScrollView>

      <LinearGradient
        colors={['rgba(255,255,255,0.95)', 'rgba(255,255,255,1)']}
        style={styles.footer}
      >
        <View style={styles.footerContent}>
          <View style={styles.totalContainer}>
            <Text style={styles.totalLabel}>Total Seat Fees</Text>
            <Text style={styles.totalPrice}>
              ${selectedSeats.reduce((total, seat) => total + getSeatPrice(seat), 0)}
            </Text>
            {selectedSeats.length > 0 && (
              <Text style={styles.totalSeats}>{selectedSeats.length} seat{selectedSeats.length > 1 ? 's' : ''}</Text>
            )}
          </View>
          <Button
            title={selectedSeats.length > 0 ? "Continue to Payment" : "Skip Seat Selection"}
            onPress={handleContinue}
            variant="primary"
            size="large"
          />
        </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  headerGradient: {
    paddingBottom: spacing.lg,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  headerTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
  },
  flightInfo: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.md,
  },
  flightRoute: {
    alignItems: "center",
  },
  routeContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: spacing.sm,
  },
  airportContainer: {
    alignItems: "center",
    gap: spacing.xs,
  },
  airportCode: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
  },
  flightLine: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: spacing.lg,
  },
  flightDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.white,
  },
  flightPath: {
    width: 60,
    height: 2,
    backgroundColor: colors.white,
    marginHorizontal: spacing.xs,
  },
  flightDetails: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: 'rgba(255,255,255,0.8)',
    textAlign: "center",
  },
  legendContainer: {
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  legend: {
    flexDirection: "row",
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    gap: spacing.lg,
  },
  legendItem: {
    alignItems: "center",
    gap: spacing.xs,
  },
  legendSeat: {
    width: 18,
    height: 18,
    borderRadius: 6,
    borderWidth: 1,
  },
  legendText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
  },
  content: {
    flex: 1,
  },
  aircraftContainer: {
    margin: spacing.lg,
    borderRadius: 20,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 12,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  aircraftBody: {
    paddingVertical: spacing.xl,
  },
  aircraftNose: {
    alignItems: "center",
    marginBottom: spacing.lg,
  },
  seatMap: {
    paddingHorizontal: spacing.md,
  },
  sectionDivider: {
    paddingVertical: spacing.md,
    alignItems: "center",
  },
  dividerLine: {
    width: '60%',
    height: 1,
    backgroundColor: colors.borderLight,
  },
  seatRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.xs,
    paddingHorizontal: spacing.sm,
  },
  rowNumber: {
    width: 24,
    alignItems: "center",
  },
  rowNumberText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
  },
  seatsContainer: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "center",
    gap: 4,
  },
  seatContainer: {
    position: 'relative',
  },
  seat: {
    width: 36,
    height: 36,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    position: 'relative',
  },
  seatAvailable: {
    backgroundColor: colors.white,
    borderColor: colors.border,
  },
  seatFirst: {
    backgroundColor: '#fef3c7',
    borderColor: '#f59e0b',
  },
  seatBusiness: {
    backgroundColor: '#e0e7ff',
    borderColor: '#6366f1',
  },
  seatPremium: {
    backgroundColor: '#f0f9ff',
    borderColor: '#0ea5e9',
  },
  seatSelected: {
    backgroundColor: '#4844b8',
    borderColor: '#4844b8',
  },
  seatOccupied: {
    backgroundColor: '#f1f5f9',
    borderColor: '#94a3b8',
  },
  seatSpacer: {
    width: 36,
    height: 36,
  },
  selectedIndicator: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#10b981',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  seatText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold as any,
  },
  seatTextDefault: {
    color: colors.text,
  },
  seatTextSelected: {
    color: colors.white,
  },
  seatTextOccupied: {
    color: colors.textSecondary,
  },
  selectionSummary: {
    margin: spacing.lg,
    borderRadius: 16,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  summaryGradient: {
    padding: spacing.lg,
  },
  summaryHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  summaryTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
  summaryBadge: {
    backgroundColor: '#4844b8',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  summaryBadgeText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
  },
  selectedSeatsList: {
    gap: spacing.md,
  },
  selectedSeatItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    backgroundColor: '#f8fafc',
    borderRadius: 12,
  },
  selectedSeatInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: spacing.md,
  },
  selectedSeatNumber: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: '#4844b8',
    justifyContent: "center",
    alignItems: "center",
  },
  selectedSeatText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
  },
  selectedSeatClass: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  selectedSeatPosition: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  selectedSeatPrice: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: '#4844b8',
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingTop: spacing.md,
    paddingBottom: Platform.OS === 'ios' ? spacing.xl : spacing.lg,
    paddingHorizontal: spacing.lg,
  },
  footerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  totalContainer: {
    flex: 1,
    marginRight: spacing.lg,
  },
  totalLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
  },
  totalPrice: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
  totalSeats: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    marginTop: 2,
  },
});