// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/screens/payment/universal_payment_screen.dart';
import 'package:culture_connect/screens/travel/flight/rn_flight_results_screen.dart';

/// Seat model for seat selection
class Seat {
  final String id;
  final String row;
  final String column;
  final SeatType type;
  final SeatStatus status;
  final double? price;

  const Seat({
    required this.id,
    required this.row,
    required this.column,
    required this.type,
    required this.status,
    this.price,
  });
}

/// Seat types
enum SeatType {
  economy,
  premiumEconomy,
  business,
  first,
  exit,
  window,
  aisle,
  middle,
}

/// Seat status
enum SeatStatus {
  available,
  occupied,
  selected,
  blocked,
}

/// Pixel-perfect React Native SeatSelectionScreen replica
class RNSeatSelectionScreen extends ConsumerStatefulWidget {
  final String flightId;

  const RNSeatSelectionScreen({
    super.key,
    required this.flightId,
  });

  @override
  ConsumerState<RNSeatSelectionScreen> createState() =>
      _RNSeatSelectionScreenState();
}

class _RNSeatSelectionScreenState extends ConsumerState<RNSeatSelectionScreen> {
  String? _selectedSeatId;
  final List<List<Seat>> _seatMap = [];
  bool _showSkipButton = true;
  bool _isLoadingSeat = false;

  @override
  void initState() {
    super.initState();
    _generateSeatMap();
  }

  void _generateSeatMap() {
    // Generate mock seat map (6 seats per row, 30 rows)
    for (int row = 1; row <= 30; row++) {
      final rowSeats = <Seat>[];
      final columns = ['A', 'B', 'C', 'D', 'E', 'F'];

      for (int col = 0; col < columns.length; col++) {
        final seatId = '$row${columns[col]}';
        final isOccupied = (row + col) % 7 == 0; // Random occupied seats
        final isExit = row == 12 || row == 25; // Exit rows

        rowSeats.add(Seat(
          id: seatId,
          row: row.toString(),
          column: columns[col],
          type: isExit ? SeatType.exit : SeatType.economy,
          status: isOccupied ? SeatStatus.occupied : SeatStatus.available,
          price: isExit ? 25.0 : null,
        ));
      }
      _seatMap.add(rowSeats);
    }
  }

  void _handleSeatSelection(String seatId) {
    final seat = _findSeatById(seatId);
    if (seat?.status == SeatStatus.available) {
      setState(() {
        _selectedSeatId = _selectedSeatId == seatId ? null : seatId;
        // Hide skip button when a seat is selected
        _showSkipButton = _selectedSeatId == null;
      });
    }
  }

  Seat? _findSeatById(String seatId) {
    for (final row in _seatMap) {
      for (final seat in row) {
        if (seat.id == seatId) return seat;
      }
    }
    return null;
  }

  /// Get flight data based on flight ID
  FlightResult? _getFlightData(String flightId) {
    try {
      return mockFlightResults.firstWhere((flight) => flight.id == flightId);
    } catch (e) {
      // Return default flight data if not found
      return mockFlightResults.isNotEmpty ? mockFlightResults.first : null;
    }
  }

  void _handleSkipSeatSelection() async {
    // Show loading modal
    setState(() {
      _isLoadingSeat = true;
    });

    // Simulate API call delay for realistic loading experience
    await Future.delayed(const Duration(milliseconds: 1500));

    // Find the first available seat (preferably not an exit row)
    String? availableSeatId;

    // First, try to find a regular economy seat
    for (final row in _seatMap) {
      for (final seat in row) {
        if (seat.status == SeatStatus.available && seat.type != SeatType.exit) {
          availableSeatId = seat.id;
          break;
        }
      }
      if (availableSeatId != null) break;
    }

    // If no regular seat found, use any available seat
    if (availableSeatId == null) {
      for (final row in _seatMap) {
        for (final seat in row) {
          if (seat.status == SeatStatus.available) {
            availableSeatId = seat.id;
            break;
          }
        }
        if (availableSeatId != null) break;
      }
    }

    // Auto-select the found seat and hide loading
    if (availableSeatId != null) {
      setState(() {
        _selectedSeatId = availableSeatId;
        _showSkipButton = false;
        _isLoadingSeat = false;
      });
    } else {
      // Hide loading if no seat found
      setState(() {
        _isLoadingSeat = false;
      });
    }
  }

  void _handleContinue() {
    if (_selectedSeatId != null) {
      final selectedSeat = _findSeatById(_selectedSeatId!);
      final extraCost = selectedSeat?.price ?? 0.0;
      final flightData = _getFlightData(widget.flightId);
      final basePrice = flightData?.price.toDouble() ?? 520.0;
      final totalAmount = basePrice + extraCost;

      // Create flight booking object for payment
      final flightBooking = Booking(
        id: 'flight_${DateTime.now().millisecondsSinceEpoch}',
        experienceId: widget.flightId,
        date:
            DateTime.now().add(const Duration(days: 7)), // Mock departure date
        timeSlot: TimeSlot(
          startTime: DateTime.now().add(const Duration(days: 7, hours: 8)),
          endTime: DateTime.now().add(const Duration(days: 7, hours: 22)),
        ),
        participantCount: 1, // Number of passengers
        totalAmount: totalAmount,
        status: BookingStatus.pending,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        specialRequirements: 'Seat: $_selectedSeatId',
      );

      // Prepare comprehensive flight details for Universal Payment Screen
      final bookingDetails = <String, dynamic>{
        'flightNumber': flightData?.flightNumber ?? 'AA1234',
        'airline': flightData?.airline ?? 'American Airlines',
        'departure':
            '${flightData?.departureCode ?? 'LAX'} (${flightData?.departure ?? '8:30'})',
        'arrival':
            '${flightData?.arrivalCode ?? 'NRT'} (${flightData?.arrival ?? '14:45'})',
        'date': 'Dec 25, 2024', // Mock departure date
        'passengers': 1,
        'class': flightData?.flightClass ?? 'Economy',
        'selectedSeat': _selectedSeatId,
        'seatFee': extraCost,
        'basePrice': basePrice,
        'duration': flightData?.duration ?? '14h 15m',
        'aircraft': flightData?.aircraft ?? 'Boeing 787',
        'stops': flightData?.stops ?? 0,
      };

      // Navigate to Universal Payment Screen
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => UniversalPaymentScreen(
            booking: flightBooking,
            userEmail: '<EMAIL>', // TODO: Get from user provider
            userName: 'User Name', // TODO: Get from user provider
            userPhone: '+**********', // TODO: Get from user provider
            bookingDetails: bookingDetails,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Stack(
        children: [
          // Main content with proper header spacing
          Column(
            children: [
              // Header
              _buildHeader(),

              // Aircraft Layout with reduced top spacing
              _buildAircraftLayout(),

              // Seat Map
              Expanded(
                child: _buildSeatMap(),
              ),

              // Legend
              _buildLegend(),

              // Continue Button
              if (_selectedSeatId != null) _buildContinueButton(),
            ],
          ),

          // Loading modal overlay
          if (_isLoadingSeat) _buildLoadingModal(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      // Complete background coverage
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withOpacity(0.8),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Padding(
          // Optimized spacing - reduced bottom padding
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
          child: Column(
            children: [
              // Top row with back button and title
              Row(
                children: [
                  Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(22),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.arrow_back_ios),
                      color: Colors.white,
                      iconSize: 18,
                    ),
                  ),
                  const Expanded(
                    child: Text(
                      'Select Your Seats',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                        letterSpacing: -0.4,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(width: 44), // Balance the back button
                ],
              ),

              const SizedBox(height: 16),

              // Flight information section
              _buildFlightInfo(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFlightInfo() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Flight route
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'LAX',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                  letterSpacing: -0.3,
                ),
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 12),
                child: const Icon(
                  Icons.arrow_forward,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const Text(
                'NRT',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                  letterSpacing: -0.3,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Flight details row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildFlightDetail(
                'Japan Airlines',
                Icons.flight,
              ),
              Container(
                width: 1,
                height: 24,
                color: Colors.white.withOpacity(0.3),
              ),
              _buildFlightDetail(
                'Dec 15, 2024',
                Icons.calendar_today,
              ),
              Container(
                width: 1,
                height: 24,
                color: Colors.white.withOpacity(0.3),
              ),
              _buildFlightDetail(
                'Boeing 787',
                Icons.airplanemode_active,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFlightDetail(String text, IconData icon) {
    return Expanded(
      child: Column(
        children: [
          Icon(
            icon,
            color: Colors.white.withOpacity(0.8),
            size: 16,
          ),
          const SizedBox(height: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w600,
              color: Colors.white.withOpacity(0.9),
              letterSpacing: 0.2,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildAircraftLayout() {
    return Container(
      // Reduced top margin for better spacing after header optimization
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFFF8FAFC),
            const Color(0xFFF1F5F9),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
        child: Column(
          children: [
            // Enhanced Aircraft nose
            Container(
              width: 220,
              height: 50,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppTheme.primaryColor.withOpacity(0.15),
                    AppTheme.primaryColor.withOpacity(0.05),
                  ],
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(110),
                  topRight: Radius.circular(110),
                ),
                border: Border.all(
                  color: AppTheme.primaryColor.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: const Center(
                child: Icon(
                  Icons.flight,
                  color: AppTheme.primaryColor,
                  size: 28,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Enhanced seat layout indicator
            Container(
              width: 220,
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.7),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.borderColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Text('A',
                      style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimaryColor,
                          letterSpacing: 0.5)),
                  Text('B',
                      style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimaryColor,
                          letterSpacing: 0.5)),
                  Text('C',
                      style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimaryColor,
                          letterSpacing: 0.5)),
                  SizedBox(width: 24), // Enhanced Aisle
                  Text('D',
                      style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimaryColor,
                          letterSpacing: 0.5)),
                  Text('E',
                      style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimaryColor,
                          letterSpacing: 0.5)),
                  Text('F',
                      style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimaryColor,
                          letterSpacing: 0.5)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSeatMap() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _seatMap.length,
      itemBuilder: (context, rowIndex) {
        final row = _seatMap[rowIndex];
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              // Row number
              SizedBox(
                width: 30,
                child: Text(
                  '${rowIndex + 1}',
                  style: const TextStyle(
                    fontSize: AppTheme.fontSizeXs,
                    color: AppTheme.textSecondaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              // Seats
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // Left side (A, B, C)
                    ...row
                        .take(3)
                        .map((seat) => Flexible(child: _buildSeat(seat))),

                    // Aisle
                    const SizedBox(width: 20),

                    // Right side (D, E, F)
                    ...row
                        .skip(3)
                        .map((seat) => Flexible(child: _buildSeat(seat))),
                  ],
                ),
              ),

              // Row number (right)
              SizedBox(
                width: 30,
                child: Text(
                  '${rowIndex + 1}',
                  style: const TextStyle(
                    fontSize: AppTheme.fontSizeXs,
                    color: AppTheme.textSecondaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSeat(Seat seat) {
    final isSelected = _selectedSeatId == seat.id;
    Color seatColor;
    Color textColor;
    Color borderColor;
    Widget? seatIcon;

    switch (seat.status) {
      case SeatStatus.available:
        if (isSelected) {
          seatColor = AppTheme.primaryColor;
          textColor = Colors.white;
          borderColor = AppTheme.primaryColor;
          seatIcon = const Icon(
            Icons.check,
            size: 12,
            color: Colors.white,
          );
        } else {
          seatColor = Colors.white;
          textColor = AppTheme.textPrimaryColor;
          borderColor = AppTheme.borderColor.withOpacity(0.3);
        }
        break;
      case SeatStatus.occupied:
        seatColor = AppTheme.textSecondaryColor;
        textColor = Colors.white;
        borderColor = AppTheme.textSecondaryColor;
        seatIcon = const Icon(
          Icons.person,
          size: 12,
          color: Colors.white,
        );
        break;
      case SeatStatus.selected:
        seatColor = AppTheme.primaryColor;
        textColor = Colors.white;
        borderColor = AppTheme.primaryColor;
        seatIcon = const Icon(
          Icons.check,
          size: 12,
          color: Colors.white,
        );
        break;
      case SeatStatus.blocked:
        seatColor = AppTheme.borderColor.withOpacity(0.5);
        textColor = AppTheme.textSecondaryColor;
        borderColor = AppTheme.borderColor;
        break;
    }

    // Override colors for exit row seats
    if (seat.type == SeatType.exit) {
      borderColor = Colors.orange;
    }

    return GestureDetector(
      onTap: () => _handleSeatSelection(seat.id),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 36,
        height: 36,
        margin: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: seatColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: borderColor,
            width: seat.type == SeatType.exit ? 2 : 1.5,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: AppTheme.primaryColor.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
        ),
        child: Stack(
          children: [
            // Seat letter
            if (seatIcon == null)
              Center(
                child: Text(
                  seat.column,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: textColor,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
            // Seat icon (for selected/occupied states)
            if (seatIcon != null) Center(child: seatIcon),
            // Exit row indicator
            if (seat.type == SeatType.exit &&
                !isSelected &&
                seat.status == SeatStatus.available)
              Positioned(
                top: 2,
                right: 2,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 1,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegend() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.borderColor.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Seat Legend',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
              letterSpacing: -0.2,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildLegendItem(
                Colors.white,
                'Available',
                AppTheme.textPrimaryColor,
                AppTheme.borderColor.withOpacity(0.3),
              ),
              _buildLegendItem(
                AppTheme.textSecondaryColor,
                'Occupied',
                Colors.white,
                AppTheme.textSecondaryColor,
                icon: Icons.person,
              ),
              _buildLegendItem(
                AppTheme.primaryColor,
                'Selected',
                Colors.white,
                AppTheme.primaryColor,
                icon: Icons.check,
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Smart skip functionality section
          _buildSkipSection(),
        ],
      ),
    );
  }

  Widget _buildSkipSection() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      child: _showSkipButton
          ? Row(
              children: [
                // Exit Row button (reduced size when skip is visible)
                Expanded(
                  flex: 3,
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.orange.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(5),
                            border:
                                Border.all(color: Colors.orange, width: 1.5),
                          ),
                          child: Stack(
                            children: [
                              const Center(
                                child: Text(
                                  'A',
                                  style: TextStyle(
                                    fontSize: 8,
                                    fontWeight: FontWeight.w600,
                                    color: AppTheme.textPrimaryColor,
                                  ),
                                ),
                              ),
                              Positioned(
                                top: 1,
                                right: 1,
                                child: Container(
                                  width: 4,
                                  height: 4,
                                  decoration: BoxDecoration(
                                    color: Colors.orange,
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.white,
                                      width: 0.3,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Flexible(
                          child: Text(
                            'Exit Row (+\$25)',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.orange,
                              letterSpacing: -0.1,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(width: 12),

                // Skip button with black color scheme
                Expanded(
                  flex: 2,
                  child: Container(
                    height: 48,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.black87,
                          Colors.black.withOpacity(0.9),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.4),
                          blurRadius: 12,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: ElevatedButton(
                      onPressed:
                          _isLoadingSeat ? null : _handleSkipSeatSelection,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        foregroundColor: Colors.white,
                        shadowColor: Colors.transparent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child: const Text(
                        'Skip',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w700,
                          color: Colors.white,
                          letterSpacing: -0.1,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            )
          : Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.orange.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.orange, width: 2),
                    ),
                    child: Stack(
                      children: [
                        const Center(
                          child: Text(
                            'A',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.textPrimaryColor,
                            ),
                          ),
                        ),
                        Positioned(
                          top: 1,
                          right: 1,
                          child: Container(
                            width: 6,
                            height: 6,
                            decoration: BoxDecoration(
                              color: Colors.orange,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Colors.white,
                                width: 0.5,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'Exit Row (+\$25)',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.orange,
                      letterSpacing: -0.1,
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildLegendItem(
    Color color,
    String label,
    Color textColor,
    Color borderColor, {
    IconData? icon,
  }) {
    return Column(
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: borderColor, width: 1.5),
          ),
          child: icon != null
              ? Icon(
                  icon,
                  size: 12,
                  color: textColor,
                )
              : Center(
                  child: Text(
                    'A',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                      color: textColor,
                    ),
                  ),
                ),
        ),
        const SizedBox(height: 6),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: AppTheme.textPrimaryColor,
            letterSpacing: -0.1,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingModal() {
    return Container(
      color: Colors.black.withOpacity(0.6),
      child: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 40),
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Loading animation
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppTheme.primaryColor,
                      AppTheme.primaryColor.withOpacity(0.7),
                    ],
                  ),
                  shape: BoxShape.circle,
                ),
                child: const Center(
                  child: SizedBox(
                    width: 30,
                    height: 30,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      strokeWidth: 3,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Loading title
              const Text(
                'Finding Your Seat',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  color: AppTheme.textPrimaryColor,
                  letterSpacing: -0.3,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 12),

              // Loading description
              Text(
                'We\'re selecting the best available seat for you...',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textSecondaryColor.withOpacity(0.8),
                  letterSpacing: 0.1,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContinueButton() {
    final selectedSeat = _findSeatById(_selectedSeatId!);
    final extraCost = selectedSeat?.price ?? 0.0;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.white.withOpacity(0.95),
            Colors.white,
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Selected seat summary
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppTheme.primaryColor.withOpacity(0.05),
                      AppTheme.primaryColor.withOpacity(0.02),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.primaryColor.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Selected Seat',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: AppTheme.textSecondaryColor,
                            letterSpacing: 0.2,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _selectedSeatId!,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                            color: AppTheme.textPrimaryColor,
                            letterSpacing: -0.3,
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        const Text(
                          'Seat Fee',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: AppTheme.textSecondaryColor,
                            letterSpacing: 0.2,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          extraCost > 0
                              ? '+\$${extraCost.toStringAsFixed(0)}'
                              : 'Free',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                            color: extraCost > 0
                                ? Colors.orange.shade700
                                : Colors.green.shade700,
                            letterSpacing: -0.3,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Continue button
              Container(
                width: double.infinity,
                height: 56,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppTheme.primaryColor,
                      AppTheme.primaryColor.withOpacity(0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryColor.withOpacity(0.4),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: ElevatedButton(
                  onPressed: _handleContinue,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: Colors.white,
                    shadowColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 0,
                  ),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.arrow_forward,
                        size: 20,
                        color: Colors.white,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Continue to Payment',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                          letterSpacing: -0.2,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
