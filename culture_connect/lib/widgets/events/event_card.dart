import 'package:flutter/material.dart';
import 'package:culture_connect/theme/kaia_design_tokens.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Event card component that matches the design from rn_home_screen.dart
/// Supports both grid and list modes with proper responsive design
class EventCard extends StatelessWidget {
  final String id;
  final String title;
  final String date;
  final String time;
  final String location;
  final String imageUrl;
  final String category;
  final String price;
  final int attendees;
  final bool isBookmarked;
  final bool isGridMode;
  final VoidCallback onPressed;
  final VoidCallback? onFavoritePressed;

  const EventCard({
    super.key,
    required this.id,
    required this.title,
    required this.date,
    required this.time,
    required this.location,
    required this.imageUrl,
    required this.category,
    required this.price,
    required this.attendees,
    required this.isBookmarked,
    required this.isGridMode,
    required this.onPressed,
    this.onFavoritePressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: isGridMode ? double.infinity : 600,
          // Reduced height constraint for better grid optimization
          maxHeight: isGridMode ? 300 : double.infinity,
        ),
        child: Card(
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Event Image with Category Badge and Bookmark
                _buildImageSection(),

                // Content Section
                isGridMode
                    ? Flexible(child: _buildContentSection())
                    : _buildContentSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Stack(
      children: [
        // Event Image
        ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          child: Image.network(
            imageUrl,
            height: isGridMode ? 140 : 180,
            width: double.infinity,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                height: isGridMode ? 140 : 180,
                color: KaiaDesignTokens.neutralGray300,
                child: const Icon(
                  Icons.image_not_supported,
                  color: KaiaDesignTokens.neutralGray500,
                ),
              );
            },
          ),
        ),

        // Category Badge - Enhanced contrast for better readability
        Positioned(
          top: 12,
          left: 12,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              // Solid background with high opacity for better contrast
              color: AppTheme.primaryColor.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(12),
              // Add subtle drop shadow for better visibility over all image types
              boxShadow: const [
                BoxShadow(
                  color: AppTheme.shadowColor,
                  offset: Offset(0, 2),
                  blurRadius: 4,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Text(
              category,
              style: TextStyle(
                fontSize: isGridMode ? 9 : 10, // Reduced for grid mode
                fontWeight: FontWeight.bold,
                // White text for maximum contrast against dark background
                color: AppTheme.textInverse,
              ),
            ),
          ),
        ),

        // Bookmark Icon
        Positioned(
          top: 8,
          right: 8,
          child: GestureDetector(
            onTap: onFavoritePressed,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.9),
                shape: BoxShape.circle,
              ),
              child: Icon(
                isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                size: 20,
                color: isBookmarked
                    ? AppTheme.primaryColor
                    : AppTheme.textSecondaryColor,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContentSection() {
    // Optimized padding for grid mode
    final contentPadding = isGridMode
        ? const EdgeInsets.all(12) // Reduced from 16 to 12 for grid mode
        : const EdgeInsets.all(16);

    return Padding(
      padding: contentPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Event Title - Optimized font size for grid mode
          Flexible(
            child: Text(
              title,
              style: TextStyle(
                fontSize: isGridMode ? 14 : 16, // Reduced for grid mode
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
                height: 1.2, // Improved line height for better spacing
              ),
              maxLines: isGridMode ? 2 : 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          SizedBox(height: isGridMode ? 6 : 8), // Reduced spacing for grid mode

          // Date and Time Row - Optimized for grid mode
          Flexible(
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: isGridMode ? 12 : 14, // Smaller icon for grid mode
                  color: AppTheme.textSecondaryColor,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    '$date • $time',
                    style: TextStyle(
                      fontSize: isGridMode ? 11 : 12, // Reduced for grid mode
                      color: AppTheme.textSecondaryColor,
                      height: 1.2, // Better line height
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: isGridMode ? 3 : 4), // Reduced spacing for grid mode

          // Location Row - Optimized for grid mode
          Flexible(
            child: Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: isGridMode ? 12 : 14, // Smaller icon for grid mode
                  color: AppTheme.textSecondaryColor,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    location,
                    style: TextStyle(
                      fontSize: isGridMode ? 11 : 12, // Reduced for grid mode
                      color: AppTheme.textSecondaryColor,
                      height: 1.2, // Better line height
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: isGridMode ? 6 : 8), // Reduced spacing for grid mode

          // Price and Attendees Row - Optimized for grid mode
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(
                  price,
                  style: TextStyle(
                    fontSize: isGridMode ? 14 : 16, // Reduced for grid mode
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
              Flexible(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.people,
                      size: isGridMode ? 12 : 14, // Smaller icon for grid mode
                      color: AppTheme.textSecondaryColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '$attendees',
                      style: TextStyle(
                        fontSize: isGridMode ? 10 : 12, // Reduced for grid mode
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
