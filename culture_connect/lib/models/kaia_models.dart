import 'package:flutter/material.dart';

/// Model for chat messages in Kaia AI screen
class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
  });
}

/// Model for quick action buttons in Kaia AI screen
class KaiaQuickAction {
  final String id;
  final String title;
  final String description;
  final IconData icon;
  final String prompt;
  final LinearGradient gradient;

  const KaiaQuickAction({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.prompt,
    required this.gradient,
  });
}
