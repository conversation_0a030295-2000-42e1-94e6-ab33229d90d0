// Flutter imports
import 'package:flutter/foundation.dart';

// Project imports
import 'package:culture_connect/models/travel/visa/visa_models.dart';

/// Status of an escrow payment
enum EscrowPaymentStatus {
  /// Payment is being initialized
  initializing,
  
  /// Funds are held in escrow, waiting for service delivery
  fundsHeld,
  
  /// Service provider is working on the service
  serviceInProgress,
  
  /// Service has been completed, awaiting client approval
  awaitingApproval,
  
  /// Client has approved service completion
  approved,
  
  /// Funds have been released to service provider
  fundsReleased,
  
  /// Payment has been refunded to client
  refunded,
  
  /// Payment is in dispute
  disputed,
  
  /// Escrow has been cancelled
  cancelled,
  
  /// Escrow has expired
  expired,
}

/// Types of escrow release conditions
enum EscrowReleaseCondition {
  /// Manual approval by client
  clientApproval,
  
  /// Automatic release after service completion
  serviceCompletion,
  
  /// Time-based release (after specified duration)
  timeBasedRelease,
  
  /// Milestone-based release
  milestoneCompletion,
  
  /// Document submission completion
  documentSubmission,
  
  /// Embassy application submission
  embassySubmission,
}

/// Represents an escrow payment for visa services
@immutable
class EscrowPayment {
  /// Unique identifier for the escrow payment
  final String id;
  
  /// Associated visa service booking
  final String bookingId;
  
  /// Client user ID
  final String clientId;
  
  /// Service provider ID
  final String providerId;
  
  /// Total amount held in escrow
  final double amount;
  
  /// Currency of the payment
  final String currency;
  
  /// Current status of the escrow
  final EscrowPaymentStatus status;
  
  /// Release conditions for the escrow
  final List<EscrowReleaseCondition> releaseConditions;
  
  /// Percentage of funds to release at each milestone
  final Map<EscrowReleaseCondition, double> releasePercentages;
  
  /// Payment reference from payment provider
  final String? paymentReference;
  
  /// Escrow reference for tracking
  final String escrowReference;
  
  /// Expected service completion date
  final DateTime expectedCompletionDate;
  
  /// Actual service completion date
  final DateTime? actualCompletionDate;
  
  /// Date when funds will be auto-released if no action taken
  final DateTime autoReleaseDate;
  
  /// Client approval status
  final bool? clientApproved;
  
  /// Client approval date
  final DateTime? clientApprovalDate;
  
  /// Service provider confirmation of completion
  final bool? providerConfirmedCompletion;
  
  /// Provider confirmation date
  final DateTime? providerConfirmationDate;
  
  /// Dispute information if any
  final EscrowDispute? dispute;
  
  /// Milestones for the service
  final List<EscrowMilestone> milestones;
  
  /// Notes or comments about the escrow
  final String? notes;
  
  /// When the escrow was created
  final DateTime createdAt;
  
  /// When the escrow was last updated
  final DateTime updatedAt;

  const EscrowPayment({
    required this.id,
    required this.bookingId,
    required this.clientId,
    required this.providerId,
    required this.amount,
    required this.currency,
    required this.status,
    required this.releaseConditions,
    required this.releasePercentages,
    this.paymentReference,
    required this.escrowReference,
    required this.expectedCompletionDate,
    this.actualCompletionDate,
    required this.autoReleaseDate,
    this.clientApproved,
    this.clientApprovalDate,
    this.providerConfirmedCompletion,
    this.providerConfirmationDate,
    this.dispute,
    required this.milestones,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Creates a copy of this escrow payment with the given fields replaced
  EscrowPayment copyWith({
    String? id,
    String? bookingId,
    String? clientId,
    String? providerId,
    double? amount,
    String? currency,
    EscrowPaymentStatus? status,
    List<EscrowReleaseCondition>? releaseConditions,
    Map<EscrowReleaseCondition, double>? releasePercentages,
    String? paymentReference,
    String? escrowReference,
    DateTime? expectedCompletionDate,
    DateTime? actualCompletionDate,
    DateTime? autoReleaseDate,
    bool? clientApproved,
    DateTime? clientApprovalDate,
    bool? providerConfirmedCompletion,
    DateTime? providerConfirmationDate,
    EscrowDispute? dispute,
    List<EscrowMilestone>? milestones,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EscrowPayment(
      id: id ?? this.id,
      bookingId: bookingId ?? this.bookingId,
      clientId: clientId ?? this.clientId,
      providerId: providerId ?? this.providerId,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      status: status ?? this.status,
      releaseConditions: releaseConditions ?? this.releaseConditions,
      releasePercentages: releasePercentages ?? this.releasePercentages,
      paymentReference: paymentReference ?? this.paymentReference,
      escrowReference: escrowReference ?? this.escrowReference,
      expectedCompletionDate: expectedCompletionDate ?? this.expectedCompletionDate,
      actualCompletionDate: actualCompletionDate ?? this.actualCompletionDate,
      autoReleaseDate: autoReleaseDate ?? this.autoReleaseDate,
      clientApproved: clientApproved ?? this.clientApproved,
      clientApprovalDate: clientApprovalDate ?? this.clientApprovalDate,
      providerConfirmedCompletion: providerConfirmedCompletion ?? this.providerConfirmedCompletion,
      providerConfirmationDate: providerConfirmationDate ?? this.providerConfirmationDate,
      dispute: dispute ?? this.dispute,
      milestones: milestones ?? this.milestones,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Calculate the percentage of funds that can be released based on current status
  double get releasablePercentage {
    double total = 0.0;
    
    for (final condition in releaseConditions) {
      final percentage = releasePercentages[condition] ?? 0.0;
      
      switch (condition) {
        case EscrowReleaseCondition.clientApproval:
          if (clientApproved == true) total += percentage;
          break;
        case EscrowReleaseCondition.serviceCompletion:
          if (providerConfirmedCompletion == true) total += percentage;
          break;
        case EscrowReleaseCondition.timeBasedRelease:
          if (DateTime.now().isAfter(autoReleaseDate)) total += percentage;
          break;
        case EscrowReleaseCondition.milestoneCompletion:
          final completedMilestones = milestones.where((m) => m.isCompleted).length;
          final totalMilestones = milestones.length;
          if (totalMilestones > 0) {
            total += (completedMilestones / totalMilestones) * percentage;
          }
          break;
        case EscrowReleaseCondition.documentSubmission:
        case EscrowReleaseCondition.embassySubmission:
          // These would be determined by external service status
          break;
      }
    }
    
    return total.clamp(0.0, 100.0);
  }

  /// Check if the escrow can be released
  bool get canRelease {
    return releasablePercentage >= 100.0 || 
           status == EscrowPaymentStatus.approved ||
           DateTime.now().isAfter(autoReleaseDate);
  }

  /// Check if the escrow is in a final state
  bool get isFinal {
    return [
      EscrowPaymentStatus.fundsReleased,
      EscrowPaymentStatus.refunded,
      EscrowPaymentStatus.cancelled,
      EscrowPaymentStatus.expired,
    ].contains(status);
  }
}

/// Represents a milestone in the escrow payment process
@immutable
class EscrowMilestone {
  /// Unique identifier for the milestone
  final String id;
  
  /// Name of the milestone
  final String name;
  
  /// Description of what needs to be completed
  final String description;
  
  /// Percentage of total payment this milestone represents
  final double percentage;
  
  /// Expected completion date
  final DateTime expectedDate;
  
  /// Actual completion date
  final DateTime? completedDate;
  
  /// Whether this milestone is completed
  final bool isCompleted;
  
  /// Whether this milestone is required for payment release
  final bool isRequired;
  
  /// Order of this milestone in the process
  final int order;

  const EscrowMilestone({
    required this.id,
    required this.name,
    required this.description,
    required this.percentage,
    required this.expectedDate,
    this.completedDate,
    required this.isCompleted,
    required this.isRequired,
    required this.order,
  });

  /// Creates a copy of this milestone with the given fields replaced
  EscrowMilestone copyWith({
    String? id,
    String? name,
    String? description,
    double? percentage,
    DateTime? expectedDate,
    DateTime? completedDate,
    bool? isCompleted,
    bool? isRequired,
    int? order,
  }) {
    return EscrowMilestone(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      percentage: percentage ?? this.percentage,
      expectedDate: expectedDate ?? this.expectedDate,
      completedDate: completedDate ?? this.completedDate,
      isCompleted: isCompleted ?? this.isCompleted,
      isRequired: isRequired ?? this.isRequired,
      order: order ?? this.order,
    );
  }
}

/// Represents a dispute in an escrow payment
@immutable
class EscrowDispute {
  /// Unique identifier for the dispute
  final String id;
  
  /// Who initiated the dispute
  final String initiatedBy;
  
  /// Reason for the dispute
  final String reason;
  
  /// Detailed description of the dispute
  final String description;
  
  /// Current status of the dispute
  final String status;
  
  /// When the dispute was created
  final DateTime createdAt;
  
  /// When the dispute was resolved
  final DateTime? resolvedAt;
  
  /// Resolution details
  final String? resolution;

  const EscrowDispute({
    required this.id,
    required this.initiatedBy,
    required this.reason,
    required this.description,
    required this.status,
    required this.createdAt,
    this.resolvedAt,
    this.resolution,
  });
}
