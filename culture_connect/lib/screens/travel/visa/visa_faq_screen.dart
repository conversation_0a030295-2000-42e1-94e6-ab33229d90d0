// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/visa/self_service_models.dart';
import 'package:culture_connect/services/travel/visa/self_service_visa_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// Screen for displaying visa FAQs
class VisaFAQScreen extends ConsumerStatefulWidget {
  const VisaFAQScreen({
    super.key,
    this.country,
  });

  final String? country;

  @override
  ConsumerState<VisaFAQScreen> createState() => _VisaFAQScreenState();
}

class _VisaFAQScreenState extends ConsumerState<VisaFAQScreen> {
  List<VisaFAQ> _faqs = [];
  List<VisaFAQ> _filteredFAQs = [];
  bool _isLoading = false;
  String? _errorMessage;
  String _searchQuery = '';
  String? _selectedCategory;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadFAQs();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadFAQs() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final selfServiceService = ref.read(selfServiceVisaServiceProvider);
      final faqs = await selfServiceService.getFAQs(
        country: widget.country,
      );

      if (mounted) {
        setState(() {
          _faqs = faqs;
          _filteredFAQs = faqs;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to load FAQs: $e';
        });
      }
    }
  }

  void _filterFAQs() {
    setState(() {
      _filteredFAQs = _faqs.where((faq) {
        final matchesSearch = _searchQuery.isEmpty ||
            faq.question.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            faq.answer.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            faq.tags.any((tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()));

        final matchesCategory = _selectedCategory == null ||
            faq.category == _selectedCategory;

        return matchesSearch && matchesCategory;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        title: Text(
          'Visa FAQ',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: AppTheme.fontWeightBold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
      ),
      body: _buildBody(theme),
    );
  }

  Widget _buildBody(ThemeData theme) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: AppTheme.primaryColor,
            ),
            SizedBox(height: AppTheme.spacingMd),
            Text(
              'Loading FAQs...',
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
                fontWeight: AppTheme.fontWeightMedium,
              ),
            ),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return ErrorView(
        error: _errorMessage!,
        onRetry: _loadFAQs,
      );
    }

    return RefreshIndicator(
      onRefresh: _loadFAQs,
      child: Column(
        children: [
          _buildSearchAndFilter(theme),
          Expanded(
            child: _filteredFAQs.isNotEmpty
                ? _buildFAQList(theme)
                : _buildEmptyState(theme),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter(ThemeData theme) {
    final categories = _faqs.map((faq) => faq.category).toSet().toList();

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      color: AppTheme.white,
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search FAQs...',
              prefixIcon: const Icon(Icons.search, color: AppTheme.primaryColor),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                        _filterFAQs();
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
                borderSide: const BorderSide(color: AppTheme.borderColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
                borderSide: const BorderSide(color: AppTheme.primaryColor),
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              _filterFAQs();
            },
          ),
          const SizedBox(height: AppTheme.spacingMd),
          // Category filter
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildCategoryChip('All', null, theme),
                const SizedBox(width: AppTheme.spacingSm),
                ...categories.map((category) => Padding(
                  padding: const EdgeInsets.only(right: AppTheme.spacingSm),
                  child: _buildCategoryChip(category, category, theme),
                )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(String label, String? category, ThemeData theme) {
    final isSelected = _selectedCategory == category;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedCategory = selected ? category : null;
        });
        _filterFAQs();
      },
      backgroundColor: AppTheme.white,
      selectedColor: AppTheme.primaryColor.withValues(alpha: 0.1),
      checkmarkColor: AppTheme.primaryColor,
      labelStyle: TextStyle(
        color: isSelected ? AppTheme.primaryColor : AppTheme.textSecondaryColor,
        fontWeight: isSelected ? AppTheme.fontWeightSemibold : AppTheme.fontWeightMedium,
      ),
      side: BorderSide(
        color: isSelected ? AppTheme.primaryColor : AppTheme.borderColor,
      ),
    );
  }

  Widget _buildFAQList(ThemeData theme) {
    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      itemCount: _filteredFAQs.length,
      itemBuilder: (context, index) {
        final faq = _filteredFAQs[index];
        return _buildFAQItem(faq, theme);
      },
    );
  }

  Widget _buildFAQItem(VisaFAQ faq, ThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMd),
      child: Material(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        elevation: 2,
        child: ExpansionTile(
          title: Text(
            faq.question,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: AppTheme.fontWeightBold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          subtitle: Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingSm,
                  vertical: AppTheme.spacingXs,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Text(
                  faq.category,
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontSize: AppTheme.fontSizeXs,
                    fontWeight: AppTheme.fontWeightSemibold,
                  ),
                ),
              ),
              const SizedBox(width: AppTheme.spacingSm),
              if (faq.isFeatured) ...[
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacingSm,
                    vertical: AppTheme.spacingXs,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.warningColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                  ),
                  child: Text(
                    'Featured',
                    style: TextStyle(
                      color: AppTheme.warningColor,
                      fontSize: AppTheme.fontSizeXs,
                      fontWeight: AppTheme.fontWeightSemibold,
                    ),
                  ),
                ),
              ],
            ],
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(AppTheme.spacingMd),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    faq.answer,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.textSecondaryColor,
                      height: 1.5,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingMd),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.thumb_up,
                            size: 16,
                            color: AppTheme.textTertiaryColor,
                          ),
                          const SizedBox(width: AppTheme.spacingXs),
                          Text(
                            '${faq.helpfulCount} found this helpful',
                            style: TextStyle(
                              fontSize: AppTheme.fontSizeXs,
                              color: AppTheme.textTertiaryColor,
                            ),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          TextButton(
                            onPressed: () => _markHelpful(faq),
                            child: const Text('Helpful'),
                          ),
                          TextButton(
                            onPressed: () => _shareAnswer(faq),
                            child: const Text('Share'),
                          ),
                        ],
                      ),
                    ],
                  ),
                  if (faq.tags.isNotEmpty) ...[
                    const SizedBox(height: AppTheme.spacingSm),
                    Wrap(
                      spacing: AppTheme.spacingXs,
                      children: faq.tags.map((tag) => Chip(
                        label: Text(
                          tag,
                          style: TextStyle(
                            fontSize: AppTheme.fontSizeXs,
                            color: AppTheme.textTertiaryColor,
                          ),
                        ),
                        backgroundColor: AppTheme.borderColor.withValues(alpha: 0.3),
                        side: BorderSide.none,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      )).toList(),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingXl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingXl),
              decoration: BoxDecoration(
                color: AppTheme.borderColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.help_outline,
                size: 64,
                color: AppTheme.borderColor,
              ),
            ),
            const SizedBox(height: AppTheme.spacingLg),
            Text(
              _searchQuery.isNotEmpty || _selectedCategory != null
                  ? 'No FAQs Found'
                  : 'No FAQs Available',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: AppTheme.fontWeightBold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSm),
            Text(
              _searchQuery.isNotEmpty || _selectedCategory != null
                  ? 'Try adjusting your search or filter criteria.'
                  : 'FAQs will be loaded here when available.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            if (_searchQuery.isNotEmpty || _selectedCategory != null) ...[
              const SizedBox(height: AppTheme.spacingLg),
              ElevatedButton(
                onPressed: () {
                  _searchController.clear();
                  setState(() {
                    _searchQuery = '';
                    _selectedCategory = null;
                  });
                  _filterFAQs();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: AppTheme.white,
                ),
                child: const Text('Clear Filters'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _markHelpful(VisaFAQ faq) {
    // TODO: Implement helpful marking
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Thank you for your feedback!'),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  void _shareAnswer(VisaFAQ faq) {
    // TODO: Implement sharing functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sharing "${faq.question}"...'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }
}
