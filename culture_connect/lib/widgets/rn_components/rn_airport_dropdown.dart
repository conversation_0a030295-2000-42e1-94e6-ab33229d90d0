import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Airport model for dropdown selection
class Airport {
  final String code;
  final String city;
  final String name;
  final String country;

  const Airport({
    required this.code,
    required this.city,
    required this.name,
    required this.country,
  });
}

/// Mock airports data
const List<Airport> mockAirports = [
  Airport(code: 'JFK', city: 'New York', name: 'John F. Kennedy International Airport', country: 'USA'),
  Airport(code: 'LHR', city: 'London', name: 'Heathrow Airport', country: 'UK'),
  Airport(code: 'LAX', city: 'Los Angeles', name: 'Los Angeles International Airport', country: 'USA'),
  Airport(code: 'NRT', city: 'Tokyo', name: 'Narita International Airport', country: 'Japan'),
  Airport(code: 'CDG', city: 'Paris', name: 'Charles <PERSON>le Airport', country: 'France'),
  Airport(code: 'DXB', city: 'Dubai', name: 'Dubai International Airport', country: 'UAE'),
  Airport(code: 'SIN', city: 'Singapore', name: 'Singapore Changi Airport', country: 'Singapore'),
  Airport(code: 'HKG', city: 'Hong Kong', name: 'Hong Kong International Airport', country: 'Hong Kong'),
];

/// Pixel-perfect React Native AirportDropdown replica
class RNAirportDropdown extends StatefulWidget {
  final bool visible;
  final VoidCallback onClose;
  final Function(Airport) onSelectAirport;
  final String placeholder;
  final String initialValue;
  final String title;

  const RNAirportDropdown({
    super.key,
    required this.visible,
    required this.onClose,
    required this.onSelectAirport,
    this.placeholder = 'Search airport...',
    this.initialValue = '',
    this.title = 'Select Airport',
  });

  @override
  State<RNAirportDropdown> createState() => _RNAirportDropdownState();
}

class _RNAirportDropdownState extends State<RNAirportDropdown> {
  final TextEditingController _searchController = TextEditingController();
  List<Airport> _filteredAirports = mockAirports;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_filterAirports);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterAirports() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredAirports = mockAirports.where((airport) {
        return airport.city.toLowerCase().contains(query) ||
               airport.code.toLowerCase().contains(query) ||
               airport.name.toLowerCase().contains(query);
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.visible) return const SizedBox.shrink();

    return Material(
      color: Colors.black54,
      child: SafeArea(
        child: Container(
          color: AppTheme.backgroundColor,
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: AppTheme.backgroundColor,
                  border: Border(
                    bottom: BorderSide(color: AppTheme.borderColor, width: 1),
                  ),
                ),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: widget.onClose,
                      icon: const Icon(Icons.close),
                      color: AppTheme.textPrimaryColor,
                    ),
                    Expanded(
                      child: Text(
                        widget.title,
                        style: const TextStyle(
                          fontSize: AppTheme.fontSizeLg,
                          fontWeight: AppTheme.fontWeightBold,
                          color: AppTheme.textPrimaryColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(width: 48), // Balance the close button
                  ],
                ),
              ),
              
              // Search Bar
              Container(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: widget.placeholder,
                    prefixIcon: const Icon(Icons.search, color: AppTheme.textSecondaryColor),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: AppTheme.borderColor),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: AppTheme.borderColor),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: AppTheme.primaryColor),
                    ),
                    filled: true,
                    fillColor: AppTheme.backgroundColor,
                  ),
                ),
              ),
              
              // Airport List
              Expanded(
                child: ListView.builder(
                  itemCount: _filteredAirports.length,
                  itemBuilder: (context, index) {
                    final airport = _filteredAirports[index];
                    return ListTile(
                      onTap: () {
                        widget.onSelectAirport(airport);
                        widget.onClose();
                      },
                      leading: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.flight_takeoff,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      title: Text(
                        '${airport.city} (${airport.code})',
                        style: const TextStyle(
                          fontSize: AppTheme.fontSizeMd,
                          fontWeight: AppTheme.fontWeightSemibold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      subtitle: Text(
                        airport.name,
                        style: const TextStyle(
                          fontSize: AppTheme.fontSizeSm,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      trailing: const Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: AppTheme.textSecondaryColor,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
