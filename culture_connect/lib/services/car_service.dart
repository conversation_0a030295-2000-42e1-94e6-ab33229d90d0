import 'package:culture_connect/models/car.dart';

/// Car service providing mock data matching React Native implementation
class CarService {
  static const List<Car> _cars = [
    Car(
      id: '1',
      name: 'Mazda CX-3',
      brand: 'Mazda',
      model: 'CX-3',
      category: 'SUV',
      pricePerDay: 200,
      rating: 5.0,
      reviewCount: 128,
      imageUrl:
          'https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      features: [
        'GPS Navigation',
        'Bluetooth',
        'USB Charging',
        'Backup Camera'
      ],
      fuelType: 'Petrol',
      transmission: 'Automatic',
      seats: 5,
      doors: 4,
      airConditioning: true,
      available: true,
      location: 'Downtown',
      description:
          'Compact SUV perfect for city driving and weekend getaways. Features modern technology and excellent fuel efficiency.',
    ),
    Car(
      id: '2',
      name: 'Buick Envision',
      brand: 'Buick',
      model: 'Envision',
      category: 'SUV',
      pricePerDay: 200,
      rating: 4.8,
      reviewCount: 95,
      imageUrl:
          'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      features: [
        'Premium Sound',
        'Heated Seats',
        'Panoramic Sunroof',
        'Advanced Safety'
      ],
      fuelType: 'Petrol',
      transmission: 'Automatic',
      seats: 5,
      doors: 4,
      airConditioning: true,
      available: true,
      location: 'Airport',
      description:
          'Luxury SUV with premium features and spacious interior. Perfect for comfortable long-distance travel.',
    ),
    Car(
      id: '3',
      name: 'Mazda 3',
      brand: 'Mazda',
      model: '3',
      category: 'Hatchback',
      pricePerDay: 100,
      rating: 4.8,
      reviewCount: 203,
      imageUrl:
          'https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      features: [
        'Sport Mode',
        'Premium Interior',
        'Advanced Infotainment',
        'LED Headlights'
      ],
      fuelType: 'Petrol',
      transmission: 'Manual',
      seats: 5,
      doors: 4,
      airConditioning: true,
      available: true,
      location: 'City Center',
      description:
          'Stylish hatchback with sporty performance and premium interior. Great for urban adventures.',
    ),
    Car(
      id: '4',
      name: 'Toyota Corolla',
      brand: 'Toyota',
      model: 'Corolla',
      category: 'Hatchback',
      pricePerDay: 110,
      rating: 4.9,
      reviewCount: 156,
      imageUrl:
          'https://images.unsplash.com/photo-1621007947382-bb3c3994e3fb?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      features: [
        'Hybrid Engine',
        'Toyota Safety Sense',
        'Wireless Charging',
        'Smart Entry'
      ],
      fuelType: 'Hybrid',
      transmission: 'Automatic',
      seats: 5,
      doors: 4,
      airConditioning: true,
      available: true,
      location: 'Suburbs',
      description:
          'Reliable and fuel-efficient hybrid vehicle. Perfect for eco-conscious travelers.',
    ),
    Car(
      id: '5',
      name: 'BMW 3 Series',
      brand: 'BMW',
      model: '3 Series',
      category: 'Sedan',
      pricePerDay: 350,
      rating: 4.9,
      reviewCount: 87,
      imageUrl:
          'https://images.unsplash.com/photo-1555215695-3004980ad54e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      features: [
        'Luxury Interior',
        'Performance Package',
        'Premium Sound',
        'Driver Assistance'
      ],
      fuelType: 'Petrol',
      transmission: 'Automatic',
      seats: 5,
      doors: 4,
      airConditioning: true,
      available: true,
      location: 'Premium Location',
      description:
          'Luxury sedan with exceptional performance and premium features. Experience driving excellence.',
    ),
    Car(
      id: '6',
      name: 'Tesla Model 3',
      brand: 'Tesla',
      model: 'Model 3',
      category: 'Electric',
      pricePerDay: 280,
      rating: 4.7,
      reviewCount: 142,
      imageUrl:
          'https://images.unsplash.com/photo-1560958089-b8a1929cea89?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      features: [
        'Autopilot',
        'Supercharging',
        'Premium Connectivity',
        'Over-the-Air Updates'
      ],
      fuelType: 'Electric',
      transmission: 'Automatic',
      seats: 5,
      doors: 4,
      airConditioning: true,
      available: true,
      location: 'Tech District',
      description:
          'Revolutionary electric vehicle with cutting-edge technology and zero emissions.',
    ),
    Car(
      id: '7',
      name: 'Audi A4',
      brand: 'Audi',
      model: 'A4',
      category: 'Sedan',
      pricePerDay: 320,
      rating: 4.8,
      reviewCount: 76,
      imageUrl:
          'https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      features: [
        'Quattro AWD',
        'Virtual Cockpit',
        'Premium Plus',
        'Bang & Olufsen Sound'
      ],
      fuelType: 'Petrol',
      transmission: 'Automatic',
      seats: 5,
      doors: 4,
      airConditioning: true,
      available: true,
      location: 'Luxury District',
      description:
          'Premium sedan with sophisticated design and advanced technology features.',
    ),
    Car(
      id: '8',
      name: 'Mercedes-Benz C-Class',
      brand: 'Mercedes-Benz',
      model: 'C-Class',
      category: 'Luxury',
      pricePerDay: 450,
      rating: 4.9,
      reviewCount: 63,
      imageUrl:
          'https://images.unsplash.com/photo-1618843479313-40f8afb4b4d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      features: [
        'MBUX Infotainment',
        'AMG Line',
        'Burmester Sound',
        'Active Safety'
      ],
      fuelType: 'Petrol',
      transmission: 'Automatic',
      seats: 5,
      doors: 4,
      airConditioning: true,
      available: true,
      location: 'Premium Location',
      description:
          'Luxury sedan with exceptional comfort and cutting-edge technology.',
    ),
    Car(
      id: '9',
      name: 'Lexus ES',
      brand: 'Lexus',
      model: 'ES',
      category: 'Luxury',
      pricePerDay: 380,
      rating: 4.8,
      reviewCount: 91,
      imageUrl:
          'https://images.unsplash.com/photo-1609521263047-f8f205293f24?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      features: [
        'Hybrid Powertrain',
        'Mark Levinson Audio',
        'Safety System+',
        'Luxury Package'
      ],
      fuelType: 'Hybrid',
      transmission: 'Automatic',
      seats: 5,
      doors: 4,
      airConditioning: true,
      available: true,
      location: 'Executive Area',
      description:
          'Luxury hybrid sedan combining performance with exceptional fuel efficiency.',
    ),
    // Luxury Bus Section - Family-oriented luxury transportation
    Car(
      id: '10',
      name: 'Mercedes-Benz Sprinter Luxury',
      brand: 'Mercedes-Benz',
      model: 'Sprinter Luxury',
      category: 'Luxury Bus',
      pricePerDay: 850,
      rating: 4.9,
      reviewCount: 42,
      imageUrl:
          'https://images.unsplash.com/photo-1544620347-c4fd4a3d5957?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      features: [
        'Premium Leather Seating',
        'Entertainment System',
        'Climate Control',
        'WiFi',
        'Mini Bar',
        'Privacy Partition'
      ],
      fuelType: 'Diesel',
      transmission: 'Automatic',
      seats: 14,
      doors: 3,
      airConditioning: true,
      available: true,
      location: 'Premium Terminal',
      description:
          'Luxury Mercedes-Benz Sprinter perfect for family groups and special occasions. Features premium amenities and spacious interior for comfortable long-distance travel.',
    ),
    Car(
      id: '11',
      name: 'Ford Transit Executive',
      brand: 'Ford',
      model: 'Transit Executive',
      category: 'Luxury Bus',
      pricePerDay: 720,
      rating: 4.7,
      reviewCount: 38,
      imageUrl:
          'https://images.unsplash.com/photo-1570125909232-eb263c188f7e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      features: [
        'Executive Seating',
        'Conference Table',
        'Premium Sound',
        'LED Lighting',
        'USB Charging Ports',
        'Tinted Windows'
      ],
      fuelType: 'Diesel',
      transmission: 'Automatic',
      seats: 12,
      doors: 3,
      airConditioning: true,
      available: true,
      location: 'Business District',
      description:
          'Executive Ford Transit designed for business families and corporate events. Combines luxury with functionality for professional transportation needs.',
    ),
    Car(
      id: '12',
      name: 'Volkswagen Crafter VIP',
      brand: 'Volkswagen',
      model: 'Crafter VIP',
      category: 'Luxury Bus',
      pricePerDay: 680,
      rating: 4.8,
      reviewCount: 29,
      imageUrl:
          'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      features: [
        'VIP Interior',
        'Panoramic Sunroof',
        'Premium Audio',
        'Individual Climate Control',
        'Reading Lights',
        'Storage Compartments'
      ],
      fuelType: 'Diesel',
      transmission: 'Automatic',
      seats: 16,
      doors: 3,
      airConditioning: true,
      available: true,
      location: 'City Center',
      description:
          'Volkswagen Crafter VIP edition offering premium family transportation. Ideal for large families and group travel with luxury amenities.',
    ),
    Car(
      id: '13',
      name: 'Iveco Daily Luxury Coach',
      brand: 'Iveco',
      model: 'Daily Luxury Coach',
      category: 'Luxury Bus',
      pricePerDay: 780,
      rating: 4.6,
      reviewCount: 35,
      imageUrl:
          'https://images.unsplash.com/photo-1544620347-c4fd4a3d5957?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      features: [
        'Luxury Coach Seating',
        'Entertainment Screens',
        'Premium Suspension',
        'Onboard Restroom',
        'Beverage Station',
        'Ambient Lighting'
      ],
      fuelType: 'Diesel',
      transmission: 'Automatic',
      seats: 18,
      doors: 2,
      airConditioning: true,
      available: true,
      location: 'Luxury Terminal',
      description:
          'Premium Iveco Daily luxury coach perfect for extended family trips and special events. Features coach-level amenities in a compact luxury bus format.',
    ),
  ];

  /// Get all cars
  static List<Car> getAllCars() {
    return List.unmodifiable(_cars);
  }

  /// Get car by ID
  static Car? getCarById(String id) {
    try {
      return _cars.firstWhere((car) => car.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get cars by category
  static List<Car> getCarsByCategory(String category) {
    if (category == 'All') {
      return getAllCars();
    }
    return _cars.where((car) => car.category == category).toList();
  }

  /// Get available categories with car counts
  static Map<String, int> getCategoryCounts() {
    final Map<String, int> counts = {};

    counts['All'] = _cars.length;

    for (final category in CarCategories.all.skip(1)) {
      counts[category] = _cars.where((car) => car.category == category).length;
    }

    return counts;
  }

  /// Search cars by query
  static List<Car> searchCars(String query) {
    if (query.isEmpty) return getAllCars();

    final lowerQuery = query.toLowerCase();
    return _cars.where((car) {
      return car.name.toLowerCase().contains(lowerQuery) ||
          car.brand.toLowerCase().contains(lowerQuery) ||
          car.model.toLowerCase().contains(lowerQuery) ||
          car.category.toLowerCase().contains(lowerQuery) ||
          car.location.toLowerCase().contains(lowerQuery) ||
          car.description.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// Filter cars by price range
  static List<Car> filterCarsByPriceRange(double minPrice, double maxPrice) {
    return _cars.where((car) {
      return car.pricePerDay >= minPrice && car.pricePerDay <= maxPrice;
    }).toList();
  }

  /// Sort cars by different criteria
  static List<Car> sortCars(List<Car> cars, String sortBy) {
    final List<Car> sortedCars = List.from(cars);

    switch (sortBy) {
      case 'price_low_to_high':
        sortedCars.sort((a, b) => a.pricePerDay.compareTo(b.pricePerDay));
        break;
      case 'price_high_to_low':
        sortedCars.sort((a, b) => b.pricePerDay.compareTo(a.pricePerDay));
        break;
      case 'rating':
        sortedCars.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case 'name':
        sortedCars.sort((a, b) => a.name.compareTo(b.name));
        break;
      default:
        // Keep original order
        break;
    }

    return sortedCars;
  }

  /// Get featured cars (highest rated)
  static List<Car> getFeaturedCars({int limit = 5}) {
    final sortedCars = List<Car>.from(_cars);
    sortedCars.sort((a, b) => b.rating.compareTo(a.rating));
    return sortedCars.take(limit).toList();
  }

  /// Get cars by fuel type
  static List<Car> getCarsByFuelType(String fuelType) {
    return _cars.where((car) => car.fuelType == fuelType).toList();
  }

  /// Get available fuel types
  static List<String> getAvailableFuelTypes() {
    return _cars.map((car) => car.fuelType).toSet().toList();
  }

  /// Get available locations
  static List<String> getAvailableLocations() {
    return _cars.map((car) => car.location).toSet().toList();
  }
}
