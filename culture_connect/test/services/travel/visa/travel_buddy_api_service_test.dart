import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:culture_connect/services/travel/visa/travel_buddy_api_service.dart';
import 'package:culture_connect/models/travel/document/visa_requirement.dart';

void main() {
  group('TravelBuddyApiService', () {
    late TravelBuddyApiService service;

    setUpAll(() async {
      // Load test environment
      await dotenv.load(fileName: ".env");
    });

    setUp(() {
      service = TravelBuddyApiService();
    });

    tearDown(() {
      service.dispose();
    });

    group('getSupportedCountries', () {
      test('should return list of countries', () async {
        final countries = await service.getSupportedCountries();

        expect(countries, isNotEmpty);
        expect(countries.length,
            greaterThan(40)); // Should have comprehensive list

        // Check that major countries are included
        final countryCodes = countries.map((c) => c.code).toList();
        expect(countryCodes, contains('US'));
        expect(countryCodes, contains('GB'));
        expect(countryCodes, contains('CA'));
        expect(countryCodes, contains('AU'));
        expect(countryCodes, contains('DE'));

        // Check country structure
        final firstCountry = countries.first;
        expect(firstCountry.code, isNotEmpty);
        expect(firstCountry.name, isNotEmpty);
        expect(firstCountry.flag, isNotEmpty);
        expect(firstCountry.dialCode, isNotEmpty);
      });

      test('should handle API errors gracefully', () async {
        // This test verifies fallback behavior
        final countries = await service.getSupportedCountries();

        // Should still return countries even if API fails
        expect(countries, isNotEmpty);
        expect(countries.length, greaterThan(20));
      });
    });

    group('getVisaRequirements', () {
      test('should return visa requirements for valid country pair', () async {
        final requirement = await service.getVisaRequirements(
          passportCountry: 'US',
          destinationCountry: 'GB',
        );

        // Should return either real data or mock data
        expect(requirement, isNotNull);
        expect(requirement!.countryFrom, equals('US'));
        expect(requirement.countryTo, equals('GB'));
        expect(requirement.requirementType, isA<VisaRequirementType>());
      });

      test('should handle invalid country codes', () async {
        final requirement = await service.getVisaRequirements(
          passportCountry: 'INVALID',
          destinationCountry: 'ALSO_INVALID',
        );

        // Should return mock data for invalid codes
        expect(requirement, isNotNull);
      });

      test('should not allow same passport and destination country', () async {
        expect(
          () => service.getVisaRequirements(
            passportCountry: 'US',
            destinationCountry: 'US',
          ),
          throwsA(isA<ArgumentError>()),
        );
      });
    });
  });
}
