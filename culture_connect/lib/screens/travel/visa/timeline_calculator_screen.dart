// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/visa/self_service_models.dart';
import 'package:culture_connect/services/travel/visa/self_service_visa_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// Screen for calculating visa application timeline
class TimelineCalculatorScreen extends ConsumerStatefulWidget {
  const TimelineCalculatorScreen({
    super.key,
    this.passportCountry,
    this.destinationCountry,
    this.visaType,
  });

  final String? passportCountry;
  final String? destinationCountry;
  final String? visaType;

  @override
  ConsumerState<TimelineCalculatorScreen> createState() => _TimelineCalculatorScreenState();
}

class _TimelineCalculatorScreenState extends ConsumerState<TimelineCalculatorScreen> {
  List<ApplicationTimelineMilestone> _timeline = [];
  bool _isLoading = false;
  String? _errorMessage;
  DateTime? _preferredSubmissionDate;

  @override
  void initState() {
    super.initState();
    _calculateTimeline();
  }

  Future<void> _calculateTimeline() async {
    if (widget.passportCountry == null || 
        widget.destinationCountry == null || 
        widget.visaType == null) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final selfServiceService = ref.read(selfServiceVisaServiceProvider);
      final timeline = await selfServiceService.calculateTimeline(
        passportCountry: widget.passportCountry!,
        destinationCountry: widget.destinationCountry!,
        visaType: widget.visaType!,
        preferredSubmissionDate: _preferredSubmissionDate,
      );

      if (mounted) {
        setState(() {
          _timeline = timeline;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to calculate timeline: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        title: Text(
          'Timeline Calculator',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: AppTheme.fontWeightBold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showTimelineInfo,
            icon: const Icon(
              Icons.info_outline,
              color: AppTheme.infoColor,
            ),
          ),
        ],
      ),
      body: _buildBody(theme),
    );
  }

  Widget _buildBody(ThemeData theme) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: AppTheme.primaryColor,
            ),
            SizedBox(height: AppTheme.spacingMd),
            Text(
              'Calculating timeline...',
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
                fontWeight: AppTheme.fontWeightMedium,
              ),
            ),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return ErrorView(
        error: _errorMessage!,
        onRetry: _calculateTimeline,
      );
    }

    return RefreshIndicator(
      onRefresh: _calculateTimeline,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeroSection(theme),
            const SizedBox(height: AppTheme.spacingLg),
            _buildDateSelector(theme),
            const SizedBox(height: AppTheme.spacingLg),
            if (_timeline.isNotEmpty) ...[
              _buildTimelineSummary(theme),
              const SizedBox(height: AppTheme.spacingLg),
              _buildTimelineSteps(theme),
            ] else ...[
              _buildEmptyState(theme),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeroSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.warningColor.withValues(alpha: 0.1),
            AppTheme.warningColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        border: Border.all(
          color: AppTheme.warningColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMd),
            decoration: BoxDecoration(
              color: AppTheme.warningColor,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
            ),
            child: const Icon(
              Icons.schedule,
              color: AppTheme.white,
              size: 32,
            ),
          ),
          const SizedBox(width: AppTheme.spacingMd),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Timeline Calculator',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: AppTheme.fontWeightBold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                Text(
                  'Plan your visa application timeline and avoid delays',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelector(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Preferred Submission Date',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: AppTheme.fontWeightBold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          InkWell(
            onTap: _selectDate,
            child: Container(
              padding: const EdgeInsets.all(AppTheme.spacingMd),
              decoration: BoxDecoration(
                border: Border.all(color: AppTheme.borderColor),
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.calendar_today,
                    color: AppTheme.primaryColor,
                  ),
                  const SizedBox(width: AppTheme.spacingMd),
                  Expanded(
                    child: Text(
                      _preferredSubmissionDate != null
                          ? _formatDate(_preferredSubmissionDate!)
                          : 'Select preferred submission date',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: _preferredSubmissionDate != null
                            ? AppTheme.textPrimaryColor
                            : AppTheme.textTertiaryColor,
                      ),
                    ),
                  ),
                  const Icon(
                    Icons.arrow_drop_down,
                    color: AppTheme.textTertiaryColor,
                  ),
                ],
              ),
            ),
          ),
          if (_preferredSubmissionDate != null) ...[
            const SizedBox(height: AppTheme.spacingSm),
            TextButton(
              onPressed: () {
                setState(() {
                  _preferredSubmissionDate = null;
                });
                _calculateTimeline();
              },
              child: const Text('Clear date'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTimelineSummary(ThemeData theme) {
    final totalDuration = _timeline.fold<Duration>(
      Duration.zero,
      (sum, milestone) => sum + milestone.estimatedDuration,
    );
    final completedSteps = _timeline.where((m) => m.isCompleted).length;

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Timeline Summary',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: AppTheme.fontWeightBold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Row(
            children: [
              _buildSummaryItem('Total Duration', '${totalDuration.inDays} days', AppTheme.primaryColor),
              const SizedBox(width: AppTheme.spacingLg),
              _buildSummaryItem('Steps', '$completedSteps/${_timeline.length}', AppTheme.successColor),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingSm),
            decoration: BoxDecoration(
              color: AppTheme.infoColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.lightbulb,
                  color: AppTheme.infoColor,
                  size: 16,
                ),
                const SizedBox(width: AppTheme.spacingSm),
                Expanded(
                  child: Text(
                    'Start early to avoid delays and allow buffer time for unexpected issues',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.infoColor,
                      fontWeight: AppTheme.fontWeightMedium,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: AppTheme.fontSizeSm,
            color: AppTheme.textTertiaryColor,
            fontWeight: AppTheme.fontWeightMedium,
          ),
        ),
        const SizedBox(height: AppTheme.spacingXs),
        Text(
          value,
          style: TextStyle(
            fontSize: AppTheme.fontSizeLg,
            color: color,
            fontWeight: AppTheme.fontWeightBold,
          ),
        ),
      ],
    );
  }

  Widget _buildTimelineSteps(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Application Timeline',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: AppTheme.fontWeightBold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMd),
        ..._timeline.asMap().entries.map((entry) {
          final index = entry.key;
          final milestone = entry.value;
          final isLast = index == _timeline.length - 1;
          return _buildTimelineStep(milestone, isLast, theme);
        }),
      ],
    );
  }

  Widget _buildTimelineStep(ApplicationTimelineMilestone milestone, bool isLast, ThemeData theme) {
    final isCompleted = milestone.isCompleted;
    final color = isCompleted ? AppTheme.successColor : AppTheme.primaryColor;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
              child: Icon(
                isCompleted ? Icons.check : Icons.circle,
                color: AppTheme.white,
                size: 16,
              ),
            ),
            if (!isLast) ...[
              Container(
                width: 2,
                height: 40,
                color: AppTheme.borderColor,
              ),
            ],
          ],
        ),
        const SizedBox(width: AppTheme.spacingMd),
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(bottom: AppTheme.spacingLg),
            padding: const EdgeInsets.all(AppTheme.spacingMd),
            decoration: BoxDecoration(
              color: AppTheme.white,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.shadowColor.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        milestone.name,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: AppTheme.fontWeightBold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingSm,
                        vertical: AppTheme.spacingXs,
                      ),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                      ),
                      child: Text(
                        '${milestone.estimatedDuration.inDays} days',
                        style: TextStyle(
                          color: color,
                          fontWeight: AppTheme.fontWeightSemibold,
                          fontSize: AppTheme.fontSizeXs,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppTheme.spacingSm),
                Text(
                  milestone.description,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                if (milestone.tips.isNotEmpty) ...[
                  const SizedBox(height: AppTheme.spacingSm),
                  ExpansionTile(
                    title: Text(
                      'Tips & Common Issues',
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontWeight: AppTheme.fontWeightSemibold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    children: [
                      ...milestone.tips.map((tip) => ListTile(
                        leading: const Icon(Icons.lightbulb, size: 16, color: AppTheme.warningColor),
                        title: Text(
                          tip,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                        dense: true,
                      )),
                      ...milestone.commonIssues.map((issue) => ListTile(
                        leading: const Icon(Icons.warning, size: 16, color: AppTheme.errorColor),
                        title: Text(
                          issue,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                        dense: true,
                      )),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingXl),
            decoration: BoxDecoration(
              color: AppTheme.borderColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.timeline,
              size: 64,
              color: AppTheme.borderColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingLg),
          Text(
            'No Timeline Available',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: AppTheme.fontWeightBold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingSm),
          Text(
            'Please provide your travel details to calculate a timeline.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _preferredSubmissionDate ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _preferredSubmissionDate = date;
      });
      _calculateTimeline();
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showTimelineInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Timeline Calculator'),
        content: const SingleChildScrollView(
          child: Text(
            'The timeline calculator helps you plan your visa application:\n\n'
            '• Shows estimated duration for each step\n'
            '• Includes document preparation time\n'
            '• Accounts for embassy processing times\n'
            '• Provides tips to avoid delays\n\n'
            'Tips for success:\n'
            '• Start early to allow buffer time\n'
            '• Gather documents before applying\n'
            '• Check embassy holiday schedules\n'
            '• Consider peak season delays\n\n'
            'Remember: Processing times can vary based on your specific circumstances.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}
