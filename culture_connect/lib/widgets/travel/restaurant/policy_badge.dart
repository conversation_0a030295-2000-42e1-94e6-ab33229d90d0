import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/models/travel/restaurant_tier.dart';

/// Widget for displaying restaurant policy badges (deposit and cancellation)
class PolicyBadge extends StatelessWidget {
  final String text;
  final IconData icon;
  final Color backgroundColor;
  final Color textColor;
  final Color iconColor;
  final bool isCompact;

  const PolicyBadge({
    super.key,
    required this.text,
    required this.icon,
    this.backgroundColor = AppTheme.surfaceVariant,
    this.textColor = AppTheme.textSecondaryColor,
    this.iconColor = AppTheme.textSecondaryColor,
    this.isCompact = false,
  });

  /// Factory constructor for deposit policy badges
  factory PolicyBadge.deposit({
    required DepositPolicy policy,
    bool isCompact = false,
  }) {
    Color backgroundColor;
    Color textColor;
    Color iconColor;
    IconData icon;

    if (policy.amount == 0.0) {
      // Free/No deposit - green theme
      backgroundColor = const Color(0xFFE8F5E8);
      textColor = const Color(0xFF2E7D32);
      iconColor = const Color(0xFF2E7D32);
      icon = Icons.check_circle_outline;
    } else if (policy.isRequired) {
      // Required deposit - amber theme
      backgroundColor = const Color(0xFFFFF3E0);
      textColor = const Color(0xFFE65100);
      iconColor = const Color(0xFFE65100);
      icon = Icons.lock_outline;
    } else {
      // Optional deposit - blue theme
      backgroundColor = const Color(0xFFE3F2FD);
      textColor = const Color(0xFF1565C0);
      iconColor = const Color(0xFF1565C0);
      icon = Icons.local_offer_outlined;
    }

    return PolicyBadge(
      text: policy.badgeText,
      icon: icon,
      backgroundColor: backgroundColor,
      textColor: textColor,
      iconColor: iconColor,
      isCompact: isCompact,
    );
  }

  /// Factory constructor for cancellation policy badges
  factory PolicyBadge.cancellation({
    required CancellationPolicy policy,
    bool isCompact = false,
  }) {
    Color backgroundColor;
    Color textColor;
    Color iconColor;

    if (policy.minimumHoursForRefund <= 2) {
      // Very flexible - green theme
      backgroundColor = const Color(0xFFE8F5E8);
      textColor = const Color(0xFF2E7D32);
      iconColor = const Color(0xFF2E7D32);
    } else if (policy.minimumHoursForRefund <= 24) {
      // Moderate flexibility - blue theme
      backgroundColor = const Color(0xFFE3F2FD);
      textColor = const Color(0xFF1565C0);
      iconColor = const Color(0xFF1565C0);
    } else {
      // Strict policy - amber theme
      backgroundColor = const Color(0xFFFFF3E0);
      textColor = const Color(0xFFE65100);
      iconColor = const Color(0xFFE65100);
    }

    return PolicyBadge(
      text: policy.badgeText,
      icon: Icons.schedule,
      backgroundColor: backgroundColor,
      textColor: textColor,
      iconColor: iconColor,
      isCompact: isCompact,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isCompact ? 8.0 : 12.0,
        vertical: isCompact ? 4.0 : 6.0,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(isCompact ? 12.0 : 16.0),
        border: Border.all(
          color: backgroundColor.withAlpha(100),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: isCompact ? 14.0 : 16.0,
            color: iconColor,
          ),
          SizedBox(width: isCompact ? 4.0 : 6.0),
          Flexible(
            child: Text(
              text,
              style: TextStyle(
                fontSize: isCompact ? 11.0 : 12.0,
                fontWeight: FontWeight.w600,
                color: textColor,
                letterSpacing: 0.2,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget for displaying restaurant tier indicator
class TierIndicator extends StatelessWidget {
  final RestaurantTier tier;
  final bool showLabel;
  final bool isCompact;

  const TierIndicator({
    super.key,
    required this.tier,
    this.showLabel = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isCompact ? 8.0 : 12.0,
        vertical: isCompact ? 4.0 : 6.0,
      ),
      decoration: BoxDecoration(
        color: tier.color.withAlpha(20),
        borderRadius: BorderRadius.circular(isCompact ? 12.0 : 16.0),
        border: Border.all(
          color: tier.color.withAlpha(60),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            tier.icon,
            size: isCompact ? 14.0 : 16.0,
            color: tier.color,
          ),
          if (showLabel) ...[
            SizedBox(width: isCompact ? 4.0 : 6.0),
            Text(
              tier.displayName,
              style: TextStyle(
                fontSize: isCompact ? 11.0 : 12.0,
                fontWeight: FontWeight.w700,
                color: tier.color,
                letterSpacing: 0.2,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Widget for displaying multiple policy badges in a row
class PolicyBadgeRow extends StatelessWidget {
  final List<Widget> badges;
  final MainAxisAlignment alignment;
  final double spacing;
  final bool wrap;

  const PolicyBadgeRow({
    super.key,
    required this.badges,
    this.alignment = MainAxisAlignment.start,
    this.spacing = 8.0,
    this.wrap = true,
  });

  @override
  Widget build(BuildContext context) {
    if (wrap) {
      return Wrap(
        spacing: spacing,
        runSpacing: spacing / 2,
        alignment: WrapAlignment.start,
        children: badges,
      );
    } else {
      return Row(
        mainAxisAlignment: alignment,
        children: badges
            .expand((badge) => [badge, SizedBox(width: spacing)])
            .take(badges.length * 2 - 1)
            .toList(),
      );
    }
  }
}

/// Utility class for creating policy badges from restaurant data
class PolicyBadgeUtils {
  /// Create deposit badge from restaurant
  static PolicyBadge createDepositBadge(
    DepositPolicy policy, {
    bool isCompact = false,
  }) {
    return PolicyBadge.deposit(
      policy: policy,
      isCompact: isCompact,
    );
  }

  /// Create cancellation badge from restaurant
  static PolicyBadge createCancellationBadge(
    CancellationPolicy policy, {
    bool isCompact = false,
  }) {
    return PolicyBadge.cancellation(
      policy: policy,
      isCompact: isCompact,
    );
  }

  /// Create tier indicator from restaurant tier
  static TierIndicator createTierIndicator(
    RestaurantTier tier, {
    bool showLabel = true,
    bool isCompact = false,
  }) {
    return TierIndicator(
      tier: tier,
      showLabel: showLabel,
      isCompact: isCompact,
    );
  }

  /// Create a complete set of badges for a restaurant
  static List<Widget> createRestaurantBadges(
    RestaurantTier tier, {
    bool isCompact = false,
    bool includeTier = false,
  }) {
    final badges = <Widget>[];

    if (includeTier) {
      badges.add(createTierIndicator(tier, isCompact: isCompact));
    }

    badges.add(createDepositBadge(
      DepositPolicy.forTier(tier),
      isCompact: isCompact,
    ));

    badges.add(createCancellationBadge(
      CancellationPolicy.forTier(tier),
      isCompact: isCompact,
    ));

    return badges;
  }
}
