import React, { useState, useRef, useEffect } from "react";
import { StyleSheet, View, Text, ScrollView, Pressable, TextInput, Platform, Alert, Dimensions, FlatList, RefreshControl } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Mic, Mic<PERSON>ff, Send, Sparkles, MessageCircle, Volume2, MapPin, Calendar, Plane, Hotel, ArrowLeft, Bot, Bell, TrendingUp, Users, Award, Clock, Star, Zap, Brain, Target, Globe, Compass, Heart, ChevronRight, Play, Eye, Lightbulb, BookOpen, Headphones } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";

import { Button } from "@/components/Button";
import { useUserStore } from "@/store/userStore";

const { width } = Dimensions.get('window');

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

const quickActions = [
  {
    id: "destination",
    title: "Find Destinations",
    description: "Discover amazing places to visit based on your preferences",
    icon: <MapPin size={24} color={colors.white} />,
    prompt: "Help me find the perfect travel destination based on my preferences",
    gradient: [colors.primary, colors.primaryLight] as const,
  },
  {
    id: "itinerary",
    title: "Plan Itinerary",
    description: "Create a detailed travel plan with activities and timing",
    icon: <Calendar size={24} color={colors.white} />,
    prompt: "Create a detailed itinerary for my upcoming trip",
    gradient: [colors.secondary, colors.accent] as const,
  },
  {
    id: "flights",
    title: "Flight Advice",
    description: "Get expert tips on finding the best flights and deals",
    icon: <Plane size={24} color={colors.white} />,
    prompt: "Give me advice on finding the best flights for my trip",
    gradient: [colors.accent, colors.primary] as const,
  },
  {
    id: "accommodation",
    title: "Hotel Recommendations",
    description: "Find the perfect stay that matches your style and budget",
    icon: <Hotel size={24} color={colors.white} />,
    prompt: "Recommend hotels and accommodations for my destination",
    gradient: [colors.primary, colors.secondary] as const,
  },
];

const aiCapabilities = [
  {
    id: "1",
    title: "Smart Planning",
    description: "AI-powered itinerary optimization",
    icon: <Brain size={20} color={colors.primary} />,
    backgroundColor: colors.primary + '15',
  },
  {
    id: "2",
    title: "Real-time Updates",
    description: "Live travel alerts & changes",
    icon: <Zap size={20} color={colors.secondary} />,
    backgroundColor: colors.secondary + '15',
  },
  {
    id: "3",
    title: "Personalized Recs",
    description: "Tailored to your preferences",
    icon: <Target size={20} color={colors.accent} />,
    backgroundColor: colors.accent + '15',
  },
  {
    id: "4",
    title: "Multi-language",
    description: "Communicate in 50+ languages",
    icon: <Globe size={20} color="#8B5CF6" />,
    backgroundColor: "#8B5CF6" + '15',
  },
];

const userStats = [
  {
    id: "1",
    title: "Trips Planned",
    value: "12",
    icon: <MapPin size={18} color={colors.primary} />,
    trend: "+3",
    color: colors.primary,
  },
  {
    id: "2",
    title: "Conversations",
    value: "47",
    icon: <MessageCircle size={18} color={colors.secondary} />,
    trend: "+8",
    color: colors.secondary,
  },
  {
    id: "3",
    title: "Saved Ideas",
    value: "23",
    icon: <Heart size={18} color={colors.accent} />,
    trend: "+5",
    color: colors.accent,
  },
  {
    id: "4",
    title: "Hours Saved",
    value: "18h",
    icon: <Clock size={18} color="#10B981" />,
    trend: "+6h",
    color: "#10B981",
  },
];

const recentConversations = [
  {
    id: "1",
    title: "Tokyo Adventure Planning",
    lastMessage: "Perfect! I've created a 7-day itinerary for your Tokyo trip...",
    timestamp: "2 hours ago",
    category: "Itinerary",
    isCompleted: true,
  },
  {
    id: "2",
    title: "European Backpacking Route",
    lastMessage: "Let me suggest some budget-friendly hostels in Prague...",
    timestamp: "1 day ago",
    category: "Planning",
    isCompleted: false,
  },
  {
    id: "3",
    title: "Bali Honeymoon Ideas",
    lastMessage: "Here are some romantic restaurants with sunset views...",
    timestamp: "3 days ago",
    category: "Recommendations",
    isCompleted: true,
  },
];

const learningResources = [
  {
    id: "1",
    title: "Travel Planning 101",
    description: "Master the basics of trip planning",
    duration: "5 min read",
    icon: <BookOpen size={18} color={colors.primary} />,
    progress: 0,
    isNew: true,
  },
  {
    id: "2",
    title: "Budget Travel Secrets",
    description: "Save money on your adventures",
    duration: "8 min read",
    icon: <Lightbulb size={18} color={colors.secondary} />,
    progress: 65,
    isNew: false,
  },
];

export default function AITravelPlannerScreen() {
  const { user } = useUserStore();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isChatMode, setIsChatMode] = useState(false);

  const [refreshing, setRefreshing] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  const handleRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 1500);
  };



  const startConversation = (action: typeof quickActions[0]) => {
    const welcomeMessage: Message = {
      id: "welcome",
      text: `Hi! I'm Kaia, your AI travel companion. I'm excited to help you ${action.title.toLowerCase()}. Let's make your travel dreams come true!`,
      isUser: false,
      timestamp: new Date(),
    };

    setMessages([welcomeMessage]);
    setIsChatMode(true);
    
    // Auto-send the initial prompt
    setTimeout(() => {
      handleSendMessage(action.prompt);
    }, 1000);
  };

  const goBackToMain = () => {
    setIsChatMode(false);
    setMessages([]);
    setInputText("");
  };

  const handleConversationPress = (conversation: typeof recentConversations[0]) => {
    // Resume conversation
    setIsChatMode(true);
  };

  const startRecording = async () => {
    if (Platform.OS === 'web') {
      Alert.alert("Voice recording is not available on web");
      return;
    }
    Alert.alert("Voice recording", "Voice recording feature coming soon!");
  };

  const stopRecording = async () => {
    setIsRecording(false);
  };

  const handleSendMessage = async (text: string) => {
    if (!text.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: text.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText("");
    setIsLoading(true);

    try {
      const response = await fetch('https://toolkit.rork.com/text/llm/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [
            {
              role: 'system',
              content: 'You are Kaia, an expert AI Travel Companion. You are friendly, knowledgeable, and passionate about travel. Help users plan their trips, find destinations, book flights, recommend accommodations, and provide travel advice. Be helpful, enthusiastic, and provide specific actionable recommendations. Keep responses conversational and engaging.',
            },
            {
              role: 'user',
              content: text.trim(),
            },
          ],
        }),
      });

      const result = await response.json();
      
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: result.completion || "I'm sorry, I couldn't process your request. Please try again.",
        isUser: false,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.log('AI response error:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: "I'm having trouble connecting right now. Please try again in a moment.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }

    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  if (isChatMode) {
    return (
      <SafeAreaView style={styles.container} edges={["top"]}>
        <View style={styles.chatHeader}>
          <Pressable style={styles.backButton} onPress={goBackToMain}>
            <ArrowLeft size={20} color={colors.text} />
          </Pressable>
          <View style={styles.chatHeaderCenter}>
            <View style={styles.kaiaIconChat}>
              <Bot size={20} color={colors.primary} />
            </View>
            <View>
              <Text style={styles.chatHeaderTitle}>Kaia</Text>
              <Text style={styles.chatHeaderSubtitle}>AI Travel Companion</Text>
            </View>
          </View>
          <View style={styles.headerRight} />
        </View>

        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.messagesContent}
        >
          {messages.map((message) => (
            <View
              key={message.id}
              style={[
                styles.messageContainer,
                message.isUser ? styles.userMessage : styles.aiMessage,
              ]}
            >
              {!message.isUser && (
                <View style={styles.aiAvatar}>
                  <Bot size={14} color={colors.primary} />
                </View>
              )}
              <View
                style={[
                  styles.messageBubble,
                  message.isUser ? styles.userBubble : styles.aiBubble,
                ]}
              >
                <Text
                  style={[
                    styles.messageText,
                    message.isUser ? styles.userText : styles.aiText,
                  ]}
                >
                  {message.text}
                </Text>
              </View>
            </View>
          ))}
          
          {isLoading && (
            <View style={[styles.messageContainer, styles.aiMessage]}>
              <View style={styles.aiAvatar}>
                <Bot size={14} color={colors.primary} />
              </View>
              <View style={[styles.messageBubble, styles.aiBubble]}>
                <Text style={styles.loadingText}>Kaia is thinking...</Text>
              </View>
            </View>
          )}
        </ScrollView>

        <View style={styles.inputContainer}>
          <View style={styles.inputRow}>
            <View style={styles.textInputContainer}>
              <TextInput
                style={styles.textInput}
                value={inputText}
                onChangeText={setInputText}
                placeholder="Ask Kaia"
                placeholderTextColor={colors.textLight}
                multiline
                maxLength={500}
              />
            </View>
            <Pressable
              style={[styles.voiceButton, isRecording && styles.voiceButtonActive]}
              onPress={isRecording ? stopRecording : startRecording}
              disabled={isLoading}
            >
              {isRecording ? (
                <MicOff size={18} color={colors.white} />
              ) : (
                <Mic size={18} color={colors.white} />
              )}
            </Pressable>
            <Pressable
              style={[styles.sendButton, (!inputText.trim() || isLoading) && styles.sendButtonDisabled]}
              onPress={() => handleSendMessage(inputText)}
              disabled={!inputText.trim() || isLoading}
            >
              <Send size={18} color={colors.white} />
            </Pressable>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <Text style={styles.greeting}>Hello, {user?.name || "Traveler"}</Text>
            <Pressable style={styles.iconButton}>
              <Bell size={24} color={colors.text} />
            </Pressable>
          </View>
          <View style={styles.headerTitleContainer}>
            <Text style={styles.mainTitle}>Meet Kaia</Text>
            <Text style={styles.mainSubtitle}>Your AI Travel Companion</Text>
          </View>
        </View>

        {/* Kaia Introduction Card */}
        <View style={styles.introContainer}>
          <View style={styles.introCard}>
            <LinearGradient
              colors={[colors.primary + '08', colors.secondary + '08']}
              style={styles.introGradient}
            />
            <View style={styles.introContent}>
              <View style={styles.introHeader}>
                <View style={styles.kaiaIconLarge}>
                  <Bot size={32} color={colors.primary} />
                </View>
                <View style={styles.introTextContainer}>
                  <Text style={styles.introTitle}>AI-Powered Travel Assistant</Text>
                  <Text style={styles.introSubtitle}>Smart planning, personalized recommendations</Text>
                </View>
              </View>
              <View style={styles.introStats}>
                <View style={styles.introStat}>
                  <Sparkles size={16} color={colors.primary} />
                  <Text style={styles.introStatText}>AI-Powered</Text>
                </View>
                <View style={styles.introStat}>
                  <Clock size={16} color={colors.secondary} />
                  <Text style={styles.introStatText}>24/7 Available</Text>
                </View>
                <View style={styles.introStat}>
                  <Globe size={16} color={colors.accent} />
                  <Text style={styles.introStatText}>Global Expert</Text>
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* AI Capabilities */}
        <View style={styles.capabilitiesContainer}>
          <Text style={styles.capabilitiesTitle}>AI Capabilities</Text>
          <View style={styles.capabilitiesGrid}>
            {aiCapabilities.map((capability) => (
              <View key={capability.id} style={[styles.capabilityCard, { backgroundColor: capability.backgroundColor }]}>
                <View style={styles.capabilityIconContainer}>
                  {capability.icon}
                </View>
                <Text style={styles.capabilityTitle}>{capability.title}</Text>
                <Text style={styles.capabilityDescription}>{capability.description}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>How can I help you today?</Text>
            <Text style={styles.sectionSubtitle}>Choose what you'd like to explore</Text>
          </View>
          
          <View style={styles.cardsContainer}>
            {quickActions.map((action) => (
              <Pressable
                key={action.id}
                style={styles.actionCard}
                onPress={() => startConversation(action)}
              >
                <LinearGradient
                  colors={action.gradient}
                  style={styles.cardGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <View style={styles.cardContent}>
                    <View style={styles.cardIcon}>
                      {action.icon}
                    </View>
                    <View style={styles.cardTextContainer}>
                      <Text style={styles.cardTitle}>{action.title}</Text>
                      <Text style={styles.cardDescription}>{action.description}</Text>
                    </View>
                    <View style={styles.cardArrow}>
                      <ChevronRight size={16} color={colors.white} />
                    </View>
                  </View>
                </LinearGradient>
              </Pressable>
            ))}
          </View>
        </View>

        {/* User Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statsHeader}>
            <TrendingUp size={20} color={colors.primary} />
            <Text style={styles.statsTitle}>My Kaia Stats</Text>
          </View>
          <Text style={styles.statsSubtitle}>Your personal travel planning insights</Text>
          <View style={styles.statsGrid}>
            {userStats.map((stat, index) => (
              <View key={stat.id} style={[styles.statCard, { backgroundColor: index % 2 === 0 ? colors.white : colors.backgroundSecondary }]}>
                <LinearGradient
                  colors={[stat.color + '10', stat.color + '05']}
                  style={styles.statGradient}
                />
                <View style={styles.statHeader}>
                  <View style={[styles.statIconContainer, { backgroundColor: stat.color + '15' }]}>
                    {stat.icon}
                  </View>
                  <View style={styles.trendContainer}>
                    <TrendingUp size={12} color="#10B981" />
                    <Text style={styles.trendText}>{stat.trend}</Text>
                  </View>
                </View>
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.title}</Text>
                <View style={styles.statProgress}>
                  <View style={[styles.statProgressBar, { width: `${75 + (index * 5)}%`, backgroundColor: stat.color }]} />
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Recent Conversations */}
        <View style={styles.conversationsContainer}>
          <View style={styles.conversationsHeader}>
            <View style={styles.conversationsHeaderLeft}>
              <View style={styles.conversationsIconBadge}>
                <MessageCircle size={18} color={colors.white} />
              </View>
              <View>
                <Text style={styles.conversationsTitle}>Recent Conversations</Text>
                <Text style={styles.conversationsSubtitle}>Continue where you left off</Text>
              </View>
            </View>
            <Pressable style={styles.viewAllButton}>
              <Text style={styles.viewAllText}>View All</Text>
              <ChevronRight size={14} color={colors.primary} />
            </Pressable>
          </View>
          
          <View style={styles.conversationsList}>
            {recentConversations.map((conversation) => (
              <Pressable
                key={conversation.id}
                style={styles.conversationCard}
                onPress={() => handleConversationPress(conversation)}
              >
                <View style={styles.conversationContent}>
                  <View style={styles.conversationHeader}>
                    <Text style={styles.conversationTitle} numberOfLines={1}>{conversation.title}</Text>
                    <View style={styles.conversationMeta}>
                      <View style={[styles.categoryBadge, { backgroundColor: conversation.isCompleted ? colors.success + '20' : colors.warning + '20' }]}>
                        <Text style={[styles.categoryBadgeText, { color: conversation.isCompleted ? colors.success : colors.warning }]}>
                          {conversation.category}
                        </Text>
                      </View>
                      <Text style={styles.conversationTime}>{conversation.timestamp}</Text>
                    </View>
                  </View>
                  <Text style={styles.conversationMessage} numberOfLines={2}>{conversation.lastMessage}</Text>
                  <View style={styles.conversationFooter}>
                    <View style={styles.conversationStatus}>
                      {conversation.isCompleted ? (
                        <View style={styles.completedStatus}>
                          <Award size={14} color={colors.success} />
                          <Text style={styles.completedText}>Completed</Text>
                        </View>
                      ) : (
                        <View style={styles.activeStatus}>
                          <Play size={14} color={colors.primary} />
                          <Text style={styles.activeText}>Continue</Text>
                        </View>
                      )}
                    </View>
                    <ChevronRight size={16} color={colors.textLight} />
                  </View>
                </View>
              </Pressable>
            ))}
          </View>
        </View>

        {/* Learning Hub */}
        <View style={styles.learningContainer}>
          <View style={styles.learningHeader}>
            <View style={styles.learningHeaderLeft}>
              <View style={styles.learningIconBadge}>
                <Eye size={18} color={colors.white} />
              </View>
              <View>
                <Text style={styles.learningTitle}>Travel Learning Hub</Text>
                <Text style={styles.learningSubtitle}>Enhance your travel knowledge</Text>
              </View>
            </View>
            <Pressable style={styles.viewAllButton}>
              <Text style={styles.viewAllText}>View All</Text>
              <ChevronRight size={14} color={colors.primary} />
            </Pressable>
          </View>
          
          <View style={styles.learningGrid}>
            {learningResources.map((resource) => (
              <Pressable key={resource.id} style={styles.learningCard}>
                <LinearGradient
                  colors={[resource.icon.props.color + '15', resource.icon.props.color + '05']}
                  style={styles.learningCardGradient}
                />
                <View style={styles.learningCardHeader}>
                  <View style={[styles.learningIconContainer, { backgroundColor: resource.icon.props.color + '20' }]}>
                    {resource.icon}
                  </View>
                  {resource.isNew && (
                    <View style={styles.learningBadge}>
                      <Text style={styles.learningBadgeText}>NEW</Text>
                    </View>
                  )}
                </View>
                <Text style={styles.learningCardTitle}>{resource.title}</Text>
                <Text style={styles.learningCardText}>{resource.description}</Text>
                <View style={styles.learningMeta}>
                  <Text style={styles.learningDuration}>{resource.duration}</Text>
                  <View style={styles.learningProgress}>
                    <View style={styles.learningProgressBar}>
                      <View style={[styles.learningProgressFill, { width: `${resource.progress}%`, backgroundColor: resource.icon.props.color }]} />
                    </View>
                    <Text style={styles.learningProgressText}>
                      {resource.progress === 0 ? 'Start' : `${resource.progress}%`}
                    </Text>
                  </View>
                </View>
              </Pressable>
            ))}
          </View>
        </View>

        {/* Call to Action */}
        <View style={styles.ctaContainer}>
          <LinearGradient
            colors={[colors.primary, colors.secondary]}
            style={styles.ctaGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.ctaContent}>
              <View style={styles.ctaIconBadge}>
                <Sparkles size={24} color={colors.white} />
              </View>
              <Text style={styles.ctaTitle}>Ready to Start Planning?</Text>
              <Text style={styles.ctaSubtitle}>
                Let Kaia help you create the perfect travel experience tailored just for you
              </Text>
              <Button
                title="Start Conversation"
                onPress={() => startConversation(quickActions[0])}
                variant="secondary"
                size="large"
                icon={<MessageCircle size={18} color={colors.white} />}
              />
            </View>
          </LinearGradient>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.lg,
  },
  headerTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  headerTitleContainer: {
    alignItems: "center",
    marginTop: spacing.md,
  },
  greeting: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
  },
  mainTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    textAlign: "center",
    marginBottom: spacing.xs,
  },
  mainSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: "center",
    fontWeight: typography.weights.medium as any,
  },
  iconButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.white,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },

  
  // Kaia Introduction
  introContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  introCard: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: spacing.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
    overflow: "hidden",
  },
  introGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  introContent: {
    alignItems: "center",
  },
  introHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  kaiaIconLarge: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 3,
  },
  introTextContainer: {
    flex: 1,
  },
  introTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  introSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.normal * typography.sizes.sm,
  },

  introStats: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  introStat: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    flex: 1,
    marginHorizontal: 2,
    justifyContent: "center",
  },
  introStatText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium as any,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },

  // AI Capabilities
  capabilitiesContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  capabilitiesTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.lg,
  },
  capabilitiesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  capabilityCard: {
    width: (width - spacing.lg * 2 - spacing.md) / 2,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
    alignItems: "center",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
  },
  capabilityIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.white,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: spacing.sm,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  capabilityTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    textAlign: "center",
    marginBottom: spacing.xs,
  },
  capabilityDescription: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    textAlign: "center",
    lineHeight: typography.lineHeights.normal * typography.sizes.xs,
  },

  // Quick Actions
  quickActionsContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  sectionHeader: {
    alignItems: "center",
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    textAlign: "center",
    marginBottom: spacing.xs,
  },
  sectionSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: "center",
    lineHeight: typography.lineHeights.normal * typography.sizes.md,
  },
  cardsContainer: {
    gap: spacing.md,
  },
  actionCard: {
    borderRadius: 20,
    overflow: "hidden",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 6,
  },
  cardGradient: {
    padding: spacing.lg,
  },
  cardContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  cardIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
  },
  cardTextContainer: {
    flex: 1,
  },
  cardTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  cardDescription: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: "rgba(255, 255, 255, 0.9)",
    lineHeight: typography.lineHeights.normal * typography.sizes.sm,
  },
  cardArrow: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
  },

  // Statistics
  statsContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  statsHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  statsTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  statsSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.lg,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
  },
  statsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  statCard: {
    width: (width - spacing.lg * 2 - spacing.sm) / 2,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    overflow: "hidden",
  },
  statGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  statHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  statIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
  },
  trendContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#ECFDF5",
    borderRadius: 12,
    paddingHorizontal: spacing.xs,
    paddingVertical: 4,
  },
  trendText: {
    fontFamily: typography.fontFamily,
    fontSize: 10,
    fontWeight: typography.weights.bold as any,
    color: "#10B981",
    marginLeft: 2,
  },
  statValue: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
    marginBottom: spacing.sm,
  },
  statProgress: {
    height: 4,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 2,
    overflow: "hidden",
  },
  statProgressBar: {
    height: "100%",
    borderRadius: 2,
  },

  // Recent Conversations
  conversationsContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  conversationsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: spacing.lg,
  },
  conversationsHeaderLeft: {
    flexDirection: "row",
    alignItems: "flex-start",
    flex: 1,
  },
  conversationsIconBadge: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.accent,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  conversationsTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  conversationsSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
  },
  viewAllButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.primary + '10',
    borderRadius: 20,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  viewAllText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium as any,
    color: colors.primary,
    marginRight: spacing.xs,
  },
  conversationsList: {
    gap: spacing.md,
  },
  conversationCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  conversationContent: {
    gap: spacing.sm,
  },
  conversationHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  conversationTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    flex: 1,
    marginRight: spacing.sm,
  },
  conversationMeta: {
    alignItems: "flex-end",
    gap: spacing.xs,
  },
  categoryBadge: {
    borderRadius: 12,
    paddingHorizontal: spacing.sm,
    paddingVertical: 4,
  },
  categoryBadgeText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium as any,
  },
  conversationTime: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
  },
  conversationMessage: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.normal * typography.sizes.sm,
  },
  conversationFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  conversationStatus: {
    flex: 1,
  },
  completedStatus: {
    flexDirection: "row",
    alignItems: "center",
  },
  completedText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.success,
    fontWeight: typography.weights.medium as any,
    marginLeft: spacing.xs,
  },
  activeStatus: {
    flexDirection: "row",
    alignItems: "center",
  },
  activeText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontWeight: typography.weights.medium as any,
    marginLeft: spacing.xs,
  },

  // Learning Hub
  learningContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  learningHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: spacing.lg,
  },
  learningHeaderLeft: {
    flexDirection: "row",
    alignItems: "flex-start",
    flex: 1,
  },
  learningIconBadge: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.secondary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  learningTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  learningSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
  },
  learningGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  learningCard: {
    width: (width - spacing.lg * 2 - spacing.md) / 2,
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: spacing.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 6,
    overflow: "hidden",
  },
  learningCardGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  learningCardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  learningIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
  },
  learningBadge: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingHorizontal: spacing.sm,
    paddingVertical: 4,
  },
  learningBadgeText: {
    fontFamily: typography.fontFamily,
    fontSize: 10,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
  },
  learningCardTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  learningCardText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.md,
    lineHeight: typography.lineHeights.normal * typography.sizes.sm,
  },
  learningMeta: {
    gap: spacing.sm,
  },
  learningDuration: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textLight,
    fontWeight: typography.weights.medium as any,
  },
  learningProgress: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  learningProgressBar: {
    flex: 1,
    height: 6,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 3,
    marginRight: spacing.sm,
    overflow: "hidden",
  },
  learningProgressFill: {
    height: "100%",
    borderRadius: 3,
  },
  learningProgressText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium as any,
    color: colors.textSecondary,
  },

  // Call to Action
  ctaContainer: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.xl,
    borderRadius: 24,
    overflow: "hidden",
    shadowColor: colors.shadowDark,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
  },
  ctaGradient: {
    padding: spacing.xl,
  },
  ctaContent: {
    alignItems: "center",
  },
  ctaIconBadge: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: spacing.lg,
  },
  ctaTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    textAlign: "center",
    marginBottom: spacing.sm,
  },
  ctaSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: "rgba(255, 255, 255, 0.9)",
    textAlign: "center",
    marginBottom: spacing.xl,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.md,
  },

  // Chat Screen Styles
  chatHeader: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: "center",
    alignItems: "center",
  },
  chatHeaderCenter: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    marginHorizontal: spacing.md,
  },
  headerRight: {
    width: 40,
  },
  kaiaIconChat: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.sm,
  },
  chatHeaderTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
  chatHeaderSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  messagesContent: {
    paddingVertical: spacing.md,
  },
  messageContainer: {
    flexDirection: "row",
    marginBottom: spacing.md,
  },
  userMessage: {
    justifyContent: "flex-end",
  },
  aiMessage: {
    justifyContent: "flex-start",
  },
  aiAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.white,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.sm,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  messageBubble: {
    maxWidth: "80%",
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
  },
  userBubble: {
    backgroundColor: colors.primary,
    borderBottomRightRadius: 6,
  },
  aiBubble: {
    backgroundColor: colors.white,
    borderBottomLeftRadius: 6,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  messageText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    lineHeight: typography.lineHeights.normal * typography.sizes.md,
  },
  userText: {
    color: colors.white,
  },
  aiText: {
    color: colors.text,
  },
  loadingText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    fontStyle: "italic",
  },
  inputContainer: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
  },
  inputRow: {
    flexDirection: "row",
    alignItems: "flex-end",
  },
  textInputContainer: {
    flex: 1,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 24,
    marginRight: spacing.sm,
  },
  textInput: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.text,
    maxHeight: 100,
    minHeight: 48,
  },
  voiceButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.secondary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.sm,
  },
  voiceButtonActive: {
    backgroundColor: colors.error,
  },
  sendButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
  },
  sendButtonDisabled: {
    backgroundColor: colors.textLight,
  },
});