import 'dart:io';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';

// Project imports
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/travel/restaurant.dart';
import 'package:culture_connect/models/travel/restaurant_reservation.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Restaurant PDF generation service for CultureConnect booking confirmations
/// Replicates the exact visual appearance and content of mobile confirmation screens
/// with exact visual fidelity for both digital and print use
class RestaurantPdfService {
  static const String _fontFamily = 'Roboto';

  /// Generate PDF for restaurant reservation with specialized content adaptation
  static Future<File> generateRestaurantReservationPdf({
    required Booking booking,
    required RestaurantReservation reservation,
    required Restaurant restaurant,
    required String transactionId,
    required String userEmail,
    required String userName,
    String? userPhone,
  }) async {
    final pdf = pw.Document();

    // Load fonts for consistent typography
    final fontRegular = await rootBundle.load('assets/fonts/Roboto-Regular.ttf');
    final fontBold = await rootBundle.load('assets/fonts/Roboto-Bold.ttf');
    final ttfRegular = pw.Font.ttf(fontRegular);
    final ttfBold = pw.Font.ttf(fontBold);

    // Create PDF pages with restaurant-specific content
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (pw.Context context) {
          return [
            _buildPdfHeader(restaurant, reservation, ttfBold, ttfRegular),
            pw.SizedBox(height: 20),
            _buildReservationDetails(reservation, restaurant, ttfBold, ttfRegular),
            pw.SizedBox(height: 20),
            _buildPaymentInformation(booking, transactionId, ttfBold, ttfRegular),
            pw.SizedBox(height: 20),
            _buildContactInformation(reservation, ttfBold, ttfRegular),
            pw.SizedBox(height: 20),
            _buildFooter(ttfRegular),
          ];
        },
      ),
    );

    // Save PDF to device
    final directory = await getApplicationDocumentsDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final file = File(
        '${directory.path}/restaurant_reservation_${reservation.id.substring(0, 8)}_$timestamp.pdf');
    await file.writeAsBytes(await pdf.save());

    return file;
  }

  /// Build PDF header with restaurant and reservation info
  static pw.Widget _buildPdfHeader(
    Restaurant restaurant,
    RestaurantReservation reservation,
    pw.Font fontBold,
    pw.Font fontRegular,
  ) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: _hexToColor('#6366F1'), // AppTheme.primaryColor
        borderRadius: pw.BorderRadius.circular(16),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'CultureConnect',
                style: pw.TextStyle(
                  font: fontBold,
                  fontSize: 18,
                  color: PdfColors.white,
                ),
              ),
              pw.Container(
                padding: const pw.EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: pw.BoxDecoration(
                  color: PdfColors.green,
                  borderRadius: pw.BorderRadius.circular(20),
                ),
                child: pw.Text(
                  'CONFIRMED',
                  style: pw.TextStyle(
                    font: fontBold,
                    fontSize: 12,
                    color: PdfColors.white,
                  ),
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 16),
          pw.Text(
            'Restaurant Reservation',
            style: pw.TextStyle(
              font: fontBold,
              fontSize: 24,
              color: PdfColors.white,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            restaurant.name,
            style: pw.TextStyle(
              font: fontBold,
              fontSize: 20,
              color: PdfColors.white,
            ),
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            restaurant.location,
            style: pw.TextStyle(
              font: fontRegular,
              fontSize: 14,
              color: PdfColors.white,
            ),
          ),
          pw.SizedBox(height: 12),
          pw.Text(
            'Reservation ID: ${reservation.id.substring(0, 8).toUpperCase()}',
            style: pw.TextStyle(
              font: fontRegular,
              fontSize: 14,
              color: PdfColors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// Build reservation details section
  static pw.Widget _buildReservationDetails(
    RestaurantReservation reservation,
    Restaurant restaurant,
    pw.Font fontBold,
    pw.Font fontRegular,
  ) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(16),
        border: pw.Border.all(color: PdfColors.grey300, width: 1),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Reservation Details',
            style: pw.TextStyle(
              font: fontBold,
              fontSize: 18,
              color: PdfColors.black,
            ),
          ),
          pw.SizedBox(height: 16),
          
          _buildDetailRow('Date', DateFormat('EEEE, MMMM d, yyyy').format(reservation.date), fontBold, fontRegular),
          _buildDetailRow('Time', '${reservation.timeSlot.startTime} - ${reservation.timeSlot.endTime}', fontBold, fontRegular),
          _buildDetailRow('Party Size', '${reservation.partySize} ${reservation.partySize == 1 ? 'Guest' : 'Guests'}', fontBold, fontRegular),
          _buildDetailRow('Restaurant', restaurant.name, fontBold, fontRegular),
          _buildDetailRow('Location', restaurant.location, fontBold, fontRegular),
          _buildDetailRow('Cuisine', restaurant.formattedCuisineTypes, fontBold, fontRegular),
          
          if (reservation.specialRequests.isNotEmpty) ...[
            pw.SizedBox(height: 8),
            _buildDetailRow('Special Requests', reservation.specialRequests, fontBold, fontRegular),
          ],
        ],
      ),
    );
  }

  /// Build payment information section
  static pw.Widget _buildPaymentInformation(
    Booking booking,
    String transactionId,
    pw.Font fontBold,
    pw.Font fontRegular,
  ) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(16),
        border: pw.Border.all(color: PdfColors.grey300, width: 1),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Payment Information',
            style: pw.TextStyle(
              font: fontBold,
              fontSize: 18,
              color: PdfColors.black,
            ),
          ),
          pw.SizedBox(height: 16),
          
          _buildDetailRow('Transaction ID', transactionId, fontBold, fontRegular),
          _buildDetailRow('Amount Paid', '\$${booking.totalAmount.toStringAsFixed(2)}', fontBold, fontRegular),
          _buildDetailRow('Payment Status', 'Confirmed', fontBold, fontRegular),
          _buildDetailRow('Payment Date', DateFormat('MMM d, yyyy \'at\' h:mm a').format(DateTime.now()), fontBold, fontRegular),
        ],
      ),
    );
  }

  /// Build contact information section
  static pw.Widget _buildContactInformation(
    RestaurantReservation reservation,
    pw.Font fontBold,
    pw.Font fontRegular,
  ) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(16),
        border: pw.Border.all(color: PdfColors.grey300, width: 1),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Contact Information',
            style: pw.TextStyle(
              font: fontBold,
              fontSize: 18,
              color: PdfColors.black,
            ),
          ),
          pw.SizedBox(height: 16),
          
          _buildDetailRow('Name', reservation.userName, fontBold, fontRegular),
          _buildDetailRow('Email', reservation.contactEmail, fontBold, fontRegular),
          _buildDetailRow('Phone', reservation.contactPhone, fontBold, fontRegular),
        ],
      ),
    );
  }

  /// Build detail row for consistent formatting
  static pw.Widget _buildDetailRow(String label, String value, pw.Font fontBold, pw.Font fontRegular) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 4),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Expanded(
            flex: 2,
            child: pw.Text(
              label,
              style: pw.TextStyle(
                font: fontBold,
                fontSize: 12,
                color: PdfColors.grey700,
              ),
            ),
          ),
          pw.Expanded(
            flex: 3,
            child: pw.Text(
              value,
              style: pw.TextStyle(
                font: fontRegular,
                fontSize: 12,
                color: PdfColors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build footer with app info
  static pw.Widget _buildFooter(pw.Font fontRegular) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border(
          top: pw.BorderSide(color: PdfColors.grey300, width: 1),
        ),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            'CultureConnect - The Soul of Travel, Guided by AI',
            style: pw.TextStyle(
              font: fontRegular,
              fontSize: 12,
              color: PdfColors.grey600,
            ),
            textAlign: pw.TextAlign.center,
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            'Generated on ${DateFormat('MMM d, yyyy \'at\' h:mm a').format(DateTime.now())}',
            style: pw.TextStyle(
              font: fontRegular,
              fontSize: 10,
              color: PdfColors.grey500,
            ),
            textAlign: pw.TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Convert hex color to PdfColor
  static PdfColor _hexToColor(String hex) {
    hex = hex.replaceAll('#', '');
    if (hex.length == 6) {
      hex = 'FF$hex'; // Add alpha if not present
    }
    return PdfColor.fromInt(int.parse(hex, radix: 16));
  }
}
