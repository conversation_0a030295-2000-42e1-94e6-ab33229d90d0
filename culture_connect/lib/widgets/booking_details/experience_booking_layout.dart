import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:culture_connect/models/premium_booking.dart';
import 'package:culture_connect/design_system/premium_design_tokens.dart';
import 'package:culture_connect/widgets/booking_details/experience_specialized_sections.dart';

/// Experience types for intelligent content adaptation
enum ExperienceType {
  musicalFestival,
  cookingClass,
  artWorkshop,
  adventureActivity,
  culturalTour,
  generic,
}

/// Experience-specific booking layout with intelligent content adaptation
/// Automatically detects experience type and displays relevant specialized sections
class ExperienceBookingLayout extends StatefulWidget {
  final PremiumBooking booking;

  const ExperienceBookingLayout({
    super.key,
    required this.booking,
  });

  @override
  State<ExperienceBookingLayout> createState() =>
      _ExperienceBookingLayoutState();
}

class _ExperienceBookingLayoutState extends State<ExperienceBookingLayout>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late ExperienceType _experienceType;

  @override
  void initState() {
    super.initState();
    _experienceType = _detectExperienceType();
    _initializeAnimations();
    _startAnimations();
  }

  /// Intelligently detects experience type based on booking data
  ExperienceType _detectExperienceType() {
    final title = widget.booking.title.toLowerCase();
    final subtitle = widget.booking.subtitle.toLowerCase();
    final highlights = widget.booking.highlights.join(' ').toLowerCase();
    final details =
        widget.booking.details?.values.join(' ').toLowerCase() ?? '';
    final allText = '$title $subtitle $highlights $details';

    // Musical Festival detection
    if (_containsAny(allText, [
      'concert',
      'festival',
      'music',
      'band',
      'artist',
      'stage',
      'performance',
      'live music',
      'dj',
      'orchestra',
      'symphony',
      'opera',
      'jazz',
      'rock',
      'pop',
      'classical',
      'venue',
      'amphitheater',
      'arena'
    ])) {
      return ExperienceType.musicalFestival;
    }

    // Cooking Class detection
    if (_containsAny(allText, [
      'cooking',
      'culinary',
      'chef',
      'kitchen',
      'recipe',
      'food',
      'cuisine',
      'baking',
      'pastry',
      'wine tasting',
      'food tour',
      'market tour',
      'ingredients',
      'dining',
      'restaurant',
      'meal',
      'dish'
    ])) {
      return ExperienceType.cookingClass;
    }

    // Art Workshop detection
    if (_containsAny(allText, [
      'art',
      'painting',
      'drawing',
      'sculpture',
      'pottery',
      'ceramics',
      'workshop',
      'studio',
      'canvas',
      'brush',
      'craft',
      'creative',
      'artistic',
      'gallery',
      'exhibition',
      'masterpiece',
      'sketch'
    ])) {
      return ExperienceType.artWorkshop;
    }

    // Adventure Activity detection
    if (_containsAny(allText, [
      'hiking',
      'climbing',
      'adventure',
      'outdoor',
      'mountain',
      'trail',
      'kayaking',
      'rafting',
      'diving',
      'snorkeling',
      'safari',
      'wildlife',
      'extreme',
      'adrenaline',
      'zip line',
      'bungee',
      'paragliding'
    ])) {
      return ExperienceType.adventureActivity;
    }

    // Cultural Tour detection
    if (_containsAny(allText, [
      'museum',
      'historical',
      'heritage',
      'cultural',
      'monument',
      'ancient',
      'architecture',
      'guided tour',
      'history',
      'archaeological',
      'temple',
      'palace',
      'castle',
      'landmark',
      'unesco',
      'traditional'
    ])) {
      return ExperienceType.culturalTour;
    }

    return ExperienceType.generic;
  }

  /// Helper method to check if text contains any of the keywords
  bool _containsAny(String text, List<String> keywords) {
    return keywords.any((keyword) => text.contains(keyword));
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _startAnimations() {
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildExperienceOverviewSection(),
            _buildMeetingPointSection(),
            ..._buildSpecializedSections(),
            _buildInclusionsSection(),
            _buildHighlightsSection(),
            _buildCancellationPolicySection(),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  /// Builds specialized sections based on detected experience type
  List<Widget> _buildSpecializedSections() {
    switch (_experienceType) {
      case ExperienceType.musicalFestival:
        return [
          _buildArtistLineupSection(),
          _buildVenueDetailsSection(),
          _buildStageScheduleSection(),
        ];
      case ExperienceType.cookingClass:
        return [
          _buildChefInformationSection(),
          ExperienceSpecializedSections.buildMenuRecipesSection(),
          ExperienceSpecializedSections.buildKitchenFacilitiesSection(),
        ];
      case ExperienceType.artWorkshop:
        return [
          ExperienceSpecializedSections.buildInstructorProfileSection(),
          ExperienceSpecializedSections.buildMaterialsProvidedSection(),
          ExperienceSpecializedSections.buildStudioInformationSection(),
        ];
      case ExperienceType.adventureActivity:
        return [
          ExperienceSpecializedSections.buildSafetyEquipmentSection(),
          _buildFitnessRequirementsSection(),
          _buildWeatherConsiderationsSection(),
        ];
      case ExperienceType.culturalTour:
        return [
          _buildHistoricalContextSection(),
          _buildExpertGuideSection(),
          _buildSiteSignificanceSection(),
        ];
      case ExperienceType.generic:
      default:
        return [_buildItinerarySection()];
    }
  }

  Widget _buildExperienceOverviewSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.local_activity,
                color: const Color(0xFF8B5CF6),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Experience Overview',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildOverviewCard(
                  icon: Icons.schedule,
                  title: 'Duration',
                  value: widget.booking.duration,
                  color: const Color(0xFF06B6D4),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildOverviewCard(
                  icon: Icons.people,
                  title: 'Group Size',
                  value: '${widget.booking.participants} people',
                  color: const Color(0xFF10B981),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildOverviewCard(
                  icon: Icons.language,
                  title: 'Language',
                  value: 'English, Spanish',
                  color: const Color(0xFFF59E0B),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildOverviewCard(
                  icon: Icons.accessibility,
                  title: 'Difficulty',
                  value: 'Easy',
                  color: const Color(0xFF10B981),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMeetingPointSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: const Color(0xFFEF4444),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Meeting Point',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFEF4444).withAlpha(26),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFFEF4444).withAlpha(51),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Central Park Entrance',
                            style: PremiumDesignTokens.bodyLarge.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '59th St & 5th Ave, New York, NY',
                            style: PremiumDesignTokens.bodyMedium.copyWith(
                              color: PremiumDesignTokens.neutralGray600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Meet at 9:00 AM sharp',
                            style: PremiumDesignTokens.labelMedium.copyWith(
                              color: const Color(0xFFEF4444),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        HapticFeedback.lightImpact();
                        _openMaps();
                      },
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: const Color(0xFFEF4444),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.directions,
                          color: PremiumDesignTokens.neutralWhite,
                          size: 20,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF59E0B).withAlpha(26),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: const Color(0xFFF59E0B),
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Look for the guide with a red umbrella',
                          style: PremiumDesignTokens.bodySmall.copyWith(
                            color: const Color(0xFFF59E0B),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItinerarySection() {
    final itineraryItems = [
      {
        'time': '9:00 AM',
        'activity': 'Meet at Central Park Entrance',
        'duration': '15 min'
      },
      {
        'time': '9:15 AM',
        'activity': 'Walking tour through Bethesda Fountain',
        'duration': '45 min'
      },
      {
        'time': '10:00 AM',
        'activity': 'Visit Strawberry Fields',
        'duration': '30 min'
      },
      {
        'time': '10:30 AM',
        'activity': 'Explore The Mall and Literary Walk',
        'duration': '45 min'
      },
      {
        'time': '11:15 AM',
        'activity': 'Photo session at Bow Bridge',
        'duration': '30 min'
      },
      {
        'time': '11:45 AM',
        'activity': 'Q&A and farewell',
        'duration': '15 min'
      },
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.schedule,
                color: const Color(0xFF06B6D4),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Itinerary',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: itineraryItems.length,
            itemBuilder: (context, index) {
              final item = itineraryItems[index];
              final isLast = index == itineraryItems.length - 1;

              return _buildItineraryItem(
                time: item['time']!,
                activity: item['activity']!,
                duration: item['duration']!,
                isLast: isLast,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInclusionsSection() {
    final inclusions = [
      'Professional tour guide',
      'Small group experience (max 12 people)',
      'Historical insights and stories',
      'Photo opportunities',
      'Map and recommendations',
    ];

    final exclusions = [
      'Food and beverages',
      'Transportation to meeting point',
      'Gratuities',
      'Personal expenses',
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.check_circle_outline,
                color: const Color(0xFF10B981),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'What\'s Included',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF10B981).withAlpha(26),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF10B981).withAlpha(51),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Included',
                  style: PremiumDesignTokens.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF10B981),
                  ),
                ),
                const SizedBox(height: 12),
                ...inclusions.map((inclusion) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Icon(
                            Icons.check,
                            color: const Color(0xFF10B981),
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              inclusion,
                              style: PremiumDesignTokens.bodyMedium,
                            ),
                          ),
                        ],
                      ),
                    )),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFEF4444).withAlpha(26),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFFEF4444).withAlpha(51),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Not Included',
                  style: PremiumDesignTokens.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFFEF4444),
                  ),
                ),
                const SizedBox(height: 12),
                ...exclusions.map((exclusion) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Icon(
                            Icons.close,
                            color: const Color(0xFFEF4444),
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              exclusion,
                              style: PremiumDesignTokens.bodyMedium,
                            ),
                          ),
                        ],
                      ),
                    )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHighlightsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.star,
                color: const Color(0xFFF59E0B),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Experience Highlights',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...widget.booking.highlights.map((highlight) => Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFFF59E0B).withAlpha(26),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFFF59E0B).withAlpha(51),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.star,
                      color: const Color(0xFFF59E0B),
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        highlight,
                        style: PremiumDesignTokens.bodyMedium.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildCancellationPolicySection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.policy,
                color: const Color(0xFF8B5CF6),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Cancellation Policy',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF8B5CF6).withAlpha(26),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF8B5CF6).withAlpha(51),
                width: 1,
              ),
            ),
            child: Text(
              widget.booking.cancellationPolicy ??
                  'Free cancellation up to 24 hours before the experience starts. After that, no refunds will be provided.',
              style: PremiumDesignTokens.bodyMedium.copyWith(
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withAlpha(51),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: PremiumDesignTokens.labelMedium.copyWith(
                  color: PremiumDesignTokens.neutralGray600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: PremiumDesignTokens.bodyLarge.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItineraryItem({
    required String time,
    required String activity,
    required String duration,
    required bool isLast,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: const Color(0xFF06B6D4),
                shape: BoxShape.circle,
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 40,
                color: const Color(0xFF06B6D4).withAlpha(77),
              ),
          ],
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Container(
            margin: EdgeInsets.only(bottom: isLast ? 0 : 16),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF06B6D4).withAlpha(26),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFF06B6D4).withAlpha(51),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      time,
                      style: PremiumDesignTokens.labelMedium.copyWith(
                        color: const Color(0xFF06B6D4),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      duration,
                      style: PremiumDesignTokens.labelSmall.copyWith(
                        color: PremiumDesignTokens.neutralGray600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  activity,
                  style: PremiumDesignTokens.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _openMaps() {
    // TODO: Implement maps integration
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Opening directions...'),
        backgroundColor: const Color(0xFFEF4444),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  // ============================================================================
  // SPECIALIZED SECTIONS FOR DIFFERENT EXPERIENCE TYPES
  // ============================================================================

  /// Musical Festival - Artist Lineup Section
  Widget _buildArtistLineupSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.music_note,
                color: const Color(0xFF8B5CF6),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Artist Lineup',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildArtistCard(
              'Main Stage', 'The Midnight', '8:00 PM - 10:00 PM', true),
          _buildArtistCard(
              'Side Stage', 'Electric Youth', '6:30 PM - 7:30 PM', false),
          _buildArtistCard('Main Stage', 'FM-84', '10:30 PM - 12:00 AM', false),
        ],
      ),
    );
  }

  Widget _buildArtistCard(
      String stage, String artist, String time, bool isHeadliner) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isHeadliner
            ? const Color(0xFF8B5CF6).withAlpha(26)
            : PremiumDesignTokens.neutralGray100,
        borderRadius: BorderRadius.circular(12),
        border: isHeadliner
            ? Border.all(color: const Color(0xFF8B5CF6).withAlpha(51), width: 1)
            : null,
      ),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 40,
            decoration: BoxDecoration(
              color: isHeadliner
                  ? const Color(0xFF8B5CF6)
                  : const Color(0xFF06B6D4),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  artist,
                  style: PremiumDesignTokens.bodyLarge.copyWith(
                    fontWeight: isHeadliner ? FontWeight.w700 : FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '$stage • $time',
                  style: PremiumDesignTokens.bodyMedium.copyWith(
                    color: PremiumDesignTokens.neutralGray600,
                  ),
                ),
              ],
            ),
          ),
          if (isHeadliner)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFF8B5CF6),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'HEADLINER',
                style: PremiumDesignTokens.labelSmall.copyWith(
                  fontWeight: FontWeight.w700,
                  color: PremiumDesignTokens.neutralWhite,
                  letterSpacing: 0.5,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Musical Festival - Venue Details Section
  Widget _buildVenueDetailsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.place,
                color: const Color(0xFFEF4444),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Venue Information',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildVenueInfoRow(
              Icons.location_city, 'Venue', 'Red Rocks Amphitheatre'),
          _buildVenueInfoRow(Icons.people, 'Capacity', '9,525 people'),
          _buildVenueInfoRow(
              Icons.local_parking, 'Parking', 'Available on-site (\$25)'),
          _buildVenueInfoRow(
              Icons.accessible, 'Accessibility', 'ADA compliant seating'),
        ],
      ),
    );
  }

  Widget _buildVenueInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            color: const Color(0xFF06B6D4),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  label,
                  style: PremiumDesignTokens.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                    color: PremiumDesignTokens.neutralGray700,
                  ),
                ),
                Flexible(
                  child: Text(
                    value,
                    style: PremiumDesignTokens.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Musical Festival - Stage Schedule Section
  Widget _buildStageScheduleSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.schedule,
                color: const Color(0xFF10B981),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Stage Schedule',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildScheduleItem(
              '6:00 PM', 'Gates Open', 'General admission begins'),
          _buildScheduleItem(
              '6:30 PM', 'Electric Youth', 'Side Stage performance'),
          _buildScheduleItem('8:00 PM', 'The Midnight', 'Main Stage headliner'),
          _buildScheduleItem('10:30 PM', 'FM-84', 'Main Stage closing act'),
        ],
      ),
    );
  }

  Widget _buildScheduleItem(String time, String event, String description) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF10B981).withAlpha(26),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF10B981).withAlpha(51),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            padding: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: const Color(0xFF10B981),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              time,
              textAlign: TextAlign.center,
              style: PremiumDesignTokens.labelMedium.copyWith(
                color: PremiumDesignTokens.neutralWhite,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  event,
                  style: PremiumDesignTokens.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: PremiumDesignTokens.bodyMedium.copyWith(
                    color: PremiumDesignTokens.neutralGray600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // ============================================================================
  // COOKING CLASS SPECIALIZED SECTIONS
  // ============================================================================

  /// Cooking Class - Chef Information Section
  Widget _buildChefInformationSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.person,
                color: const Color(0xFFF59E0B),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Your Chef',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: const Color(0xFFF59E0B),
                  borderRadius: BorderRadius.circular(40),
                ),
                child: Icon(
                  Icons.person,
                  color: PremiumDesignTokens.neutralWhite,
                  size: 40,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Chef Maria Rodriguez',
                      style: PremiumDesignTokens.bodyLarge.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Michelin-starred chef with 15 years experience',
                      style: PremiumDesignTokens.bodyMedium.copyWith(
                        color: PremiumDesignTokens.neutralGray600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          color: const Color(0xFFF59E0B),
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '4.9 (127 reviews)',
                          style: PremiumDesignTokens.bodySmall.copyWith(
                            color: PremiumDesignTokens.neutralGray600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // ============================================================================
  // PLACEHOLDER METHODS FOR REMAINING SPECIALIZED SECTIONS
  // ============================================================================

  /// Adventure Activity - Fitness Requirements Section
  Widget _buildFitnessRequirementsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.fitness_center,
                color: const Color(0xFF10B981),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Fitness Requirements',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildRequirementItem('Moderate fitness level required'),
          _buildRequirementItem('Ability to hike 3-5 miles'),
          _buildRequirementItem('Comfortable with heights'),
          _buildRequirementItem('No serious medical conditions'),
        ],
      ),
    );
  }

  Widget _buildRequirementItem(String requirement) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF10B981).withAlpha(26),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: const Color(0xFF10B981),
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              requirement,
              style: PremiumDesignTokens.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  /// Adventure Activity - Weather Considerations Section
  Widget _buildWeatherConsiderationsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.wb_cloudy,
                color: const Color(0xFF06B6D4),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Weather Considerations',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Activity operates in most weather conditions. In case of severe weather, we will reschedule or provide full refund.',
            style: PremiumDesignTokens.bodyMedium.copyWith(
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// Cultural Tour - Historical Context Section
  Widget _buildHistoricalContextSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.history_edu,
                color: const Color(0xFF8B5CF6),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Historical Context',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Discover the rich history spanning over 500 years, from ancient civilizations to modern cultural significance.',
            style: PremiumDesignTokens.bodyMedium.copyWith(
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// Cultural Tour - Expert Guide Section
  Widget _buildExpertGuideSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.school,
                color: const Color(0xFFF59E0B),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Expert Guide',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Led by Dr. Sarah Johnson, PhD in Art History with 20+ years of expertise in cultural heritage.',
            style: PremiumDesignTokens.bodyMedium.copyWith(
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// Cultural Tour - Site Significance Section
  Widget _buildSiteSignificanceSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.place,
                color: const Color(0xFFEF4444),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Site Significance',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'UNESCO World Heritage Site recognized for its outstanding universal value and cultural importance.',
            style: PremiumDesignTokens.bodyMedium.copyWith(
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
