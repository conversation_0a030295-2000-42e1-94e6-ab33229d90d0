// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/visa/self_service_models.dart';
import 'package:culture_connect/services/travel/visa/self_service_visa_service.dart';
import 'package:culture_connect/screens/travel/visa/eligibility_checker_screen.dart';
import 'package:culture_connect/screens/travel/visa/document_checklist_screen.dart';
import 'package:culture_connect/screens/travel/visa/timeline_calculator_screen.dart';
import 'package:culture_connect/screens/travel/visa/visa_faq_screen.dart';
import 'package:culture_connect/screens/travel/visa/visa_provider_marketplace_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// Main hub for self-service visa resources and tools
class SelfServiceHubScreen extends ConsumerStatefulWidget {
  const SelfServiceHubScreen({
    super.key,
    this.passportCountry,
    this.destinationCountry,
    this.visaType,
  });

  final String? passportCountry;
  final String? destinationCountry;
  final String? visaType;

  @override
  ConsumerState<SelfServiceHubScreen> createState() =>
      _SelfServiceHubScreenState();
}

class _SelfServiceHubScreenState extends ConsumerState<SelfServiceHubScreen> {
  List<SelfServiceResource> _resources = [];
  SelfServiceUserProgress? _userProgress;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadResources();
    _loadUserProgress();
  }

  Future<void> _loadResources() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final selfServiceService = ref.read(selfServiceVisaServiceProvider);
      final resources = await selfServiceService.getResources(
        country: widget.destinationCountry,
        visaType: widget.visaType,
        featuredOnly: false,
      );

      if (mounted) {
        setState(() {
          _resources = resources;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to load resources: $e';
        });
      }
    }
  }

  Future<void> _loadUserProgress() async {
    if (widget.destinationCountry == null || widget.visaType == null) return;

    try {
      final selfServiceService = ref.read(selfServiceVisaServiceProvider);
      final progress = await selfServiceService.getUserProgress(
        userId: 'current_user', // TODO: Get actual user ID
        country: widget.destinationCountry!,
        visaType: widget.visaType!,
      );

      if (mounted) {
        setState(() {
          _userProgress = progress;
        });
      }
    } catch (e) {
      // Progress loading is optional, don't show error
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        title: Text(
          'Self-Service Resources',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: AppTheme.fontWeightBold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: AppTheme.spacingMd),
            child: IconButton(
              onPressed: _showSelfServiceInfo,
              icon: Container(
                padding: const EdgeInsets.all(AppTheme.spacingXs),
                decoration: BoxDecoration(
                  color: AppTheme.infoColor.withValues(alpha: 0.1),
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: const Icon(
                  Icons.info_outline,
                  color: AppTheme.infoColor,
                  size: 20,
                ),
              ),
              tooltip: 'About self-service resources',
            ),
          ),
        ],
      ),
      body: _buildBody(theme),
    );
  }

  Widget _buildBody(ThemeData theme) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: AppTheme.primaryColor,
            ),
            SizedBox(height: AppTheme.spacingMd),
            Text(
              'Loading self-service resources...',
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
                fontWeight: AppTheme.fontWeightMedium,
              ),
            ),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return ErrorView(
        error: _errorMessage!,
        onRetry: _loadResources,
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await _loadResources();
        await _loadUserProgress();
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeroSection(theme),
            const SizedBox(height: AppTheme.spacingLg),
            if (_userProgress != null) ...[
              _buildProgressCard(theme),
              const SizedBox(height: AppTheme.spacingLg),
            ],
            _buildQuickTools(theme),
            const SizedBox(height: AppTheme.spacingLg),
            _buildFeaturedResources(theme),
            const SizedBox(height: AppTheme.spacingLg),
            _buildAllResources(theme),
            const SizedBox(height: AppTheme.spacingLg),
            _buildNeedHelpSection(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildHeroSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.infoColor.withValues(alpha: 0.1),
            AppTheme.primaryColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        border: Border.all(
          color: AppTheme.infoColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingMd),
                decoration: BoxDecoration(
                  color: AppTheme.infoColor,
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusLarge),
                ),
                child: const Icon(
                  Icons.self_improvement,
                  color: AppTheme.white,
                  size: 32,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMd),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Apply Independently',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: AppTheme.fontWeightBold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    Text(
                      'Use our comprehensive tools and guides to apply for your visa yourself',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingSm),
            decoration: BoxDecoration(
              color: AppTheme.successColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.savings,
                  color: AppTheme.successColor,
                  size: 16,
                ),
                const SizedBox(width: AppTheme.spacingSm),
                Text(
                  'Save money by applying yourself',
                  style: TextStyle(
                    color: AppTheme.successColor,
                    fontWeight: AppTheme.fontWeightSemibold,
                    fontSize: AppTheme.fontSizeSm,
                  ),
                ),
              ],
            ),
          ),
          if (widget.destinationCountry != null) ...[
            const SizedBox(height: AppTheme.spacingSm),
            Text(
              'Resources for: ${widget.destinationCountry}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: AppTheme.textTertiaryColor,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProgressCard(ThemeData theme) {
    final progress = _userProgress!;
    final progressValue = progress.completionPercentage / 100.0;

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Your Progress',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: AppTheme.fontWeightBold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              Text(
                '${progress.completionPercentage}%',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: AppTheme.fontWeightBold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          LinearProgressIndicator(
            value: progressValue,
            backgroundColor: AppTheme.borderColor,
            valueColor:
                const AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          if (progress.currentStep != null) ...[
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingSm),
              decoration: BoxDecoration(
                color: AppTheme.infoColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.flag,
                    color: AppTheme.infoColor,
                    size: 16,
                  ),
                  const SizedBox(width: AppTheme.spacingSm),
                  Expanded(
                    child: Text(
                      'Current: ${progress.currentStep}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.infoColor,
                        fontWeight: AppTheme.fontWeightMedium,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickTools(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Tools',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: AppTheme.fontWeightBold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMd),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: AppTheme.spacingMd,
          mainAxisSpacing: AppTheme.spacingMd,
          childAspectRatio: 1.2,
          children: [
            _buildQuickToolCard(
              theme,
              'Eligibility Check',
              'Check if you qualify',
              Icons.check_circle,
              AppTheme.successColor,
              () => _navigateToEligibilityChecker(),
            ),
            _buildQuickToolCard(
              theme,
              'Document List',
              'Get required documents',
              Icons.checklist,
              AppTheme.infoColor,
              () => _navigateToDocumentChecklist(),
            ),
            _buildQuickToolCard(
              theme,
              'Timeline Planner',
              'Plan your application',
              Icons.schedule,
              AppTheme.warningColor,
              () => _navigateToTimelineCalculator(),
            ),
            _buildQuickToolCard(
              theme,
              'FAQ & Help',
              'Find quick answers',
              Icons.help,
              AppTheme.primaryColor,
              () => _navigateToFAQ(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickToolCard(
    ThemeData theme,
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Material(
      color: AppTheme.white,
      borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        child: Container(
          padding: const EdgeInsets.all(AppTheme.spacingMd),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingMd),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusLarge),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 32,
                ),
              ),
              const SizedBox(height: AppTheme.spacingMd),
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: AppTheme.fontWeightBold,
                  color: AppTheme.textPrimaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppTheme.spacingXs),
              Text(
                description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturedResources(ThemeData theme) {
    final featuredResources = _resources.where((r) => r.isFeatured).toList();

    if (featuredResources.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Featured Guides',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: AppTheme.fontWeightBold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMd),
        ...featuredResources
            .map((resource) => _buildResourceCard(resource, theme)),
      ],
    );
  }

  Widget _buildAllResources(ThemeData theme) {
    final nonFeaturedResources =
        _resources.where((r) => !r.isFeatured).toList();

    if (nonFeaturedResources.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'All Resources',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: AppTheme.fontWeightBold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMd),
        ...nonFeaturedResources
            .map((resource) => _buildResourceCard(resource, theme)),
      ],
    );
  }

  Widget _buildResourceCard(SelfServiceResource resource, ThemeData theme) {
    final color = _getResourceColor(resource.colorTheme);
    final icon = _getResourceIcon(resource.iconName);

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMd),
      child: Material(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        elevation: 2,
        child: InkWell(
          onTap: () => _navigateToResource(resource),
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
          child: Container(
            padding: const EdgeInsets.all(AppTheme.spacingMd),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingMd),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusLarge),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingMd),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              resource.title,
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: AppTheme.fontWeightBold,
                                color: AppTheme.textPrimaryColor,
                              ),
                            ),
                          ),
                          if (resource.estimatedTime != null) ...[
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppTheme.spacingSm,
                                vertical: AppTheme.spacingXs,
                              ),
                              decoration: BoxDecoration(
                                color:
                                    AppTheme.borderColor.withValues(alpha: 0.3),
                                borderRadius: BorderRadius.circular(
                                    AppTheme.borderRadiusSmall),
                              ),
                              child: Text(
                                _formatDuration(resource.estimatedTime!),
                                style: TextStyle(
                                  fontSize: AppTheme.fontSizeXs,
                                  color: AppTheme.textTertiaryColor,
                                  fontWeight: AppTheme.fontWeightMedium,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: AppTheme.spacingXs),
                      Text(
                        resource.description,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondaryColor,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (resource.difficulty != null) ...[
                        const SizedBox(height: AppTheme.spacingXs),
                        Row(
                          children: [
                            Icon(
                              _getDifficultyIcon(resource.difficulty!),
                              size: 14,
                              color: _getDifficultyColor(resource.difficulty!),
                            ),
                            const SizedBox(width: AppTheme.spacingXs),
                            Text(
                              _getDifficultyText(resource.difficulty!),
                              style: TextStyle(
                                fontSize: AppTheme.fontSizeXs,
                                color:
                                    _getDifficultyColor(resource.difficulty!),
                                fontWeight: AppTheme.fontWeightMedium,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  color: AppTheme.textTertiaryColor,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNeedHelpSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryColor.withValues(alpha: 0.1),
            AppTheme.primaryColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        border: Border.all(
          color: AppTheme.primaryColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingSm),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: const Icon(
                  Icons.people_alt,
                  color: AppTheme.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMd),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Need Professional Help?',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: AppTheme.fontWeightBold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    Text(
                      'Get expert assistance from certified visa consultants',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _navigateToConsultants,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: AppTheme.white,
                padding:
                    const EdgeInsets.symmetric(vertical: AppTheme.spacingMd),
                shape: RoundedRectangleBorder(
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusLarge),
                ),
              ),
              child: const Text(
                'Find Visa Consultants',
                style: TextStyle(
                  fontWeight: AppTheme.fontWeightSemibold,
                  fontSize: AppTheme.fontSizeMd,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  Color _getResourceColor(String colorTheme) {
    switch (colorTheme) {
      case 'blue':
        return AppTheme.primaryColor;
      case 'green':
        return AppTheme.successColor;
      case 'purple':
        return Colors.purple;
      case 'orange':
        return AppTheme.warningColor;
      case 'teal':
        return Colors.teal;
      default:
        return AppTheme.infoColor;
    }
  }

  IconData _getResourceIcon(String iconName) {
    switch (iconName) {
      case 'book':
        return Icons.book;
      case 'checklist':
        return Icons.checklist;
      case 'check_circle':
        return Icons.check_circle;
      case 'schedule':
        return Icons.schedule;
      case 'help':
        return Icons.help;
      default:
        return Icons.description;
    }
  }

  String _formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays}d';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h';
    } else {
      return '${duration.inMinutes}m';
    }
  }

  IconData _getDifficultyIcon(ApplicationDifficulty difficulty) {
    switch (difficulty) {
      case ApplicationDifficulty.easy:
        return Icons.sentiment_very_satisfied;
      case ApplicationDifficulty.moderate:
        return Icons.sentiment_satisfied;
      case ApplicationDifficulty.difficult:
        return Icons.sentiment_dissatisfied;
      case ApplicationDifficulty.expert:
        return Icons.sentiment_very_dissatisfied;
    }
  }

  Color _getDifficultyColor(ApplicationDifficulty difficulty) {
    switch (difficulty) {
      case ApplicationDifficulty.easy:
        return AppTheme.successColor;
      case ApplicationDifficulty.moderate:
        return AppTheme.warningColor;
      case ApplicationDifficulty.difficult:
        return AppTheme.errorColor;
      case ApplicationDifficulty.expert:
        return Colors.red.shade800;
    }
  }

  String _getDifficultyText(ApplicationDifficulty difficulty) {
    switch (difficulty) {
      case ApplicationDifficulty.easy:
        return 'Easy';
      case ApplicationDifficulty.moderate:
        return 'Moderate';
      case ApplicationDifficulty.difficult:
        return 'Difficult';
      case ApplicationDifficulty.expert:
        return 'Expert';
    }
  }

  // Navigation methods
  void _navigateToEligibilityChecker() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EligibilityCheckerScreen(
          passportCountry: widget.passportCountry,
          destinationCountry: widget.destinationCountry,
          visaType: widget.visaType,
        ),
      ),
    );
  }

  void _navigateToDocumentChecklist() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DocumentChecklistScreen(
          passportCountry: widget.passportCountry,
          destinationCountry: widget.destinationCountry,
          visaType: widget.visaType,
        ),
      ),
    );
  }

  void _navigateToTimelineCalculator() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => TimelineCalculatorScreen(
          passportCountry: widget.passportCountry,
          destinationCountry: widget.destinationCountry,
          visaType: widget.visaType,
        ),
      ),
    );
  }

  void _navigateToFAQ() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => VisaFAQScreen(
          country: widget.destinationCountry,
        ),
      ),
    );
  }

  void _navigateToResource(SelfServiceResource resource) {
    // TODO: Navigate to specific resource detail screen based on type
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${resource.title}...'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }

  void _navigateToConsultants() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => VisaProviderMarketplaceScreen(
          destinationCountry: widget.destinationCountry,
          // TODO: Convert string to VisaProviderSpecialization enum
          // visaType: widget.visaType,
        ),
      ),
    );
  }

  void _showSelfServiceInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Self-Service Resources'),
        content: const SingleChildScrollView(
          child: Text(
            'Our self-service resources help you apply for your visa independently:\n\n'
            '• Eligibility Checker - Verify if you qualify\n'
            '• Document Checklist - Get required documents\n'
            '• Timeline Calculator - Plan your application\n'
            '• FAQ & Guides - Find answers and guidance\n\n'
            'Benefits of self-service:\n'
            '• Save money on consultant fees\n'
            '• Learn the process thoroughly\n'
            '• Complete control over your application\n'
            '• Access to up-to-date information\n\n'
            'If you need help at any point, our certified consultants are available to assist you.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}
