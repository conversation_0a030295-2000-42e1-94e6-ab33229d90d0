// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/visa/collaboration_models.dart';
import 'package:culture_connect/models/travel/visa/visa_models.dart';
import 'package:culture_connect/models/travel/visa/escrow_payment_models.dart';
import 'package:culture_connect/services/travel/visa/collaboration_service.dart';
import 'package:culture_connect/screens/messaging/chat_screen.dart';
import 'package:culture_connect/screens/travel/visa/escrow_payment_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';

/// Screen for managing visa collaboration between clients and consultants
class CollaborationHubScreen extends ConsumerStatefulWidget {
  const CollaborationHubScreen({
    super.key,
    required this.collaborationId,
    this.booking,
  });

  final String collaborationId;
  final VisaServiceBooking? booking;

  @override
  ConsumerState<CollaborationHubScreen> createState() =>
      _CollaborationHubScreenState();
}

class _CollaborationHubScreenState extends ConsumerState<CollaborationHubScreen>
    with SingleTickerProviderStateMixin {
  VisaCollaborationSession? _collaboration;
  bool _isLoading = false;
  String? _errorMessage;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadCollaboration();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadCollaboration() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final collaborationService = ref.read(collaborationServiceProvider);
      final collaboration = await collaborationService
          .getCollaborationSession(widget.collaborationId);

      if (mounted) {
        setState(() {
          _collaboration = collaboration;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to load collaboration: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        title: Text(
          'Collaboration Hub',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: AppTheme.fontWeightBold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: AppTheme.spacingMd),
            child: IconButton(
              onPressed: _showCollaborationInfo,
              icon: Container(
                padding: const EdgeInsets.all(AppTheme.spacingXs),
                decoration: BoxDecoration(
                  color: AppTheme.infoColor.withValues(alpha: 0.1),
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: const Icon(
                  Icons.info_outline,
                  color: AppTheme.infoColor,
                  size: 20,
                ),
              ),
              tooltip: 'How collaboration works',
            ),
          ),
        ],
        bottom: _collaboration != null
            ? TabBar(
                controller: _tabController,
                labelColor: AppTheme.primaryColor,
                unselectedLabelColor: AppTheme.textSecondaryColor,
                indicatorColor: AppTheme.primaryColor,
                indicatorWeight: 3,
                labelStyle: const TextStyle(
                  fontWeight: AppTheme.fontWeightSemibold,
                  fontSize: AppTheme.fontSizeSm,
                ),
                tabs: const [
                  Tab(text: 'Overview'),
                  Tab(text: 'Messages'),
                  Tab(text: 'Documents'),
                  Tab(text: 'Timeline'),
                ],
              )
            : null,
      ),
      body: _buildBody(theme),
    );
  }

  Widget _buildBody(ThemeData theme) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: AppTheme.primaryColor,
            ),
            SizedBox(height: AppTheme.spacingMd),
            Text(
              'Loading collaboration details...',
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
                fontWeight: AppTheme.fontWeightMedium,
              ),
            ),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return ErrorView(
        error: _errorMessage!,
        onRetry: _loadCollaboration,
      );
    }

    if (_collaboration == null) {
      return _buildEmptyState(theme);
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildOverviewTab(theme),
        _buildMessagesTab(theme),
        _buildDocumentsTab(theme),
        _buildTimelineTab(theme),
      ],
    );
  }

  Widget _buildOverviewTab(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCollaborationHeader(theme),
          const SizedBox(height: AppTheme.spacingLg),
          _buildProgressCard(theme),
          const SizedBox(height: AppTheme.spacingLg),
          _buildQuickActions(theme),
          const SizedBox(height: AppTheme.spacingLg),
          _buildUpcomingDeadlines(theme),
          const SizedBox(height: AppTheme.spacingLg),
          _buildRecentActivity(theme),
        ],
      ),
    );
  }

  Widget _buildCollaborationHeader(ThemeData theme) {
    final status = _collaboration!.status;
    final statusColor = _getStatusColor(status);
    final statusText = _getStatusText(status);

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            statusColor.withValues(alpha: 0.1),
            statusColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        border: Border.all(
          color: statusColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingSm),
                decoration: BoxDecoration(
                  color: statusColor,
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Icon(
                  _getStatusIcon(status),
                  color: AppTheme.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMd),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _collaboration!.serviceName,
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: AppTheme.fontWeightBold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    Text(
                      'Destination: ${_collaboration!.destinationCountry}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingSm,
              vertical: AppTheme.spacingXs,
            ),
            decoration: BoxDecoration(
              color: statusColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
            ),
            child: Text(
              statusText,
              style: TextStyle(
                color: statusColor,
                fontWeight: AppTheme.fontWeightSemibold,
                fontSize: AppTheme.fontSizeSm,
              ),
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Row(
            children: [
              _buildInfoItem('Progress',
                  '${_collaboration!.progressPercentage.toStringAsFixed(0)}%'),
              const SizedBox(width: AppTheme.spacingLg),
              _buildInfoItem('Expected Completion',
                  _formatDate(_collaboration!.expectedCompletionDate)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: AppTheme.fontSizeSm,
            color: AppTheme.textTertiaryColor,
            fontWeight: AppTheme.fontWeightMedium,
          ),
        ),
        const SizedBox(height: AppTheme.spacingXs),
        Text(
          value,
          style: const TextStyle(
            fontSize: AppTheme.fontSizeMd,
            color: AppTheme.textPrimaryColor,
            fontWeight: AppTheme.fontWeightSemibold,
          ),
        ),
      ],
    );
  }

  Widget _buildProgressCard(ThemeData theme) {
    final progress = _collaboration!.progressPercentage / 100;
    final completedMilestones = _collaboration!.completedMilestones.length;
    final totalMilestones = 5; // Mock total milestones

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Service Progress',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: AppTheme.fontWeightBold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              Text(
                '$completedMilestones/$totalMilestones milestones',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: AppTheme.fontWeightSemibold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: AppTheme.borderColor,
            valueColor:
                const AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          if (_collaboration!.currentMilestone != null) ...[
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingSm),
              decoration: BoxDecoration(
                color: AppTheme.infoColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.flag,
                    color: AppTheme.infoColor,
                    size: 16,
                  ),
                  const SizedBox(width: AppTheme.spacingSm),
                  Expanded(
                    child: Text(
                      'Current: ${_collaboration!.currentMilestone}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.infoColor,
                        fontWeight: AppTheme.fontWeightMedium,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMessagesTab(ThemeData theme) {
    if (_collaboration?.chatId == null) {
      return const Center(
        child: Text(
          'Chat not available',
          style: TextStyle(
            color: AppTheme.textSecondaryColor,
          ),
        ),
      );
    }

    // Navigate to existing chat screen
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMd),
            decoration: BoxDecoration(
              color: AppTheme.infoColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.security,
                  color: AppTheme.infoColor,
                  size: 20,
                ),
                const SizedBox(width: AppTheme.spacingMd),
                Expanded(
                  child: Text(
                    'All messages are encrypted and secure. Only you and your consultant can see them.',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.infoColor,
                      fontWeight: AppTheme.fontWeightMedium,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Expanded(
            child: ElevatedButton(
              onPressed: _openChat,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: AppTheme.white,
                padding:
                    const EdgeInsets.symmetric(vertical: AppTheme.spacingMd),
                shape: RoundedRectangleBorder(
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusLarge),
                ),
              ),
              child: const Text(
                'Open Chat',
                style: TextStyle(
                  fontWeight: AppTheme.fontWeightSemibold,
                  fontSize: AppTheme.fontSizeMd,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentsTab(ThemeData theme) {
    final documents = _collaboration?.sharedDocuments ?? [];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Shared Documents',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: AppTheme.fontWeightBold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              ElevatedButton.icon(
                onPressed: _uploadDocument,
                icon: const Icon(Icons.upload_file, size: 18),
                label: const Text('Upload'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: AppTheme.white,
                  shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusLarge),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          if (documents.isEmpty) ...[
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingXl),
              decoration: BoxDecoration(
                color: AppTheme.borderColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
                border: Border.all(
                  color: AppTheme.borderColor,
                  style: BorderStyle.solid,
                ),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.folder_open,
                    size: 48,
                    color: AppTheme.borderColor,
                  ),
                  const SizedBox(height: AppTheme.spacingMd),
                  Text(
                    'No documents shared yet',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: AppTheme.textSecondaryColor,
                      fontWeight: AppTheme.fontWeightSemibold,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingSm),
                  Text(
                    'Upload your visa documents to share them securely with your consultant',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.textTertiaryColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ] else ...[
            ...documents.map((doc) => _buildDocumentItem(doc, theme)),
          ],
        ],
      ),
    );
  }

  Widget _buildDocumentItem(CollaborationDocument document, ThemeData theme) {
    final isReviewed = document.isReviewed;
    final isApproved = document.isApproved;

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMd),
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingSm),
            decoration: BoxDecoration(
              color: _getDocumentTypeColor(document.mimeType)
                  .withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
            ),
            child: Icon(
              _getDocumentTypeIcon(document.mimeType),
              color: _getDocumentTypeColor(document.mimeType),
              size: 24,
            ),
          ),
          const SizedBox(width: AppTheme.spacingMd),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  document.name,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: AppTheme.fontWeightSemibold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingXs),
                Text(
                  'Uploaded ${_formatDate(document.uploadedAt)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: AppTheme.textTertiaryColor,
                  ),
                ),
                if (isReviewed) ...[
                  const SizedBox(height: AppTheme.spacingXs),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacingSm,
                      vertical: AppTheme.spacingXs,
                    ),
                    decoration: BoxDecoration(
                      color: isApproved
                          ? AppTheme.successColor.withValues(alpha: 0.1)
                          : AppTheme.warningColor.withValues(alpha: 0.1),
                      borderRadius:
                          BorderRadius.circular(AppTheme.borderRadiusSmall),
                    ),
                    child: Text(
                      isApproved ? 'Approved' : 'Needs revision',
                      style: TextStyle(
                        color: isApproved
                            ? AppTheme.successColor
                            : AppTheme.warningColor,
                        fontWeight: AppTheme.fontWeightSemibold,
                        fontSize: AppTheme.fontSizeXs,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          IconButton(
            onPressed: () => _viewDocument(document),
            icon: const Icon(
              Icons.visibility,
              color: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineTab(ThemeData theme) {
    return const Center(
      child: Text(
        'Timeline view coming soon',
        style: TextStyle(
          color: AppTheme.textSecondaryColor,
        ),
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingXl),
            decoration: BoxDecoration(
              color: AppTheme.borderColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.people_outline,
              size: 64,
              color: AppTheme.borderColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingLg),
          Text(
            'No Collaboration Found',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: AppTheme.fontWeightBold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingSm),
          Text(
            'Unable to load collaboration details.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: AppTheme.fontWeightBold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  icon: Icons.chat,
                  label: 'Message',
                  color: AppTheme.primaryColor,
                  onTap: _openChat,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMd),
              Expanded(
                child: _buildActionButton(
                  icon: Icons.upload_file,
                  label: 'Upload Doc',
                  color: AppTheme.infoColor,
                  onTap: _uploadDocument,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  icon: Icons.video_call,
                  label: 'Schedule Call',
                  color: AppTheme.successColor,
                  onTap: _scheduleConsultation,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMd),
              Expanded(
                child: _buildActionButton(
                  icon: Icons.account_balance_wallet,
                  label: 'Payment',
                  color: AppTheme.warningColor,
                  onTap: _viewPayment,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: color.withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        child: Container(
          padding: const EdgeInsets.all(AppTheme.spacingMd),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingSm),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Icon(
                  icon,
                  color: AppTheme.white,
                  size: 20,
                ),
              ),
              const SizedBox(height: AppTheme.spacingSm),
              Text(
                label,
                style: TextStyle(
                  color: color,
                  fontWeight: AppTheme.fontWeightSemibold,
                  fontSize: AppTheme.fontSizeSm,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUpcomingDeadlines(ThemeData theme) {
    final upcomingDeadlines = _collaboration!.deadlines
        .where((d) => !d.isCompleted && d.dueDate.isAfter(DateTime.now()))
        .take(3)
        .toList();

    if (upcomingDeadlines.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Upcoming Deadlines',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: AppTheme.fontWeightBold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              if (upcomingDeadlines.length > 3)
                TextButton(
                  onPressed: () =>
                      _tabController.animateTo(3), // Go to timeline tab
                  child: const Text('View All'),
                ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          ...upcomingDeadlines
              .map((deadline) => _buildDeadlineItem(deadline, theme)),
        ],
      ),
    );
  }

  Widget _buildDeadlineItem(CollaborationDeadline deadline, ThemeData theme) {
    final isUrgent = deadline.dueDate.difference(DateTime.now()).inDays <= 1;
    final priorityColor =
        isUrgent ? AppTheme.errorColor : AppTheme.warningColor;

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingSm),
      padding: const EdgeInsets.all(AppTheme.spacingSm),
      decoration: BoxDecoration(
        color: priorityColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
        border: Border.all(
          color: priorityColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isUrgent ? Icons.warning : Icons.schedule,
            color: priorityColor,
            size: 20,
          ),
          const SizedBox(width: AppTheme.spacingMd),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  deadline.title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: AppTheme.fontWeightSemibold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                Text(
                  'Due: ${_formatDate(deadline.dueDate)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: priorityColor,
                    fontWeight: AppTheme.fontWeightMedium,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity(ThemeData theme) {
    // Mock recent activities
    final activities = [
      'Document "Passport Copy" uploaded',
      'Milestone "Initial consultation" completed',
      'New message from consultant',
    ];

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Activity',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: AppTheme.fontWeightBold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              TextButton(
                onPressed: () =>
                    _tabController.animateTo(3), // Go to timeline tab
                child: const Text('View All'),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          ...activities.map((activity) => _buildActivityItem(activity, theme)),
        ],
      ),
    );
  }

  Widget _buildActivityItem(String activity, ThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingSm),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: AppTheme.primaryColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: AppTheme.spacingMd),
          Expanded(
            child: Text(
              activity,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  Color _getStatusColor(CollaborationStatus status) {
    switch (status) {
      case CollaborationStatus.active:
        return AppTheme.successColor;
      case CollaborationStatus.paused:
        return AppTheme.warningColor;
      case CollaborationStatus.completed:
        return AppTheme.primaryColor;
      case CollaborationStatus.cancelled:
      case CollaborationStatus.disputed:
        return AppTheme.errorColor;
    }
  }

  String _getStatusText(CollaborationStatus status) {
    switch (status) {
      case CollaborationStatus.active:
        return 'Active Collaboration';
      case CollaborationStatus.paused:
        return 'Paused';
      case CollaborationStatus.completed:
        return 'Completed';
      case CollaborationStatus.cancelled:
        return 'Cancelled';
      case CollaborationStatus.disputed:
        return 'Under Dispute';
    }
  }

  IconData _getStatusIcon(CollaborationStatus status) {
    switch (status) {
      case CollaborationStatus.active:
        return Icons.play_circle;
      case CollaborationStatus.paused:
        return Icons.pause_circle;
      case CollaborationStatus.completed:
        return Icons.check_circle;
      case CollaborationStatus.cancelled:
        return Icons.cancel;
      case CollaborationStatus.disputed:
        return Icons.gavel;
    }
  }

  Color _getDocumentTypeColor(String mimeType) {
    if (mimeType.contains('pdf')) return AppTheme.errorColor;
    if (mimeType.contains('image')) return AppTheme.successColor;
    if (mimeType.contains('word') || mimeType.contains('document'))
      return AppTheme.infoColor;
    return AppTheme.primaryColor;
  }

  IconData _getDocumentTypeIcon(String mimeType) {
    if (mimeType.contains('pdf')) return Icons.picture_as_pdf;
    if (mimeType.contains('image')) return Icons.image;
    if (mimeType.contains('word') || mimeType.contains('document'))
      return Icons.description;
    return Icons.insert_drive_file;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Action methods
  void _openChat() {
    if (_collaboration?.chatId != null) {
      // TODO: Navigate to chat screen with collaboration chat ID
      // Navigator.of(context).push(
      //   MaterialPageRoute(
      //     builder: (context) => ChatScreen(
      //       chatId: _collaboration!.chatId,
      //       recipient: consultantUser,
      //     ),
      //   ),
      // );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
              'Chat feature will be integrated with existing messaging system'),
          backgroundColor: AppTheme.infoColor,
        ),
      );
    }
  }

  void _uploadDocument() {
    // TODO: Implement document upload
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Document upload feature coming soon'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }

  void _scheduleConsultation() {
    // TODO: Implement consultation scheduling
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Consultation scheduling feature coming soon'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }

  void _viewPayment() {
    if (_collaboration?.escrowPaymentId != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => EscrowPaymentScreen(
              // TODO: Pass actual escrow payment data
              ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No payment information available'),
          backgroundColor: AppTheme.warningColor,
        ),
      );
    }
  }

  void _viewDocument(CollaborationDocument document) {
    // TODO: Implement document viewer
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${document.name}...'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }

  void _showCollaborationInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Collaboration Hub'),
        content: const SingleChildScrollView(
          child: Text(
            'The Collaboration Hub is your central place to:\n\n'
            '• Communicate securely with your visa consultant\n'
            '• Share and review documents safely\n'
            '• Track progress and milestones\n'
            '• Manage payments through escrow\n'
            '• Schedule consultations and calls\n\n'
            'All communications and documents are encrypted and secure. '
            'Your consultant will guide you through each step of the visa process.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}
