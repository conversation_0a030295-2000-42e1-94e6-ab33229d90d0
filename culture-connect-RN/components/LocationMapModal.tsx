import React, { useState } from 'react';
import { StyleSheet, View, Text, Modal, Pressable, TextInput, Alert, Platform, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { X, MapPin, Search, Navigation } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';
import { Button } from '@/components/Button';

interface LocationMapModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectLocation: (location: { name: string; address: string; coordinates?: { lat: number; lng: number } }) => void;
  initialLocation?: string;
}

export function LocationMapModal({ visible, onClose, onSelectLocation, initialLocation }: LocationMapModalProps) {
  const [searchText, setSearchText] = useState(initialLocation || '');
  const [selectedLocation, setSelectedLocation] = useState<{ name: string; address: string; coordinates?: { lat: number; lng: number } } | null>(null);
  const [isSearching, setIsSearching] = useState(false);

  // Mock locations for demonstration
  const mockLocations = [
    {
      name: 'Downtown Airport',
      address: '123 Airport Blvd, Downtown',
      coordinates: { lat: 40.7128, lng: -74.0060 }
    },
    {
      name: 'City Center Plaza',
      address: '456 Main St, City Center',
      coordinates: { lat: 40.7589, lng: -73.9851 }
    },
    {
      name: 'Metro Station Hub',
      address: '789 Transit Ave, Metro District',
      coordinates: { lat: 40.7505, lng: -73.9934 }
    },
    {
      name: 'Business District',
      address: '321 Corporate Dr, Business District',
      coordinates: { lat: 40.7614, lng: -73.9776 }
    },
    {
      name: 'Shopping Mall Entrance',
      address: '654 Commerce Way, Shopping District',
      coordinates: { lat: 40.7282, lng: -73.7949 }
    }
  ];

  const handleSearch = async () => {
    if (!searchText.trim()) return;
    
    setIsSearching(true);
    
    // Simulate API call delay
    setTimeout(() => {
      const filtered = mockLocations.filter(loc => 
        loc.name.toLowerCase().includes(searchText.toLowerCase()) ||
        loc.address.toLowerCase().includes(searchText.toLowerCase())
      );
      
      if (filtered.length > 0) {
        setSelectedLocation(filtered[0]);
      } else {
        // Create a custom location based on search
        setSelectedLocation({
          name: searchText,
          address: `${searchText}, Current City`,
          coordinates: { lat: 40.7128 + Math.random() * 0.1, lng: -74.0060 + Math.random() * 0.1 }
        });
      }
      setIsSearching(false);
    }, 1000);
  };

  const handleCurrentLocation = () => {
    if (Platform.OS === 'web') {
      // For web, use geolocation API
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            setSelectedLocation({
              name: 'Current Location',
              address: 'Your current location',
              coordinates: {
                lat: position.coords.latitude,
                lng: position.coords.longitude
              }
            });
          },
          (error) => {
            Alert.alert('Location Error', 'Unable to get your current location');
          }
        );
      } else {
        Alert.alert('Location Error', 'Geolocation is not supported by this browser');
      }
    } else {
      // For mobile, simulate getting current location
      setSelectedLocation({
        name: 'Current Location',
        address: 'Your current location',
        coordinates: { lat: 40.7128, lng: -74.0060 }
      });
    }
  };

  const handleConfirm = () => {
    if (selectedLocation) {
      onSelectLocation(selectedLocation);
      onClose();
    }
  };

  const handleLocationSelect = (location: typeof mockLocations[0]) => {
    setSelectedLocation(location);
    setSearchText(location.name);
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitle}>Select Pickup Location</Text>
            <Pressable style={styles.closeButton} onPress={onClose}>
              <X size={24} color={colors.text} />
            </Pressable>
          </View>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Search size={20} color={colors.textSecondary} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search for a location..."
              value={searchText}
              onChangeText={setSearchText}
              onSubmitEditing={handleSearch}
              returnKeyType="search"
            />
          </View>
          <Pressable style={styles.currentLocationButton} onPress={handleCurrentLocation}>
            <Navigation size={20} color={colors.accent} />
          </Pressable>
        </View>

        {/* Map Placeholder */}
        <View style={styles.mapContainer}>
          <View style={styles.mapPlaceholder}>
            <MapPin size={48} color={colors.accent} />
            <Text style={styles.mapPlaceholderText}>Interactive Map</Text>
            <Text style={styles.mapPlaceholderSubtext}>
              {selectedLocation ? `Selected: ${selectedLocation.name}` : 'Tap a location below or search above'}
            </Text>
          </View>
          
          {selectedLocation && (
            <View style={styles.selectedLocationOverlay}>
              <View style={styles.locationPin}>
                <MapPin size={24} color={colors.white} />
              </View>
            </View>
          )}
        </View>

        {/* Location Suggestions */}
        <View style={styles.suggestionsContainer}>
          <Text style={styles.suggestionsTitle}>Popular Locations</Text>
          <ScrollView 
            style={styles.suggestionsList}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.suggestionsContent}
          >
            {mockLocations.map((location, index) => (
              <Pressable
                key={index}
                style={[
                  styles.locationItem,
                  selectedLocation?.name === location.name && styles.selectedLocationItem
                ]}
                onPress={() => handleLocationSelect(location)}
              >
                <View style={styles.locationIcon}>
                  <MapPin size={16} color={colors.accent} />
                </View>
                <View style={styles.locationInfo}>
                  <Text style={styles.locationName}>{location.name}</Text>
                  <Text style={styles.locationAddress}>{location.address}</Text>
                </View>
                {selectedLocation?.name === location.name && (
                  <View style={styles.selectedIndicator}>
                    <View style={styles.selectedDot} />
                  </View>
                )}
              </Pressable>
            ))}
          </ScrollView>
        </View>

        {/* Bottom Action */}
        <View style={styles.bottomAction}>
          <Button
            title={selectedLocation ? `Confirm Location` : 'Select a Location'}
            onPress={handleConfirm}
            variant="primary"
            size="large"
            disabled={!selectedLocation}
          />
        </View>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  closeButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    gap: spacing.md,
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    gap: spacing.sm,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  searchInput: {
    flex: 1,
    fontSize: typography.sizes.md,
    color: colors.text,
  },
  currentLocationButton: {
    width: 48,
    height: 48,
    backgroundColor: colors.white,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  mapContainer: {
    flex: 1,
    margin: spacing.lg,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 16,
    position: 'relative',
    overflow: 'hidden',
  },
  mapPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  mapPlaceholderText: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginTop: spacing.md,
  },
  mapPlaceholderSubtext: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.sm,
    lineHeight: typography.sizes.sm * 1.4,
  },
  selectedLocationOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -12 }, { translateY: -24 }],
  },
  locationPin: {
    width: 24,
    height: 24,
    backgroundColor: colors.accent,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  suggestionsContainer: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    flex: 1,
  },
  suggestionsList: {
    flex: 1,
    maxHeight: 240,
  },
  suggestionsContent: {
    paddingBottom: spacing.md,
  },
  suggestionsTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.md,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.md,
    borderRadius: 12,
    marginBottom: spacing.sm,
    backgroundColor: colors.backgroundSecondary,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  selectedLocationItem: {
    backgroundColor: colors.accent + '15',
    borderColor: colors.accent,
    borderWidth: 2,
  },
  locationIcon: {
    width: 32,
    height: 32,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  locationInfo: {
    flex: 1,
  },
  locationName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  locationAddress: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: typography.sizes.sm * 1.3,
  },
  selectedIndicator: {
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedDot: {
    width: 8,
    height: 8,
    backgroundColor: colors.accent,
    borderRadius: 4,
  },
  bottomAction: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    paddingBottom: spacing.xl,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
});