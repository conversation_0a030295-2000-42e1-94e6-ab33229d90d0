import 'dart:async';
import 'package:flutter/material.dart';

/// Service for progressive loading to improve performance on older devices
class ProgressiveLoadingService {
  static final ProgressiveLoadingService _instance =
      ProgressiveLoadingService._internal();
  factory ProgressiveLoadingService() => _instance;
  ProgressiveLoadingService._internal();

  // Progressive loading configuration
  static const int _batchSize = 5; // Load 5 items at a time
  static const Duration _batchDelay =
      Duration(milliseconds: 100); // Delay between batches
  static const int _maxConcurrentLoads = 3; // Max concurrent loading operations

  final Map<String, ProgressiveLoader> _activeLoaders = {};
  int _currentConcurrentLoads = 0;

  /// Create a progressive loader for a list of items
  ProgressiveLoader<T> createLoader<T>({
    required String loaderId,
    required List<T> items,
    required Widget Function(T item) itemBuilder,
    int? batchSize,
    Duration? batchDelay,
  }) {
    // Dispose existing loader with same ID
    _activeLoaders[loaderId]?.dispose();

    final loader = ProgressiveLoader<T>(
      id: loaderId,
      items: items,
      itemBuilder: itemBuilder,
      batchSize: batchSize ?? _batchSize,
      batchDelay: batchDelay ?? _batchDelay,
      onDispose: () => _activeLoaders.remove(loaderId),
    );

    _activeLoaders[loaderId] = loader;
    return loader;
  }

  /// Get active loader by ID
  ProgressiveLoader? getLoader(String loaderId) {
    return _activeLoaders[loaderId];
  }

  /// Check if we can start a new concurrent load
  bool canStartNewLoad() {
    return _currentConcurrentLoads < _maxConcurrentLoads;
  }

  /// Register a new concurrent load
  void registerLoad() {
    _currentConcurrentLoads++;
  }

  /// Unregister a completed load
  void unregisterLoad() {
    if (_currentConcurrentLoads > 0) {
      _currentConcurrentLoads--;
    }
  }

  /// Get service statistics
  Map<String, dynamic> getStatistics() {
    return {
      'active_loaders': _activeLoaders.length,
      'concurrent_loads': _currentConcurrentLoads,
      'max_concurrent_loads': _maxConcurrentLoads,
      'loader_ids': _activeLoaders.keys.toList(),
    };
  }

  /// Dispose all loaders
  void dispose() {
    for (final loader in _activeLoaders.values) {
      loader.dispose();
    }
    _activeLoaders.clear();
    _currentConcurrentLoads = 0;
    debugPrint('🛑 Progressive loading service disposed');
  }
}

/// Progressive loader for incremental content rendering
class ProgressiveLoader<T> extends ChangeNotifier {
  final String id;
  final List<T> _allItems;
  final Widget Function(T item) _itemBuilder;
  final int batchSize;
  final Duration batchDelay;
  final VoidCallback? onDispose;

  final List<T> _loadedItems = [];
  final List<Widget> _loadedWidgets = [];
  Timer? _loadTimer;
  bool _isLoading = false;
  bool _isCompleted = false;
  int _currentBatch = 0;

  ProgressiveLoader({
    required this.id,
    required List<T> items,
    required Widget Function(T item) itemBuilder,
    required this.batchSize,
    required this.batchDelay,
    this.onDispose,
  })  : _allItems = items,
        _itemBuilder = itemBuilder;

  /// Get currently loaded items
  List<T> get loadedItems => List.unmodifiable(_loadedItems);

  /// Get currently loaded widgets
  List<Widget> get loadedWidgets => List.unmodifiable(_loadedWidgets);

  /// Check if loading is in progress
  bool get isLoading => _isLoading;

  /// Check if all items are loaded
  bool get isCompleted => _isCompleted;

  /// Get loading progress (0.0 to 1.0)
  double get progress =>
      _allItems.isEmpty ? 1.0 : _loadedItems.length / _allItems.length;

  /// Start progressive loading
  void startLoading() {
    if (_isLoading || _isCompleted) return;

    final service = ProgressiveLoadingService();
    if (!service.canStartNewLoad()) {
      // Queue for later if too many concurrent loads
      Future.delayed(const Duration(milliseconds: 500), startLoading);
      return;
    }

    _isLoading = true;
    service.registerLoad();
    notifyListeners();

    _loadNextBatch();
  }

  /// Load the next batch of items
  void _loadNextBatch() {
    if (_isCompleted) return;

    final startIndex = _currentBatch * batchSize;
    final endIndex = (startIndex + batchSize).clamp(0, _allItems.length);

    if (startIndex >= _allItems.length) {
      _completeLoading();
      return;
    }

    // Load batch items
    final batchItems = _allItems.sublist(startIndex, endIndex);

    // Process batch in microtask to avoid blocking UI
    Future.microtask(() {
      try {
        for (final item in batchItems) {
          _loadedItems.add(item);
          _loadedWidgets.add(_itemBuilder(item));
        }

        _currentBatch++;
        notifyListeners();

        // Schedule next batch
        if (_loadedItems.length < _allItems.length) {
          _loadTimer = Timer(batchDelay, _loadNextBatch);
        } else {
          _completeLoading();
        }
      } catch (e) {
        debugPrint('❌ Error loading batch for $id: $e');
        _completeLoading();
      }
    });
  }

  /// Complete the loading process
  void _completeLoading() {
    _isLoading = false;
    _isCompleted = true;
    _loadTimer?.cancel();

    final service = ProgressiveLoadingService();
    service.unregisterLoad();

    notifyListeners();
    debugPrint(
        '✅ Progressive loading completed for $id: ${_loadedItems.length} items');
  }

  /// Pause loading
  void pauseLoading() {
    _loadTimer?.cancel();
    _isLoading = false;

    final service = ProgressiveLoadingService();
    service.unregisterLoad();

    notifyListeners();
  }

  /// Resume loading
  void resumeLoading() {
    if (!_isCompleted && !_isLoading) {
      startLoading();
    }
  }

  /// Reset loader to initial state
  void reset() {
    _loadTimer?.cancel();
    _loadedItems.clear();
    _loadedWidgets.clear();
    _isLoading = false;
    _isCompleted = false;
    _currentBatch = 0;

    final service = ProgressiveLoadingService();
    service.unregisterLoad();

    notifyListeners();
  }

  @override
  void dispose() {
    _loadTimer?.cancel();

    final service = ProgressiveLoadingService();
    service.unregisterLoad();

    onDispose?.call();
    super.dispose();
  }
}

/// Widget for progressive loading with built-in loading indicators
class ProgressiveListView<T> extends StatefulWidget {
  final String loaderId;
  final List<T> items;
  final Widget Function(T item) itemBuilder;
  final Widget? loadingIndicator;
  final Widget? emptyWidget;
  final int? batchSize;
  final Duration? batchDelay;
  final bool autoStart;

  const ProgressiveListView({
    super.key,
    required this.loaderId,
    required this.items,
    required this.itemBuilder,
    this.loadingIndicator,
    this.emptyWidget,
    this.batchSize,
    this.batchDelay,
    this.autoStart = true,
  });

  @override
  State<ProgressiveListView<T>> createState() => _ProgressiveListViewState<T>();
}

class _ProgressiveListViewState<T> extends State<ProgressiveListView<T>> {
  late ProgressiveLoader<T> _loader;

  @override
  void initState() {
    super.initState();

    final service = ProgressiveLoadingService();
    _loader = service.createLoader<T>(
      loaderId: widget.loaderId,
      items: widget.items,
      itemBuilder: widget.itemBuilder,
      batchSize: widget.batchSize,
      batchDelay: widget.batchDelay,
    );

    _loader.addListener(_onLoaderChanged);

    if (widget.autoStart) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loader.startLoading();
      });
    }
  }

  void _onLoaderChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _loader.removeListener(_onLoaderChanged);
    _loader.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.items.isEmpty) {
      return widget.emptyWidget ?? const SizedBox.shrink();
    }

    final loadedWidgets = _loader.loadedWidgets;

    return Column(
      children: [
        ...loadedWidgets,
        if (_loader.isLoading)
          widget.loadingIndicator ??
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
      ],
    );
  }
}
