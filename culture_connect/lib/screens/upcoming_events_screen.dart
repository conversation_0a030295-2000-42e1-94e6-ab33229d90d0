import 'package:flutter/material.dart';
import 'package:culture_connect/theme/kaia_design_tokens.dart';
import 'package:culture_connect/widgets/events/event_card.dart';
import 'package:culture_connect/screens/event_details_screen.dart';

/// Comprehensive "Upcoming Events" listing screen for CultureConnect
/// Serves as the destination when users click "See All" next to the "Upcoming Events" section
class UpcomingEventsScreen extends StatefulWidget {
  const UpcomingEventsScreen({super.key});

  @override
  State<UpcomingEventsScreen> createState() => _UpcomingEventsScreenState();
}

class _UpcomingEventsScreenState extends State<UpcomingEventsScreen>
    with TickerProviderStateMixin {
  // State variables
  bool _isGridView = true;
  bool _isLoading = false;
  String _searchQuery = '';
  String? _selectedCategory;

  // Animation controllers
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  // Categories for filtering
  final List<String> _categories = [
    'All',
    'Festival',
    'Food Tour',
    'History',
    'Workshop',
    'Performance',
    'Concert',
    'Art Tour',
    'Cultural',
  ];

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    _loadEvents();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  Future<void> _loadEvents() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate loading delay
    await Future.delayed(const Duration(milliseconds: 800));

    setState(() {
      _isLoading = false;
    });

    _fadeController.forward();
  }

  // Comprehensive mock events data - all 15 events from rn_home_screen.dart
  final List<Map<String, dynamic>> _mockUpcomingEvents = [
    {
      'id': '1',
      'title': 'Tokyo Cherry Blossom Festival',
      'date': 'March 25, 2025',
      'time': '10:00 AM',
      'location': 'Ueno Park, Tokyo',
      'imageUrl':
          'https://images.unsplash.com/photo-1522383225653-ed111181a951?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'Festival',
      'price': 'Free',
      'attendees': 1250,
      'isBookmarked': false,
    },
    {
      'id': '2',
      'title': 'Barcelona Food & Wine Tour',
      'date': 'April 12, 2025',
      'time': '6:00 PM',
      'location': 'Gothic Quarter, Barcelona',
      'imageUrl':
          'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'Food Tour',
      'price': '€45',
      'attendees': 85,
      'isBookmarked': true,
    },
    {
      'id': '3',
      'title': 'Cairo Historical Walking Tour',
      'date': 'April 20, 2025',
      'time': '9:00 AM',
      'location': 'Islamic Cairo, Egypt',
      'imageUrl':
          'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'History',
      'price': r'$25',
      'attendees': 156,
      'isBookmarked': false,
    },
    {
      'id': '4',
      'title': 'Moroccan Cooking Workshop',
      'date': 'April 5, 2025',
      'time': '2:00 PM',
      'location': 'Marrakech Medina, Morocco',
      'imageUrl':
          'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'Workshop',
      'price': '\$35',
      'attendees': 24,
      'isBookmarked': false,
    },
    {
      'id': '5',
      'title': 'Bali Traditional Dance Performance',
      'date': 'March 30, 2025',
      'time': '7:30 PM',
      'location': 'Ubud Cultural Center, Bali',
      'imageUrl':
          'https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'Performance',
      'price': '\$18',
      'attendees': 200,
      'isBookmarked': true,
    },
    {
      'id': '6',
      'title': 'London Street Art Tour',
      'date': 'April 8, 2025',
      'time': '11:00 AM',
      'location': 'Shoreditch, London',
      'imageUrl':
          'https://images.unsplash.com/photo-1541961017774-22349e4a1262?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'Art Tour',
      'price': '£20',
      'attendees': 45,
      'isBookmarked': false,
    },
    {
      'id': '7',
      'title': 'Peruvian Music Concert',
      'date': 'April 15, 2025',
      'time': '8:00 PM',
      'location': 'Plaza de Armas, Cusco',
      'imageUrl':
          'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'Concert',
      'price': '\$12',
      'attendees': 350,
      'isBookmarked': false,
    },
    {
      'id': '8',
      'title': 'Thai Temple Architecture Tour',
      'date': 'April 25, 2025',
      'time': '8:30 AM',
      'location': 'Wat Pho, Bangkok',
      'imageUrl':
          'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'History',
      'price': '\$15',
      'attendees': 78,
      'isBookmarked': true,
    },
    {
      'id': '9',
      'title': 'Italian Pasta Making Class',
      'date': 'April 18, 2025',
      'time': '3:00 PM',
      'location': 'Trastevere, Rome',
      'imageUrl':
          'https://images.unsplash.com/photo-1551183053-bf91a1d81141?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'Food Tour',
      'price': '€38',
      'attendees': 16,
      'isBookmarked': false,
    },
    {
      'id': '10',
      'title': 'Aboriginal Art Workshop',
      'date': 'May 2, 2025',
      'time': '10:00 AM',
      'location': 'Sydney Opera House, Australia',
      'imageUrl':
          'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'Workshop',
      'price': 'AUD 42',
      'attendees': 30,
      'isBookmarked': false,
    },
    {
      'id': '11',
      'title': 'Flamenco Night in Seville',
      'date': 'April 22, 2025',
      'time': '9:00 PM',
      'location': 'Barrio Santa Cruz, Seville',
      'imageUrl':
          'https://images.unsplash.com/photo-1504609813442-a8924e83f76e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'Performance',
      'price': '€25',
      'attendees': 120,
      'isBookmarked': true,
    },
    {
      'id': '12',
      'title': 'Kyoto Tea Ceremony Experience',
      'date': 'May 5, 2025',
      'time': '2:30 PM',
      'location': 'Gion District, Kyoto',
      'imageUrl':
          'https://images.unsplash.com/photo-1544787219-7f47ccb76574?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'Cultural',
      'price': '¥3500',
      'attendees': 12,
      'isBookmarked': false,
    },
    {
      'id': '13',
      'title': 'Maasai Village Cultural Exchange',
      'date': 'May 8, 2025',
      'time': '9:00 AM',
      'location': 'Maasai Mara, Kenya',
      'imageUrl':
          'https://images.unsplash.com/photo-1516026672322-bc52d61a55d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'Cultural',
      'price': '\$55',
      'attendees': 18,
      'isBookmarked': false,
    },
    {
      'id': '14',
      'title': 'Viking History Museum Tour',
      'date': 'May 12, 2025',
      'time': '1:00 PM',
      'location': 'Oslo Viking Ship Museum, Norway',
      'imageUrl':
          'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'History',
      'price': 'NOK 180',
      'attendees': 65,
      'isBookmarked': true,
    },
    {
      'id': '15',
      'title': 'Brazilian Carnival Workshop',
      'date': 'May 15, 2025',
      'time': '4:00 PM',
      'location': 'Copacabana Beach, Rio de Janeiro',
      'imageUrl':
          'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'Festival',
      'price': 'R\$ 45',
      'attendees': 95,
      'isBookmarked': false,
    },
  ];

  List<Map<String, dynamic>> get _filteredEvents {
    var events = _mockUpcomingEvents;

    // Filter by category
    if (_selectedCategory != null && _selectedCategory != 'All') {
      events = events
          .where((event) => event['category'] == _selectedCategory)
          .toList();
    }

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      events = events
          .where((event) =>
              event['title']
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              event['location']
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              event['category']
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()))
          .toList();
    }

    return events;
  }

  void _handleEventPress(String eventId) {
    final event = _mockUpcomingEvents.firstWhere(
      (evt) => evt['id'] == eventId,
      orElse: () => _mockUpcomingEvents.first,
    );

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EventDetailsScreen(
          id: event['id'],
          title: event['title'],
          date: event['date'],
          time: event['time'],
          location: event['location'],
          imageUrl: event['imageUrl'],
          category: event['category'],
          price: event['price'],
          attendees: event['attendees'],
          isBookmarked: event['isBookmarked'],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: KaiaDesignTokens.neutralGray50,
      appBar: AppBar(
        backgroundColor: KaiaDesignTokens.neutralWhite,
        elevation: 0,
        title: const Text(
          'Upcoming Events',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: KaiaDesignTokens.neutralGray900,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _isGridView ? Icons.view_list : Icons.grid_view,
              color: KaiaDesignTokens.neutralGray700,
            ),
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          _buildSearchAndFilterSection(),

          // Results Count
          _buildResultsCount(),

          // Events Grid/List
          Expanded(
            child: _isLoading
                ? _buildLoadingState()
                : _filteredEvents.isEmpty
                    ? _buildEmptyState()
                    : FadeTransition(
                        opacity: _fadeAnimation,
                        child:
                            _isGridView ? _buildGridView() : _buildListView(),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilterSection() {
    return Container(
      color: KaiaDesignTokens.neutralWhite,
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
      child: Column(
        children: [
          // Search Bar
          Container(
            decoration: BoxDecoration(
              color: KaiaDesignTokens.neutralGray100,
              borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
              border: Border.all(
                color: KaiaDesignTokens.neutralGray200,
                width: 1,
              ),
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: const InputDecoration(
                hintText: 'Search events...',
                prefixIcon: Icon(
                  Icons.search,
                  color: KaiaDesignTokens.neutralGray500,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: KaiaDesignTokens.spacing16,
                  vertical: KaiaDesignTokens.spacing12,
                ),
              ),
            ),
          ),

          const SizedBox(height: KaiaDesignTokens.spacing16),

          // Category Filter Pills
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = _selectedCategory == category ||
                    (_selectedCategory == null && category == 'All');

                return Padding(
                  padding: EdgeInsets.only(
                    right: index < _categories.length - 1
                        ? KaiaDesignTokens.spacing8
                        : 0,
                  ),
                  child: FilterChip(
                    label: Text(category),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = selected ? category : null;
                        if (category == 'All') {
                          _selectedCategory = null;
                        }
                      });
                    },
                    backgroundColor: KaiaDesignTokens.neutralWhite,
                    selectedColor:
                        KaiaDesignTokens.primaryIndigo.withValues(alpha: 0.1),
                    checkmarkColor: KaiaDesignTokens.primaryIndigo,
                    labelStyle: TextStyle(
                      color: isSelected
                          ? KaiaDesignTokens.primaryIndigo
                          : KaiaDesignTokens.neutralGray700,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                    side: BorderSide(
                      color: isSelected
                          ? KaiaDesignTokens.primaryIndigo
                          : KaiaDesignTokens.neutralGray300,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsCount() {
    return Container(
      color: KaiaDesignTokens.neutralWhite,
      padding: const EdgeInsets.fromLTRB(
        KaiaDesignTokens.spacing16,
        0,
        KaiaDesignTokens.spacing16,
        KaiaDesignTokens.spacing16,
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              '${_filteredEvents.length} events found',
              style: const TextStyle(
                fontSize: 14,
                color: KaiaDesignTokens.neutralGray600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (_selectedCategory != null && _selectedCategory != 'All')
            Text(
              '• Filtered by $_selectedCategory',
              style: const TextStyle(
                fontSize: 12,
                color: KaiaDesignTokens.neutralGray500,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildGridView() {
    return RefreshIndicator(
      onRefresh: _loadEvents,
      child: GridView.builder(
        padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.75,
          crossAxisSpacing: KaiaDesignTokens.spacing16,
          mainAxisSpacing: KaiaDesignTokens.spacing16,
        ),
        itemCount: _filteredEvents.length,
        itemBuilder: (context, index) {
          final event = _filteredEvents[index];
          return EventCard(
            id: event['id'],
            title: event['title'],
            date: event['date'],
            time: event['time'],
            location: event['location'],
            imageUrl: event['imageUrl'],
            category: event['category'],
            price: event['price'],
            attendees: event['attendees'],
            isBookmarked: event['isBookmarked'],
            isGridMode: true,
            onPressed: () => _handleEventPress(event['id']),
            onFavoritePressed: () {
              // Handle favorite functionality
              // TODO: Implement favorite functionality
            },
          );
        },
      ),
    );
  }

  Widget _buildListView() {
    return RefreshIndicator(
      onRefresh: _loadEvents,
      child: ListView.builder(
        padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
        itemCount: _filteredEvents.length,
        itemBuilder: (context, index) {
          final event = _filteredEvents[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: KaiaDesignTokens.spacing16),
            child: EventCard(
              id: event['id'],
              title: event['title'],
              date: event['date'],
              time: event['time'],
              location: event['location'],
              imageUrl: event['imageUrl'],
              category: event['category'],
              price: event['price'],
              attendees: event['attendees'],
              isBookmarked: event['isBookmarked'],
              isGridMode: false,
              onPressed: () => _handleEventPress(event['id']),
              onFavoritePressed: () {
                // Handle favorite functionality
                // TODO: Implement favorite functionality
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildLoadingState() {
    return GridView.builder(
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: KaiaDesignTokens.spacing16,
        mainAxisSpacing: KaiaDesignTokens.spacing16,
      ),
      itemCount: 6,
      itemBuilder: (context, index) {
        return Container(
          decoration: BoxDecoration(
            color: KaiaDesignTokens.neutralWhite,
            borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
            border: Border.all(
              color: KaiaDesignTokens.neutralGray200,
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image skeleton
              Container(
                height: 140,
                decoration: BoxDecoration(
                  color: KaiaDesignTokens.neutralGray200,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(KaiaDesignTokens.radiusLg),
                  ),
                ),
              ),
              // Content skeleton
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 16,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: KaiaDesignTokens.neutralGray200,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      height: 12,
                      width: 120,
                      decoration: BoxDecoration(
                        color: KaiaDesignTokens.neutralGray200,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      height: 12,
                      width: 100,
                      decoration: BoxDecoration(
                        color: KaiaDesignTokens.neutralGray200,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(KaiaDesignTokens.spacing32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.event_busy,
              size: 80,
              color: KaiaDesignTokens.neutralGray400,
            ),
            const SizedBox(height: KaiaDesignTokens.spacing24),
            const Text(
              'No events found',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: KaiaDesignTokens.neutralGray700,
              ),
            ),
            const SizedBox(height: KaiaDesignTokens.spacing8),
            Text(
              _searchQuery.isNotEmpty
                  ? 'Try adjusting your search or filters'
                  : 'Check back later for upcoming events',
              style: const TextStyle(
                fontSize: 16,
                color: KaiaDesignTokens.neutralGray500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: KaiaDesignTokens.spacing24),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _searchQuery = '';
                  _selectedCategory = null;
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: KaiaDesignTokens.primaryIndigo,
                foregroundColor: KaiaDesignTokens.neutralWhite,
                padding: const EdgeInsets.symmetric(
                  horizontal: KaiaDesignTokens.spacing24,
                  vertical: KaiaDesignTokens.spacing12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius:
                      BorderRadius.circular(KaiaDesignTokens.radiusLg),
                ),
              ),
              child: const Text('Clear Filters'),
            ),
          ],
        ),
      ),
    );
  }
}
