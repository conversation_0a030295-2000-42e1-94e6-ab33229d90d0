import React, { useState } from 'react';
import { StyleSheet, View, Text, FlatList, Pressable, Dimensions } from 'react-native';
import { useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, Filter } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';
import { TopGuideCard } from '@/components/TopGuideCard';
import { topGuides, TopGuide } from '@/mocks/guides';

const { width } = Dimensions.get('window');
const cardWidth = (width - spacing.lg * 3) / 2;

export default function GuidesScreen() {
  const router = useRouter();
  const [selectedFilter, setSelectedFilter] = useState('All');

  const filters = ['All', 'Certified', 'Top Rated', 'Cultural', 'Adventure', 'Food'];

  const filteredGuides = topGuides.filter(guide => {
    switch (selectedFilter) {
      case 'Certified':
        return guide.isCertified;
      case 'Top Rated':
        return guide.rating >= 4.8;
      case 'Cultural':
        return guide.specialties.some(s => s.toLowerCase().includes('cultural') || s.toLowerCase().includes('historical'));
      case 'Adventure':
        return guide.specialties.some(s => s.toLowerCase().includes('adventure') || s.toLowerCase().includes('nature'));
      case 'Food':
        return guide.specialties.some(s => s.toLowerCase().includes('food') || s.toLowerCase().includes('culinary'));
      default:
        return true;
    }
  });

  const handleGuidePress = (guide: TopGuide) => {
    router.push(`/guides/swipe?guideId=${guide.id}`);
  };

  const handleFilterPress = (filter: string) => {
    setSelectedFilter(filter);
  };

  const renderGuideCard = ({ item }: { item: TopGuide }) => (
    <View style={styles.cardContainer}>
      <TopGuideCard guide={item} onPress={handleGuidePress} />
    </View>
  );

  const renderFilterPill = ({ item }: { item: string }) => (
    <Pressable
      style={[
        styles.filterPill,
        selectedFilter === item && styles.activeFilterPill
      ]}
      onPress={() => handleFilterPress(item)}
    >
      <Text style={[
        styles.filterText,
        selectedFilter === item && styles.activeFilterText
      ]}>
        {item}
      </Text>
    </Pressable>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      {/* Custom Header */}
      <View style={styles.header}>
        <Pressable style={styles.headerBackButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </Pressable>
        <Text style={styles.headerTitle}>Choose a Guide</Text>
        <Pressable style={styles.filterButton}>
          <Filter size={20} color={colors.text} />
        </Pressable>
      </View>

      <View style={styles.content}>
        {/* Filter Pills */}
        <View style={styles.filtersContainer}>
          <FlatList
            data={filters}
            renderItem={renderFilterPill}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item}
            contentContainerStyle={styles.filtersList}
          />
        </View>

        {/* Results Count */}
        <View style={styles.resultsContainer}>
          <Text style={styles.resultsText}>
            {filteredGuides.length} guide{filteredGuides.length !== 1 ? 's' : ''} found
          </Text>
        </View>

        {/* Guides Grid */}
        <FlatList
          data={filteredGuides}
          renderItem={renderGuideCard}
          numColumns={2}
          showsVerticalScrollIndicator={false}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.guidesList}
          columnWrapperStyle={styles.row}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  headerBackButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  headerTitle: {
    flex: 1,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    textAlign: 'center',
    marginHorizontal: spacing.md,
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  content: {
    flex: 1,
  },
  filtersContainer: {
    paddingVertical: spacing.md,
  },
  filtersList: {
    paddingHorizontal: spacing.lg,
    gap: spacing.sm,
  },
  filterPill: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    backgroundColor: colors.backgroundSecondary,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  activeFilterPill: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  filterText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium as any,
    color: colors.textSecondary,
  },
  activeFilterText: {
    color: colors.white,
  },
  resultsContainer: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.md,
  },
  resultsText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
  },
  guidesList: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
  },
  row: {
    justifyContent: 'space-between',
  },
  cardContainer: {
    width: cardWidth,
    marginBottom: spacing.lg,
  },
});