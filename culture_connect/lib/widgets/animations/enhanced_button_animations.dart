import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Enhanced button with modern micro-interactions
class EnhancedAnimatedButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final bool enabled;
  final bool showRipple;
  final bool enableHapticFeedback;
  final double? width;
  final double? height;

  const EnhancedAnimatedButton({
    super.key,
    required this.child,
    this.onPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.borderRadius,
    this.enabled = true,
    this.showRipple = true,
    this.enableHapticFeedback = true,
    this.width,
    this.height,
  });

  @override
  State<EnhancedAnimatedButton> createState() => _EnhancedAnimatedButtonState();
}

class _EnhancedAnimatedButtonState extends State<EnhancedAnimatedButton>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _colorController;
  late AnimationController _rippleController;

  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;
  late Animation<double> _rippleAnimation;

  bool _isPressed = false;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    // Scale animation for press effect
    _scaleController = AnimationController(
      duration: AppTheme.animationFast,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.96,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: AppTheme.curveEmphasized,
    ));

    // Color animation for hover/press states
    _colorController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );
    _colorAnimation = ColorTween(
      begin: widget.backgroundColor ?? AppTheme.primaryColor,
      end: (widget.backgroundColor ?? AppTheme.primaryColor).withAlpha(204),
    ).animate(CurvedAnimation(
      parent: _colorController,
      curve: AppTheme.curveDefault,
    ));

    // Ripple animation
    _rippleController = AnimationController(
      duration: AppTheme.animationSlow,
      vsync: this,
    );
    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rippleController,
      curve: AppTheme.curveDecelerated,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _colorController.dispose();
    _rippleController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (!widget.enabled || widget.onPressed == null) return;

    setState(() {
      _isPressed = true;
    });

    _scaleController.forward();
    _colorController.forward();

    if (widget.showRipple) {
      _rippleController.forward();
    }

    if (widget.enableHapticFeedback) {
      HapticFeedback.lightImpact();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    _handleTapEnd();
  }

  void _handleTapCancel() {
    _handleTapEnd();
  }

  void _handleTapEnd() {
    if (!_isPressed) return;

    setState(() {
      _isPressed = false;
    });

    _scaleController.reverse();
    _colorController.reverse();

    if (widget.showRipple) {
      _rippleController.reverse();
    }
  }

  void _handleTap() {
    if (!widget.enabled || widget.onPressed == null) return;
    widget.onPressed!();
  }

  void _handleHoverEnter(PointerEnterEvent event) {
    if (!widget.enabled) return;

    setState(() {
      _isHovered = true;
    });
  }

  void _handleHoverExit(PointerExitEvent event) {
    setState(() {
      _isHovered = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: _handleHoverEnter,
      onExit: _handleHoverExit,
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _scaleAnimation,
          _colorAnimation,
          _rippleAnimation,
        ]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: widget.width,
              height: widget.height,
              decoration: BoxDecoration(
                color: _colorAnimation.value,
                borderRadius: widget.borderRadius ?? 
                    BorderRadius.circular(AppTheme.borderRadiusLarge),
                boxShadow: _isHovered && widget.enabled
                    ? AppTheme.shadowMedium
                    : AppTheme.shadowSmall,
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTapDown: _handleTapDown,
                  onTapUp: _handleTapUp,
                  onTapCancel: _handleTapCancel,
                  onTap: _handleTap,
                  borderRadius: widget.borderRadius ?? 
                      BorderRadius.circular(AppTheme.borderRadiusLarge),
                  child: Container(
                    padding: widget.padding ?? 
                        const EdgeInsets.symmetric(
                          horizontal: AppTheme.spacingLarge,
                          vertical: AppTheme.spacingMedium,
                        ),
                    child: Center(
                      child: DefaultTextStyle(
                        style: TextStyle(
                          color: widget.foregroundColor ?? Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                        child: widget.child,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Enhanced card with hover and tap animations
class EnhancedAnimatedCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final bool enableHoverEffect;
  final bool enableTapEffect;

  const EnhancedAnimatedCard({
    super.key,
    required this.child,
    this.onTap,
    this.margin,
    this.padding,
    this.enableHoverEffect = true,
    this.enableTapEffect = true,
  });

  @override
  State<EnhancedAnimatedCard> createState() => _EnhancedAnimatedCardState();
}

class _EnhancedAnimatedCardState extends State<EnhancedAnimatedCard>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _tapController;

  late Animation<double> _elevationAnimation;
  late Animation<double> _scaleAnimation;

  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _hoverController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );
    _elevationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: AppTheme.curveDefault,
    ));

    _tapController = AnimationController(
      duration: AppTheme.animationFast,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _tapController,
      curve: AppTheme.curveEmphasized,
    ));
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _tapController.dispose();
    super.dispose();
  }

  void _handleHoverEnter(PointerEnterEvent event) {
    if (!widget.enableHoverEffect) return;

    setState(() {
      _isHovered = true;
    });
    _hoverController.forward();
  }

  void _handleHoverExit(PointerExitEvent event) {
    setState(() {
      _isHovered = false;
    });
    _hoverController.reverse();
  }

  void _handleTapDown(TapDownDetails details) {
    if (!widget.enableTapEffect) return;
    _tapController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    _tapController.reverse();
  }

  void _handleTapCancel() {
    _tapController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: _handleHoverEnter,
      onExit: _handleHoverExit,
      child: AnimatedBuilder(
        animation: Listenable.merge([_elevationAnimation, _scaleAnimation]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              margin: widget.margin,
              decoration: BoxDecoration(
                gradient: AppTheme.cardGradient,
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
                boxShadow: _isHovered 
                    ? AppTheme.shadowLarge 
                    : AppTheme.shadowSmall,
                border: Border.all(
                  color: AppTheme.outline,
                  width: 0.5,
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTapDown: _handleTapDown,
                  onTapUp: _handleTapUp,
                  onTapCancel: _handleTapCancel,
                  onTap: widget.onTap,
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
                  child: Container(
                    padding: widget.padding ?? 
                        const EdgeInsets.all(AppTheme.spacingMedium),
                    child: widget.child,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
