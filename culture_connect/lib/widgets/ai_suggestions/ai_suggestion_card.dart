import 'package:flutter/material.dart';
import 'package:culture_connect/theme/kaia_design_tokens.dart';

/// AI-powered suggestion card with distinctive AI badge and optimized layout
/// Supports both grid and list modes with proper defensive overflow handling
class AISuggestionCard extends StatelessWidget {
  final String title;
  final String location;
  final String imageUrl;
  final double aiConfidence;
  final int reviewCount;
  final String price;
  final String duration;
  final List<String> tags;
  final String category;
  final String matchReason;
  final bool isGridMode;
  final VoidCallback onPressed;
  final VoidCallback? onFavoritePressed;
  final bool isFavorite;

  const AISuggestionCard({
    super.key,
    required this.title,
    required this.location,
    required this.imageUrl,
    required this.aiConfidence,
    required this.reviewCount,
    required this.price,
    required this.duration,
    required this.tags,
    required this.category,
    required this.matchReason,
    required this.isGridMode,
    required this.onPressed,
    this.onFavoritePressed,
    this.isFavorite = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: isGridMode ? double.infinity : 600,
          // Add height constraint for grid mode to prevent overflow (following established pattern)
          maxHeight: isGridMode ? 275 : double.infinity,
        ),
        decoration: BoxDecoration(
          color: KaiaDesignTokens.neutralWhite,
          borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
          border: Border.all(
            color: KaiaDesignTokens.neutralGray200,
            width: 1,
          ),
          boxShadow: KaiaDesignTokens.shadowMd,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min, // Important: minimize column size
            children: [
              // Image Section with AI Badge
              _buildImageSection(),

              // Content Section - Use Flexible in grid mode to prevent overflow
              isGridMode
                  ? Flexible(child: _buildContentSection())
                  : _buildContentSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Stack(
      children: [
        // Main Image
        Container(
          height: isGridMode ? 140 : 180,
          width: double.infinity,
          color: KaiaDesignTokens.neutralGray100,
          child: imageUrl.isNotEmpty
              ? Image.network(
                  imageUrl,
                  fit: BoxFit.cover,
                  // Performance optimizations (following established pattern)
                  cacheWidth: isGridMode ? 300 : 400,
                  cacheHeight: isGridMode ? 200 : 300,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: KaiaDesignTokens.neutralGray100,
                      child: Icon(
                        Icons.image_outlined,
                        size: isGridMode ? 32 : 48,
                        color: KaiaDesignTokens.neutralGray400,
                      ),
                    );
                  },
                )
              : Icon(
                  Icons.image_outlined,
                  size: isGridMode ? 32 : 48,
                  color: KaiaDesignTokens.neutralGray400,
                ),
        ),

        // AI Powered Badge - Top Left (distinctive styling)
        Positioned(
          top: 12,
          left: 12,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: KaiaDesignTokens.primaryIndigo, // Purple for AI branding
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: KaiaDesignTokens.primaryIndigo.withOpacity(0.3),
                  offset: const Offset(0, 2),
                  blurRadius: 4,
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.auto_awesome, // Distinctive AI icon
                  size: 12,
                  color: KaiaDesignTokens.neutralWhite,
                ),
                const SizedBox(width: 4),
                Text(
                  'AI Powered',
                  style: TextStyle(
                    fontSize: isGridMode ? 9 : 10,
                    fontWeight: FontWeight.w700,
                    color: KaiaDesignTokens.neutralWhite,
                    letterSpacing: 0.2,
                  ),
                ),
              ],
            ),
          ),
        ),

        // Confidence Badge - Top Right
        Positioned(
          top: 12,
          right: 12,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
            decoration: BoxDecoration(
              color: KaiaDesignTokens.neutralWhite.withOpacity(0.9),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '${aiConfidence.toInt()}%',
              style: TextStyle(
                fontSize: isGridMode ? 10 : 11,
                fontWeight: FontWeight.w600,
                color: KaiaDesignTokens.primaryIndigo,
              ),
            ),
          ),
        ),

        // Favorite Button - Bottom Right (if provided)
        if (onFavoritePressed != null)
          Positioned(
            bottom: 12,
            right: 12,
            child: GestureDetector(
              onTap: onFavoritePressed,
              child: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: KaiaDesignTokens.neutralWhite.withOpacity(0.9),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  isFavorite ? Icons.favorite : Icons.favorite_border,
                  size: 16,
                  color:
                      isFavorite ? Colors.red : KaiaDesignTokens.neutralGray600,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildContentSection() {
    // Grid mode optimizations - ultra-aggressive spacing reduction to prevent overflow
    final contentPadding = isGridMode
        ? const EdgeInsets.all(8) // Further reduced padding for grid mode
        : const EdgeInsets.all(KaiaDesignTokens.spacing16);

    return Padding(
      padding: contentPadding,
      child: ConstrainedBox(
        constraints: isGridMode
            ? const BoxConstraints(
                maxHeight: 105) // Reduced height to prevent overflow
            : const BoxConstraints(),
        child: SingleChildScrollView(
          // Ultimate fallback to prevent overflow
          physics:
              const NeverScrollableScrollPhysics(), // Disable scrolling in normal cases
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min, // Important: minimize column size
            children: [
              // Title
              Text(
                title,
                style: TextStyle(
                  fontSize: isGridMode ? 14 : 16,
                  fontWeight: FontWeight.w700,
                  color: KaiaDesignTokens.neutralGray900,
                  height: 1.2,
                ),
                maxLines:
                    isGridMode ? 1 : 2, // Single line in grid mode saves space
                overflow: TextOverflow.ellipsis,
              ),

              SizedBox(
                  height: isGridMode
                      ? 2
                      : 6), // Ultra-reduced spacing for grid mode

              // Match Reason (instead of location for AI suggestions)
              Row(
                children: [
                  Icon(
                    Icons.psychology_outlined, // AI brain icon
                    size: isGridMode ? 11 : 14,
                    color: KaiaDesignTokens.primaryIndigo,
                  ),
                  const SizedBox(width: 3),
                  Expanded(
                    child: Text(
                      matchReason,
                      style: TextStyle(
                        fontSize: isGridMode ? 10 : 13,
                        color: KaiaDesignTokens.primaryIndigo,
                        fontWeight: FontWeight.w500,
                        fontStyle: FontStyle.italic,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),

              SizedBox(
                  height: isGridMode
                      ? 2
                      : 8), // Ultra-reduced spacing for grid mode

              // Tags (only show first 1 in grid mode, 3 in list mode to save space)
              if (tags.isNotEmpty)
                Wrap(
                  spacing: 3,
                  runSpacing: 2,
                  children: tags
                      .take(isGridMode ? 1 : 3)
                      .map((tag) => Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: _getCategoryColor().withOpacity(0.1),
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: _getCategoryColor().withOpacity(0.3),
                                width: 0.5,
                              ),
                            ),
                            child: Text(
                              tag,
                              style: TextStyle(
                                fontSize: isGridMode ? 9 : 10,
                                color: _getCategoryColor(),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ))
                      .toList(),
                ),

              SizedBox(
                  height: isGridMode
                      ? 3
                      : 10), // Ultra-reduced spacing for grid mode

              // Bottom Row - Price, Duration, Confidence
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Price - Use Flexible to prevent overflow
                  Flexible(
                    child: Text(
                      price,
                      style: TextStyle(
                        fontSize: isGridMode ? 14 : 16,
                        fontWeight: FontWeight.w700,
                        color: KaiaDesignTokens.primaryIndigo,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // Duration and Confidence for list mode
                  if (!isGridMode)
                    Row(
                      children: [
                        const Icon(
                          Icons.access_time,
                          size: 12,
                          color: KaiaDesignTokens.neutralGray500,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          duration,
                          style: const TextStyle(
                            fontSize: 11,
                            color: KaiaDesignTokens.neutralGray600,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          Icons.auto_awesome,
                          size: 12,
                          color: KaiaDesignTokens.primaryIndigo,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          '${aiConfidence.toInt()}%',
                          style: TextStyle(
                            fontSize: 11,
                            color: KaiaDesignTokens.primaryIndigo,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                ],
              ),

              // Duration for grid mode (separate line)
              if (isGridMode) ...[
                const SizedBox(
                    height: 2), // Ultra-reduced spacing for tighter fit
                Row(
                  children: [
                    const Icon(
                      Icons.access_time,
                      size: 10,
                      color: KaiaDesignTokens.neutralGray500,
                    ),
                    const SizedBox(width: 2),
                    Flexible(
                      child: Text(
                        duration,
                        style: const TextStyle(
                          fontSize: 10,
                          color: KaiaDesignTokens.neutralGray600,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 4), // Fixed width instead of Spacer
                    Icon(
                      Icons.auto_awesome,
                      size: 10,
                      color: KaiaDesignTokens.primaryIndigo,
                    ),
                    const SizedBox(width: 2),
                    Text(
                      '${aiConfidence.toInt()}%',
                      style: TextStyle(
                        fontSize: 10,
                        color: KaiaDesignTokens.primaryIndigo,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getCategoryColor() {
    switch (category.toLowerCase()) {
      case 'cultural':
        return KaiaDesignTokens.primaryIndigo;
      case 'adventure':
        return Colors.orange;
      case 'food & drink':
        return Colors.green;
      case 'nature':
        return Colors.teal;
      case 'historical':
        return Colors.brown;
      case 'art & museums':
        return Colors.purple;
      case 'nightlife':
        return Colors.pink;
      case 'wellness':
        return Colors.blue;
      default:
        return KaiaDesignTokens.primaryIndigo;
    }
  }
}
