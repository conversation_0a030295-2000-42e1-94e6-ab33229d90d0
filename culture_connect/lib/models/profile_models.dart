import 'package:flutter/material.dart';

/// Model for Quick Action cards in the Profile screen
class QuickAction {
  final String title;
  final String description;
  final IconData icon;
  final Color backgroundColor;
  final VoidCallback onTap;

  const QuickAction({
    required this.title,
    required this.description,
    required this.icon,
    required this.backgroundColor,
    required this.onTap,
  });
}

/// Model for Travel Statistics cards
class TravelStatistic {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color iconColor;
  final double progress; // 0.0 to 1.0
  final Color progressColor;

  const TravelStatistic({
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.iconColor,
    required this.progress,
    required this.progressColor,
  });
}

/// Model for Achievement cards
class Achievement {
  final String title;
  final String description;
  final IconData icon;
  final List<Color> gradientColors;
  final bool unlocked;
  final double progress; // 0.0 to 1.0 for locked achievements
  final String? progressText;

  const Achievement({
    required this.title,
    required this.description,
    required this.icon,
    required this.gradientColors,
    required this.unlocked,
    this.progress = 0.0,
    this.progressText,
  });
}

/// Model for Menu Items in settings sections
class MenuItem {
  final String title;
  final String? subtitle;
  final IconData icon;
  final Color iconColor;
  final VoidCallback? onTap;
  final Widget? trailing; // For switches or custom widgets

  const MenuItem({
    required this.title,
    this.subtitle,
    required this.icon,
    required this.iconColor,
    this.onTap,
    this.trailing,
  });
}

/// Model for Toggle Settings
class ToggleSetting {
  final String title;
  final String description;
  final IconData icon;
  final Color iconColor;
  final bool value;
  final ValueChanged<bool> onChanged;

  const ToggleSetting({
    required this.title,
    required this.description,
    required this.icon,
    required this.iconColor,
    required this.value,
    required this.onChanged,
  });
}

/// Model for User Statistics displayed in profile header
class UserStatistic {
  final String label;
  final String value;
  final IconData? icon;

  const UserStatistic({
    required this.label,
    required this.value,
    this.icon,
  });
}
