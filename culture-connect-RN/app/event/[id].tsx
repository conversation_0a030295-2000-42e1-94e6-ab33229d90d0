import React, { useState } from "react";
import { StyleSheet, View, Text, ScrollView, Pressable, Dimensions } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Image } from "expo-image";
import { SafeAreaView } from "react-native-safe-area-context";
import { ArrowLeft, Heart, Calendar, Clock, MapPin, Users, Ticket } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { Button } from "@/components/Button";
import { events } from "@/mocks/events";

const { width } = Dimensions.get("window");

export default function EventDetailScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const [isFavorite, setIsFavorite] = useState(false);
  
  const event = events.find((e) => e.id === id);
  
  if (!event) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Event not found</Text>
      </SafeAreaView>
    );
  }

  const handleFavoritePress = () => {
    setIsFavorite(!isFavorite);
  };

  const handleBookNow = () => {
    // Navigate to booking flow
    router.push("/bookings");
  };

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: event.imageUrl }}
            style={styles.image}
            contentFit="cover"
          />
          <LinearGradient
            colors={["rgba(0,0,0,0.5)", "transparent", "transparent"]}
            style={styles.gradient}
          />
          <SafeAreaView style={styles.header} edges={["top"]}>
            <Pressable style={styles.backButton} onPress={() => router.back()}>
              <ArrowLeft size={24} color={colors.white} />
            </Pressable>
            <Pressable
              style={styles.favoriteButton}
              onPress={handleFavoritePress}
            >
              <Heart
                size={24}
                color={colors.white}
                fill={isFavorite ? colors.secondary : "none"}
              />
            </Pressable>
          </SafeAreaView>
        </View>

        <View style={styles.content}>
          <Text style={styles.title}>{event.title}</Text>

          <View style={styles.infoContainer}>
            <View style={styles.infoItem}>
              <Calendar size={18} color={colors.primary} />
              <Text style={styles.infoText}>{event.date}</Text>
            </View>
            <View style={styles.infoItem}>
              <Clock size={18} color={colors.primary} />
              <Text style={styles.infoText}>10:00 AM - 6:00 PM</Text>
            </View>
            <View style={styles.infoItem}>
              <MapPin size={18} color={colors.primary} />
              <Text style={styles.infoText}>{event.location}</Text>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>About the Event</Text>
            <Text style={styles.description}>
              Join us for an unforgettable cultural experience at the {event.title}. 
              This annual celebration brings together locals and tourists alike to 
              enjoy traditional performances, delicious food, and beautiful decorations.
              
              The festival features various activities throughout the day, including 
              live music, dance performances, art exhibitions, and interactive workshops 
              where you can learn about local traditions and crafts.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>What to Expect</Text>
            <View style={styles.expectationCard}>
              <View style={styles.expectationIconContainer}>
                <Users size={20} color={colors.white} />
              </View>
              <View style={styles.expectationContent}>
                <Text style={styles.expectationTitle}>Crowd Size</Text>
                <Text style={styles.expectationText}>
                  Large crowds expected. Arrive early for better viewing spots.
                </Text>
              </View>
            </View>
            <View style={styles.expectationCard}>
              <View style={[styles.expectationIconContainer, { backgroundColor: colors.secondary }]}>
                <Clock size={20} color={colors.white} />
              </View>
              <View style={styles.expectationContent}>
                <Text style={styles.expectationTitle}>Duration</Text>
                <Text style={styles.expectationText}>
                  Full-day event with peak activities in the afternoon.
                </Text>
              </View>
            </View>
            <View style={styles.expectationCard}>
              <View style={[styles.expectationIconContainer, { backgroundColor: colors.accent }]}>
                <Ticket size={20} color={colors.white} />
              </View>
              <View style={styles.expectationContent}>
                <Text style={styles.expectationTitle}>Ticket Information</Text>
                <Text style={styles.expectationText}>
                  General admission includes access to all areas. VIP options available.
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Ticket Options</Text>
            <View style={styles.ticketCard}>
              <View style={styles.ticketInfo}>
                <Text style={styles.ticketTitle}>General Admission</Text>
                <Text style={styles.ticketDescription}>
                  Access to all general areas and performances
                </Text>
                <Text style={styles.ticketPrice}>
                  {event.currency}{event.price}
                </Text>
              </View>
              <Pressable style={styles.selectButton}>
                <Text style={styles.selectButtonText}>Select</Text>
              </Pressable>
            </View>
            <View style={styles.ticketCard}>
              <View style={styles.ticketInfo}>
                <Text style={styles.ticketTitle}>VIP Experience</Text>
                <Text style={styles.ticketDescription}>
                  Priority access, exclusive viewing areas, and complimentary refreshments
                </Text>
                <Text style={styles.ticketPrice}>
                  {event.currency}{Math.round(event.price! * 2.5)}
                </Text>
              </View>
              <Pressable style={styles.selectButton}>
                <Text style={styles.selectButtonText}>Select</Text>
              </Pressable>
            </View>
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <View style={styles.priceContainer}>
          <Text style={styles.priceLabel}>From</Text>
          <Text style={styles.price}>
            {event.currency}{event.price}
          </Text>
          <Text style={styles.priceUnit}>/person</Text>
        </View>
        <Button
          title="Book Now"
          onPress={handleBookNow}
          variant="primary"
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  imageContainer: {
    height: 250,
    width: width,
  },
  image: {
    ...StyleSheet.absoluteFillObject,
  },
  gradient: {
    ...StyleSheet.absoluteFillObject,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: spacing.xl,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0,0,0,0.3)",
    justifyContent: "center",
    alignItems: "center",
  },
  favoriteButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0,0,0,0.3)",
    justifyContent: "center",
    alignItems: "center",
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.xl,
    paddingBottom: 100, // Space for the footer
  },
  title: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.md,
  },
  infoContainer: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.xl,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  infoText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.md,
  },
  description: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.text,
    lineHeight: 24,
  },
  expectationCard: {
    flexDirection: "row",
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  expectationIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
  },
  expectationContent: {
    flex: 1,
  },
  expectationTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  expectationText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textLight,
  },
  ticketCard: {
    flexDirection: "row",
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  ticketInfo: {
    flex: 1,
  },
  ticketTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  ticketDescription: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textLight,
    marginBottom: spacing.sm,
  },
  ticketPrice: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.primary,
  },
  selectButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    justifyContent: "center",
    alignSelf: "center",
  },
  selectButtonText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium as any,
    color: colors.white,
  },
  footer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 10,
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "baseline",
  },
  priceLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textLight,
    marginRight: spacing.xs,
  },
  price: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.primary,
  },
  priceUnit: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textLight,
    marginLeft: spacing.xs,
  },
});