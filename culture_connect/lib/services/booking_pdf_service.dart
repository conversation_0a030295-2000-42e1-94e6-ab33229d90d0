import 'dart:io';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:culture_connect/models/premium_booking.dart';
import 'package:culture_connect/widgets/booking_details/experience_booking_layout.dart';

/// Comprehensive PDF generation service for CultureConnect booking details
/// Replicates the exact visual appearance and content of mobile screens
class BookingPdfService {
  static const String _fontFamily = 'Roboto';

  /// Generate PDF for any booking type with specialized content adaptation
  static Future<File> generateBookingPdf(PremiumBooking booking) async {
    final pdf = pw.Document();

    // Load fonts for consistent typography
    final fontRegular =
        await rootBundle.load('assets/fonts/Roboto-Regular.ttf');
    final fontBold = await rootBundle.load('assets/fonts/Roboto-Bold.ttf');
    final ttfRegular = pw.Font.ttf(fontRegular);
    final ttfBold = pw.Font.ttf(fontBold);

    // Create PDF pages based on booking type
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (pw.Context context) {
          return [
            _buildPdfHeader(booking, ttfBold, ttfRegular),
            pw.SizedBox(height: 20),
            _buildBookingOverview(booking, ttfBold, ttfRegular),
            pw.SizedBox(height: 20),
            ..._buildSpecializedContent(booking, ttfBold, ttfRegular),
            pw.SizedBox(height: 20),
            _buildFooter(booking, ttfRegular),
          ];
        },
      ),
    );

    // Save PDF to device
    final directory = await getApplicationDocumentsDirectory();
    final file = File(
        '${directory.path}/booking_${booking.id}_${DateTime.now().millisecondsSinceEpoch}.pdf');
    await file.writeAsBytes(await pdf.save());

    return file;
  }

  /// Build PDF header with hero section styling
  static pw.Widget _buildPdfHeader(
      PremiumBooking booking, pw.Font fontBold, pw.Font fontRegular) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: _getTypeColor(booking.type),
        borderRadius: pw.BorderRadius.circular(16),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'CultureConnect',
                style: pw.TextStyle(
                  font: fontBold,
                  fontSize: 18,
                  color: PdfColors.white,
                ),
              ),
              pw.Container(
                padding:
                    const pw.EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: pw.BoxDecoration(
                  color: _getStatusColor(booking.status),
                  borderRadius: pw.BorderRadius.circular(20),
                ),
                child: pw.Text(
                  _getStatusText(booking.status),
                  style: pw.TextStyle(
                    font: fontBold,
                    fontSize: 12,
                    color: PdfColors.white,
                  ),
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 16),
          pw.Text(
            booking.title,
            style: pw.TextStyle(
              font: fontBold,
              fontSize: 24,
              color: PdfColors.white,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            booking.subtitle,
            style: pw.TextStyle(
              font: fontRegular,
              fontSize: 16,
              color: PdfColors.white,
            ),
          ),
          pw.SizedBox(height: 12),
          pw.Row(
            children: [
              pw.Icon(
                pw.IconData(0xe0c8), // location icon
                color: PdfColors.white,
                size: 16,
              ),
              pw.SizedBox(width: 8),
              pw.Text(
                booking.location,
                style: pw.TextStyle(
                  font: fontRegular,
                  fontSize: 14,
                  color: PdfColors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build booking overview section
  static pw.Widget _buildBookingOverview(
      PremiumBooking booking, pw.Font fontBold, pw.Font fontRegular) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(16),
        border: pw.Border.all(color: PdfColors.grey300, width: 1),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Booking Details',
            style: pw.TextStyle(
              font: fontBold,
              fontSize: 18,
              color: PdfColors.grey800,
            ),
          ),
          pw.SizedBox(height: 16),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              _buildDetailItem('Confirmation Code', booking.confirmationCode,
                  fontBold, fontRegular),
              _buildDetailItem('Price', '\$${booking.price.toStringAsFixed(0)}',
                  fontBold, fontRegular),
            ],
          ),
          pw.SizedBox(height: 12),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              _buildDetailItem(
                  'Date', _formatDate(booking.date), fontBold, fontRegular),
              _buildDetailItem(
                  'Duration', booking.duration, fontBold, fontRegular),
            ],
          ),
          pw.SizedBox(height: 12),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              _buildDetailItem('Participants', '${booking.participants}',
                  fontBold, fontRegular),
              _buildDetailItem(
                  'Rating', '${booking.rating} ⭐', fontBold, fontRegular),
            ],
          ),
        ],
      ),
    );
  }

  /// Build detail item for overview section
  static pw.Widget _buildDetailItem(
      String label, String value, pw.Font fontBold, pw.Font fontRegular) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          label,
          style: pw.TextStyle(
            font: fontRegular,
            fontSize: 12,
            color: PdfColors.grey600,
          ),
        ),
        pw.SizedBox(height: 4),
        pw.Text(
          value,
          style: pw.TextStyle(
            font: fontBold,
            fontSize: 14,
            color: PdfColors.grey800,
          ),
        ),
      ],
    );
  }

  /// Build specialized content based on booking type and experience type
  static List<pw.Widget> _buildSpecializedContent(
      PremiumBooking booking, pw.Font fontBold, pw.Font fontRegular) {
    final widgets = <pw.Widget>[];

    switch (booking.type) {
      case PremiumBookingType.experience:
        widgets.addAll(
            _buildExperienceSpecializedContent(booking, fontBold, fontRegular));
        break;
      case PremiumBookingType.flight:
        widgets.addAll(
            _buildFlightSpecializedContent(booking, fontBold, fontRegular));
        break;
      case PremiumBookingType.hotel:
        widgets.addAll(
            _buildHotelSpecializedContent(booking, fontBold, fontRegular));
        break;
      case PremiumBookingType.package:
        widgets.addAll(
            _buildPackageSpecializedContent(booking, fontBold, fontRegular));
        break;
      default:
        widgets.addAll(_buildGenericContent(booking, fontBold, fontRegular));
    }

    return widgets;
  }

  /// Build experience-specific content with intelligent adaptation
  static List<pw.Widget> _buildExperienceSpecializedContent(
      PremiumBooking booking, pw.Font fontBold, pw.Font fontRegular) {
    final widgets = <pw.Widget>[];
    final experienceType = _detectExperienceType(booking);

    switch (experienceType) {
      case ExperienceType.musicalFestival:
        widgets.addAll(
            _buildMusicalFestivalContent(booking, fontBold, fontRegular));
        break;
      case ExperienceType.cookingClass:
        widgets
            .addAll(_buildCookingClassContent(booking, fontBold, fontRegular));
        break;
      case ExperienceType.artWorkshop:
        widgets
            .addAll(_buildArtWorkshopContent(booking, fontBold, fontRegular));
        break;
      case ExperienceType.adventureActivity:
        widgets.addAll(
            _buildAdventureActivityContent(booking, fontBold, fontRegular));
        break;
      case ExperienceType.culturalTour:
        widgets
            .addAll(_buildCulturalTourContent(booking, fontBold, fontRegular));
        break;
      default:
        widgets.addAll(
            _buildGenericExperienceContent(booking, fontBold, fontRegular));
    }

    return widgets;
  }

  /// Detect experience type using same logic as mobile app
  static ExperienceType _detectExperienceType(PremiumBooking booking) {
    final title = booking.title.toLowerCase();
    final subtitle = booking.subtitle.toLowerCase();
    final highlights = booking.highlights.join(' ').toLowerCase();
    final details = booking.details?.values.join(' ').toLowerCase() ?? '';
    final allText = '$title $subtitle $highlights $details';

    if (_containsAny(allText, [
      'concert',
      'festival',
      'music',
      'band',
      'artist',
      'stage',
      'performance'
    ])) {
      return ExperienceType.musicalFestival;
    }
    if (_containsAny(allText, [
      'cooking',
      'culinary',
      'chef',
      'kitchen',
      'recipe',
      'food',
      'cuisine'
    ])) {
      return ExperienceType.cookingClass;
    }
    if (_containsAny(allText, [
      'art',
      'painting',
      'drawing',
      'sculpture',
      'pottery',
      'ceramics',
      'workshop'
    ])) {
      return ExperienceType.artWorkshop;
    }
    if (_containsAny(allText, [
      'hiking',
      'climbing',
      'adventure',
      'outdoor',
      'mountain',
      'trail',
      'trekking'
    ])) {
      return ExperienceType.adventureActivity;
    }
    if (_containsAny(allText, [
      'museum',
      'historical',
      'heritage',
      'cultural',
      'monument',
      'ancient'
    ])) {
      return ExperienceType.culturalTour;
    }

    return ExperienceType.generic;
  }

  /// Helper method to check if text contains any keywords
  static bool _containsAny(String text, List<String> keywords) {
    return keywords.any((keyword) => text.contains(keyword));
  }

  /// Build musical festival specialized content
  static List<pw.Widget> _buildMusicalFestivalContent(
      PremiumBooking booking, pw.Font fontBold, pw.Font fontRegular) {
    return [
      _buildSectionHeader('Artist Lineup', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard([
        'Main Stage: The Midnight (8:00 PM - 10:00 PM) - HEADLINER',
        'Side Stage: Electric Youth (6:30 PM - 7:30 PM)',
        'Main Stage: FM-84 (10:30 PM - 12:00 AM)',
      ], fontRegular),
      pw.SizedBox(height: 16),
      _buildSectionHeader('Venue Information', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard([
        'Venue: Red Rocks Amphitheatre',
        'Capacity: 9,525 people',
        'Parking: Available on-site (\$25)',
        'Accessibility: ADA compliant seating',
      ], fontRegular),
    ];
  }

  /// Build cooking class specialized content
  static List<pw.Widget> _buildCookingClassContent(
      PremiumBooking booking, pw.Font fontBold, pw.Font fontRegular) {
    return [
      _buildSectionHeader('Chef Information', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard([
        'Chef: Marco Rodriguez',
        'Experience: 15 years Michelin-starred',
        'Rating: 4.9 (127 reviews)',
      ], fontRegular),
      pw.SizedBox(height: 16),
      _buildSectionHeader('Menu & Recipes', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard([
        'Appetizer: Bruschetta with Tomato & Basil',
        'Main Course: Homemade Pasta Carbonara',
        'Dessert: Traditional Tiramisu',
      ], fontRegular),
      pw.SizedBox(height: 16),
      _buildSectionHeader('Kitchen Facilities', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard([
        'Professional stoves and equipment',
        'Fresh ingredients provided',
        'All utensils and recipe cards included',
        'Take-home containers provided',
      ], fontRegular),
    ];
  }

  /// Build art workshop specialized content
  static List<pw.Widget> _buildArtWorkshopContent(
      PremiumBooking booking, pw.Font fontBold, pw.Font fontRegular) {
    return [
      _buildSectionHeader('Instructor Profile', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard([
        'Instructor: Isabella Chen',
        'Background: Professional artist & gallery owner',
        'Rating: 4.8 (89 reviews)',
      ], fontRegular),
      pw.SizedBox(height: 16),
      _buildSectionHeader('Materials Provided', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard([
        'Canvas (16x20")',
        'Watercolor paints and brushes set',
        'Easel and apron provided',
        'Take-home frame included',
      ], fontRegular),
    ];
  }

  /// Build adventure activity specialized content
  static List<pw.Widget> _buildAdventureActivityContent(
      PremiumBooking booking, pw.Font fontBold, pw.Font fontRegular) {
    return [
      _buildSectionHeader('Safety Equipment', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard([
        'Professional helmets and harnesses',
        'First aid kit and emergency radio',
        'Safety equipment included',
        'Professional guide supervision',
      ], fontRegular),
      pw.SizedBox(height: 16),
      _buildSectionHeader('Fitness Requirements', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard([
        'Moderate fitness level required',
        'Ability to hike 3-5 miles',
        'Comfortable with heights',
        'No serious medical conditions',
      ], fontRegular),
    ];
  }

  /// Build cultural tour specialized content
  static List<pw.Widget> _buildCulturalTourContent(
      PremiumBooking booking, pw.Font fontBold, pw.Font fontRegular) {
    return [
      _buildSectionHeader('Expert Guide', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard([
        'Guide: Dr. Sarah Johnson',
        'Credentials: PhD in Classical Archaeology',
        'Experience: 20+ years expertise',
        'Specialization: Ancient Roman architecture',
      ], fontRegular),
      pw.SizedBox(height: 16),
      _buildSectionHeader('Site Significance', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard([
        'UNESCO World Heritage Sites',
        'Outstanding universal value',
        'Cultural importance recognized globally',
        'Access to restricted archaeological areas',
      ], fontRegular),
    ];
  }

  /// Build flight specialized content
  static List<pw.Widget> _buildFlightSpecializedContent(
      PremiumBooking booking, pw.Font fontBold, pw.Font fontRegular) {
    return [
      _buildSectionHeader('Flight Details', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard([
        'Route: ${booking.location}',
        'Aircraft: Emirates A380-800',
        'Seat: 1A (First Class Suite)',
        'Gate: A2',
      ], fontRegular),
      pw.SizedBox(height: 16),
      _buildSectionHeader('Baggage Information', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard([
        'Carry-on: Included (2 pieces, 7kg each)',
        'Checked: Included (2 pieces, 32kg each)',
        'Additional baggage available for purchase',
      ], fontRegular),
    ];
  }

  /// Build hotel specialized content
  static List<pw.Widget> _buildHotelSpecializedContent(
      PremiumBooking booking, pw.Font fontBold, pw.Font fontRegular) {
    return [
      _buildSectionHeader('Room Details', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard([
        'Room: Presidential Suite 4501',
        'Size: 2,500 sq ft',
        'Features: Panoramic city views, private terrace',
        'Amenities: King bed, marble bathroom, walk-in closet',
      ], fontRegular),
      pw.SizedBox(height: 16),
      _buildSectionHeader('Hotel Amenities', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard([
        'Rooftop pool with city views',
        'Full-service spa and wellness center',
        'Michelin-starred restaurant',
        'Concierge and butler services',
      ], fontRegular),
    ];
  }

  /// Build package specialized content
  static List<pw.Widget> _buildPackageSpecializedContent(
      PremiumBooking booking, pw.Font fontBold, pw.Font fontRegular) {
    return [
      _buildSectionHeader('Package Overview', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard([
        'Flights: Business class round-trip',
        'Hotels: 5-star accommodations',
        'Activities: 5 cultural experiences',
        'Dining: 4 Michelin-starred restaurants',
      ], fontRegular),
      pw.SizedBox(height: 16),
      _buildSectionHeader('Travel Guide', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard([
        'Guide: Hiroshi Tanaka',
        'Experience: 12+ years cultural tourism',
        'Languages: English, Japanese',
        'Rating: 4.9 (234 reviews)',
      ], fontRegular),
    ];
  }

  /// Build generic experience content
  static List<pw.Widget> _buildGenericExperienceContent(
      PremiumBooking booking, pw.Font fontBold, pw.Font fontRegular) {
    return [
      _buildSectionHeader('Experience Highlights', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard(booking.highlights, fontRegular),
    ];
  }

  /// Build generic content for other booking types
  static List<pw.Widget> _buildGenericContent(
      PremiumBooking booking, pw.Font fontBold, pw.Font fontRegular) {
    return [
      _buildSectionHeader('Highlights', fontBold),
      pw.SizedBox(height: 12),
      _buildInfoCard(booking.highlights, fontRegular),
    ];
  }

  /// Build section header with consistent styling
  static pw.Widget _buildSectionHeader(String title, pw.Font fontBold) {
    return pw.Text(
      title,
      style: pw.TextStyle(
        font: fontBold,
        fontSize: 18,
        color: PdfColors.grey800,
      ),
    );
  }

  /// Build info card with list of items
  static pw.Widget _buildInfoCard(List<String> items, pw.Font fontRegular) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey50,
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(color: PdfColors.grey200, width: 1),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: items
            .map((item) => pw.Padding(
                  padding: const pw.EdgeInsets.only(bottom: 8),
                  child: pw.Row(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        '• ',
                        style: pw.TextStyle(
                          font: fontRegular,
                          fontSize: 14,
                          color: PdfColors.grey700,
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Text(
                          item,
                          style: pw.TextStyle(
                            font: fontRegular,
                            fontSize: 14,
                            color: PdfColors.grey700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ))
            .toList(),
      ),
    );
  }

  /// Build PDF footer with contact information
  static pw.Widget _buildFooter(PremiumBooking booking, pw.Font fontRegular) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(12),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          pw.Text(
            'CultureConnect - The Soul of Travel, Guided by AI',
            style: pw.TextStyle(
              font: fontRegular,
              fontSize: 12,
              color: PdfColors.grey600,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            'Generated on ${_formatDate(DateTime.now())}',
            style: pw.TextStyle(
              font: fontRegular,
              fontSize: 10,
              color: PdfColors.grey500,
            ),
          ),
        ],
      ),
    );
  }

  /// Get color based on booking type
  static PdfColor _getTypeColor(PremiumBookingType type) {
    switch (type) {
      case PremiumBookingType.flight:
        return PdfColors.blue;
      case PremiumBookingType.hotel:
        return PdfColors.green;
      case PremiumBookingType.experience:
        return PdfColors.purple;
      case PremiumBookingType.package:
        return PdfColors.orange;
      default:
        return PdfColors.grey;
    }
  }

  /// Get color based on booking status
  static PdfColor _getStatusColor(PremiumBookingStatus status) {
    switch (status) {
      case PremiumBookingStatus.confirmed:
        return PdfColors.green;
      case PremiumBookingStatus.pending:
        return PdfColors.orange;
      case PremiumBookingStatus.cancelled:
        return PdfColors.red;
      case PremiumBookingStatus.completed:
        return PdfColors.blue;
      default:
        return PdfColors.grey;
    }
  }

  /// Get status text
  static String _getStatusText(PremiumBookingStatus status) {
    switch (status) {
      case PremiumBookingStatus.confirmed:
        return 'CONFIRMED';
      case PremiumBookingStatus.pending:
        return 'PENDING';
      case PremiumBookingStatus.cancelled:
        return 'CANCELLED';
      case PremiumBookingStatus.completed:
        return 'COMPLETED';
      case PremiumBookingStatus.upcoming:
        return 'UPCOMING';
    }
  }

  /// Format date for display
  static String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
