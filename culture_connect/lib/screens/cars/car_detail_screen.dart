import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:culture_connect/models/car.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/app_button.dart';
import 'package:culture_connect/screens/cars/car_booking_screen.dart';

/// Car detail screen with hero image, specs, and booking functionality
/// Matches React Native implementation with parallax scrolling
class CarDetailScreen extends StatefulWidget {
  final Car car;

  const CarDetailScreen({
    super.key,
    required this.car,
  });

  @override
  State<CarDetailScreen> createState() => _CarDetailScreenState();
}

class _CarDetailScreenState extends State<CarDetailScreen>
    with TickerProviderStateMixin {
  late ScrollController _scrollController;
  late AnimationController _headerAnimationController;
  late Animation<double> _headerOpacity;

  String _selectedDuration = '1 day';
  bool _isFavorite = false;

  // Custom duration input state
  final TextEditingController _customDurationController =
      TextEditingController();
  bool _showCustomDuration = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _headerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _headerOpacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _headerAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _headerAnimationController.dispose();
    _customDurationController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final offset = _scrollController.offset;
    final opacity = (offset / 200).clamp(0.0, 1.0);

    if (opacity > 0.5 && !_headerAnimationController.isCompleted) {
      _headerAnimationController.forward();
    } else if (opacity <= 0.5 && _headerAnimationController.isCompleted) {
      _headerAnimationController.reverse();
    }
  }

  void _handleBackPress() {
    Navigator.pop(context);
  }

  void _handleBookNow() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CarBookingScreen(
          car: widget.car,
          selectedDuration: _selectedDuration,
        ),
      ),
    );
  }

  void _handleCustomDurationAdd() {
    final customDaysText = _customDurationController.text.trim();
    final customDays = int.tryParse(customDaysText);

    if (customDays != null && customDays > 0 && customDays <= 365) {
      setState(() {
        _selectedDuration = 'custom:$customDays';
        _customDurationController.clear();
        _showCustomDuration = false;
      });

      // Show success feedback
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Rental Duration: $customDays ${customDays == 1 ? 'day' : 'days'}'),
          duration: const Duration(seconds: 2),
          backgroundColor: AppTheme
              .accentColor, // Updated to match car booking design system
        ),
      );
    } else {
      // Show error feedback
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid number of days (1-365)'),
          duration: Duration(seconds: 2),
          backgroundColor: AppTheme.errorColor,
        ),
      );
    }
  }

  void _handleShare() {
    // TODO: Implement share functionality
    debugPrint('Share car: ${widget.car.name}');
  }

  void _handleFavorite() {
    setState(() {
      _isFavorite = !_isFavorite;
    });
  }

  double _calculateTotalPrice() {
    final multiplier = _getDaysFromSelectedDuration();
    return widget.car.pricePerDay * multiplier;
  }

  int _getDaysFromSelectedDuration() {
    // Handle custom duration
    if (_selectedDuration.startsWith('custom:')) {
      final customDays = _selectedDuration.split(':')[1];
      return int.tryParse(customDays) ?? 1;
    }
    // Handle preset durations
    return BookingDuration.getDaysFromDuration(_selectedDuration);
  }

  String _getDisplayDuration() {
    // Handle custom duration
    if (_selectedDuration.startsWith('custom:')) {
      final customDays = _selectedDuration.split(':')[1];
      final days = int.tryParse(customDays) ?? 1;
      return '$days ${days == 1 ? 'day' : 'days'}';
    }
    // Handle preset durations
    return _selectedDuration;
  }

  Widget _buildSpecCard({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacingLg),
        decoration: BoxDecoration(
          color: AppTheme.backgroundSecondary,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppTheme.accentColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                icon,
                size: 20,
                color: AppTheme.accentColor,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSm),
            Text(
              label.toUpperCase(),
              style: const TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: AppTheme.textSecondaryColor,
                letterSpacing: 0.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingXs),
            Text(
              value,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimaryColor,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDurationCard(String duration) {
    final isSelected = _selectedDuration == duration;
    final multiplier = BookingDuration.getDaysFromDuration(duration);
    final totalPrice = widget.car.pricePerDay * multiplier;

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedDuration = duration;
          });
        },
        child: Container(
          padding: const EdgeInsets.all(AppTheme.spacingLg),
          decoration: BoxDecoration(
            color: isSelected
                ? AppTheme.accentColor.withOpacity(0.1)
                : AppTheme.backgroundSecondary,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isSelected ? AppTheme.accentColor : Colors.transparent,
              width: 2,
            ),
          ),
          child: Column(
            children: [
              Text(
                duration,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: isSelected
                      ? AppTheme.accentColor
                      : AppTheme.textPrimaryColor,
                ),
              ),
              const SizedBox(height: AppTheme.spacingXs),
              Text(
                '\$${totalPrice.toInt()}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: isSelected
                      ? AppTheme.accentColor
                      : AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomDurationInput() {
    return Container(
      margin: const EdgeInsets.only(top: AppTheme.spacingMd),
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        color: AppTheme.backgroundSecondary,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.edit_calendar,
                size: 20,
                color: AppTheme
                    .accentColor, // Updated to match car booking design system
              ),
              const SizedBox(width: AppTheme.spacingSm),
              const Text(
                'Custom Duration',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  setState(() {
                    _showCustomDuration = !_showCustomDuration;
                    if (!_showCustomDuration) {
                      _customDurationController.clear();
                    }
                  });
                },
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: AppTheme.accentColor.withValues(
                        alpha:
                            0.1), // Updated to match car booking design system
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _showCustomDuration
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    size: 20,
                    color: AppTheme
                        .accentColor, // Updated to match car booking design system
                  ),
                ),
              ),
            ],
          ),
          if (_showCustomDuration) ...[
            const SizedBox(height: AppTheme.spacingMd),
            const Text(
              'Enter number of days (1-365)',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSm),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _customDurationController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: 'e.g., 5',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide:
                            const BorderSide(color: AppTheme.borderLight),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(
                            color: AppTheme.accentColor,
                            width:
                                2), // Updated to match car booking design system
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingMd,
                        vertical: AppTheme.spacingSm,
                      ),
                    ),
                    onSubmitted: (_) => _handleCustomDurationAdd(),
                  ),
                ),
                const SizedBox(width: AppTheme.spacingMd),
                Container(
                  height: 48,
                  child: ElevatedButton(
                    onPressed: _handleCustomDurationAdd,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme
                          .accentColor, // Updated to match car booking design system
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: AppTheme.spacingLg),
                    ),
                    child: const Text(
                      'Add',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Stack(
        children: [
          // Main content
          CustomScrollView(
            controller: _scrollController,
            slivers: [
              // Hero section
              SliverToBoxAdapter(
                child: Container(
                  height: screenHeight * 0.5,
                  decoration: const BoxDecoration(
                    color: Color(0xFFF8F9FA),
                  ),
                  child: Stack(
                    children: [
                      // Car image
                      Positioned.fill(
                        child: CachedNetworkImage(
                          imageUrl: widget.car.imageUrl,
                          fit: BoxFit.contain,
                          placeholder: (context, url) => const Center(
                            child: CircularProgressIndicator(),
                          ),
                          errorWidget: (context, url, error) => const Center(
                            child: Icon(
                              Icons.directions_car,
                              size: 64,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ),
                      ),
                      // Floating header
                      Positioned(
                        top: MediaQuery.of(context).padding.top +
                            AppTheme.spacingLg,
                        left: AppTheme.spacingLg,
                        right: AppTheme.spacingLg,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            GestureDetector(
                              onTap: _handleBackPress,
                              child: Container(
                                width: 44,
                                height: 44,
                                decoration: BoxDecoration(
                                  color: Colors.black.withOpacity(0.3),
                                  borderRadius: BorderRadius.circular(22),
                                ),
                                child: const Icon(
                                  Icons.arrow_back,
                                  color: AppTheme.white,
                                  size: 24,
                                ),
                              ),
                            ),
                            Row(
                              children: [
                                GestureDetector(
                                  onTap: _handleShare,
                                  child: Container(
                                    width: 44,
                                    height: 44,
                                    decoration: BoxDecoration(
                                      color: Colors.black.withOpacity(0.3),
                                      borderRadius: BorderRadius.circular(22),
                                    ),
                                    child: const Icon(
                                      Icons.share,
                                      color: AppTheme.white,
                                      size: 20,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: AppTheme.spacingSm),
                                GestureDetector(
                                  onTap: _handleFavorite,
                                  child: Container(
                                    width: 44,
                                    height: 44,
                                    decoration: BoxDecoration(
                                      color: Colors.black.withOpacity(0.3),
                                      borderRadius: BorderRadius.circular(22),
                                    ),
                                    child: Icon(
                                      _isFavorite
                                          ? Icons.favorite
                                          : Icons.favorite_border,
                                      color: AppTheme.white,
                                      size: 20,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      // Rating badge
                      Positioned(
                        bottom: AppTheme.spacingXl,
                        right: AppTheme.spacingLg,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppTheme.spacingMd,
                            vertical: AppTheme.spacingSm,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.shadowColor,
                                offset: const Offset(0, 4),
                                blurRadius: 12,
                                spreadRadius: 0,
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.star,
                                size: 16,
                                color: AppTheme.warningColor,
                              ),
                              const SizedBox(width: AppTheme.spacingXs),
                              Text(
                                widget.car.rating.toString(),
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.textPrimaryColor,
                                ),
                              ),
                              Text(
                                ' (${widget.car.reviewCount})',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: AppTheme.textSecondaryColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Content container
              SliverToBoxAdapter(
                child: Container(
                  decoration: const BoxDecoration(
                    color: AppTheme.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                  ),
                  transform: Matrix4.translationValues(0, -24, 0),
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(
                      AppTheme.spacingLg,
                      AppTheme.spacingXl,
                      AppTheme.spacingLg,
                      120, // Bottom padding for floating button
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Car header
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    widget.car.name,
                                    style: const TextStyle(
                                      fontSize: 28,
                                      fontWeight: FontWeight.bold,
                                      color: AppTheme.textPrimaryColor,
                                      height: 34 / 28,
                                    ),
                                  ),
                                  const SizedBox(height: AppTheme.spacingSm),
                                  Row(
                                    children: [
                                      Text(
                                        widget.car.category.toUpperCase(),
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                          color: AppTheme.accentColor,
                                          letterSpacing: 0.5,
                                        ),
                                      ),
                                      const SizedBox(width: AppTheme.spacingMd),
                                      Row(
                                        children: [
                                          Container(
                                            width: 8,
                                            height: 8,
                                            decoration: const BoxDecoration(
                                              color: AppTheme.successColor,
                                              shape: BoxShape.circle,
                                            ),
                                          ),
                                          const SizedBox(
                                              width: AppTheme.spacingXs),
                                          const Text(
                                            'Available',
                                            style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                              color: AppTheme.successColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(
                                  '\$${widget.car.pricePerDay.toInt()}',
                                  style: const TextStyle(
                                    fontSize: 32,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.textPrimaryColor,
                                    height: 38 / 32,
                                  ),
                                ),
                                const Text(
                                  'per day',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: AppTheme.textSecondaryColor,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: AppTheme.spacingXl),
                        // Quick specs
                        Column(
                          children: [
                            Row(
                              children: [
                                _buildSpecCard(
                                  icon: Icons.people,
                                  label: 'Seats',
                                  value: widget.car.seats.toString(),
                                ),
                                const SizedBox(width: AppTheme.spacingMd),
                                _buildSpecCard(
                                  icon: Icons.local_gas_station,
                                  label: 'Fuel',
                                  value: widget.car.fuelType,
                                ),
                              ],
                            ),
                            const SizedBox(height: AppTheme.spacingMd),
                            Row(
                              children: [
                                _buildSpecCard(
                                  icon: Icons.settings,
                                  label: 'Transmission',
                                  value: widget.car.transmission,
                                ),
                                const SizedBox(width: AppTheme.spacingMd),
                                _buildSpecCard(
                                  icon: Icons.ac_unit,
                                  label: 'AC',
                                  value: 'Available',
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: AppTheme.spacingXl),
                        // Location section
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(
                                  Icons.location_on,
                                  size: 20,
                                  color: AppTheme.accentColor,
                                ),
                                const SizedBox(width: AppTheme.spacingSm),
                                const Text(
                                  'Pickup Location',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                    color: AppTheme.textPrimaryColor,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: AppTheme.spacingMd),
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(AppTheme.spacingLg),
                              decoration: BoxDecoration(
                                color: AppTheme.backgroundSecondary,
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    widget.car.location,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: AppTheme.textPrimaryColor,
                                    ),
                                  ),
                                  const SizedBox(height: AppTheme.spacingXs),
                                  const Text(
                                    'Available 24/7 • Free pickup',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: AppTheme.textSecondaryColor,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: AppTheme.spacingXl),
                        // Description
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'About this car',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: AppTheme.textPrimaryColor,
                              ),
                            ),
                            const SizedBox(height: AppTheme.spacingSm),
                            Text(
                              widget.car.description,
                              style: const TextStyle(
                                fontSize: 16,
                                color: AppTheme.textSecondaryColor,
                                height: 1.5,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: AppTheme.spacingXl),
                        // Features
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Features & Amenities',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: AppTheme.textPrimaryColor,
                              ),
                            ),
                            const SizedBox(height: AppTheme.spacingSm),
                            Wrap(
                              spacing: AppTheme.spacingSm,
                              runSpacing: AppTheme.spacingSm,
                              children: widget.car.features.map((feature) {
                                return Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: AppTheme.spacingMd,
                                    vertical: AppTheme.spacingSm,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppTheme.backgroundSecondary,
                                    borderRadius: BorderRadius.circular(20),
                                    border: Border.all(
                                      color: AppTheme.borderLight,
                                      width: 1,
                                    ),
                                  ),
                                  child: Text(
                                    feature,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: AppTheme.textPrimaryColor,
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                          ],
                        ),
                        const SizedBox(height: AppTheme.spacingXl),
                        // Duration selection
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Select Rental Duration',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: AppTheme.textPrimaryColor,
                              ),
                            ),
                            const SizedBox(height: AppTheme.spacingSm),
                            Column(
                              children: [
                                Row(
                                  children: [
                                    _buildDurationCard('1 day'),
                                    const SizedBox(width: AppTheme.spacingMd),
                                    _buildDurationCard('3 days'),
                                  ],
                                ),
                                const SizedBox(height: AppTheme.spacingMd),
                                Row(
                                  children: [
                                    _buildDurationCard('1 week'),
                                    const SizedBox(width: AppTheme.spacingMd),
                                    _buildDurationCard('1 month'),
                                  ],
                                ),
                              ],
                            ),
                            // Custom duration input
                            _buildCustomDurationInput(),
                            // Additional spacing to prevent overlap with bottom section
                            const SizedBox(height: AppTheme.spacingXxl),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          // Animated header
          AnimatedBuilder(
            animation: _headerOpacity,
            builder: (context, child) {
              return Opacity(
                opacity: _headerOpacity.value,
                child: Container(
                  padding: EdgeInsets.only(
                    top: MediaQuery.of(context).padding.top,
                  ),
                  decoration: const BoxDecoration(
                    color: AppTheme.white,
                    border: Border(
                      bottom: BorderSide(
                        color: AppTheme.borderLight,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacingLg,
                      vertical: AppTheme.spacingMd,
                    ),
                    child: Row(
                      children: [
                        GestureDetector(
                          onTap: _handleBackPress,
                          child: Container(
                            width: 44,
                            height: 44,
                            decoration: BoxDecoration(
                              color: AppTheme.backgroundSecondary,
                              borderRadius: BorderRadius.circular(22),
                            ),
                            child: const Icon(
                              Icons.arrow_back,
                              color: AppTheme.textPrimaryColor,
                              size: 24,
                            ),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            widget.car.name,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.textPrimaryColor,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Row(
                          children: [
                            GestureDetector(
                              onTap: _handleShare,
                              child: Container(
                                width: 44,
                                height: 44,
                                decoration: BoxDecoration(
                                  color: AppTheme.backgroundSecondary,
                                  borderRadius: BorderRadius.circular(22),
                                ),
                                child: const Icon(
                                  Icons.share,
                                  color: AppTheme.textPrimaryColor,
                                  size: 20,
                                ),
                              ),
                            ),
                            const SizedBox(width: AppTheme.spacingSm),
                            GestureDetector(
                              onTap: _handleFavorite,
                              child: Container(
                                width: 44,
                                height: 44,
                                decoration: BoxDecoration(
                                  color: AppTheme.backgroundSecondary,
                                  borderRadius: BorderRadius.circular(22),
                                ),
                                child: Icon(
                                  _isFavorite
                                      ? Icons.favorite
                                      : Icons.favorite_border,
                                  color: _isFavorite
                                      ? AppTheme.errorColor
                                      : AppTheme.textPrimaryColor,
                                  size: 20,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
          // Bottom bar
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: EdgeInsets.fromLTRB(
                AppTheme.spacingLg,
                AppTheme.spacingMd, // Reduced from spacingLg to spacingMd
                AppTheme.spacingLg,
                AppTheme.spacingMd +
                    MediaQuery.of(context)
                        .padding
                        .bottom, // Reduced from spacingLg to spacingMd
              ),
              decoration: const BoxDecoration(
                color: AppTheme.white,
                border: Border(
                  top: BorderSide(
                    color: AppTheme.borderLight,
                    width: 1,
                  ),
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.shadowColor,
                    offset: Offset(0, -4),
                    blurRadius: 12,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Text(
                          'Total',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                        Text(
                          '\$${_calculateTotalPrice().toInt()}',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimaryColor,
                            height: 1.2,
                          ),
                        ),
                        Text(
                          'for ${_getDisplayDuration()}',
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacingLg),
                  SizedBox(
                    width: 140,
                    child: AppButton(
                      text: 'Book Now',
                      onPressed: _handleBookNow,
                      variant: ButtonVariant.filled,
                      color: AppTheme
                          .accentColor, // Updated to match car booking design system
                      height: 48,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
