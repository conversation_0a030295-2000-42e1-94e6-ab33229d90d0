import 'package:flutter/material.dart';
import 'package:culture_connect/theme/kaia_design_tokens.dart';
import 'package:culture_connect/models/guide.dart';
import 'package:culture_connect/utils/price_formatter.dart';

/// Company guide card component for the guides listing screen
/// Follows CultureConnect design system with premium aesthetics
class CompanyGuideCard extends StatelessWidget {
  final Guide guide;
  final VoidCallback onPressed;

  const CompanyGuideCard({
    super.key,
    required this.guide,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: KaiaDesignTokens.neutralWhite,
          borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
          boxShadow: KaiaDesignTokens.shadowMd,
          border: Border.all(
            color: KaiaDesignTokens.primaryIndigo.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Company Header Section
            Container(
              padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    KaiaDesignTokens.primaryIndigo.withOpacity(0.05),
                    KaiaDesignTokens.secondaryCyan.withOpacity(0.05),
                  ],
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(KaiaDesignTokens.radiusLg),
                  topRight: Radius.circular(KaiaDesignTokens.radiusLg),
                ),
              ),
              child: Row(
                children: [
                  // Company Logo
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      borderRadius:
                          BorderRadius.circular(KaiaDesignTokens.radiusMd),
                      border: Border.all(
                        color: KaiaDesignTokens.neutralGray200,
                        width: 2,
                      ),
                      boxShadow: KaiaDesignTokens.shadowSm,
                    ),
                    child: ClipRRect(
                      borderRadius:
                          BorderRadius.circular(KaiaDesignTokens.radiusMd),
                      child: guide.companyLogoUrl != null
                          ? Image.network(
                              guide.companyLogoUrl!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  color: KaiaDesignTokens.neutralGray100,
                                  child: Icon(
                                    Icons.business,
                                    color: KaiaDesignTokens.neutralGray400,
                                    size: 30,
                                  ),
                                );
                              },
                            )
                          : Container(
                              color: KaiaDesignTokens.neutralGray100,
                              child: Icon(
                                Icons.business,
                                color: KaiaDesignTokens.neutralGray400,
                                size: 30,
                              ),
                            ),
                    ),
                  ),

                  const SizedBox(width: KaiaDesignTokens.spacing12),

                  // Company Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Flexible(
                              child: Text(
                                guide.name,
                                style: TextStyle(
                                  color: KaiaDesignTokens.neutralGray800,
                                  fontSize: KaiaDesignTokens.fontSizeLg,
                                  fontWeight: KaiaDesignTokens.fontWeightBold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: KaiaDesignTokens.spacing6),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: KaiaDesignTokens.spacing6,
                                vertical: KaiaDesignTokens.spacing2,
                              ),
                              decoration: BoxDecoration(
                                color: KaiaDesignTokens.accentEmerald,
                                borderRadius: BorderRadius.circular(
                                    KaiaDesignTokens.radiusXs),
                              ),
                              child: Text(
                                'COMPANY',
                                style: TextStyle(
                                  color: KaiaDesignTokens.neutralWhite,
                                  fontSize: KaiaDesignTokens.fontSizeXs,
                                  fontWeight: KaiaDesignTokens.fontWeightBold,
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: KaiaDesignTokens.spacing4),

                        if (guide.location != null)
                          Row(
                            children: [
                              Icon(
                                Icons.location_on_outlined,
                                color: KaiaDesignTokens.neutralGray500,
                                size: 14,
                              ),
                              const SizedBox(width: KaiaDesignTokens.spacing4),
                              Flexible(
                                child: Text(
                                  guide.location!,
                                  style: TextStyle(
                                    color: KaiaDesignTokens.neutralGray500,
                                    fontSize: KaiaDesignTokens.fontSizeSm,
                                    fontWeight:
                                        KaiaDesignTokens.fontWeightRegular,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),

                        const SizedBox(height: KaiaDesignTokens.spacing4),

                        // Rating and Team Size
                        Row(
                          children: [
                            if (guide.rating != null) ...[
                              Icon(
                                Icons.star,
                                color: KaiaDesignTokens.warningAmber,
                                size: 16,
                              ),
                              const SizedBox(width: KaiaDesignTokens.spacing4),
                              Text(
                                guide.rating!.toStringAsFixed(1),
                                style: TextStyle(
                                  color: KaiaDesignTokens.neutralGray700,
                                  fontSize: KaiaDesignTokens.fontSizeSm,
                                  fontWeight: KaiaDesignTokens.fontWeightMedium,
                                ),
                              ),
                              if (guide.reviewCount != null) ...[
                                const SizedBox(
                                    width: KaiaDesignTokens.spacing4),
                                Text(
                                  '(${guide.reviewCount})',
                                  style: TextStyle(
                                    color: KaiaDesignTokens.neutralGray500,
                                    fontSize: KaiaDesignTokens.fontSizeSm,
                                    fontWeight:
                                        KaiaDesignTokens.fontWeightRegular,
                                  ),
                                ),
                              ],
                              const SizedBox(width: KaiaDesignTokens.spacing12),
                            ],
                            if (guide.teamSize != null)
                              Row(
                                children: [
                                  Icon(
                                    Icons.group_outlined,
                                    color: KaiaDesignTokens.neutralGray500,
                                    size: 14,
                                  ),
                                  const SizedBox(
                                      width: KaiaDesignTokens.spacing4),
                                  Text(
                                    '${guide.teamSize} team',
                                    style: TextStyle(
                                      color: KaiaDesignTokens.neutralGray500,
                                      fontSize: KaiaDesignTokens.fontSizeSm,
                                      fontWeight:
                                          KaiaDesignTokens.fontWeightRegular,
                                    ),
                                  ),
                                ],
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Price Range
                  if (guide.priceRange.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: KaiaDesignTokens.spacing8,
                        vertical: KaiaDesignTokens.spacing4,
                      ),
                      decoration: BoxDecoration(
                        color: KaiaDesignTokens.accentEmerald.withOpacity(0.1),
                        borderRadius:
                            BorderRadius.circular(KaiaDesignTokens.radiusSm),
                      ),
                      child: Text(
                        PriceFormatter.formatPriceWithLocation(
                            guide.priceRange, guide.location ?? ''),
                        style: TextStyle(
                          color: KaiaDesignTokens.accentEmerald,
                          fontSize: KaiaDesignTokens.fontSizeXs,
                          fontWeight: KaiaDesignTokens.fontWeightBold,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Content Section
            Padding(
              padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Service Highlights
                  if (guide.serviceHighlights.isNotEmpty) ...[
                    Row(
                      children: [
                        Icon(
                          Icons.verified_outlined,
                          color: KaiaDesignTokens.accentEmerald,
                          size: 16,
                        ),
                        const SizedBox(width: KaiaDesignTokens.spacing6),
                        Text(
                          'Services:',
                          style: TextStyle(
                            color: KaiaDesignTokens.neutralGray700,
                            fontSize: KaiaDesignTokens.fontSizeSm,
                            fontWeight: KaiaDesignTokens.fontWeightMedium,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: KaiaDesignTokens.spacing8),
                    Wrap(
                      spacing: KaiaDesignTokens.spacing8,
                      runSpacing: KaiaDesignTokens.spacing6,
                      children: guide.serviceHighlights.take(4).map((service) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: KaiaDesignTokens.spacing8,
                            vertical: KaiaDesignTokens.spacing4,
                          ),
                          decoration: BoxDecoration(
                            color:
                                KaiaDesignTokens.primaryIndigo.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(
                                KaiaDesignTokens.radiusSm),
                          ),
                          child: Text(
                            service,
                            style: TextStyle(
                              color: KaiaDesignTokens.primaryIndigo,
                              fontSize: KaiaDesignTokens.fontSizeXs,
                              fontWeight: KaiaDesignTokens.fontWeightMedium,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: KaiaDesignTokens.spacing12),
                  ],

                  // Specialties
                  if (guide.specialties.isNotEmpty) ...[
                    Text(
                      'Specialties:',
                      style: TextStyle(
                        color: KaiaDesignTokens.neutralGray700,
                        fontSize: KaiaDesignTokens.fontSizeSm,
                        fontWeight: KaiaDesignTokens.fontWeightMedium,
                      ),
                    ),
                    const SizedBox(height: KaiaDesignTokens.spacing6),
                    Wrap(
                      spacing: KaiaDesignTokens.spacing8,
                      runSpacing: KaiaDesignTokens.spacing6,
                      children: guide.specialties.take(3).map((specialty) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: KaiaDesignTokens.spacing8,
                            vertical: KaiaDesignTokens.spacing4,
                          ),
                          decoration: BoxDecoration(
                            color: KaiaDesignTokens.neutralGray100,
                            borderRadius: BorderRadius.circular(
                                KaiaDesignTokens.radiusSm),
                          ),
                          child: Text(
                            specialty,
                            style: TextStyle(
                              color: KaiaDesignTokens.neutralGray600,
                              fontSize: KaiaDesignTokens.fontSizeXs,
                              fontWeight: KaiaDesignTokens.fontWeightMedium,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: KaiaDesignTokens.spacing12),
                  ],

                  // Bottom Info Row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      if (guide.responseTime.isNotEmpty)
                        Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              color: KaiaDesignTokens.neutralGray500,
                              size: 14,
                            ),
                            const SizedBox(width: KaiaDesignTokens.spacing4),
                            Text(
                              guide.responseTime,
                              style: TextStyle(
                                color: KaiaDesignTokens.neutralGray600,
                                fontSize: KaiaDesignTokens.fontSizeXs,
                                fontWeight: KaiaDesignTokens.fontWeightRegular,
                              ),
                            ),
                          ],
                        ),
                      if (guide.isCertified)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: KaiaDesignTokens.spacing6,
                            vertical: KaiaDesignTokens.spacing2,
                          ),
                          decoration: BoxDecoration(
                            color: KaiaDesignTokens.successGreen,
                            borderRadius: BorderRadius.circular(
                                KaiaDesignTokens.radiusXs),
                          ),
                          child: Text(
                            'CERTIFIED',
                            style: TextStyle(
                              color: KaiaDesignTokens.neutralWhite,
                              fontSize: KaiaDesignTokens.fontSizeXs,
                              fontWeight: KaiaDesignTokens.fontWeightBold,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
