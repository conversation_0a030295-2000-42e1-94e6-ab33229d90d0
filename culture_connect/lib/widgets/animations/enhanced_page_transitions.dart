import 'dart:math' show sin, pi;
import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Enhanced page transition animations using modern design system
class EnhancedPageTransitions {
  /// Slide transition from right to left
  static PageRouteBuilder<T> slideFromRight<T>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: AppTheme.animationNormal,
      reverseTransitionDuration: AppTheme.animationNormal,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        final tween = Tween(begin: begin, end: end);
        final offsetAnimation = animation.drive(tween.chain(
          CurveTween(curve: AppTheme.curveEmphasized),
        ));

        return SlideTransition(
          position: offsetAnimation,
          child: child,
        );
      },
    );
  }

  /// Slide transition from bottom to top
  static PageRouteBuilder<T> slideFromBottom<T>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: AppTheme.animationNormal,
      reverseTransitionDuration: AppTheme.animationNormal,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        final tween = Tween(begin: begin, end: end);
        final offsetAnimation = animation.drive(tween.chain(
          CurveTween(curve: AppTheme.curveEmphasized),
        ));

        return SlideTransition(
          position: offsetAnimation,
          child: child,
        );
      },
    );
  }

  /// Fade transition with scale effect
  static PageRouteBuilder<T> fadeWithScale<T>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: AppTheme.animationNormal,
      reverseTransitionDuration: AppTheme.animationNormal,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        final fadeAnimation = animation.drive(
          CurveTween(curve: AppTheme.curveDefault),
        );
        final scaleAnimation = animation.drive(
          Tween(begin: 0.95, end: 1.0).chain(
            CurveTween(curve: AppTheme.curveEmphasized),
          ),
        );

        return FadeTransition(
          opacity: fadeAnimation,
          child: ScaleTransition(
            scale: scaleAnimation,
            child: child,
          ),
        );
      },
    );
  }

  /// Modern hero transition with enhanced animations
  static PageRouteBuilder<T> heroTransition<T>(
    Widget page, {
    String? heroTag,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: AppTheme.animationSlow,
      reverseTransitionDuration: AppTheme.animationSlow,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        final fadeAnimation = animation.drive(
          Tween(begin: 0.0, end: 1.0).chain(
            CurveTween(curve: const Interval(0.3, 1.0, curve: Curves.easeIn)),
          ),
        );

        final slideAnimation = animation.drive(
          Tween(begin: const Offset(0.0, 0.1), end: Offset.zero).chain(
            CurveTween(curve: AppTheme.curveEmphasized),
          ),
        );

        return SlideTransition(
          position: slideAnimation,
          child: FadeTransition(
            opacity: fadeAnimation,
            child: child,
          ),
        );
      },
    );
  }

  /// Shared axis transition (Material Design 3)
  static PageRouteBuilder<T> sharedAxisTransition<T>(
    Widget page, {
    SharedAxisTransitionType transitionType =
        SharedAxisTransitionType.horizontal,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: AppTheme.animationNormal,
      reverseTransitionDuration: AppTheme.animationNormal,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return _buildSharedAxisTransition(
          animation,
          secondaryAnimation,
          child,
          transitionType,
        );
      },
    );
  }

  static Widget _buildSharedAxisTransition(
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
    SharedAxisTransitionType transitionType,
  ) {
    final primaryAnimation = animation.drive(
      CurveTween(curve: AppTheme.curveEmphasized),
    );
    final secondaryAnimationCurved = secondaryAnimation.drive(
      CurveTween(curve: AppTheme.curveEmphasized),
    );

    switch (transitionType) {
      case SharedAxisTransitionType.horizontal:
        return SlideTransition(
          position: primaryAnimation.drive(
            Tween(begin: const Offset(0.3, 0.0), end: Offset.zero),
          ),
          child: SlideTransition(
            position: secondaryAnimationCurved.drive(
              Tween(begin: Offset.zero, end: const Offset(-0.3, 0.0)),
            ),
            child: FadeTransition(
              opacity: primaryAnimation,
              child: child,
            ),
          ),
        );
      case SharedAxisTransitionType.vertical:
        return SlideTransition(
          position: primaryAnimation.drive(
            Tween(begin: const Offset(0.0, 0.3), end: Offset.zero),
          ),
          child: SlideTransition(
            position: secondaryAnimationCurved.drive(
              Tween(begin: Offset.zero, end: const Offset(0.0, -0.3)),
            ),
            child: FadeTransition(
              opacity: primaryAnimation,
              child: child,
            ),
          ),
        );
      case SharedAxisTransitionType.scaled:
        return ScaleTransition(
          scale: primaryAnimation.drive(
            Tween(begin: 0.8, end: 1.0),
          ),
          child: ScaleTransition(
            scale: secondaryAnimationCurved.drive(
              Tween(begin: 1.0, end: 1.1),
            ),
            child: FadeTransition(
              opacity: primaryAnimation,
              child: child,
            ),
          ),
        );
    }
  }
}

enum SharedAxisTransitionType {
  horizontal,
  vertical,
  scaled,
}

/// Enhanced loading animation widget
class EnhancedLoadingAnimation extends StatefulWidget {
  final Color? color;
  final double size;
  final LoadingAnimationType type;

  const EnhancedLoadingAnimation({
    super.key,
    this.color,
    this.size = 24.0,
    this.type = LoadingAnimationType.circular,
  });

  @override
  State<EnhancedLoadingAnimation> createState() =>
      _EnhancedLoadingAnimationState();
}

class _EnhancedLoadingAnimationState extends State<EnhancedLoadingAnimation>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: AppTheme.animationSlower,
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: AppTheme.curveDefault,
    ));
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final color = widget.color ?? AppTheme.primaryColor;

    switch (widget.type) {
      case LoadingAnimationType.circular:
        return SizedBox(
          width: widget.size,
          height: widget.size,
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(color),
            strokeWidth: 2.0,
          ),
        );
      case LoadingAnimationType.dots:
        return AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: List.generate(3, (index) {
                final delay = index * 0.2;
                final animationValue =
                    (_animation.value - delay).clamp(0.0, 1.0);
                final scale =
                    (sin(animationValue * pi) * 0.5 + 0.5).clamp(0.3, 1.0);

                return Container(
                  margin: EdgeInsets.symmetric(horizontal: widget.size * 0.1),
                  child: Transform.scale(
                    scale: scale,
                    child: Container(
                      width: widget.size * 0.2,
                      height: widget.size * 0.2,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                );
              }),
            );
          },
        );
      case LoadingAnimationType.pulse:
        return AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            final scale =
                (sin(_animation.value * pi * 2) * 0.2 + 0.8).clamp(0.6, 1.0);
            final opacity =
                (sin(_animation.value * pi * 2) * 0.3 + 0.7).clamp(0.4, 1.0);

            return Transform.scale(
              scale: scale,
              child: Opacity(
                opacity: opacity,
                child: Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            );
          },
        );
    }
  }
}

enum LoadingAnimationType {
  circular,
  dots,
  pulse,
}
