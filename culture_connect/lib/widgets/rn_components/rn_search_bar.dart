import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Pixel-perfect React Native SearchBar replica
/// Matches the exact styling from React Native reference images
class RNSearchBar extends StatelessWidget {
  final String value;
  final ValueChanged<String> onChanged;
  final String placeholder;
  final VoidCallback? onFilterPressed;
  final VoidCallback? onClearPressed;

  const RNSearchBar({
    super.key,
    required this.value,
    required this.onChanged,
    this.placeholder = 'Search destination and explore',
    this.onFilterPressed,
    this.onClearPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(12), // Exact React Native radius
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor, // rgba(34, 34, 34, 0.12)
            offset: const Offset(0, 2),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          // Search Icon - Left
          const Padding(
            padding: EdgeInsets.only(left: 16, right: 8),
            child: Icon(
              Icons.search,
              size: 20,
              color: AppTheme.textSecondaryColor, // #B0B0B0
            ),
          ),
          
          // Text Input
          Expanded(
            child: TextField(
              onChanged: onChanged,
              style: const TextStyle(
                fontFamily: AppTheme.fontFamily,
                fontSize: AppTheme.fontSizeMd, // 16px
                color: AppTheme.textPrimaryColor, // #222222
                fontWeight: FontWeight.w400,
              ),
              decoration: InputDecoration(
                hintText: placeholder,
                hintStyle: const TextStyle(
                  fontFamily: AppTheme.fontFamily,
                  fontSize: AppTheme.fontSizeMd,
                  color: AppTheme.textSecondaryColor, // #B0B0B0
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
          
          // Clear Button (if text exists)
          if (value.isNotEmpty)
            GestureDetector(
              onTap: onClearPressed,
              child: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 8),
                child: Icon(
                  Icons.close,
                  size: 20,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ),
          
          // Filter Button - Right
          if (onFilterPressed != null)
            GestureDetector(
              onTap: onFilterPressed,
              child: Container(
                margin: const EdgeInsets.only(left: 8, right: 8),
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AppTheme.backgroundSecondary, // #F7F7F7
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.tune,
                  size: 20,
                  color: AppTheme.primaryColor, // #FF385C
                ),
              ),
            ),
        ],
      ),
    );
  }
}
