import 'package:culture_connect/models/premium_booking.dart';

/// Premium mock data with stunning visual content for the new booking interface
class PremiumMockData {
  static final List<PremiumBooking> _bookings = [
    // Luxury Hotel Booking
    PremiumBooking(
      id: '1',
      type: PremiumBookingType.hotel,
      title: 'Coral Bay Ocean Suite',
      subtitle: 'Luxury beachfront with private balcony',
      status: PremiumBookingStatus.confirmed,
      date: DateTime(2025, 9, 15),
      endDate: DateTime(2025, 9, 19),
      price: 1299.0,
      imageUrl:
          'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&q=80',
      imageGallery: [
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&q=80',
        'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800&q=80',
        'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&q=80',
      ],
      confirmationCode: 'HOTEL-89234',
      location: 'Maldives',
      country: 'Maldives',
      rating: 4.9,
      reviewCount: 1247,
      duration: '4 nights',
      participants: 2,
      highlights: [
        'Private beach access',
        'Infinity pool with ocean view',
        'World-class spa treatments',
        'Fine dining restaurants',
      ],
      amenities: ['WiFi', 'Pool', 'Spa', 'Restaurant', 'Beach Access'],
      isFavorite: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),

    // Adventure Tour
    PremiumBooking(
      id: '2',
      type: PremiumBookingType.tour,
      title: 'Guided Safari Adventure',
      subtitle: 'Witness the Great Migration in Serengeti',
      status: PremiumBookingStatus.upcoming,
      date: DateTime(2025, 8, 22),
      endDate: DateTime(2025, 8, 25),
      price: 2450.0,
      imageUrl:
          'https://images.unsplash.com/photo-1516426122078-c23e76319801?w=800&q=80',
      imageGallery: [
        'https://images.unsplash.com/photo-1516426122078-c23e76319801?w=800&q=80',
        'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=800&q=80',
        'https://images.unsplash.com/photo-1534177616072-ef7dc120449d?w=800&q=80',
      ],
      confirmationCode: 'SAFARI-45612',
      location: 'Serengeti National Park',
      country: 'Tanzania',
      rating: 4.8,
      reviewCount: 892,
      duration: '4 days',
      participants: 4,
      highlights: [
        'Big Five wildlife viewing',
        'Professional safari guide',
        'Luxury tented accommodation',
        'All meals included',
      ],
      amenities: ['Transportation', 'Guide', 'Meals', 'Accommodation'],
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),

    // Premium Flight
    PremiumBooking(
      id: '3',
      type: PremiumBookingType.flight,
      title: 'Business Class to Tokyo',
      subtitle: 'Direct flight with premium amenities',
      status: PremiumBookingStatus.confirmed,
      date: DateTime(2025, 8, 18),
      price: 3200.0,
      imageUrl:
          'https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=800&q=80',
      confirmationCode: 'FLIGHT-78901',
      location: 'JFK → NRT',
      country: 'Japan',
      rating: 4.6,
      reviewCount: 2341,
      duration: '14h 30m',
      participants: 1,
      highlights: [
        'Lie-flat business class seats',
        'Premium dining experience',
        'Priority boarding and check-in',
        'Extra baggage allowance',
      ],
      amenities: ['WiFi', 'Entertainment', 'Meals', 'Priority Service'],
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
    ),

    // Cultural Experience
    PremiumBooking(
      id: '4',
      type: PremiumBookingType.experience,
      title: 'Traditional Tea Ceremony',
      subtitle: 'Authentic Japanese cultural immersion',
      status: PremiumBookingStatus.completed,
      date: DateTime(2025, 7, 10),
      price: 180.0,
      imageUrl:
          'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=800&q=80',
      confirmationCode: 'EXP-23456',
      location: 'Kyoto',
      country: 'Japan',
      rating: 4.9,
      reviewCount: 567,
      duration: '3 hours',
      participants: 2,
      highlights: [
        'Master tea ceremony instructor',
        'Traditional kimono provided',
        'Historic tea house setting',
        'Seasonal wagashi sweets',
      ],
      amenities: ['Traditional Attire', 'Tea & Sweets', 'Cultural Guide'],
      createdAt: DateTime.now().subtract(const Duration(days: 45)),
      updatedAt: DateTime.now().subtract(const Duration(days: 15)),
    ),

    // Music Festival Event
    PremiumBooking(
      id: '5',
      type: PremiumBookingType.event,
      title: 'Coachella Music Festival',
      subtitle: 'VIP weekend pass with exclusive access',
      status: PremiumBookingStatus.pending,
      date: DateTime(2025, 4, 12),
      endDate: DateTime(2025, 4, 14),
      price: 899.0,
      imageUrl:
          'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&q=80',
      confirmationCode: 'EVENT-67890',
      location: 'Indio, California',
      country: 'United States',
      rating: 4.7,
      reviewCount: 3421,
      duration: '3 days',
      participants: 2,
      highlights: [
        'VIP viewing areas',
        'Exclusive artist meet & greets',
        'Premium food and beverage',
        'Dedicated VIP entrance',
      ],
      amenities: ['VIP Access', 'Food & Drinks', 'Parking', 'Merchandise'],
      createdAt: DateTime.now().subtract(const Duration(days: 60)),
      updatedAt: DateTime.now().subtract(const Duration(days: 5)),
    ),

    // Luxury Transport
    PremiumBooking(
      id: '6',
      type: PremiumBookingType.transport,
      title: 'Helicopter Transfer',
      subtitle: 'Scenic route to mountain resort',
      status: PremiumBookingStatus.cancelled,
      date: DateTime(2025, 6, 5),
      price: 1200.0,
      imageUrl:
          'https://images.unsplash.com/photo-1544620347-c4fd4a3d5957?w=800&q=80',
      confirmationCode: 'HELI-34567',
      location: 'Swiss Alps',
      country: 'Switzerland',
      rating: 4.8,
      reviewCount: 234,
      duration: '45 minutes',
      participants: 3,
      highlights: [
        'Breathtaking alpine views',
        'Professional pilot guide',
        'Luxury helicopter interior',
        'Champagne service',
      ],
      amenities: ['Scenic Views', 'Professional Pilot', 'Refreshments'],
      createdAt: DateTime.now().subtract(const Duration(days: 40)),
      updatedAt: DateTime.now().subtract(const Duration(days: 10)),
    ),

    // Cooking Experience
    PremiumBooking(
      id: '7',
      type: PremiumBookingType.experience,
      title: 'Michelin Cooking Class',
      subtitle: 'Learn from world-renowned chef',
      status: PremiumBookingStatus.upcoming,
      date: DateTime(2025, 9, 8),
      price: 450.0,
      imageUrl:
          'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&q=80',
      confirmationCode: 'COOK-78123',
      location: 'Paris',
      country: 'France',
      rating: 4.9,
      reviewCount: 445,
      duration: '5 hours',
      participants: 1,
      highlights: [
        'Michelin-starred chef instruction',
        'Premium ingredients included',
        'Wine pairing session',
        'Recipe collection to take home',
      ],
      amenities: ['Professional Kitchen', 'Ingredients', 'Wine', 'Recipes'],
      isFavorite: true,
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),

    // Luxury Resort
    PremiumBooking(
      id: '8',
      type: PremiumBookingType.hotel,
      title: 'Overwater Villa Paradise',
      subtitle: 'Private villa with glass floor panels',
      status: PremiumBookingStatus.confirmed,
      date: DateTime(2025, 10, 20),
      endDate: DateTime(2025, 10, 27),
      price: 4200.0,
      imageUrl:
          'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&q=80',
      confirmationCode: 'VILLA-90123',
      location: 'Bora Bora',
      country: 'French Polynesia',
      rating: 5.0,
      reviewCount: 789,
      duration: '7 nights',
      participants: 2,
      highlights: [
        'Private overwater villa',
        'Glass floor marine viewing',
        'Personal butler service',
        'Sunset dinner on deck',
      ],
      amenities: [
        'Butler Service',
        'Private Deck',
        'Snorkeling Gear',
        'Kayaks'
      ],
      isFavorite: true,
      createdAt: DateTime.now().subtract(const Duration(days: 35)),
      updatedAt: DateTime.now(),
    ),

    // ============================================================================
    // COMPREHENSIVE EXPERIENCE TYPE DEMONSTRATIONS
    // ============================================================================

    // Musical Festival Experience - Comprehensive Example
    PremiumBooking(
      id: '9',
      type: PremiumBookingType.experience,
      title: 'Synthwave Festival 2025',
      subtitle: 'Three-day electronic music experience',
      status: PremiumBookingStatus.confirmed,
      date: DateTime(2025, 8, 15),
      endDate: DateTime(2025, 8, 17),
      price: 299.0,
      imageUrl:
          'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&q=80',
      imageGallery: [
        'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&q=80',
        'https://images.unsplash.com/photo-1514525253161-7a46d19cd819?w=800&q=80',
        'https://images.unsplash.com/photo-1459749411175-04bf5292ceea?w=800&q=80',
      ],
      confirmationCode: 'SYNTH-2025',
      location: 'Red Rocks Amphitheatre, Colorado',
      country: 'United States',
      rating: 4.8,
      reviewCount: 1247,
      duration: '3 days',
      participants: 2,
      details: {
        'venue': 'Red Rocks Amphitheatre',
        'capacity': '9,525 people',
        'parking': 'Available on-site (\$25)',
        'accessibility': 'ADA compliant seating',
        'artists': ['The Midnight', 'Electric Youth', 'FM-84', 'Gunship'],
        'stages': ['Main Stage', 'Side Stage'],
        'schedule': {
          'day1':
              'Gates open 6:00 PM, Electric Youth 6:30 PM, The Midnight 8:00 PM',
          'day2': 'FM-84 7:00 PM, Gunship 9:30 PM',
          'day3': 'Closing ceremony 10:00 PM'
        }
      },
      highlights: [
        'Headliner performances by The Midnight and Electric Youth',
        'VIP viewing areas with premium amenities',
        'Exclusive artist meet & greet opportunities',
        'Professional sound system and lighting',
        'Food trucks and merchandise vendors',
        'Camping options available nearby'
      ],
      amenities: ['VIP Areas', 'Food Vendors', 'Merchandise', 'Parking'],
      createdAt: DateTime.now().subtract(const Duration(days: 45)),
      updatedAt: DateTime.now(),
    ),

    // Cooking Class Experience - Comprehensive Example
    PremiumBooking(
      id: '10',
      type: PremiumBookingType.experience,
      title: 'Italian Culinary Class',
      subtitle: 'Learn authentic techniques with Chef Marco',
      status: PremiumBookingStatus.upcoming,
      date: DateTime(2025, 8, 28),
      price: 385.0,
      imageUrl:
          'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&q=80',
      imageGallery: [
        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&q=80',
        'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&q=80',
        'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&q=80',
      ],
      confirmationCode: 'COOK-MARCO',
      location: 'Culinary Institute, Florence',
      country: 'Italy',
      rating: 4.9,
      reviewCount: 342,
      duration: '6 hours',
      participants: 1,
      details: {
        'chef': 'Chef Marco Rodriguez',
        'experience': '15 years Michelin-starred experience',
        'rating': '4.9 (127 reviews)',
        'menu': {
          'appetizer': 'Bruschetta with Tomato & Basil',
          'main': 'Homemade Pasta Carbonara',
          'dessert': 'Traditional Tiramisu'
        },
        'ingredients': {
          'bruschetta': 'Fresh tomatoes, basil, garlic, olive oil',
          'carbonara': 'Eggs, pancetta, parmesan, black pepper',
          'tiramisu': 'Mascarpone, coffee, ladyfingers, cocoa'
        },
        'facilities': [
          'Professional Stoves',
          'Fresh Ingredients',
          'All Utensils',
          'Recipe Cards',
          'Aprons Provided',
          'Take-home Containers'
        ]
      },
      highlights: [
        'Learn from Michelin-starred Chef Marco Rodriguez',
        'Hands-on preparation of 3-course Italian meal',
        'Professional kitchen with premium equipment',
        'Take home recipe collection and ingredients',
        'Wine pairing session with local Tuscan wines',
        'Small class size (maximum 8 students)'
      ],
      amenities: [
        'Professional Kitchen',
        'Wine Pairing',
        'Recipe Cards',
        'Take-home Items'
      ],
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      updatedAt: DateTime.now(),
    ),

    // Art Workshop Experience - Comprehensive Example
    PremiumBooking(
      id: '11',
      type: PremiumBookingType.experience,
      title: 'Provence Painting Workshop',
      subtitle: 'Create stunning watercolor landscapes',
      status: PremiumBookingStatus.confirmed,
      date: DateTime(2025, 9, 12),
      price: 275.0,
      imageUrl:
          'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=800&q=80',
      imageGallery: [
        'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=800&q=80',
        'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&q=80',
        'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=800&q=80',
      ],
      confirmationCode: 'ART-PROVENCE',
      location: 'Provence Art Studio, France',
      country: 'France',
      rating: 4.7,
      reviewCount: 189,
      duration: '4 hours',
      participants: 1,
      details: {
        'instructor': 'Isabella Chen',
        'background': 'Professional artist & gallery owner',
        'rating': '4.8 (89 reviews)',
        'materials': [
          'Canvas (16x20")',
          'Watercolor Paints',
          'Brushes Set',
          'Easel',
          'Apron',
          'Take-home Frame'
        ],
        'studio': {
          'location': 'Provence Art District',
          'lighting': 'Natural north-facing light',
          'class_size': 'Maximum 8 students',
          'duration': '4 hours with breaks'
        }
      },
      highlights: [
        'Paint en plein air in beautiful Provence countryside',
        'Learn professional watercolor techniques',
        'All materials provided including premium paints',
        'Take home your finished artwork with frame',
        'Small group instruction with personal attention',
        'Light refreshments and local wine included'
      ],
      amenities: [
        'All Materials',
        'Professional Instruction',
        'Take-home Artwork',
        'Refreshments'
      ],
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
    ),

    // Adventure Activity Experience - Comprehensive Example
    PremiumBooking(
      id: '12',
      type: PremiumBookingType.experience,
      title: 'Everest Base Camp Trek',
      subtitle: 'Epic 14-day Himalayan adventure',
      status: PremiumBookingStatus.upcoming,
      date: DateTime(2025, 10, 5),
      endDate: DateTime(2025, 10, 19),
      price: 2850.0,
      imageUrl:
          'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&q=80',
      imageGallery: [
        'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&q=80',
        'https://images.unsplash.com/photo-1464822759844-d150baec0494?w=800&q=80',
        'https://images.unsplash.com/photo-1551632811-561732d1e306?w=800&q=80',
      ],
      confirmationCode: 'TREK-EBC',
      location: 'Everest Base Camp, Nepal',
      country: 'Nepal',
      rating: 4.9,
      reviewCount: 567,
      duration: '14 days',
      participants: 1,
      details: {
        'accommodation': {
          'kathmandu': 'Hotel Yak & Yeti (4-star)',
          'trekking': 'Mountain lodges and tea houses',
          'base_camp': 'Expedition-grade camping equipment'
        },
        'flights': {
          'international': 'Round-trip flights to Kathmandu included',
          'domestic': 'Kathmandu-Lukla scenic mountain flight',
          'route': 'JFK → DOH → KTM'
        },
        'itinerary': {
          'day1-2': 'Arrival in Kathmandu, preparation and briefing',
          'day3': 'Fly to Lukla (2,840m), trek to Phakding',
          'day4-6': 'Trek through Namche Bazaar and acclimatization',
          'day7-10': 'Trek through Tengboche, Dingboche, Lobuche',
          'day11': 'Summit day to Everest Base Camp (5,364m)',
          'day12-14': 'Return trek to Lukla and fly back to Kathmandu'
        },
        'guide': {
          'name': 'Pemba Sherpa',
          'experience': '15+ years Himalayan guiding',
          'certifications': 'IFMGA certified mountain guide',
          'languages': 'English, Nepali, Tibetan'
        },
        'safety_equipment': [
          'Helmets',
          'Harnesses',
          'First Aid Kit',
          'Emergency Radio',
          'Oxygen',
          'Professional Guide'
        ],
        'fitness_requirements': [
          'Excellent physical fitness required',
          'Ability to hike 6-8 hours daily',
          'Previous high-altitude experience recommended',
          'Medical clearance required'
        ]
      },
      highlights: [
        'Reach the iconic Everest Base Camp at 5,364m',
        'Expert Sherpa guides with 15+ years experience',
        'All accommodation and meals included',
        'Scenic mountain flights and stunning views',
        'Cultural immersion in Sherpa villages',
        'Professional safety equipment and support'
      ],
      amenities: [
        'Expert Guides',
        'All Meals',
        'Accommodation',
        'Safety Equipment',
        'Permits'
      ],
      createdAt: DateTime.now().subtract(const Duration(days: 60)),
      updatedAt: DateTime.now(),
    ),

    // Cultural Tour Experience - Comprehensive Example
    PremiumBooking(
      id: '13',
      type: PremiumBookingType.experience,
      title: 'Ancient Rome Discovery',
      subtitle: 'Explore archaeological sites with experts',
      status: PremiumBookingStatus.confirmed,
      date: DateTime(2025, 9, 22),
      price: 195.0,
      imageUrl:
          'https://images.unsplash.com/photo-1552832230-c0197dd311b5?w=800&q=80',
      imageGallery: [
        'https://images.unsplash.com/photo-1552832230-c0197dd311b5?w=800&q=80',
        'https://images.unsplash.com/photo-1515542622106-78bda8ba0e5b?w=800&q=80',
        'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=800&q=80',
      ],
      confirmationCode: 'ROME-ARCH',
      location: 'Rome Archaeological Sites',
      country: 'Italy',
      rating: 4.8,
      reviewCount: 423,
      duration: '8 hours',
      participants: 2,
      details: {
        'historical_context':
            'Discover 2,000+ years of Roman history from ancient empire to modern significance',
        'expert_guide': {
          'name': 'Dr. Sarah Johnson',
          'credentials': 'PhD in Classical Archaeology',
          'experience': '20+ years expertise in Roman history',
          'specialization': 'Ancient Roman architecture and urban planning'
        },
        'sites': {
          'colosseum': 'Underground chambers and arena floor access',
          'forum': 'Roman Forum with exclusive archaeological areas',
          'palatine': 'Palatine Hill imperial palaces',
          'baths': 'Baths of Caracalla with virtual reality experience'
        },
        'significance':
            'Multiple UNESCO World Heritage Sites with outstanding universal value and cultural importance'
      },
      highlights: [
        'Access to restricted archaeological areas',
        'Expert guide with PhD in Classical Archaeology',
        'Skip-the-line access to all major sites',
        'Virtual reality experience of ancient Rome',
        'Small group size (maximum 12 people)',
        'Professional headset system for clear audio'
      ],
      amenities: [
        'Expert Guide',
        'Skip-the-line Access',
        'VR Experience',
        'Audio System'
      ],
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
      updatedAt: DateTime.now(),
    ),

    // ============================================================================
    // COMPREHENSIVE BOOKING TYPE DEMONSTRATIONS
    // ============================================================================

    // Flight-only Booking - Comprehensive Example
    PremiumBooking(
      id: '14',
      type: PremiumBookingType.flight,
      title: 'First Class to Dubai',
      subtitle: 'Emirates A380 suite with shower spa',
      status: PremiumBookingStatus.confirmed,
      date: DateTime(2025, 9, 5),
      price: 8500.0,
      imageUrl:
          'https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=800&q=80',
      imageGallery: [
        'https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=800&q=80',
        'https://images.unsplash.com/photo-1544620347-c4fd4a3d5957?w=800&q=80',
        'https://images.unsplash.com/photo-1583500178690-0c6d8b6f2e5b?w=800&q=80',
      ],
      confirmationCode: 'EK-FIRST-001',
      location: 'JFK → DXB',
      country: 'United Arab Emirates',
      rating: 4.9,
      reviewCount: 1834,
      duration: '12h 45m',
      participants: 1,
      details: {
        'flight': {
          'airline': 'Emirates',
          'aircraft': 'Airbus A380-800',
          'departure': 'JFK Terminal 4, Gate A2',
          'arrival': 'DXB Terminal 3, Gate B7',
          'seat': '1A (First Class Suite)',
          'gate': 'A2',
          'boarding_time': '10:30 PM',
          'departure_time': '11:55 PM',
          'arrival_time': '8:40 PM +1'
        },
        'seat_info': {
          'type': 'First Class Suite 1A',
          'features': [
            'Fully flat bed',
            'Private shower',
            'Mini-bar',
            'Personal wardrobe'
          ],
          'change_option': 'Available (additional fee may apply)'
        },
        'boarding_pass': {
          'qr_code': 'Available for mobile check-in',
          'status': 'Ready for download 24 hours before departure'
        },
        'baggage': {
          'carry_on': 'Included (2 pieces, 7kg each)',
          'checked': 'Included (2 pieces, 32kg each)',
          'extra': 'Additional baggage available for purchase'
        },
        'check_in_status': 'Online check-in opens 24 hours before departure'
      },
      highlights: [
        'Emirates A380 First Class Suite with private shower',
        'Gourmet dining with celebrity chef menus',
        'Chauffeur drive service to/from airport',
        'Access to Emirates First Class Lounge',
        'Priority boarding and baggage handling',
        'Complimentary Wi-Fi and entertainment system'
      ],
      amenities: [
        'First Class Suite',
        'Shower Spa',
        'Chauffeur Service',
        'Lounge Access'
      ],
      createdAt: DateTime.now().subtract(const Duration(days: 40)),
      updatedAt: DateTime.now(),
    ),

    // Hotel-only Booking - Comprehensive Example
    PremiumBooking(
      id: '15',
      type: PremiumBookingType.hotel,
      title: 'Ritz-Carlton Presidential',
      subtitle: 'Luxury penthouse with city views',
      status: PremiumBookingStatus.upcoming,
      date: DateTime(2025, 9, 18),
      endDate: DateTime(2025, 9, 22),
      price: 3200.0,
      imageUrl:
          'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&q=80',
      imageGallery: [
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&q=80',
        'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800&q=80',
        'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80',
      ],
      confirmationCode: 'RC-PRES-2025',
      location: 'The Ritz-Carlton, New York',
      country: 'United States',
      rating: 4.9,
      reviewCount: 2156,
      duration: '4 nights',
      participants: 2,
      details: {
        'check_in': {
          'date': 'September 18, 2025',
          'time': '3:00 PM (Early check-in available)',
          'location': 'VIP Reception Desk'
        },
        'check_out': {
          'date': 'September 22, 2025',
          'time': '12:00 PM (Late check-out complimentary)',
          'express': 'Available via mobile app'
        },
        'room': {
          'number': 'Presidential Suite 4501',
          'type': 'Presidential Suite',
          'size': '2,500 sq ft',
          'features': [
            'Panoramic city views',
            'Private terrace',
            'Full kitchen',
            'Butler service'
          ],
          'amenities': [
            'King bed',
            'Separate living room',
            'Marble bathroom',
            'Walk-in closet'
          ]
        },
        'hotel_amenities': [
          'Rooftop pool with city views',
          'Full-service spa and wellness center',
          'Michelin-starred restaurant',
          'Fitness center with personal trainers',
          'Business center and meeting rooms',
          'Concierge and butler services'
        ],
        'contact': {
          'phone': '+****************',
          'email': '<EMAIL>',
          'address': '50 Central Park South, New York, NY 10019'
        },
        'photo_gallery': [
          'Suite living room with city views',
          'Master bedroom with king bed',
          'Marble bathroom with soaking tub',
          'Private terrace overlooking Central Park',
          'Full kitchen with premium appliances'
        ]
      },
      highlights: [
        'Presidential Suite with 2,500 sq ft of luxury',
        'Panoramic views of Central Park and Manhattan',
        'Personal butler and concierge services',
        'Access to exclusive Club Level amenities',
        'Michelin-starred dining and rooftop pool',
        'Prime location steps from Central Park'
      ],
      amenities: [
        'Butler Service',
        'Rooftop Pool',
        'Spa',
        'Michelin Restaurant',
        'Concierge'
      ],
      createdAt: DateTime.now().subtract(const Duration(days: 35)),
      updatedAt: DateTime.now(),
    ),

    // Package Booking - Comprehensive Example
    PremiumBooking(
      id: '16',
      type: PremiumBookingType.package,
      title: 'Japan Cultural Package',
      subtitle: 'Complete 7-day journey with flights & hotels',
      status: PremiumBookingStatus.confirmed,
      date: DateTime(2025, 10, 12),
      endDate: DateTime(2025, 10, 19),
      price: 4850.0,
      imageUrl:
          'https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?w=800&q=80',
      imageGallery: [
        'https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?w=800&q=80',
        'https://images.unsplash.com/photo-1528164344705-47542687000d?w=800&q=80',
        'https://images.unsplash.com/photo-1545569341-9eb8b30979d9?w=800&q=80',
      ],
      confirmationCode: 'JAPAN-PKG-2025',
      location: 'Tokyo & Kyoto, Japan',
      country: 'Japan',
      rating: 4.9,
      reviewCount: 892,
      duration: '7 days',
      participants: 2,
      details: {
        'package_overview': {
          'flights': 'Round-trip business class flights',
          'hotels': '5-star accommodations in Tokyo and Kyoto',
          'activities': '5 cultural experiences and guided tours',
          'dining': '4 premium restaurant experiences',
          'transport': 'Private transfers and JR Pass included'
        },
        'itinerary': {
          'day1': 'Arrival in Tokyo, check-in at Park Hyatt Tokyo',
          'day2': 'Traditional tea ceremony and Senso-ji Temple tour',
          'day3': 'Tsukiji Fish Market tour and sushi masterclass',
          'day4': 'Travel to Kyoto, check-in at The Ritz-Carlton Kyoto',
          'day5': 'Bamboo Grove walk and traditional kaiseki dinner',
          'day6': 'Fushimi Inari shrine and sake tasting experience',
          'day7': 'Departure from Kansai International Airport'
        },
        'flights': {
          'outbound': {
            'airline': 'Japan Airlines',
            'route': 'JFK → NRT',
            'class': 'Business Class',
            'seat': '2A & 2B',
            'departure': 'October 12, 11:30 AM',
            'arrival': 'October 13, 3:15 PM +1'
          },
          'return': {
            'airline': 'Japan Airlines',
            'route': 'KIX → JFK',
            'class': 'Business Class',
            'seat': '3A & 3B',
            'departure': 'October 19, 6:45 PM',
            'arrival': 'October 19, 4:20 PM'
          }
        },
        'accommodation': {
          'tokyo': {
            'hotel': 'Park Hyatt Tokyo',
            'room': 'Deluxe Twin Room with City View',
            'nights': '3 nights',
            'amenities': ['Spa', 'Pool', 'Michelin Restaurant', 'Concierge']
          },
          'kyoto': {
            'hotel': 'The Ritz-Carlton Kyoto',
            'room': 'Luxury Room with Garden View',
            'nights': '3 nights',
            'amenities': [
              'Traditional Spa',
              'Tea House',
              'Fine Dining',
              'Butler Service'
            ]
          }
        },
        'activities': [
          {
            'name': 'Traditional Tea Ceremony',
            'duration': '3 hours',
            'day': 'Day 2',
            'location': 'Historic Kyoto Tea House'
          },
          {
            'name': 'Tsukiji Fish Market & Sushi Class',
            'duration': '5 hours',
            'day': 'Day 3',
            'location': 'Tokyo Central Market'
          },
          {
            'name': 'Bamboo Grove Experience',
            'duration': '4 hours',
            'day': 'Day 5',
            'location': 'Arashiyama Bamboo Grove'
          },
          {
            'name': 'Fushimi Inari Shrine Tour',
            'duration': '6 hours',
            'day': 'Day 6',
            'location': 'Fushimi Inari Taisha'
          },
          {
            'name': 'Sake Tasting Experience',
            'duration': '2 hours',
            'day': 'Day 6',
            'location': 'Traditional Sake Brewery'
          }
        ],
        'dining': [
          {
            'restaurant': 'Sukiyabashi Jiro',
            'cuisine': 'Sushi Omakase',
            'stars': '3 Michelin Stars',
            'day': 'Day 3'
          },
          {
            'restaurant': 'Kikunoi',
            'cuisine': 'Traditional Kaiseki',
            'stars': '3 Michelin Stars',
            'day': 'Day 5'
          },
          {
            'restaurant': 'Tempura Kondo',
            'cuisine': 'Tempura',
            'stars': '2 Michelin Stars',
            'day': 'Day 6'
          },
          {
            'restaurant': 'Narisawa',
            'cuisine': 'Innovative Japanese',
            'stars': '2 Michelin Stars',
            'day': 'Day 7'
          }
        ],
        'guide': {
          'name': 'Hiroshi Tanaka',
          'experience': '12+ years cultural tourism',
          'languages': 'English, Japanese',
          'specialization': 'Japanese history and culture',
          'rating': '4.9 (234 reviews)'
        }
      },
      highlights: [
        'Business class flights with Japan Airlines',
        '5-star accommodations in Tokyo and Kyoto',
        'Private cultural guide throughout the journey',
        '4 Michelin-starred restaurant experiences',
        '5 authentic cultural activities and tours',
        'All transfers and transportation included'
      ],
      amenities: [
        'Business Class Flights',
        '5-Star Hotels',
        'Private Guide',
        'Michelin Dining',
        'All Transfers'
      ],
      createdAt: DateTime.now().subtract(const Duration(days: 50)),
      updatedAt: DateTime.now(),
    ),
  ];

  /// Get all premium bookings
  static List<PremiumBooking> getAllBookings() => List.from(_bookings);

  /// Get bookings by status
  static List<PremiumBooking> getBookingsByStatus(PremiumBookingStatus status) {
    return _bookings.where((booking) => booking.status == status).toList();
  }

  /// Get upcoming bookings
  static List<PremiumBooking> getUpcomingBookings() {
    return _bookings
        .where((booking) =>
            booking.status == PremiumBookingStatus.upcoming ||
            booking.status == PremiumBookingStatus.confirmed ||
            booking.status == PremiumBookingStatus.pending)
        .toList();
  }

  /// Get completed bookings
  static List<PremiumBooking> getCompletedBookings() {
    return _bookings
        .where((booking) => booking.status == PremiumBookingStatus.completed)
        .toList();
  }

  /// Get cancelled bookings
  static List<PremiumBooking> getCancelledBookings() {
    return _bookings
        .where((booking) => booking.status == PremiumBookingStatus.cancelled)
        .toList();
  }

  /// Search bookings
  static List<PremiumBooking> searchBookings(String query) {
    if (query.isEmpty) return getAllBookings();

    final lowercaseQuery = query.toLowerCase();
    return _bookings
        .where((booking) =>
            booking.title.toLowerCase().contains(lowercaseQuery) ||
            booking.subtitle.toLowerCase().contains(lowercaseQuery) ||
            booking.location.toLowerCase().contains(lowercaseQuery) ||
            (booking.country?.toLowerCase().contains(lowercaseQuery) ?? false))
        .toList();
  }

  /// Get booking by ID
  static PremiumBooking? getBookingById(String id) {
    try {
      return _bookings.firstWhere((booking) => booking.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get premium booking statistics
  static PremiumBookingStats getBookingStats() {
    final totalBookings = _bookings.length;
    final upcomingBookings = getUpcomingBookings().length;
    final completedBookings = getCompletedBookings().length;

    // Calculate unique countries
    final countries = _bookings
        .where((b) => b.country != null)
        .map((b) => b.country!)
        .toSet()
        .length;

    // Calculate average rating
    final totalRating =
        _bookings.fold<double>(0, (sum, booking) => sum + booking.rating);
    final averageRating = totalRating / _bookings.length;

    // Calculate total spent and saved
    final totalSpent =
        _bookings.fold<double>(0, (sum, booking) => sum + booking.price);
    final totalSaved = totalSpent * 0.18; // Assume 18% savings

    // Find favorite destination (most booked country)
    final countryCount = <String, int>{};
    for (final booking in _bookings) {
      if (booking.country != null) {
        countryCount[booking.country!] =
            (countryCount[booking.country!] ?? 0) + 1;
      }
    }
    final favoriteDestination =
        countryCount.entries.reduce((a, b) => a.value > b.value ? a : b).key;

    // Find most booked type
    final typeCount = <PremiumBookingType, int>{};
    for (final booking in _bookings) {
      typeCount[booking.type] = (typeCount[booking.type] ?? 0) + 1;
    }
    final mostBookedType =
        typeCount.entries.reduce((a, b) => a.value > b.value ? a : b).key;

    return PremiumBookingStats(
      totalBookings: totalBookings,
      upcomingBookings: upcomingBookings,
      completedTrips: completedBookings,
      countriesVisited: countries,
      averageRating: averageRating,
      totalSpent: totalSpent,
      totalSaved: totalSaved,
      favoriteDestination: favoriteDestination,
      mostBookedType: mostBookedType,
    );
  }
}
