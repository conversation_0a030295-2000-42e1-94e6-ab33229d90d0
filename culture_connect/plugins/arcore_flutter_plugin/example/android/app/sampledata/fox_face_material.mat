material {
    "name" : "Fox Face",

    "parameters" : [
        {
           "type" : "sampler2d",
           "name" : "texture"
        }
    ],
    "requires" : [
        "position",
        "uv0"
    ],
    "shadingModel" : "unlit",
    "blending" : "transparent"
}
fragment {
    void material(inout MaterialInputs material) {
        prepareMaterial(material);
        material.baseColor = texture(materialParams_texture, getUV0());
        material.baseColor.rgb = inverseTonemap(material.baseColor.rgb);
    }
}
