import { Insurance } from "@/types";

export const insuranceOptions: Insurance[] = [
  {
    id: "1",
    type: "Basic Travel",
    coverage: "Medical emergencies, trip cancellation, lost baggage",
    price: 45,
    currency: "USD",
    duration: "7 days",
    provider: "SafeTravel Insurance"
  },
  {
    id: "2",
    type: "Premium Travel",
    coverage: "Comprehensive medical, trip cancellation/interruption, lost baggage, flight delays, 24/7 assistance",
    price: 85,
    currency: "USD",
    duration: "7 days",
    provider: "GlobalGuard"
  },
  {
    id: "3",
    type: "Adventure",
    coverage: "High-risk activities, emergency evacuation, medical emergencies, equipment protection",
    price: 120,
    currency: "USD",
    duration: "7 days",
    provider: "ExtremeShield"
  },
  {
    id: "4",
    type: "Family Plan",
    coverage: "Coverage for up to 5 family members, medical emergencies, trip cancellation, lost baggage",
    price: 150,
    currency: "USD",
    duration: "7 days",
    provider: "FamilySafe Travel"
  }
];