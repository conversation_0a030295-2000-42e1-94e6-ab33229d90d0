import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Environment configuration service for managing API keys and environment variables
class EnvironmentConfig {
  // Singleton instance
  static final EnvironmentConfig _instance = EnvironmentConfig._internal();
  factory EnvironmentConfig() => _instance;
  EnvironmentConfig._internal();

  /// Get RapidAPI key for Travel Buddy API
  static String get rapidApiKey {
    final key = dotenv.env['RAPIDAPI_KEY'];
    if (key == null || key.isEmpty) {
      throw Exception('RAPIDAPI_KEY not found in environment variables');
    }
    return key;
  }

  /// Get Firebase API key
  static String get firebaseApiKey {
    return dotenv.env['FIREBASE_API_KEY'] ?? '';
  }

  /// Get Google Maps API key
  static String get googleMapsApiKey {
    return dotenv.env['GOOGLE_MAPS_API_KEY'] ?? '';
  }

  /// Get Stripe publishable key
  static String get stripePublishableKey {
    return dotenv.env['STRIPE_PUBLISHABLE_KEY'] ?? '';
  }

  /// Get app environment (development, staging, production)
  static String get appEnvironment {
    return dotenv.env['APP_ENVIRONMENT'] ?? 'development';
  }

  /// Check if app is in development mode
  static bool get isDevelopment {
    return appEnvironment == 'development';
  }

  /// Check if app is in production mode
  static bool get isProduction {
    return appEnvironment == 'production';
  }

  /// Get app name
  static String get appName {
    return dotenv.env['APP_NAME'] ?? 'CultureConnect';
  }

  /// Get app version
  static String get appVersion {
    return dotenv.env['APP_VERSION'] ?? '1.0.0';
  }

  /// Validate that all required environment variables are present
  static void validateEnvironment() {
    final requiredKeys = ['RAPIDAPI_KEY'];
    final optionalKeys = [
      'FIREBASE_API_KEY',
      'GOOGLE_MAPS_API_KEY',
      'STRIPE_PUBLISHABLE_KEY'
    ];

    final missingRequired = <String>[];
    final missingOptional = <String>[];

    // Check required keys
    for (final key in requiredKeys) {
      if (dotenv.env[key] == null || dotenv.env[key]!.isEmpty) {
        missingRequired.add(key);
      }
    }

    // Check optional keys (just for logging)
    for (final key in optionalKeys) {
      if (dotenv.env[key] == null || dotenv.env[key]!.isEmpty) {
        missingOptional.add(key);
      }
    }

    if (missingRequired.isNotEmpty) {
      throw Exception(
        'Missing required environment variables: ${missingRequired.join(', ')}. '
        'Please check your .env file.',
      );
    }

    if (missingOptional.isNotEmpty && isDevelopment) {
      print(
          'ℹ️ Optional environment variables not set: ${missingOptional.join(', ')}');
    }
  }

  /// Get all environment variables (for debugging - don't use in production)
  static Map<String, String> getAllEnvironmentVariables() {
    if (!isDevelopment) {
      throw Exception(
          'getAllEnvironmentVariables() can only be used in development mode');
    }
    return Map<String, String>.from(dotenv.env);
  }
}
