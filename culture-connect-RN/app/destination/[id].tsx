import React, { useState } from "react";
import { StyleSheet, View, Text, ScrollView, Pressable, Dimensions } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Image } from "expo-image";
import { SafeAreaView } from "react-native-safe-area-context";
import { ArrowLeft, Heart, Star, MapPin, Calendar, Clock, Sparkles } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { Button } from "@/components/Button";
import { destinations } from "@/mocks/destinations";

const { width } = Dimensions.get("window");

export default function DestinationDetailScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const [isFavorite, setIsFavorite] = useState(false);
  
  const destination = destinations.find((d) => d.id === id);
  
  if (!destination) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Destination not found</Text>
      </SafeAreaView>
    );
  }

  const handleFavoritePress = () => {
    setIsFavorite(!isFavorite);
  };

  const handleBookNow = () => {
    // Navigate to booking flow
    router.push("/bookings");
  };

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: destination.imageUrl }}
            style={styles.image}
            contentFit="cover"
          />
          <LinearGradient
            colors={["rgba(0,0,0,0.5)", "transparent", "transparent"]}
            style={styles.gradient}
          />
          <SafeAreaView style={styles.header} edges={["top"]}>
            <Pressable style={styles.backButton} onPress={() => router.back()}>
              <ArrowLeft size={24} color={colors.white} />
            </Pressable>
            <Pressable
              style={styles.favoriteButton}
              onPress={handleFavoritePress}
            >
              <Heart
                size={24}
                color={colors.white}
                fill={isFavorite ? colors.secondary : "none"}
              />
            </Pressable>
          </SafeAreaView>
        </View>

        <View style={styles.content}>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{destination.name}</Text>
            <View style={styles.ratingContainer}>
              <Star size={16} color={colors.warning} fill={colors.warning} />
              <Text style={styles.rating}>{destination.rating.toFixed(1)}</Text>
            </View>
          </View>

          <View style={styles.locationContainer}>
            <MapPin size={16} color={colors.textLight} />
            <Text style={styles.location}>{destination.location}</Text>
          </View>

          <View style={styles.tagsContainer}>
            {destination.tags.map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>About</Text>
            <Text style={styles.description}>{destination.description}</Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Available Tours</Text>
            
            <View style={styles.tourCard}>
              <View style={styles.tourInfo}>
                <Text style={styles.tourTitle}>Guided Cultural Tour</Text>
                <View style={styles.tourDetail}>
                  <Calendar size={14} color={colors.textLight} />
                  <Text style={styles.tourDetailText}>Available Daily</Text>
                </View>
                <View style={styles.tourDetail}>
                  <Clock size={14} color={colors.textLight} />
                  <Text style={styles.tourDetailText}>3 hours</Text>
                </View>
                <Text style={styles.tourPrice}>
                  From {destination.currency}{destination.price}
                </Text>
              </View>
              <Pressable style={styles.bookButton}>
                <Text style={styles.bookButtonText}>Book</Text>
              </Pressable>
            </View>
            
            <View style={styles.tourCard}>
              <View style={styles.tourInfo}>
                <Text style={styles.tourTitle}>Private Experience</Text>
                <View style={styles.tourDetail}>
                  <Calendar size={14} color={colors.textLight} />
                  <Text style={styles.tourDetailText}>Mon, Wed, Fri</Text>
                </View>
                <View style={styles.tourDetail}>
                  <Clock size={14} color={colors.textLight} />
                  <Text style={styles.tourDetailText}>5 hours</Text>
                </View>
                <Text style={styles.tourPrice}>
                  From {destination.currency}{Math.round(destination.price! * 1.8)}
                </Text>
              </View>
              <Pressable style={styles.bookButton}>
                <Text style={styles.bookButtonText}>Book</Text>
              </Pressable>
            </View>
          </View>

          <View style={styles.aiSection}>
            <View style={styles.aiHeader}>
              <Sparkles size={20} color={colors.secondary} />
              <Text style={styles.aiTitle}>AI Travel Insights</Text>
            </View>
            <Text style={styles.aiText}>
              Based on current trends and your preferences, the best time to
              visit {destination.name} is during spring when the weather is
              pleasant and crowds are smaller. Consider booking accommodations
              in the central district for easy access to major attractions.
            </Text>
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <View style={styles.priceContainer}>
          <Text style={styles.priceLabel}>From</Text>
          <Text style={styles.price}>
            {destination.currency}{destination.price}
          </Text>
          <Text style={styles.priceUnit}>/person</Text>
        </View>
        <Button
          title="Book Now"
          onPress={handleBookNow}
          variant="primary"
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  imageContainer: {
    height: 300,
    width: width,
  },
  image: {
    ...StyleSheet.absoluteFillObject,
  },
  gradient: {
    ...StyleSheet.absoluteFillObject,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: spacing.xl,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0,0,0,0.3)",
    justifyContent: "center",
    alignItems: "center",
  },
  favoriteButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0,0,0,0.3)",
    justifyContent: "center",
    alignItems: "center",
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.xl,
    paddingBottom: 100, // Space for the footer
  },
  titleContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  title: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    flex: 1,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.white,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  rating: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium as any,
    color: colors.text,
    marginLeft: 4,
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  location: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textLight,
    marginLeft: spacing.xs,
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: spacing.lg,
  },
  tag: {
    backgroundColor: colors.white,
    borderRadius: 16,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    marginRight: spacing.xs,
    marginBottom: spacing.xs,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  tagText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.primary,
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.md,
  },
  description: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.text,
    lineHeight: 24,
  },
  tourCard: {
    flexDirection: "row",
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  tourInfo: {
    flex: 1,
  },
  tourTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  tourDetail: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.xs,
  },
  tourDetailText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textLight,
    marginLeft: spacing.xs,
  },
  tourPrice: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.primary,
    marginTop: spacing.xs,
  },
  bookButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    justifyContent: "center",
    alignSelf: "center",
  },
  bookButtonText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium as any,
    color: colors.white,
  },
  aiSection: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    marginBottom: spacing.xl,
    borderLeftWidth: 3,
    borderLeftColor: colors.secondary,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  aiHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  aiTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  aiText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.text,
    lineHeight: 20,
  },
  footer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 10,
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "baseline",
  },
  priceLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textLight,
    marginRight: spacing.xs,
  },
  price: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.primary,
  },
  priceUnit: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textLight,
    marginLeft: spacing.xs,
  },
});