import React, { useState } from 'react';
import { StyleSheet, View, Text, ScrollView, Image, Pressable, Alert, Animated, Dimensions, Modal } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, Calendar, Clock, MapPin, Users, Fuel, Settings, Star, CheckCircle, Info, ChevronRight, Shield, Car } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';
import { cars } from '@/mocks/cars';
import { Button } from '@/components/Button';
import { CalendarModal } from '@/components/CalendarModal';
import { LocationMapModal } from '@/components/LocationMapModal';

const { width: screenWidth } = Dimensions.get('window');

export default function CarBookingScreen() {
  const router = useRouter();
  const { carId, duration } = useLocalSearchParams();
  const [pickupDate, setPickupDate] = useState(new Date());
  const [returnDate, setReturnDate] = useState(new Date(Date.now() + 24 * 60 * 60 * 1000));
  const [pickupTime, setPickupTime] = useState('10:00 AM');
  const [returnTime, setReturnTime] = useState('10:00 AM');
  const [showPickupCalendar, setShowPickupCalendar] = useState(false);
  const [showReturnCalendar, setShowReturnCalendar] = useState(false);
  const [selectedInsurance, setSelectedInsurance] = useState('basic');
  const [selectedLocation, setSelectedLocation] = useState<{ name: string; address: string; coordinates?: { lat: number; lng: number } } | null>(null);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [selectedSecurity, setSelectedSecurity] = useState<string | null>(null);
  const [showDurationAlert, setShowDurationAlert] = useState(false);
  const [durationAlertMessage, setDurationAlertMessage] = useState('');
  const scaleAnim = new Animated.Value(1);
  
  const car = cars.find(c => c.id === carId);

  if (!car) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Car not found</Text>
      </SafeAreaView>
    );
  }

  const handleBackPress = () => {
    router.back();
  };

  const calculateDays = () => {
    if (duration) {
      // Parse duration from car details screen
      const durationStr = duration as string;
      if (durationStr.includes('day')) {
        const days = parseInt(durationStr);
        return days || 1;
      } else if (durationStr.includes('week')) {
        return 7;
      } else if (durationStr.includes('month')) {
        return 30;
      }
    }
    
    const diffTime = Math.abs(returnDate.getTime() - pickupDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays || 1;
  };

  const baseAmount = car.pricePerDay * calculateDays();
  const insuranceCost = selectedInsurance === 'basic' ? 0 : selectedInsurance === 'premium' ? 25 * calculateDays() : 45 * calculateDays();
  const securityCost = selectedSecurity === 'personal' ? 50 * calculateDays() : selectedSecurity === 'full' ? 120 * calculateDays() : 0;
  const totalAmount = baseAmount + insuranceCost + securityCost;

  const handleProceedToPayment = () => {
    // Animate button press
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      })
    ]).start();

    // Store booking details and navigate to payment
    const bookingData = {
      type: 'car',
      carId: car.id,
      carName: car.name,
      pickupDate: pickupDate.toISOString(),
      returnDate: returnDate.toISOString(),
      pickupTime,
      returnTime,
      days: calculateDays(),
      dailyRate: car.pricePerDay,
      baseAmount,
      insuranceCost,
      securityCost,
      totalAmount,
      insurance: selectedInsurance,
      security: selectedSecurity,
      location: selectedLocation || { name: car.location, address: 'Default pickup location' }
    };

    router.push({
      pathname: '/booking/payment',
      params: { 
        bookingData: JSON.stringify(bookingData),
        amount: totalAmount.toString(),
        type: 'car'
      }
    });
  };

  const handleLocationSelect = (location: { name: string; address: string; coordinates?: { lat: number; lng: number } }) => {
    setSelectedLocation(location);
  };

  const getInsuranceDetails = (type: string) => {
    switch (type) {
      case 'basic':
        return { name: 'Basic Coverage', price: 0, description: 'Standard protection included' };
      case 'premium':
        return { name: 'Premium Coverage', price: 25, description: 'Enhanced protection with lower deductible' };
      case 'comprehensive':
        return { name: 'Comprehensive Coverage', price: 45, description: 'Full protection with zero deductible' };
      default:
        return { name: 'Basic Coverage', price: 0, description: 'Standard protection included' };
    }
  };

  const getSecurityDetails = (type: string) => {
    switch (type) {
      case 'personal':
        return { 
          name: 'Personal Security', 
          price: 50, 
          description: 'Professional bodyguard without vehicle',
          features: ['Personal protection', '24/7 availability', 'Trained professional']
        };
      case 'full':
        return { 
          name: 'Full Security Detail', 
          price: 120, 
          description: 'Complete security team with escort vehicle',
          features: ['Security team', 'Escort vehicle', 'Route planning', 'Emergency response']
        };
      default:
        return null;
    }
  };

  const handleDurationError = (message: string) => {
    setDurationAlertMessage(message);
    setShowDurationAlert(true);
  };

  const getDurationFromSelection = () => {
    if (duration) {
      const durationStr = duration as string;
      if (durationStr.includes('day')) {
        const days = parseInt(durationStr);
        return days || 1;
      } else if (durationStr.includes('week')) {
        return 7;
      } else if (durationStr.includes('month')) {
        return 30;
      }
    }
    return 1;
  };



  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <SafeAreaView edges={['top']} style={styles.header}>
        <View style={styles.headerContent}>
          <Pressable style={styles.backButton} onPress={handleBackPress}>
            <ArrowLeft size={24} color={colors.text} />
          </Pressable>
          <View style={styles.headerTitleContainer}>
            <Text style={styles.headerTitle}>Complete Booking</Text>
            <Text style={styles.headerSubtitle}>Review and confirm details</Text>
          </View>
          <View style={styles.placeholder} />
        </View>
      </SafeAreaView>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.content} contentContainerStyle={styles.contentContainer}>
        {/* Car Summary Card */}
        <View style={styles.carSummaryCard}>
          <View style={styles.carSummaryHeader}>
            <Text style={styles.sectionTitle}>Selected Vehicle</Text>
            <View style={styles.ratingBadge}>
              <Star size={14} color={colors.warning} fill={colors.warning} />
              <Text style={styles.ratingText}>{car.rating}</Text>
            </View>
          </View>
          
          <View style={styles.carSummaryContent}>
            <View style={styles.carImageContainer}>
              <Image source={{ uri: car.imageUrl }} style={styles.carImage} />
              <View style={styles.availabilityBadge}>
                <CheckCircle size={12} color={colors.success} />
                <Text style={styles.availabilityText}>Available</Text>
              </View>
            </View>
            
            <View style={styles.carDetails}>
              <Text style={styles.carName}>{car.name}</Text>
              <Text style={styles.carCategory}>{car.category}</Text>
              
              <View style={styles.carSpecs}>
                <View style={styles.specChip}>
                  <Users size={12} color={colors.accent} />
                  <Text style={styles.specText}>{car.seats} seats</Text>
                </View>
                <View style={styles.specChip}>
                  <Fuel size={12} color={colors.accent} />
                  <Text style={styles.specText}>{car.fuelType}</Text>
                </View>
                <View style={styles.specChip}>
                  <Settings size={12} color={colors.accent} />
                  <Text style={styles.specText}>{car.transmission}</Text>
                </View>
              </View>
              
              <View style={styles.priceDisplay}>
                <Text style={styles.dailyRate}>${car.pricePerDay}</Text>
                <Text style={styles.perDay}>per day</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Rental Details */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Rental Details</Text>
            <View style={styles.durationBadge}>
              <Text style={styles.durationText}>{calculateDays()} day{calculateDays() > 1 ? 's' : ''}</Text>
            </View>
          </View>
          
          {/* Location */}
          <Pressable 
            style={styles.detailCard}
            onPress={() => setShowLocationModal(true)}
          >
            <View style={styles.detailHeader}>
              <MapPin size={18} color={colors.accent} />
              <Text style={styles.detailTitle}>Pickup Location</Text>
              <ChevronRight size={16} color={colors.textSecondary} />
            </View>
            <Text style={styles.locationName}>
              {selectedLocation ? selectedLocation.name : car.location}
            </Text>
            <Text style={styles.locationAddress}>
              {selectedLocation ? selectedLocation.address : 'Available 24/7 • Free pickup service'}
            </Text>
          </Pressable>

          {/* Date & Time Selection */}
          <View style={styles.dateTimeGrid}>
            <Pressable 
              style={styles.dateTimeCard}
              onPress={() => setShowPickupCalendar(true)}
            >
              <View style={styles.dateTimeHeader}>
                <Calendar size={16} color={colors.accent} />
                <Text style={styles.dateTimeLabel}>Pickup</Text>
                <ChevronRight size={16} color={colors.textSecondary} />
              </View>
              <Text style={styles.dateTimeValue}>{formatDate(pickupDate)}</Text>
              <Text style={styles.timeValue}>{pickupTime}</Text>
            </Pressable>

            <Pressable 
              style={styles.dateTimeCard}
              onPress={() => setShowReturnCalendar(true)}
            >
              <View style={styles.dateTimeHeader}>
                <Calendar size={16} color={colors.accent} />
                <Text style={styles.dateTimeLabel}>Return</Text>
                <ChevronRight size={16} color={colors.textSecondary} />
              </View>
              <Text style={styles.dateTimeValue}>{formatDate(returnDate)}</Text>
              <Text style={styles.timeValue}>{returnTime}</Text>
            </Pressable>
          </View>
        </View>

        {/* Insurance Options */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Insurance Coverage</Text>
            <View style={styles.infoButton}>
              <Info size={16} color={colors.textSecondary} />
            </View>
          </View>
          
          <View style={styles.insuranceOptions}>
            {['basic', 'premium', 'comprehensive'].map((type) => {
              const insurance = getInsuranceDetails(type);
              const isSelected = selectedInsurance === type;
              return (
                <Pressable
                  key={type}
                  style={[styles.insuranceCard, isSelected && styles.selectedInsuranceCard]}
                  onPress={() => setSelectedInsurance(type)}
                >
                  <View style={styles.insuranceHeader}>
                    <View style={[styles.radioButton, isSelected && styles.selectedRadio]}>
                      {isSelected && <View style={styles.radioInner} />}
                    </View>
                    <View style={styles.insuranceInfo}>
                      <Text style={[styles.insuranceName, isSelected && styles.selectedInsuranceName]}>
                        {insurance.name}
                      </Text>
                      <Text style={styles.insuranceDescription}>{insurance.description}</Text>
                    </View>
                    <Text style={[styles.insurancePrice, isSelected && styles.selectedInsurancePrice]}>
                      {insurance.price === 0 ? 'Included' : `+${insurance.price}/day`}
                    </Text>
                  </View>
                </Pressable>
              );
            })}
          </View>
        </View>

        {/* Security Add-ons */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Security Add-ons</Text>
            <View style={styles.infoButton}>
              <Shield size={16} color={colors.textSecondary} />
            </View>
          </View>
          
          <View style={styles.securityOptions}>
            {/* No Security Option */}
            <Pressable
              style={[styles.securityCard, selectedSecurity === null && styles.selectedSecurityCard]}
              onPress={() => setSelectedSecurity(null)}
            >
              <View style={styles.securityHeader}>
                <View style={[styles.radioButton, selectedSecurity === null && styles.selectedRadio]}>
                  {selectedSecurity === null && <View style={styles.radioInner} />}
                </View>
                <View style={styles.securityInfo}>
                  <Text style={[styles.securityName, selectedSecurity === null && styles.selectedSecurityName]}>
                    No Security
                  </Text>
                  <Text style={styles.securityDescription}>Standard rental without additional security</Text>
                </View>
                <Text style={[styles.securityPrice, selectedSecurity === null && styles.selectedSecurityPrice]}>
                  Included
                </Text>
              </View>
            </Pressable>

            {/* Personal Security */}
            <Pressable
              style={[styles.securityCard, selectedSecurity === 'personal' && styles.selectedSecurityCard]}
              onPress={() => setSelectedSecurity('personal')}
            >
              <View style={styles.securityHeader}>
                <View style={[styles.radioButton, selectedSecurity === 'personal' && styles.selectedRadio]}>
                  {selectedSecurity === 'personal' && <View style={styles.radioInner} />}
                </View>
                <View style={styles.securityInfo}>
                  <Text style={[styles.securityName, selectedSecurity === 'personal' && styles.selectedSecurityName]}>
                    Personal Security
                  </Text>
                  <Text style={styles.securityDescription}>Professional bodyguard without vehicle</Text>
                  <View style={styles.securityFeatures}>
                    <Text style={styles.featureItem}>• Personal protection</Text>
                    <Text style={styles.featureItem}>• 24/7 availability</Text>
                    <Text style={styles.featureItem}>• Trained professional</Text>
                  </View>
                </View>
                <Text style={[styles.securityPrice, selectedSecurity === 'personal' && styles.selectedSecurityPrice]}>
                  +$50/day
                </Text>
              </View>
            </Pressable>

            {/* Full Security Detail */}
            <Pressable
              style={[styles.securityCard, selectedSecurity === 'full' && styles.selectedSecurityCard]}
              onPress={() => setSelectedSecurity('full')}
            >
              <View style={styles.securityHeader}>
                <View style={[styles.radioButton, selectedSecurity === 'full' && styles.selectedRadio]}>
                  {selectedSecurity === 'full' && <View style={styles.radioInner} />}
                </View>
                <View style={styles.securityInfo}>
                  <Text style={[styles.securityName, selectedSecurity === 'full' && styles.selectedSecurityName]}>
                    Full Security Detail
                  </Text>
                  <Text style={styles.securityDescription}>Complete security team with escort vehicle</Text>
                  <View style={styles.securityFeatures}>
                    <Text style={styles.featureItem}>• Security team</Text>
                    <Text style={styles.featureItem}>• Escort vehicle</Text>
                    <Text style={styles.featureItem}>• Route planning</Text>
                    <Text style={styles.featureItem}>• Emergency response</Text>
                  </View>
                </View>
                <Text style={[styles.securityPrice, selectedSecurity === 'full' && styles.selectedSecurityPrice]}>
                  +$120/day
                </Text>
              </View>
            </Pressable>
          </View>
        </View>

        {/* Price Breakdown */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Price Breakdown</Text>
          
          <View style={styles.priceBreakdown}>
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Car rental ({calculateDays()} day{calculateDays() > 1 ? 's' : ''})</Text>
              <Text style={styles.priceValue}>${baseAmount}</Text>
            </View>
            
            {insuranceCost > 0 && (
              <View style={styles.priceRow}>
                <Text style={styles.priceLabel}>Insurance ({getInsuranceDetails(selectedInsurance).name})</Text>
                <Text style={styles.priceValue}>${insuranceCost}</Text>
              </View>
            )}
            
            {securityCost > 0 && (
              <View style={styles.priceRow}>
                <Text style={styles.priceLabel}>Security ({getSecurityDetails(selectedSecurity!)?.name})</Text>
                <Text style={styles.priceValue}>${securityCost}</Text>
              </View>
            )}
            
            <View style={styles.priceDivider} />
            
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Total Amount</Text>
              <Text style={styles.totalValue}>${totalAmount}</Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Action Bar */}
      <View style={styles.bottomBar}>
        <View style={styles.priceInfo}>
          <Text style={styles.totalLabel}>Total</Text>
          <Text style={styles.totalPrice}>${totalAmount}</Text>
          <Text style={styles.totalDuration}>for {calculateDays()} day{calculateDays() > 1 ? 's' : ''}</Text>
        </View>
        <Animated.View style={[styles.paymentButton, { transform: [{ scale: scaleAnim }] }]}>
          <Button
            title="Proceed to Payment"
            onPress={handleProceedToPayment}
            variant="primary"
            size="large"
          />
        </Animated.View>
      </View>

      {/* Calendar Modals */}
      <CalendarModal
        visible={showPickupCalendar}
        onClose={() => setShowPickupCalendar(false)}
        onSelectDates={(date, returnDate) => {
          setPickupDate(date);
          if (returnDate) {
            setReturnDate(returnDate);
          } else {
            // Calculate return date based on selected duration
            const selectedDays = getDurationFromSelection();
            const calculatedReturnDate = new Date(date);
            calculatedReturnDate.setDate(calculatedReturnDate.getDate() + selectedDays);
            setReturnDate(calculatedReturnDate);
          }
          setShowPickupCalendar(false);
        }}
        tripType="round-trip"
        initialDepartDate={pickupDate}
        initialReturnDate={returnDate}
        maxDays={getDurationFromSelection()}
        minDays={getDurationFromSelection()}
        onDurationError={handleDurationError}
      />

      <CalendarModal
        visible={showReturnCalendar}
        onClose={() => setShowReturnCalendar(false)}
        onSelectDates={(date) => {
          // Validate that return date maintains the selected duration
          const selectedDays = getDurationFromSelection();
          const daysDifference = Math.ceil((date.getTime() - pickupDate.getTime()) / (1000 * 60 * 60 * 24));
          
          if (daysDifference !== selectedDays) {
            handleDurationError(`Return date must be exactly ${selectedDays} day${selectedDays > 1 ? 's' : ''} from pickup date to match your selected rental duration.`);
            return;
          }
          
          setReturnDate(date);
          setShowReturnCalendar(false);
        }}
        tripType="one-way"
        initialDepartDate={returnDate}
        maxDays={getDurationFromSelection()}
        minDays={getDurationFromSelection()}
        onDurationError={handleDurationError}
      />

      {/* Location Modal */}
      <LocationMapModal
        visible={showLocationModal}
        onClose={() => setShowLocationModal(false)}
        onSelectLocation={handleLocationSelect}
        initialLocation={selectedLocation?.name || car.location}
      />

      {/* Duration Alert Modal */}
      <Modal
        visible={showDurationAlert}
        transparent
        animationType="fade"
        onRequestClose={() => setShowDurationAlert(false)}
      >
        <View style={styles.alertOverlay}>
          <View style={styles.alertContainer}>
            <View style={styles.alertHeader}>
              <View style={styles.alertIcon}>
                <Info size={24} color={colors.warning} />
              </View>
              <Text style={styles.alertTitle}>Duration Restriction</Text>
            </View>
            <Text style={styles.alertMessage}>{durationAlertMessage}</Text>
            <Pressable 
              style={styles.alertButton}
              onPress={() => setShowDurationAlert(false)}
            >
              <Text style={styles.alertButtonText}>OK</Text>
            </Pressable>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flex: 1,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  headerSubtitle: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginTop: 2,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  contentContainer: {
    paddingBottom: 100,
  },
  carSummaryCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    marginVertical: spacing.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  carSummaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  carSummaryContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  carImageContainer: {
    position: 'relative',
    width: 100,
    height: 70,
  },
  availabilityBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.success,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  availabilityText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    marginLeft: 2,
    fontWeight: typography.weights.medium as any,
  },
  carDetails: {
    flex: 1,
    justifyContent: 'space-between',
    minHeight: 70,
  },
  ratingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  ratingText: {
    fontSize: typography.sizes.xs,
    color: colors.warning,
    marginLeft: 4,
    fontWeight: typography.weights.medium as any,
  },
  specChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 6,
  },
  priceDisplay: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  carImage: {
    width: 100,
    height: 70,
    borderRadius: 8,
    resizeMode: 'cover',
  },
  carInfo: {
    flex: 1,
    marginLeft: spacing.md,
  },
  carName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  carCategory: {
    fontSize: typography.sizes.sm,
    color: colors.accent,
    marginBottom: spacing.sm,
  },
  carSpecs: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.xs,
    marginTop: spacing.xs,
    marginBottom: spacing.sm,
  },
  specItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  specText: {
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    marginLeft: 4,
  },
  priceInfo: {
    alignItems: 'flex-end',
  },
  dailyRate: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
  perDay: {
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
  },
  section: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    marginBottom: spacing.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  durationBadge: {
    backgroundColor: colors.accent,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  durationText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.medium as any,
  },
  detailCard: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.md,
  },
  detailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  detailTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium as any,
    color: colors.text,
    marginLeft: spacing.sm,
    flex: 1,
  },
  locationName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  locationAddress: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  dateTimeGrid: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  dateTimeCard: {
    flex: 1,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: spacing.md,
  },
  dateTimeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  dateTimeLabel: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  dateTimeValue: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  timeValue: {
    fontSize: typography.sizes.sm,
    color: colors.accent,
    fontWeight: typography.weights.medium as any,
  },
  infoButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  insuranceOptions: {
    gap: spacing.md,
  },
  insuranceCard: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: spacing.md,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedInsuranceCard: {
    borderColor: colors.accent,
    backgroundColor: colors.white,
  },
  insuranceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.borderLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  selectedRadio: {
    borderColor: colors.accent,
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.accent,
  },
  insuranceInfo: {
    flex: 1,
  },
  insuranceName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  selectedInsuranceName: {
    color: colors.accent,
  },
  insuranceDescription: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  insurancePrice: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  selectedInsurancePrice: {
    color: colors.accent,
  },
  securityOptions: {
    gap: spacing.md,
  },
  securityCard: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: spacing.md,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedSecurityCard: {
    borderColor: colors.accent,
    backgroundColor: colors.white,
  },
  securityHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  securityInfo: {
    flex: 1,
  },
  securityName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  selectedSecurityName: {
    color: colors.accent,
  },
  securityDescription: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  securityFeatures: {
    gap: spacing.xs / 2,
  },
  featureItem: {
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    lineHeight: typography.sizes.xs * 1.4,
  },
  securityPrice: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  selectedSecurityPrice: {
    color: colors.accent,
  },
  alertOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  alertContainer: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.xl,
    width: '100%',
    maxWidth: 320,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
  },
  alertHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  alertIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.warning + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  alertTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  alertMessage: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.md,
    marginBottom: spacing.xl,
  },
  alertButton: {
    backgroundColor: colors.accent,
    borderRadius: 12,
    paddingVertical: spacing.md,
    alignItems: 'center',
  },
  alertButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.white,
  },
  priceBreakdown: {
    gap: spacing.sm,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  priceLabel: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
  },
  priceValue: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  priceDivider: {
    height: 1,
    backgroundColor: colors.borderLight,
    marginVertical: spacing.md,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: spacing.md,
  },
  totalLabel: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  totalValue: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.accent,
  },
  bottomBar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  totalPrice: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.accent,
  },
  totalDuration: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  paymentButton: {
    flex: 1,
    marginLeft: spacing.lg,
  },
});