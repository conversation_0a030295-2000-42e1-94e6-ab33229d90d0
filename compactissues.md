(base)  🐍 base  mac@Macs-MacBook-Pro  ~/Desktop/CurrentProject/cultureConnect/culture_connect  flutter run -d "iPhone 15 Pro Max"   
Launching lib/main.dart on iPhone 15 Pro Max in debug mode...
Running pod install...                                              6.3s
CocoaPods' output:
↳
      Preparing

    Analyzing dependencies

    Inspecting targets to integrate
      Using `ARCHS` setting to build architectures of target `Pods-Runner`: (``)
      Using `ARCHS` setting to build architectures of target `Pods-RunnerTests`: (``)

    Finding Podfile changes
      - Flutter
      - audio_session
      - battery_plus
      - camera_avfoundation
      - cloud_firestore
      - connectivity_plus
      - device_info_plus
      - disk_space
      - file_picker
      - firebase_analytics
      - firebase_app_check
      - firebase_auth
      - firebase_core
      - firebase_crashlytics
      - firebase_messaging
      - firebase_performance
      - firebase_storage
      - flutter_image_compress_common
      - flutter_local_notifications
      - flutter_native_splash
      - flutter_pdfview
      - flutter_secure_storage
      - geolocator_apple
      - google_maps_flutter_ios
      - google_mlkit_commons
      - google_mlkit_language_id
      - google_mlkit_text_recognition
      - image_picker_ios
      - integration_test
      - just_audio
      - local_auth_darwin
      - location
      - open_file_ios
      - package_info_plus
      - path_provider_foundation
      - permission_handler_apple
      - record_darwin
      - sensors_plus
      - share_plus
      - shared_preferences_foundation
      - smart_auth
      - speech_to_text
      - sqflite_darwin
      - stripe_ios
      - url_launcher_ios
      - video_compress
      - video_player_avfoundation
      - webview_flutter_wkwebview
      - workmanager

    Fetching external sources
    -> Fetching podspec for `Flutter` from `Flutter`
    -> Fetching podspec for `audio_session` from `.symlinks/plugins/audio_session/ios`
    -> Fetching podspec for `battery_plus` from `.symlinks/plugins/battery_plus/ios`
    -> Fetching podspec for `camera_avfoundation` from `.symlinks/plugins/camera_avfoundation/ios`
    -> Fetching podspec for `cloud_firestore` from `.symlinks/plugins/cloud_firestore/ios`
    cloud_firestore: Using Firebase SDK version '10.25.0' defined in 'firebase_core'
    -> Fetching podspec for `connectivity_plus` from `.symlinks/plugins/connectivity_plus/ios`
    -> Fetching podspec for `device_info_plus` from `.symlinks/plugins/device_info_plus/ios`
    -> Fetching podspec for `disk_space` from `.symlinks/plugins/disk_space/ios`
    -> Fetching podspec for `file_picker` from `.symlinks/plugins/file_picker/ios`
    -> Fetching podspec for `firebase_analytics` from `.symlinks/plugins/firebase_analytics/ios`
    firebase_analytics: Using Firebase SDK version '10.25.0' defined in 'firebase_core'
    -> Fetching podspec for `firebase_app_check` from `.symlinks/plugins/firebase_app_check/ios`
    firebase_app_check: Using Firebase SDK version '10.25.0' defined in 'firebase_core'
    -> Fetching podspec for `firebase_auth` from `.symlinks/plugins/firebase_auth/ios`
    firebase_auth: Using Firebase SDK version '10.25.0' defined in 'firebase_core'
    -> Fetching podspec for `firebase_core` from `.symlinks/plugins/firebase_core/ios`
    firebase_core: Using Firebase SDK version '10.25.0' defined in 'firebase_core'
    -> Fetching podspec for `firebase_crashlytics` from `.symlinks/plugins/firebase_crashlytics/ios`
    Warning: firebase_app_id_file.json file does not exist. This may cause issues in upload-symbols. If this error is unexpected, try running flutterfire
    configure again.
    firebase_crashlytics: Using Firebase SDK version '10.25.0' defined in 'firebase_core'
    -> Fetching podspec for `firebase_messaging` from `.symlinks/plugins/firebase_messaging/ios`
    firebase_messaging: Using Firebase SDK version '10.25.0' defined in 'firebase_core'
    -> Fetching podspec for `firebase_performance` from `.symlinks/plugins/firebase_performance/ios`
    firebase_performance: Using Firebase SDK version '10.25.0' defined in 'firebase_core'
    -> Fetching podspec for `firebase_storage` from `.symlinks/plugins/firebase_storage/ios`
    firebase_storage: Using Firebase SDK version '10.25.0' defined in 'firebase_core'
    -> Fetching podspec for `flutter_image_compress_common` from `.symlinks/plugins/flutter_image_compress_common/ios`
    -> Fetching podspec for `flutter_local_notifications` from `.symlinks/plugins/flutter_local_notifications/ios`
    -> Fetching podspec for `flutter_native_splash` from `.symlinks/plugins/flutter_native_splash/ios`
    -> Fetching podspec for `flutter_pdfview` from `.symlinks/plugins/flutter_pdfview/ios`
    -> Fetching podspec for `flutter_secure_storage` from `.symlinks/plugins/flutter_secure_storage/ios`
    -> Fetching podspec for `geolocator_apple` from `.symlinks/plugins/geolocator_apple/ios`
    -> Fetching podspec for `google_maps_flutter_ios` from `.symlinks/plugins/google_maps_flutter_ios/ios`
    -> Fetching podspec for `google_mlkit_commons` from `.symlinks/plugins/google_mlkit_commons/ios`
    -> Fetching podspec for `google_mlkit_language_id` from `.symlinks/plugins/google_mlkit_language_id/ios`
    -> Fetching podspec for `google_mlkit_text_recognition` from `.symlinks/plugins/google_mlkit_text_recognition/ios`
    -> Fetching podspec for `image_picker_ios` from `.symlinks/plugins/image_picker_ios/ios`
    -> Fetching podspec for `integration_test` from `.symlinks/plugins/integration_test/ios`
    -> Fetching podspec for `just_audio` from `.symlinks/plugins/just_audio/darwin`
    -> Fetching podspec for `local_auth_darwin` from `.symlinks/plugins/local_auth_darwin/darwin`
    -> Fetching podspec for `location` from `.symlinks/plugins/location/ios`
    -> Fetching podspec for `open_file_ios` from `.symlinks/plugins/open_file_ios/ios`
    -> Fetching podspec for `package_info_plus` from `.symlinks/plugins/package_info_plus/ios`
    -> Fetching podspec for `path_provider_foundation` from `.symlinks/plugins/path_provider_foundation/darwin`
    -> Fetching podspec for `permission_handler_apple` from `.symlinks/plugins/permission_handler_apple/ios`
    -> Fetching podspec for `record_darwin` from `.symlinks/plugins/record_darwin/ios`
    -> Fetching podspec for `sensors_plus` from `.symlinks/plugins/sensors_plus/ios`
    -> Fetching podspec for `share_plus` from `.symlinks/plugins/share_plus/ios`
    -> Fetching podspec for `shared_preferences_foundation` from `.symlinks/plugins/shared_preferences_foundation/darwin`
    -> Fetching podspec for `smart_auth` from `.symlinks/plugins/smart_auth/ios`
    -> Fetching podspec for `speech_to_text` from `.symlinks/plugins/speech_to_text/ios`
    -> Fetching podspec for `sqflite_darwin` from `.symlinks/plugins/sqflite_darwin/darwin`
    -> Fetching podspec for `stripe_ios` from `.symlinks/plugins/stripe_ios/ios`
    -> Fetching podspec for `url_launcher_ios` from `.symlinks/plugins/url_launcher_ios/ios`
    -> Fetching podspec for `video_compress` from `.symlinks/plugins/video_compress/ios`
    -> Fetching podspec for `video_player_avfoundation` from `.symlinks/plugins/video_player_avfoundation/darwin`
    -> Fetching podspec for `webview_flutter_wkwebview` from `.symlinks/plugins/webview_flutter_wkwebview/darwin`
    -> Fetching podspec for `workmanager` from `.symlinks/plugins/workmanager/ios`

    Resolving dependencies of `Podfile`
      CDN: trunk Relative path: CocoaPods-version.yml exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: all_pods_versions_4_2_c.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/4/2/c/FlutterMacOS/3.16.0/FlutterMacOS.podspec.json exists! Returning local because checking is only performed in repo
      update
      CDN: trunk Relative path: all_pods_versions_0_3_5.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/0/3/5/Firebase/12.1.0/Firebase.podspec.json exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/0/3/5/Firebase/10.25.0/Firebase.podspec.json exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: all_pods_versions_1_6_1.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/1/6/1/DKImagePickerController/4.3.9/DKImagePickerController.podspec.json exists! Returning local because checking is only
      performed in repo update
      CDN: trunk Relative path: Specs/1/6/1/DKImagePickerController/4.3.9/DKImagePickerController.podspec.json exists! Returning local because checking is only
      performed in repo update
      CDN: trunk Relative path: all_pods_versions_c_e_7.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/c/e/7/Stripe/24.20.0/Stripe.podspec.json exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: all_pods_versions_f_d_6.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/f/d/6/StripePaymentSheet/24.20.0/StripePaymentSheet.podspec.json exists! Returning local because checking is only
      performed in repo update
      CDN: trunk Relative path: all_pods_versions_d_6_6.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/d/6/6/StripePayments/24.20.0/StripePayments.podspec.json exists! Returning local because checking is only performed in
      repo update
      CDN: trunk Relative path: all_pods_versions_d_c_9.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/d/c/9/StripePaymentsUI/24.20.0/StripePaymentsUI.podspec.json exists! Returning local because checking is only performed in
      repo update
      CDN: trunk Relative path: all_pods_versions_3_6_1.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/3/6/1/StripeApplePay/24.20.0/StripeApplePay.podspec.json exists! Returning local because checking is only performed in
      repo update
      CDN: trunk Relative path: all_pods_versions_4_f_c.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/4/f/c/StripeFinancialConnections/24.20.0/StripeFinancialConnections.podspec.json exists! Returning local because checking
      is only performed in repo update
      CDN: trunk Relative path: all_pods_versions_a_d_d.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/a/d/d/GoogleMaps/10.2.0/GoogleMaps.podspec.json exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: all_pods_versions_3_5_e.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/3/5/e/Google-Maps-iOS-Utils/6.1.0/Google-Maps-iOS-Utils.podspec.json exists! Returning local because checking is only
      performed in repo update
      CDN: trunk Relative path: all_pods_versions_8_1_e.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/8/1/e/MLKitVision/10.0.0/MLKitVision.podspec.json exists! Returning local because checking is only performed in repo
      update
      CDN: trunk Relative path: all_pods_versions_b_e_b.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/b/e/b/GoogleMLKit/9.0.0/GoogleMLKit.podspec.json exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/0/3/5/Firebase/10.25.0/Firebase.podspec.json exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/0/3/5/Firebase/10.25.0/Firebase.podspec.json exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: all_pods_versions_5_d_c.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/5/d/c/Mantle/2.2.0/Mantle.podspec.json exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: all_pods_versions_1_1_7.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/1/1/7/SDWebImage/5.21.1/SDWebImage.podspec.json exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: all_pods_versions_e_9_d.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/e/9/d/SDWebImageWebPCoder/0.14.6/SDWebImageWebPCoder.podspec.json exists! Returning local because checking is only
      performed in repo update
      CDN: trunk Relative path: Specs/0/3/5/Firebase/10.25.0/Firebase.podspec.json exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/0/3/5/Firebase/10.25.0/Firebase.podspec.json exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/0/3/5/Firebase/10.25.0/Firebase.podspec.json exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/0/3/5/Firebase/10.25.0/Firebase.podspec.json exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: all_pods_versions_d_d_6.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/d/d/6/ReachabilitySwift/5.2.4/ReachabilitySwift.podspec.json exists! Returning local because checking is only performed in
      repo update
      CDN: trunk Relative path: all_pods_versions_e_e_a.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/e/e/a/Try/2.1.1/Try.podspec.json exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/0/3/5/Firebase/10.25.0/Firebase.podspec.json exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/0/3/5/Firebase/10.25.0/Firebase.podspec.json exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: all_pods_versions_1_8_e.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/1/8/e/FirebaseAppCheck/12.1.0/FirebaseAppCheck.podspec.json exists! Returning local because checking is only performed in
      repo update
      CDN: trunk Relative path: Specs/1/8/e/FirebaseAppCheck/10.25.0/FirebaseAppCheck.podspec.json exists! Returning local because checking is only performed in
      repo update
      CDN: trunk Relative path: all_pods_versions_3_b_6.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/3/b/6/AppCheckCore/11.2.0/AppCheckCore.podspec.json exists! Returning local because checking is only performed in repo
      update
      CDN: trunk Relative path: all_pods_versions_e_9_8.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/e/9/8/FirebaseAppCheckInterop/12.1.0/FirebaseAppCheckInterop.podspec.json exists! Returning local because checking is only
      performed in repo update
      CDN: trunk Relative path: all_pods_versions_8_b_d.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/8/b/d/FirebaseCore/12.1.0/FirebaseCore.podspec.json exists! Returning local because checking is only performed in repo
      update
      CDN: trunk Relative path: all_pods_versions_0_b_5.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/0/b/5/PromisesObjC/2.4.0/PromisesObjC.podspec.json exists! Returning local because checking is only performed in repo
      update
      CDN: trunk Relative path: all_pods_versions_0_8_4.txt exists! Returning local because checking is only performed in repo update
      CDN: trunk Relative path: Specs/0/8/4/GoogleUtilities/8.1.0/GoogleUtilities.podspec.json exists! Returning local because checking is only performed in
      repo update
      CDN: trunk Relative path: Specs/0/8/4/GoogleUtilities/7.13.3/GoogleUtilities.podspec.json exists! Returning local because checking is only performed in
      repo update
      CDN: trunk Relative path: Specs/0/8/4/GoogleUtilities/7.13.3/GoogleUtilities.podspec.json exists! Returning local because checking is only performed in
      repo update
      CDN: trunk Relative path: Specs/b/e/b/GoogleMLKit/7.0.0/GoogleMLKit.podspec.json exists! Returning local because checking is only performed in repo update
    [!] CocoaPods could not find compatible versions for pod "GoogleMLKit/TextRecognition":
      In snapshot (Podfile.lock):
        GoogleMLKit/TextRecognition (= 4.0.0, ~> 4.0.0)

      In Podfile:
        google_mlkit_text_recognition (from `.symlinks/plugins/google_mlkit_text_recognition/ios`) was resolved to 0.15.0, which depends on
          GoogleMLKit/TextRecognition (~> 7.0.0)


    You have either:
     * out-of-date source repos which you can update with `pod repo update` or with `pod install --repo-update`.
     * changed the constraints of dependency `GoogleMLKit/TextRecognition` inside your development pod `google_mlkit_text_recognition`.
       You should run `pod update GoogleMLKit/TextRecognition` to apply changes you've made.

    /usr/local/lib/ruby/gems/3.4.0/gems/molinillo-0.8.0/lib/molinillo/resolution.rb:317:in 'Molinillo::Resolver::Resolution#raise_error_unless_state'
    /usr/local/lib/ruby/gems/3.4.0/gems/molinillo-0.8.0/lib/molinillo/resolution.rb:299:in 'block in Molinillo::Resolver::Resolution#unwind_for_conflict'
    <internal:kernel>:91:in 'Kernel#tap'
    /usr/local/lib/ruby/gems/3.4.0/gems/molinillo-0.8.0/lib/molinillo/resolution.rb:297:in 'Molinillo::Resolver::Resolution#unwind_for_conflict'
    /usr/local/lib/ruby/gems/3.4.0/gems/molinillo-0.8.0/lib/molinillo/resolution.rb:257:in 'Molinillo::Resolver::Resolution#process_topmost_state'
    /usr/local/lib/ruby/gems/3.4.0/gems/molinillo-0.8.0/lib/molinillo/resolution.rb:182:in 'Molinillo::Resolver::Resolution#resolve'
    /usr/local/lib/ruby/gems/3.4.0/gems/molinillo-0.8.0/lib/molinillo/resolver.rb:43:in 'Molinillo::Resolver#resolve'
    /usr/local/lib/ruby/gems/3.4.0/gems/cocoapods-1.16.2/lib/cocoapods/resolver.rb:94:in 'Pod::Resolver#resolve'
    /usr/local/lib/ruby/gems/3.4.0/gems/cocoapods-1.16.2/lib/cocoapods/installer/analyzer.rb:1082:in 'block in Pod::Installer::Analyzer#resolve_dependencies'
    /usr/local/lib/ruby/gems/3.4.0/gems/cocoapods-1.16.2/lib/cocoapods/user_interface.rb:64:in 'Pod::UserInterface.section'
    /usr/local/lib/ruby/gems/3.4.0/gems/cocoapods-1.16.2/lib/cocoapods/installer/analyzer.rb:1080:in 'Pod::Installer::Analyzer#resolve_dependencies'
    /usr/local/lib/ruby/gems/3.4.0/gems/cocoapods-1.16.2/lib/cocoapods/installer/analyzer.rb:125:in 'Pod::Installer::Analyzer#analyze'
    /usr/local/lib/ruby/gems/3.4.0/gems/cocoapods-1.16.2/lib/cocoapods/installer.rb:422:in 'Pod::Installer#analyze'
    /usr/local/lib/ruby/gems/3.4.0/gems/cocoapods-1.16.2/lib/cocoapods/installer.rb:244:in 'block in Pod::Installer#resolve_dependencies'
    /usr/local/lib/ruby/gems/3.4.0/gems/cocoapods-1.16.2/lib/cocoapods/user_interface.rb:64:in 'Pod::UserInterface.section'
    /usr/local/lib/ruby/gems/3.4.0/gems/cocoapods-1.16.2/lib/cocoapods/installer.rb:243:in 'Pod::Installer#resolve_dependencies'
    /usr/local/lib/ruby/gems/3.4.0/gems/cocoapods-1.16.2/lib/cocoapods/installer.rb:162:in 'Pod::Installer#install!'
    /usr/local/lib/ruby/gems/3.4.0/gems/cocoapods-1.16.2/lib/cocoapods/command/install.rb:52:in 'Pod::Command::Install#run'
    /usr/local/lib/ruby/gems/3.4.0/gems/claide-1.1.0/lib/claide/command.rb:334:in 'CLAide::Command.run'
    /usr/local/lib/ruby/gems/3.4.0/gems/cocoapods-1.16.2/lib/cocoapods/command.rb:52:in 'Pod::Command.run'
    /usr/local/lib/ruby/gems/3.4.0/gems/cocoapods-1.16.2/bin/pod:55:in '<top (required)>'
    /usr/local/Cellar/cocoapods/1.16.2_1/libexec/bin/pod:25:in 'Kernel#load'
    /usr/local/Cellar/cocoapods/1.16.2_1/libexec/bin/pod:25:in '<main>'

Error output from CocoaPods:
↳
    Ignoring ffi-1.17.0 because its extensions are not built. Try: gem pristine ffi --version 1.17.0

Error: CocoaPods's specs repository is too out-of-date to satisfy dependencies.
To update the CocoaPods specs, run:
  pod repo update



Error running pod install
Error launching application on iPhone 15 Pro Max.
(base)  ✘  🐍 base  mac@Macs-MacBook-Pro  ~/Desktop/CurrentProject/cultureConnect/culture_connect  