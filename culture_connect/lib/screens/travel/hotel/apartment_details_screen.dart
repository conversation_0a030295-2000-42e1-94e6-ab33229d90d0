import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/travel/hotel.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/services/wishlist_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/widgets/travel/share_travel_service_dialog.dart';
import 'package:culture_connect/screens/payment/universal_payment_screen.dart';

/// A specialized detail screen for short-let apartments with apartment-specific content
class ApartmentDetailsScreen extends ConsumerStatefulWidget {
  /// The apartment (represented as Hotel model) to display
  final Hotel apartment;

  /// Creates a new apartment details screen
  const ApartmentDetailsScreen({
    super.key,
    required this.apartment,
  });

  @override
  ConsumerState<ApartmentDetailsScreen> createState() =>
      _ApartmentDetailsScreenState();
}

class _ApartmentDetailsScreenState extends ConsumerState<ApartmentDetailsScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  final ScrollController _scrollController = ScrollController();

  // Booking state variables
  DateTime _checkInDate = DateTime.now().add(const Duration(days: 1));
  DateTime _checkOutDate = DateTime.now().add(const Duration(days: 3));
  int _nights = 2;
  int _guestCount = 2;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// Show share dialog for apartment
  void _showShareDialog() {
    showDialog(
      context: context,
      builder: (context) => ShareTravelServiceDialog(
        travelService: widget.apartment,
      ),
    );
  }

  /// Build favorite button with persistent state management
  Widget _buildFavoriteButton(ThemeData theme) {
    final isInWishlist = ref.watch(isInWishlistProvider(widget.apartment.id));

    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(242), // 0.95 opacity
        borderRadius: BorderRadius.circular(14),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(26), // 0.1 opacity
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: IconButton(
          key: ValueKey(isInWishlist),
          icon: Icon(
            isInWishlist ? Icons.favorite : Icons.favorite_border_outlined,
            color: isInWishlist ? Colors.red : Colors.black87,
          ),
          onPressed: () => _toggleFavorite(isInWishlist),
          splashRadius: 20,
        ),
      ),
    );
  }

  /// Toggle favorite status with persistent storage
  Future<void> _toggleFavorite(bool currentStatus) async {
    final wishlistNotifier = ref.read(wishlistProvider.notifier);
    final loggingService = ref.read(loggingServiceProvider);

    try {
      // Add haptic feedback
      HapticFeedback.lightImpact();

      // Toggle wishlist status
      await wishlistNotifier.toggleWishlist(widget.apartment.id);

      // Show appropriate feedback
      if (mounted) {
        final newStatus = !currentStatus;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              newStatus ? 'Added to favorites' : 'Removed from favorites',
            ),
            backgroundColor: newStatus ? Colors.green : Colors.orange,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            duration: const Duration(seconds: 2),
            action: newStatus
                ? null
                : SnackBarAction(
                    label: 'Undo',
                    textColor: Colors.white,
                    onPressed: () async {
                      // Undo the action
                      await wishlistNotifier
                          .toggleWishlist(widget.apartment.id);
                    },
                  ),
          ),
        );
      }

      // Log the action
      loggingService.info(
        'ApartmentDetailsScreen',
        'Apartment ${currentStatus ? 'removed from' : 'added to'} favorites',
        {
          'apartment_id': widget.apartment.id,
          'apartment_name': widget.apartment.name,
          'action': currentStatus ? 'remove' : 'add',
        },
      );
    } catch (e, stackTrace) {
      // Log error
      loggingService.error(
        'ApartmentDetailsScreen',
        'Failed to toggle favorite status',
        e,
        stackTrace,
      );

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                const Text('Failed to update favorites. Please try again.'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Hero Image Section
            _buildHeroSection(),

            // Scrollable Content
            Expanded(
              child: SingleChildScrollView(
                controller: _scrollController,
                child: Column(
                  children: [
                    // Apartment Info Section
                    _buildApartmentInfoSection(),

                    // Quick Features Section
                    _buildQuickFeaturesSection(),

                    // Apartment-specific Sections
                    _buildApartmentSpecificSections(),

                    // Bottom padding for floating button
                    const SizedBox(height: 100),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      // Floating Book Now Button
      floatingActionButton: _buildFloatingBookButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildHeroSection() {
    return Container(
      height: 300,
      child: Stack(
        children: [
          // Hero Image
          Hero(
            tag: 'apartment_image_${widget.apartment.id}',
            child: Container(
              width: double.infinity,
              height: 300,
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
                child: Image.network(
                  widget.apartment.imageUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: AppTheme.surfaceVariant,
                      child: const Icon(
                        Icons.apartment,
                        size: 48,
                        color: AppTheme.textSecondaryColor,
                      ),
                    );
                  },
                ),
              ),
            ),
          ),

          // Gradient Overlay
          Container(
            height: 300,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(24),
                bottomRight: Radius.circular(24),
              ),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withAlpha(77),
                ],
              ),
            ),
          ),

          // Safe Area for Status Bar
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Back Button
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(230),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.black),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),

                  // Action Buttons
                  Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(242), // 0.95 opacity
                          borderRadius: BorderRadius.circular(14),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(26), // 0.1 opacity
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: IconButton(
                          icon: const Icon(Icons.share_outlined,
                              color: Colors.black87),
                          onPressed: _showShareDialog,
                          splashRadius: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      _buildFavoriteButton(Theme.of(context)),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Apartment Badge
          Positioned(
            bottom: 16,
            left: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppTheme.secondaryColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.apartment,
                    color: Colors.white,
                    size: 16,
                  ),
                  SizedBox(width: 6),
                  Text(
                    'Short-let Apartment',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildApartmentInfoSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(8),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Apartment Name and Rating
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.apartment.name,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(
                          Icons.apartment,
                          size: 18,
                          color: AppTheme.secondaryColor,
                        ),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            'Self-catering Apartment',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.secondaryColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        size: 16,
                        color: Colors.amber,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        widget.apartment.rating.toStringAsFixed(1),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${widget.apartment.reviewCount} reviews',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Location
          Row(
            children: [
              const Icon(
                Icons.location_on_outlined,
                size: 18,
                color: Colors.grey,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  widget.apartment.location,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Price Section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  flex: 3,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Starting from',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.apartment.formattedPrice,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                const Flexible(
                  flex: 1,
                  child: Text(
                    '/night',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickFeaturesSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildQuickFeatureButton(
              icon: Icons.kitchen,
              label: 'Full Kitchen',
              onTap: () {},
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildQuickFeatureButton(
              icon: Icons.wifi,
              label: 'Free WiFi',
              onTap: () {},
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildQuickFeatureButton(
              icon: Icons.local_laundry_service,
              label: 'Laundry',
              onTap: () {},
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickFeatureButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(8),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
            child: Column(
              children: [
                Icon(
                  icon,
                  size: 24,
                  color: AppTheme.secondaryColor,
                ),
                const SizedBox(height: 8),
                Text(
                  label,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildApartmentSpecificSections() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(8),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Apartment Features
          _buildSectionHeader('Apartment Features'),
          _buildApartmentFeatures(),

          const Divider(height: 32),

          // Living Spaces
          _buildSectionHeader('Living Spaces'),
          _buildLivingSpaces(),

          const Divider(height: 32),

          // House Rules
          _buildSectionHeader('House Rules'),
          _buildHouseRules(),

          const Divider(height: 32),

          // Local Area
          _buildSectionHeader('Local Area'),
          _buildLocalArea(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildApartmentFeatures() {
    final features = [
      {
        'icon': Icons.kitchen,
        'title': 'Full Kitchen',
        'subtitle': 'Refrigerator, stove, microwave, dishwasher'
      },
      {
        'icon': Icons.local_laundry_service,
        'title': 'Washer & Dryer',
        'subtitle': 'In-unit laundry facilities'
      },
      {
        'icon': Icons.wifi,
        'title': 'High-speed WiFi',
        'subtitle': 'Complimentary internet access'
      },
      {
        'icon': Icons.tv,
        'title': 'Smart TV',
        'subtitle': 'Netflix, streaming services available'
      },
      {
        'icon': Icons.ac_unit,
        'title': 'Air Conditioning',
        'subtitle': 'Climate control in all rooms'
      },
      {
        'icon': Icons.local_parking,
        'title': 'Parking',
        'subtitle': 'Dedicated parking space included'
      },
    ];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: features
            .map((feature) => _buildFeatureItem(
                  icon: feature['icon'] as IconData,
                  title: feature['title'] as String,
                  subtitle: feature['subtitle'] as String,
                ))
            .toList(),
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.secondaryColor.withAlpha(26),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 20,
              color: AppTheme.secondaryColor,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLivingSpaces() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          _buildSpaceItem('Living Room',
              'Comfortable seating area with sofa and coffee table'),
          _buildSpaceItem('Bedroom', 'Queen-size bed with premium linens'),
          _buildSpaceItem('Kitchen', 'Fully equipped with modern appliances'),
          _buildSpaceItem('Bathroom', 'Full bathroom with shower and bathtub'),
          _buildSpaceItem('Balcony', 'Private outdoor space with city views'),
        ],
      ),
    );
  }

  Widget _buildSpaceItem(String space, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 6),
            decoration: const BoxDecoration(
              color: AppTheme.secondaryColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  space,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHouseRules() {
    final rules = [
      'Check-in: 3:00 PM - 10:00 PM',
      'Check-out: 11:00 AM',
      'No smoking inside the apartment',
      'No pets allowed',
      'Maximum 4 guests',
      'Quiet hours: 10:00 PM - 8:00 AM',
    ];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: rules
            .map((rule) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(
                        Icons.check_circle_outline,
                        size: 16,
                        color: AppTheme.successColor,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          rule,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildLocalArea() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          _buildLocalAreaItem('Grocery Store', '2 min walk'),
          _buildLocalAreaItem('Public Transport', '5 min walk'),
          _buildLocalAreaItem('Restaurant District', '8 min walk'),
          _buildLocalAreaItem('City Center', '15 min by metro'),
        ],
      ),
    );
  }

  Widget _buildLocalAreaItem(String place, String distance) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            place,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black,
            ),
          ),
          Text(
            distance,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingBookButton() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ElevatedButton(
        onPressed: () => _navigateToPayment(),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.secondaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 8,
        ),
        child: Text(
          'Book Apartment - ${widget.apartment.formattedPrice}',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// Navigate to Universal Payment Screen with apartment booking details
  void _navigateToPayment() {
    // Add haptic feedback
    HapticFeedback.mediumImpact();

    // Create booking object for payment
    final now = DateTime.now();
    final booking = Booking(
      id: 'apartment_${now.millisecondsSinceEpoch}',
      experienceId: widget.apartment.id,
      date: _checkInDate,
      timeSlot: TimeSlot(
        startTime: DateTime(
            _checkInDate.year,
            _checkInDate.month,
            _checkInDate.day,
            widget.apartment.checkInTime.hour,
            widget.apartment.checkInTime.minute),
        endTime: DateTime(
            _checkOutDate.year,
            _checkOutDate.month,
            _checkOutDate.day,
            widget.apartment.checkOutTime.hour,
            widget.apartment.checkOutTime.minute),
      ),
      participantCount: _guestCount,
      totalAmount: widget.apartment.price * _nights,
      status: BookingStatus.pending,
      specialRequirements: 'Short-let apartment booking',
      createdAt: now,
      updatedAt: now,
    );

    // Create apartment-specific booking details
    final bookingDetails = {
      'hotelName': widget.apartment.name,
      'hotelLocation': widget.apartment.location,
      'starRating': widget.apartment.rating.round(),
      'roomType': 'Apartment',
      'roomDescription': 'Self-catering apartment accommodation',
      'checkIn': DateFormat('MMM dd, yyyy').format(_checkInDate),
      'checkOut': DateFormat('MMM dd, yyyy').format(_checkOutDate),
      'nights': _nights,
      'guests': _guestCount,
      'basePrice': widget.apartment.price,
      'totalPrice': widget.apartment.price * _nights,
      'currency': widget.apartment.currency,
      'cancellationPolicy': widget.apartment.cancellationPolicy,
      'hotelImage': widget.apartment.imageUrl,
      'amenities': widget.apartment.amenities,
    };

    // Navigate to Universal Payment Screen
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UniversalPaymentScreen(
          booking: booking,
          userEmail: '<EMAIL>', // TODO: Get from user provider
          userName: 'User Name', // TODO: Get from user provider
          userPhone: '+**********', // TODO: Get from user provider
          bookingDetails: bookingDetails,
        ),
      ),
    );
  }
}
