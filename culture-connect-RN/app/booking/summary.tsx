import React, { useState } from "react";
import { StyleSheet, View, Text, ScrollView, Pressable } from "react-native";
import { useRouter } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { ArrowLeft, CreditCard, Shield } from "lucide-react-native";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { Button } from "@/components/Button";
import { BookingSummaryCard } from "@/components/BookingSummaryCard";

const bookingSummary = [
  {
    id: "experience",
    title: "Kyoto Gardens Experience",
    subtitle: "Cultural tour with local guide",
    price: "$50",
    status: "booked" as const,
    details: [
      "Date: August 15, 2025",
      "Time: 10:00 AM - 2:00 PM",
      "Group size: 2 people",
      "Includes: Guide, entrance fees, refreshments"
    ],
  },
  {
    id: "flight",
    title: "Round-trip Flight",
    subtitle: "NYC ↔ Tokyo via Japan Airlines",
    price: "$900",
    status: "booked" as const,
    details: [
      "Outbound: Aug 15, 8:30 AM - 2:45 PM +1",
      "Return: Aug 25, 6:15 PM - 11:30 PM",
      "Passengers: 2 adults",
      "Confirmation: JL2847"
    ],
  },
  {
    id: "hotel",
    title: "Hotel Accommodation",
    subtitle: "5 nights at Tokyo Grand Hotel",
    price: "$600",
    status: "booked" as const,
    details: [
      "Check-in: August 15, 2025",
      "Check-out: August 20, 2025",
      "Room: Deluxe Twin Room",
      "Includes: Breakfast, WiFi, Gym access"
    ],
  },
  {
    id: "insurance",
    title: "Travel Insurance",
    subtitle: "Comprehensive coverage",
    price: "$45",
    status: "booked" as const,
    details: [
      "Coverage: Medical, trip cancellation, baggage",
      "Duration: 10 days",
      "Provider: SafeTravel Insurance",
      "Policy: ST-2025-789456"
    ],
  },
];

export default function BookingSummaryScreen() {
  const router = useRouter();
  const [expandedCards, setExpandedCards] = useState<string[]>([]);

  const handleToggleExpand = (cardId: string) => {
    setExpandedCards(prev => 
      prev.includes(cardId) 
        ? prev.filter(id => id !== cardId)
        : [...prev, cardId]
    );
  };

  const handleDownload = (cardId: string) => {
    alert(`Downloading receipt for ${cardId}`);
  };

  const handleConfirmBooking = () => {
    router.push("/booking/final-confirmation");
  };

  const totalAmount = bookingSummary.reduce((sum, item) => 
    sum + parseInt(item.price.replace('$', '')), 0
  );

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <View style={styles.header}>
        <Pressable style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </Pressable>
        <Text style={styles.headerTitle}>Booking Summary</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.summaryHeader}>
          <Text style={styles.summaryTitle}>Your Complete Trip</Text>
          <Text style={styles.summarySubtitle}>
            Review all your bookings and confirm your amazing journey to Tokyo
          </Text>
        </View>

        <View style={styles.bookingsList}>
          {bookingSummary.map((booking) => (
            <BookingSummaryCard
              key={booking.id}
              title={booking.title}
              subtitle={booking.subtitle}
              price={booking.price}
              status={booking.status}
              details={booking.details}
              isExpanded={expandedCards.includes(booking.id)}
              onToggleExpand={() => handleToggleExpand(booking.id)}
              onDownload={() => handleDownload(booking.id)}
            />
          ))}
        </View>

        <View style={styles.totalContainer}>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Subtotal</Text>
            <Text style={styles.totalValue}>${totalAmount}</Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Taxes & Fees</Text>
            <Text style={styles.totalValue}>$89</Text>
          </View>
          <View style={[styles.totalRow, styles.grandTotalRow]}>
            <Text style={styles.grandTotalLabel}>Total Amount</Text>
            <Text style={styles.grandTotalValue}>${totalAmount + 89}</Text>
          </View>
        </View>

        <View style={styles.paymentSection}>
          <Text style={styles.sectionTitle}>Payment Method</Text>
          <View style={styles.paymentCard}>
            <CreditCard size={24} color={colors.primary} />
            <View style={styles.paymentInfo}>
              <Text style={styles.paymentMethod}>Visa ending in 4582</Text>
              <Text style={styles.paymentDetails}>Expires 12/27</Text>
            </View>
            <Pressable>
              <Text style={styles.changeText}>Change</Text>
            </Pressable>
          </View>
        </View>

        <View style={styles.protectionSection}>
          <View style={styles.protectionHeader}>
            <Shield size={20} color={colors.success} />
            <Text style={styles.protectionTitle}>Your booking is protected</Text>
          </View>
          <Text style={styles.protectionText}>
            All bookings are secured with our comprehensive protection policy. 
            Free cancellation up to 24 hours before your trip.
          </Text>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <View style={styles.footerContent}>
          <View>
            <Text style={styles.footerLabel}>Total</Text>
            <Text style={styles.footerPrice}>${totalAmount + 89}</Text>
          </View>
          <Button
            title="Confirm & Pay"
            onPress={handleConfirmBooking}
            variant="primary"
            size="large"
          />
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  headerTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  content: {
    flex: 1,
  },
  summaryHeader: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
    alignItems: "center",
  },
  summaryTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.sm,
    textAlign: "center",
  },
  summarySubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: "center",
    lineHeight: typography.lineHeights.relaxed * typography.sizes.md,
  },
  bookingsList: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  totalContainer: {
    backgroundColor: colors.white,
    marginHorizontal: spacing.lg,
    borderRadius: 12,
    padding: spacing.lg,
    marginBottom: spacing.xl,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: spacing.sm,
  },
  totalLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
  },
  totalValue: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.text,
  },
  grandTotalRow: {
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    marginTop: spacing.sm,
    paddingTop: spacing.md,
  },
  grandTotalLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  grandTotalValue: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.primary,
  },
  paymentSection: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.md,
  },
  paymentCard: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  paymentInfo: {
    flex: 1,
    marginLeft: spacing.md,
  },
  paymentMethod: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  paymentDetails: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  changeText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.primary,
  },
  protectionSection: {
    backgroundColor: colors.success + "10",
    marginHorizontal: spacing.lg,
    borderRadius: 12,
    padding: spacing.lg,
    marginBottom: spacing.xl,
  },
  protectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  protectionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium as any,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  protectionText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
  },
  footer: {
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    shadowColor: colors.shadowDark,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 10,
  },
  footerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  footerLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  footerPrice: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
});