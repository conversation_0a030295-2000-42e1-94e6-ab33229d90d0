// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Project imports
import 'package:culture_connect/models/travel/insurance/insurance.dart'
    hide InsuranceCoverageType, InsuranceClaimStatus;
import 'package:culture_connect/models/travel/insurance/insurance_coverage_type.dart';
import 'package:culture_connect/models/travel/insurance/insurance_claim_status.dart';
import 'package:culture_connect/widgets/travel/insurance/insurance_card.dart';
import 'package:culture_connect/widgets/travel/insurance/insurance_policy_card.dart';
import 'package:culture_connect/widgets/travel/insurance/insurance_claim_card.dart';
import 'package:culture_connect/widgets/travel/insurance/coverage_breakdown_widget.dart';
import 'package:culture_connect/widgets/travel/insurance/claim_status_widget.dart';

// Test imports
import '../../../utils/test_helpers.dart';

void main() {
  group('Insurance Widget Tests', () {
    // Test data
    late InsuranceProvider testProvider;
    late InsurancePolicy testPolicy;
    late InsuranceClaim testClaim;
    late List<InsuranceCoverage> testCoverages;

    setUp(() {
      // Create test provider
      testProvider = const InsuranceProvider(
        id: 'provider-1',
        name: 'Test Insurance Co.',
        description: 'A reliable insurance provider for testing',
        logoUrl: 'https://example.com/logo.png',
        websiteUrl: 'https://example.com',
        phoneNumber: '******-123-4567',
        email: '<EMAIL>',
        rating: 4.5,
        reviewCount: 150,
        isFeatured: true,
        isPartner: false,
        countries: ['US', 'CA', 'UK'],
      );

      // Create test coverages
      testCoverages = [
        const InsuranceCoverage(
          id: 'coverage-1',
          type: InsuranceCoverageType.medical,
          amount: 100000.0,
          currency: 'USD',
          isIncluded: true,
        ),
        const InsuranceCoverage(
          id: 'coverage-2',
          type: InsuranceCoverageType.cancellation,
          amount: 5000.0,
          currency: 'USD',
          deductible: 250.0,
          isIncluded: true,
        ),
        const InsuranceCoverage(
          id: 'coverage-3',
          type: InsuranceCoverageType.baggageLoss,
          amount: 2000.0,
          currency: 'USD',
          isIncluded: false,
        ),
      ];

      // Create test policy
      testPolicy = InsurancePolicy(
        id: 'policy-1',
        name: 'Comprehensive Travel Insurance',
        description: 'Complete coverage for international travel',
        type: InsurancePolicyType.singleTrip,
        provider: testProvider,
        price: 89.99,
        currency: 'USD',
        coverages: testCoverages,
        status: InsurancePolicyStatus.active,
        destinationCountries: ['France', 'Italy'],
        travelerCount: 2,
        isRefundable: true,
        refundPolicy: 'Full refund within 14 days',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
      );

      // Create test claim
      testClaim = InsuranceClaim(
        id: 'claim-1',
        policy: testPolicy,
        coverageType: InsuranceCoverageType.medical,
        status: InsuranceClaimStatus.inReview,
        incidentDate: DateTime.now().subtract(const Duration(days: 5)),
        incidentDescription: 'Medical emergency during travel',
        incidentLocation: 'Paris, France',
        claimAmount: 2500.0,
        currency: 'USD',
        submittedDate: DateTime.now().subtract(const Duration(days: 3)),
        lastUpdatedDate: DateTime.now().subtract(const Duration(days: 1)),
        documentUrls: ['https://example.com/doc1.pdf'],
        referenceNumber: 'CLM-2024-001',
      );
    });

    group('InsuranceCard', () {
      testWidgets('should display provider information correctly',
          (tester) async {
        // Arrange
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: InsuranceCard(
              provider: testProvider,
              showProviderDetails: true,
              showRating: true,
            ),
          ),
        );

        // Act & Assert
        expect(find.text('Test Insurance Co.'), findsOneWidget);
        expect(find.text('A reliable insurance provider for testing'),
            findsOneWidget);
        expect(find.text('4.5'), findsOneWidget);
        expect(find.text('150 reviews'), findsOneWidget);
      });

      testWidgets('should display policy information correctly',
          (tester) async {
        // Arrange
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: InsuranceCard(
              policy: testPolicy,
              showPolicyDetails: true,
              showPrice: true,
            ),
          ),
        );

        // Act & Assert
        expect(find.text('Comprehensive Travel Insurance'), findsOneWidget);
        expect(find.text('Complete coverage for international travel'),
            findsOneWidget);
        expect(find.text('USD89.99'), findsOneWidget);
      });

      testWidgets('should handle tap events', (tester) async {
        // Arrange
        bool tapped = false;
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: InsuranceCard(
              provider: testProvider,
              onTap: () => tapped = true,
            ),
          ),
        );

        // Act
        await tester.tap(find.byType(InsuranceCard));
        await tester.pump();

        // Assert
        expect(tapped, isTrue);
      });

      testWidgets('should show action buttons when enabled', (tester) async {
        // Arrange
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: InsuranceCard(
              provider: testProvider,
              showActions: true,
            ),
          ),
        );

        // Act & Assert
        expect(find.text('View Details'), findsOneWidget);
        expect(find.text('Contact'), findsOneWidget);
      });

      testWidgets('should hide action buttons when disabled', (tester) async {
        // Arrange
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: InsuranceCard(
              provider: testProvider,
              showActions: false,
            ),
          ),
        );

        // Act & Assert
        expect(find.text('View Details'), findsNothing);
        expect(find.text('Contact'), findsNothing);
      });
    });

    group('InsurancePolicyCard', () {
      testWidgets('should display policy information', (tester) async {
        // Arrange
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: InsurancePolicyCard(
              policy: testPolicy,
              showDetails: true,
              showStatus: true,
              showProvider: true,
              showPrice: true,
            ),
          ),
        );

        // Act & Assert
        expect(find.text('Comprehensive Travel Insurance'), findsOneWidget);
        expect(find.text('Test Insurance Co.'), findsOneWidget);
        expect(find.text('USD89.99'), findsOneWidget);
      });

      testWidgets('should handle different layouts', (tester) async {
        // Test vertical layout
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: InsurancePolicyCard(
              policy: testPolicy,
              isHorizontal: false,
            ),
          ),
        );
        expect(find.byType(InsurancePolicyCard), findsOneWidget);

        // Test horizontal layout
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: InsurancePolicyCard(
              policy: testPolicy,
              isHorizontal: true,
            ),
          ),
        );
        expect(find.byType(InsurancePolicyCard), findsOneWidget);
      });

      testWidgets('should handle tap events', (tester) async {
        // Arrange
        bool tapped = false;
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: InsurancePolicyCard(
              policy: testPolicy,
              onTap: () => tapped = true,
            ),
          ),
        );

        // Act
        await tester.tap(find.byType(InsurancePolicyCard));
        await tester.pump();

        // Assert
        expect(tapped, isTrue);
      });
    });

    group('InsuranceClaimCard', () {
      testWidgets('should display claim information', (tester) async {
        // Arrange
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: InsuranceClaimCard(
              claim: testClaim,
              showDetails: true,
              showStatus: true,
              showAmount: true,
            ),
          ),
        );

        // Act & Assert
        expect(find.text('CLM-2024-001'), findsOneWidget);
        expect(find.text('Medical emergency during travel'), findsOneWidget);
        expect(find.text('USD2500.00'), findsOneWidget);
        expect(find.text('In Review'), findsOneWidget);
      });

      testWidgets('should display status correctly', (tester) async {
        // Arrange
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: InsuranceClaimCard(
              claim: testClaim,
              showStatus: true,
            ),
          ),
        );

        // Act & Assert
        expect(find.text('In Review'), findsOneWidget);
        // Should show status icon
        expect(find.byIcon(Icons.search), findsOneWidget);
      });

      testWidgets('should handle different layouts', (tester) async {
        // Test vertical layout
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: InsuranceClaimCard(
              claim: testClaim,
              isHorizontal: false,
            ),
          ),
        );
        expect(find.byType(InsuranceClaimCard), findsOneWidget);

        // Test horizontal layout
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: InsuranceClaimCard(
              claim: testClaim,
              isHorizontal: true,
            ),
          ),
        );
        expect(find.byType(InsuranceClaimCard), findsOneWidget);
      });
    });

    group('CoverageBreakdownWidget', () {
      testWidgets('should display coverage information', (tester) async {
        // Arrange
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: CoverageBreakdownWidget(
              coverages: testCoverages,
              showAmounts: true,
              showDescriptions: true,
            ),
          ),
        );

        // Act & Assert
        expect(find.text('Medical Coverage'), findsOneWidget);
        expect(find.text('Trip Cancellation'), findsOneWidget);
        expect(find.text('USD100000.00'), findsOneWidget);
        expect(find.text('USD5000.00'), findsOneWidget);
      });

      testWidgets('should filter included coverages when specified',
          (tester) async {
        // Arrange
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: CoverageBreakdownWidget(
              coverages: testCoverages,
              showOnlyIncluded: true,
            ),
          ),
        );

        // Act & Assert
        expect(find.text('Medical Coverage'), findsOneWidget);
        expect(find.text('Trip Cancellation'), findsOneWidget);
        expect(find.text('Baggage Loss/Damage'), findsNothing); // Not included
      });

      testWidgets('should show empty state when no coverages', (tester) async {
        // Arrange
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: const CoverageBreakdownWidget(
              coverages: [],
            ),
          ),
        );

        // Act & Assert
        expect(find.text('No coverage information available'), findsOneWidget);
      });
    });

    group('ClaimStatusWidget', () {
      testWidgets('should display status information', (tester) async {
        // Arrange
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: const ClaimStatusWidget(
              status: InsuranceClaimStatus.inReview,
              showDescription: true,
            ),
          ),
        );

        // Act & Assert
        expect(find.text('In Review'), findsOneWidget);
        expect(find.text('Claim is under review by the insurance provider'),
            findsOneWidget);
        expect(find.byIcon(Icons.search), findsOneWidget);
      });

      testWidgets('should handle different status types', (tester) async {
        final statuses = [
          InsuranceClaimStatus.submitted,
          InsuranceClaimStatus.approved,
          InsuranceClaimStatus.paid,
          InsuranceClaimStatus.denied,
        ];

        for (final status in statuses) {
          await tester.pumpWidget(
            TestHelpers.testableWidget(
              child: ClaimStatusWidget(
                status: status,
                showDescription: true,
              ),
            ),
          );

          expect(find.text(status.displayName), findsOneWidget);
          expect(find.byIcon(status.icon), findsOneWidget);
        }
      });

      testWidgets('should handle tap events', (tester) async {
        // Arrange
        bool tapped = false;
        await tester.pumpWidget(
          TestHelpers.testableWidget(
            child: ClaimStatusWidget(
              status: InsuranceClaimStatus.inReview,
              onTap: () => tapped = true,
            ),
          ),
        );

        // Act
        await tester.tap(find.byType(ClaimStatusWidget));
        await tester.pump();

        // Assert
        expect(tapped, isTrue);
      });
    });
  });
}
