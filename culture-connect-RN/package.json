{"name": "culture-connect", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "npx expo start", "start:clear": "npx expo start --clear", "android": "npx expo start --android", "ios": "npx expo start --ios", "web": "npx expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.1.6", "expo": "^53.0.4", "expo-av": "~15.1.7", "expo-blur": "~14.1.4", "expo-constants": "~17.1.4", "expo-file-system": "~18.1.11", "expo-font": "~13.3.0", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.4", "expo-location": "~18.1.4", "expo-router": "~5.1.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.7", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.6", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.525.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.24.0", "react-native-html-to-pdf": "^0.12.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/react-dom": "~19.1.6", "typescript": "~5.8.3"}, "private": true}