import 'package:flutter/material.dart';
import 'package:culture_connect/theme/kaia_design_tokens.dart';

/// Detail screen for trending experiences from the Explore Screen
/// Follows CultureConnect premium design system with enhanced visual aesthetics
/// Implements comprehensive visual overhaul with modern UI patterns
class TrendingExperienceDetailScreen extends StatefulWidget {
  final String id;
  final String title;
  final String location;
  final String imageUrl;
  final double rating;
  final int reviewCount;
  final String price;
  final String duration;
  final List<String> tags;

  const TrendingExperienceDetailScreen({
    super.key,
    required this.id,
    required this.title,
    required this.location,
    required this.imageUrl,
    required this.rating,
    required this.reviewCount,
    required this.price,
    required this.duration,
    required this.tags,
  });

  @override
  State<TrendingExperienceDetailScreen> createState() =>
      _TrendingExperienceDetailScreenState();
}

class _TrendingExperienceDetailScreenState
    extends State<TrendingExperienceDetailScreen>
    with TickerProviderStateMixin {
  bool _isFavorite = false;
  final PageController _imagePageController = PageController();
  int _currentImageIndex = 0;

  // Animation controllers for smooth card appearances
  late AnimationController _headerAnimationController;
  late AnimationController _descriptionAnimationController;
  late AnimationController _highlightsAnimationController;
  late AnimationController _includedAnimationController;
  late AnimationController _reviewsAnimationController;
  late AnimationController _buttonsAnimationController;

  // Animations
  late Animation<double> _headerAnimation;
  late Animation<double> _descriptionAnimation;
  late Animation<double> _highlightsAnimation;
  late Animation<double> _includedAnimation;
  late Animation<double> _reviewsAnimation;
  late Animation<double> _buttonsAnimation;

  // Mock additional images for carousel
  List<String> get _images => [
        widget.imageUrl,
        'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        'https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startStaggeredAnimations();
  }

  void _initializeAnimations() {
    _headerAnimationController = AnimationController(
      duration: KaiaDesignTokens.animationNormal,
      vsync: this,
    );
    _descriptionAnimationController = AnimationController(
      duration: KaiaDesignTokens.animationNormal,
      vsync: this,
    );
    _highlightsAnimationController = AnimationController(
      duration: KaiaDesignTokens.animationNormal,
      vsync: this,
    );
    _includedAnimationController = AnimationController(
      duration: KaiaDesignTokens.animationNormal,
      vsync: this,
    );
    _reviewsAnimationController = AnimationController(
      duration: KaiaDesignTokens.animationNormal,
      vsync: this,
    );
    _buttonsAnimationController = AnimationController(
      duration: KaiaDesignTokens.animationNormal,
      vsync: this,
    );

    // Create slide and fade animations
    _headerAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _headerAnimationController,
        curve: KaiaDesignTokens.curveEaseOut,
      ),
    );
    _descriptionAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _descriptionAnimationController,
        curve: KaiaDesignTokens.curveEaseOut,
      ),
    );
    _highlightsAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _highlightsAnimationController,
        curve: KaiaDesignTokens.curveEaseOut,
      ),
    );
    _includedAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _includedAnimationController,
        curve: KaiaDesignTokens.curveEaseOut,
      ),
    );
    _reviewsAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _reviewsAnimationController,
        curve: KaiaDesignTokens.curveEaseOut,
      ),
    );
    _buttonsAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _buttonsAnimationController,
        curve: KaiaDesignTokens.curveEaseOut,
      ),
    );
  }

  void _startStaggeredAnimations() {
    // Start animations with staggered delays for smooth appearance
    _headerAnimationController.forward();

    Future.delayed(const Duration(milliseconds: 80), () {
      if (mounted) _descriptionAnimationController.forward();
    });

    Future.delayed(const Duration(milliseconds: 160), () {
      if (mounted) _highlightsAnimationController.forward();
    });

    Future.delayed(const Duration(milliseconds: 240), () {
      if (mounted) _includedAnimationController.forward();
    });

    Future.delayed(const Duration(milliseconds: 320), () {
      if (mounted) _reviewsAnimationController.forward();
    });

    Future.delayed(const Duration(milliseconds: 400), () {
      if (mounted) _buttonsAnimationController.forward();
    });
  }

  @override
  void dispose() {
    _imagePageController.dispose();
    _headerAnimationController.dispose();
    _descriptionAnimationController.dispose();
    _highlightsAnimationController.dispose();
    _includedAnimationController.dispose();
    _reviewsAnimationController.dispose();
    _buttonsAnimationController.dispose();
    super.dispose();
  }

  void _handleFavoritePress() {
    setState(() {
      _isFavorite = !_isFavorite;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text(_isFavorite ? 'Added to favorites' : 'Removed from favorites'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _handleSharePress() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality coming soon'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _handleBookNow() {
    // TODO: Navigate to booking screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Booking ${widget.title}...'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: KaiaDesignTokens.neutralGray50,
      body: CustomScrollView(
        slivers: [
          _buildHeroSection(),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Animated Header
                  AnimatedBuilder(
                    animation: _headerAnimation,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(0, 30 * (1 - _headerAnimation.value)),
                        child: Opacity(
                          opacity: _headerAnimation.value,
                          child: _buildHeader(),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: KaiaDesignTokens.spacing24),
                  // Animated Description
                  AnimatedBuilder(
                    animation: _descriptionAnimation,
                    builder: (context, child) {
                      return Transform.translate(
                        offset:
                            Offset(0, 30 * (1 - _descriptionAnimation.value)),
                        child: Opacity(
                          opacity: _descriptionAnimation.value,
                          child: _buildDescription(),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: KaiaDesignTokens.spacing24),
                  // Animated Highlights
                  AnimatedBuilder(
                    animation: _highlightsAnimation,
                    builder: (context, child) {
                      return Transform.translate(
                        offset:
                            Offset(0, 30 * (1 - _highlightsAnimation.value)),
                        child: Opacity(
                          opacity: _highlightsAnimation.value,
                          child: _buildHighlights(),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: KaiaDesignTokens.spacing24),
                  // Animated Included
                  AnimatedBuilder(
                    animation: _includedAnimation,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(0, 30 * (1 - _includedAnimation.value)),
                        child: Opacity(
                          opacity: _includedAnimation.value,
                          child: _buildIncluded(),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: KaiaDesignTokens.spacing24),
                  // Animated Reviews
                  AnimatedBuilder(
                    animation: _reviewsAnimation,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(0, 30 * (1 - _reviewsAnimation.value)),
                        child: Opacity(
                          opacity: _reviewsAnimation.value,
                          child: _buildReviews(),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: KaiaDesignTokens.spacing32),
                  // Animated Action Buttons
                  AnimatedBuilder(
                    animation: _buttonsAnimation,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(0, 30 * (1 - _buttonsAnimation.value)),
                        child: Opacity(
                          opacity: _buttonsAnimation.value,
                          child: _buildActionButtons(),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: KaiaDesignTokens.spacing16),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeroSection() {
    return SliverAppBar(
      expandedHeight: 320,
      pinned: true,
      backgroundColor: KaiaDesignTokens.primaryIndigo,
      elevation: 0,
      leading: Container(
        margin: const EdgeInsets.all(KaiaDesignTokens.spacing8),
        decoration: BoxDecoration(
          color: KaiaDesignTokens.neutralWhite.withAlpha(240),
          borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusMd),
          boxShadow: KaiaDesignTokens.shadowMd,
        ),
        child: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios_new,
            color: KaiaDesignTokens.neutralGray700,
            size: 20,
          ),
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.all(KaiaDesignTokens.spacing8),
          decoration: BoxDecoration(
            color: KaiaDesignTokens.neutralWhite.withAlpha(240),
            borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusMd),
            boxShadow: KaiaDesignTokens.shadowMd,
          ),
          child: IconButton(
            onPressed: _handleFavoritePress,
            icon: AnimatedSwitcher(
              duration: KaiaDesignTokens.animationFast,
              child: Icon(
                _isFavorite ? Icons.favorite : Icons.favorite_border,
                key: ValueKey(_isFavorite),
                color: _isFavorite
                    ? KaiaDesignTokens.errorRed
                    : KaiaDesignTokens.neutralGray600,
                size: 20,
              ),
            ),
          ),
        ),
        Container(
          margin: const EdgeInsets.all(KaiaDesignTokens.spacing8),
          decoration: BoxDecoration(
            color: KaiaDesignTokens.neutralWhite.withAlpha(240),
            borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusMd),
            boxShadow: KaiaDesignTokens.shadowMd,
          ),
          child: IconButton(
            onPressed: _handleSharePress,
            icon: const Icon(
              Icons.share,
              color: KaiaDesignTokens.neutralGray600,
              size: 20,
            ),
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            // Enhanced Image Carousel with smooth transitions
            AnimatedSwitcher(
              duration: KaiaDesignTokens.animationNormal,
              child: PageView.builder(
                key: ValueKey(_currentImageIndex),
                controller: _imagePageController,
                itemCount: _images.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentImageIndex = index;
                  });
                },
                itemBuilder: (context, index) {
                  return Image.network(
                    _images[index],
                    fit: BoxFit.cover,
                    // Performance optimizations for memory usage
                    cacheWidth: 800,
                    cacheHeight: 600,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Container(
                        color: KaiaDesignTokens.neutralGray100,
                        child: const Center(
                          child: CircularProgressIndicator(
                            color: KaiaDesignTokens.primaryIndigo,
                            strokeWidth: 2,
                          ),
                        ),
                      );
                    },
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: KaiaDesignTokens.neutralGray100,
                        child: const Center(
                          child: Icon(
                            Icons.image_outlined,
                            size: 64,
                            color: KaiaDesignTokens.neutralGray400,
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
            // Enhanced gradient overlay for better text readability
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Color(0x40000000),
                    Color(0x80000000),
                  ],
                  stops: [0.0, 0.7, 1.0],
                ),
              ),
            ),
            // Premium image indicators with smooth animations
            if (_images.length > 1)
              Positioned(
                bottom: KaiaDesignTokens.spacing20,
                left: 0,
                right: 0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: _images.asMap().entries.map((entry) {
                    return AnimatedContainer(
                      duration: KaiaDesignTokens.animationFast,
                      width: _currentImageIndex == entry.key ? 24 : 8,
                      height: 8,
                      margin: const EdgeInsets.symmetric(
                          horizontal: KaiaDesignTokens.spacing4),
                      decoration: BoxDecoration(
                        borderRadius:
                            BorderRadius.circular(KaiaDesignTokens.radiusXs),
                        color: _currentImageIndex == entry.key
                            ? KaiaDesignTokens.neutralWhite
                            : KaiaDesignTokens.neutralWhite.withAlpha(128),
                        boxShadow: _currentImageIndex == entry.key
                            ? KaiaDesignTokens.shadowSm
                            : null,
                      ),
                    );
                  }).toList(),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing20),
      decoration: BoxDecoration(
        color: KaiaDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
        boxShadow: KaiaDesignTokens.shadowMd,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and rating with defensive overflow handling
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 3,
                child: ConstrainedBox(
                  constraints: const BoxConstraints(minWidth: 0),
                  child: Text(
                    widget.title,
                    style: const TextStyle(
                      fontSize: KaiaDesignTokens.fontSize2Xl,
                      fontWeight: KaiaDesignTokens.fontWeightBold,
                      color: KaiaDesignTokens.neutralGray900,
                      height: KaiaDesignTokens.lineHeightTight,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
              ),
              const SizedBox(width: KaiaDesignTokens.spacing16),
              Flexible(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: KaiaDesignTokens.spacing12,
                      vertical: KaiaDesignTokens.spacing6),
                  decoration: BoxDecoration(
                    color: KaiaDesignTokens.successGreen,
                    borderRadius:
                        BorderRadius.circular(KaiaDesignTokens.radiusSm),
                    boxShadow: KaiaDesignTokens.shadowSm,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.star_rounded,
                        size: 16,
                        color: KaiaDesignTokens.neutralWhite,
                      ),
                      const SizedBox(width: KaiaDesignTokens.spacing4),
                      Text(
                        widget.rating.toStringAsFixed(1),
                        style: const TextStyle(
                          fontSize: KaiaDesignTokens.fontSizeSm,
                          fontWeight: KaiaDesignTokens.fontWeightBold,
                          color: KaiaDesignTokens.neutralWhite,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: KaiaDesignTokens.spacing12),
          // Location and reviews
          Row(
            children: [
              const Icon(
                Icons.location_on_rounded,
                size: 18,
                color: KaiaDesignTokens.secondaryCyan,
              ),
              const SizedBox(width: KaiaDesignTokens.spacing6),
              Expanded(
                child: Text(
                  widget.location,
                  style: const TextStyle(
                    fontSize: KaiaDesignTokens.fontSizeMd,
                    color: KaiaDesignTokens.neutralGray600,
                    fontWeight: KaiaDesignTokens.fontWeightMedium,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: KaiaDesignTokens.spacing8,
                  vertical: KaiaDesignTokens.spacing4,
                ),
                decoration: BoxDecoration(
                  color: KaiaDesignTokens.neutralGray100,
                  borderRadius:
                      BorderRadius.circular(KaiaDesignTokens.radiusXs),
                ),
                child: Text(
                  '${widget.reviewCount} reviews',
                  style: const TextStyle(
                    fontSize: KaiaDesignTokens.fontSizeXs,
                    color: KaiaDesignTokens.neutralGray600,
                    fontWeight: KaiaDesignTokens.fontWeightMedium,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: KaiaDesignTokens.spacing16),
          // Enhanced Tags with premium styling
          if (widget.tags.isNotEmpty)
            Wrap(
              spacing: KaiaDesignTokens.spacing8,
              runSpacing: KaiaDesignTokens.spacing8,
              children: widget.tags.map((tag) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: KaiaDesignTokens.spacing12,
                      vertical: KaiaDesignTokens.spacing6),
                  decoration: BoxDecoration(
                    color: KaiaDesignTokens.secondaryCyan.withAlpha(26),
                    borderRadius:
                        BorderRadius.circular(KaiaDesignTokens.radiusLg),
                    border: Border.all(
                      color: KaiaDesignTokens.secondaryCyan.withAlpha(77),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    tag,
                    style: const TextStyle(
                      fontSize: KaiaDesignTokens.fontSizeXs,
                      fontWeight: KaiaDesignTokens.fontWeightMedium,
                      color: KaiaDesignTokens.secondaryCyan,
                    ),
                  ),
                );
              }).toList(),
            ),
        ],
      ),
    );
  }

  Widget _buildDescription() {
    return Container(
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing20),
      decoration: BoxDecoration(
        color: KaiaDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
        boxShadow: KaiaDesignTokens.shadowMd,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(KaiaDesignTokens.spacing8),
                decoration: BoxDecoration(
                  color: KaiaDesignTokens.primaryIndigo.withAlpha(26),
                  borderRadius:
                      BorderRadius.circular(KaiaDesignTokens.radiusSm),
                ),
                child: const Icon(
                  Icons.info_outline_rounded,
                  size: 20,
                  color: KaiaDesignTokens.primaryIndigo,
                ),
              ),
              const SizedBox(width: KaiaDesignTokens.spacing12),
              const Expanded(
                child: Text(
                  'About this experience',
                  style: TextStyle(
                    fontSize: KaiaDesignTokens.fontSizeLg,
                    fontWeight: KaiaDesignTokens.fontWeightBold,
                    color: KaiaDesignTokens.neutralGray900,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: KaiaDesignTokens.spacing16),
          const Text(
            'Immerse yourself in an unforgettable cultural journey that combines authentic local experiences with breathtaking scenery. This carefully curated experience offers you the perfect blend of adventure, culture, and relaxation.',
            style: TextStyle(
              fontSize: KaiaDesignTokens.fontSizeMd,
              height: KaiaDesignTokens.lineHeightRelaxed,
              color: KaiaDesignTokens.neutralGray700,
            ),
          ),
          const SizedBox(height: KaiaDesignTokens.spacing20),
          Container(
            padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
            decoration: BoxDecoration(
              color: KaiaDesignTokens.primaryIndigo.withAlpha(26),
              borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusMd),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(KaiaDesignTokens.spacing6),
                  decoration: BoxDecoration(
                    color: KaiaDesignTokens.primaryIndigo,
                    borderRadius:
                        BorderRadius.circular(KaiaDesignTokens.radiusXs),
                  ),
                  child: const Icon(
                    Icons.access_time_rounded,
                    size: 16,
                    color: KaiaDesignTokens.neutralWhite,
                  ),
                ),
                const SizedBox(width: KaiaDesignTokens.spacing12),
                Expanded(
                  child: Text(
                    'Duration: ${widget.duration}',
                    style: const TextStyle(
                      fontSize: KaiaDesignTokens.fontSizeSm,
                      fontWeight: KaiaDesignTokens.fontWeightMedium,
                      color: KaiaDesignTokens.neutralGray600,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: KaiaDesignTokens.spacing12),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: KaiaDesignTokens.spacing12,
                    vertical: KaiaDesignTokens.spacing6,
                  ),
                  decoration: BoxDecoration(
                    color: KaiaDesignTokens.primaryIndigo,
                    borderRadius:
                        BorderRadius.circular(KaiaDesignTokens.radiusSm),
                    boxShadow: KaiaDesignTokens.shadowSm,
                  ),
                  child: Text(
                    widget.price,
                    style: const TextStyle(
                      fontSize: KaiaDesignTokens.fontSizeLg,
                      fontWeight: KaiaDesignTokens.fontWeightBold,
                      color: KaiaDesignTokens.neutralWhite,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHighlights() {
    final highlights = [
      {
        'icon': Icons.person_outline_rounded,
        'text': 'Professional local guide'
      },
      {
        'icon': Icons.group_outlined,
        'text': 'Small group experience (max 12 people)'
      },
      {'icon': Icons.explore_outlined, 'text': 'Authentic cultural immersion'},
      {
        'icon': Icons.camera_alt_outlined,
        'text': 'Photo opportunities at scenic spots'
      },
      {
        'icon': Icons.restaurant_outlined,
        'text': 'Traditional local cuisine tasting'
      },
    ];

    return Container(
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing20),
      decoration: BoxDecoration(
        color: KaiaDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
        boxShadow: KaiaDesignTokens.shadowMd,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(KaiaDesignTokens.spacing8),
                decoration: BoxDecoration(
                  color: KaiaDesignTokens.primaryIndigo.withAlpha(26),
                  borderRadius:
                      BorderRadius.circular(KaiaDesignTokens.radiusSm),
                ),
                child: const Icon(
                  Icons.star_outline_rounded,
                  size: 20,
                  color: KaiaDesignTokens.primaryIndigo,
                ),
              ),
              const SizedBox(width: KaiaDesignTokens.spacing12),
              const Expanded(
                child: Text(
                  'Experience highlights',
                  style: TextStyle(
                    fontSize: KaiaDesignTokens.fontSizeLg,
                    fontWeight: KaiaDesignTokens.fontWeightBold,
                    color: KaiaDesignTokens.neutralGray900,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: KaiaDesignTokens.spacing20),
          ...highlights.map((highlight) {
            return Container(
              margin: const EdgeInsets.only(bottom: KaiaDesignTokens.spacing12),
              padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
              decoration: BoxDecoration(
                color: KaiaDesignTokens.neutralGray50,
                borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusMd),
                border: Border.all(
                  color: KaiaDesignTokens.neutralGray200,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(KaiaDesignTokens.spacing6),
                    decoration: BoxDecoration(
                      color: KaiaDesignTokens.primaryIndigo,
                      borderRadius:
                          BorderRadius.circular(KaiaDesignTokens.radiusXs),
                    ),
                    child: Icon(
                      highlight['icon'] as IconData,
                      size: 16,
                      color: KaiaDesignTokens.neutralWhite,
                    ),
                  ),
                  const SizedBox(width: KaiaDesignTokens.spacing12),
                  Expanded(
                    child: Text(
                      highlight['text'] as String,
                      style: const TextStyle(
                        fontSize: KaiaDesignTokens.fontSizeMd,
                        fontWeight: KaiaDesignTokens.fontWeightMedium,
                        color: KaiaDesignTokens.neutralGray700,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildIncluded() {
    final included = [
      {'icon': Icons.person_pin_rounded, 'text': 'Professional guide'},
      {'icon': Icons.directions_car_rounded, 'text': 'Transportation'},
      {'icon': Icons.confirmation_number_rounded, 'text': 'Entry fees'},
      {'icon': Icons.local_cafe_rounded, 'text': 'Light refreshments'},
      {'icon': Icons.photo_camera_rounded, 'text': 'Photo service'},
    ];

    return Container(
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing20),
      decoration: BoxDecoration(
        color: KaiaDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
        boxShadow: KaiaDesignTokens.shadowMd,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(KaiaDesignTokens.spacing8),
                decoration: BoxDecoration(
                  color: KaiaDesignTokens.successGreen.withAlpha(26),
                  borderRadius:
                      BorderRadius.circular(KaiaDesignTokens.radiusSm),
                ),
                child: const Icon(
                  Icons.check_circle_outline_rounded,
                  size: 20,
                  color: KaiaDesignTokens.successGreen,
                ),
              ),
              const SizedBox(width: KaiaDesignTokens.spacing12),
              const Expanded(
                child: Text(
                  'What\'s included',
                  style: TextStyle(
                    fontSize: KaiaDesignTokens.fontSizeLg,
                    fontWeight: KaiaDesignTokens.fontWeightBold,
                    color: KaiaDesignTokens.neutralGray900,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: KaiaDesignTokens.spacing20),
          ...included.map((item) {
            return Container(
              margin: const EdgeInsets.only(bottom: KaiaDesignTokens.spacing8),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(KaiaDesignTokens.spacing4),
                    decoration: BoxDecoration(
                      color: KaiaDesignTokens.successGreen,
                      borderRadius:
                          BorderRadius.circular(KaiaDesignTokens.radiusFull),
                    ),
                    child: Icon(
                      item['icon'] as IconData,
                      size: 16,
                      color: KaiaDesignTokens.neutralWhite,
                    ),
                  ),
                  const SizedBox(width: KaiaDesignTokens.spacing16),
                  Expanded(
                    child: Text(
                      item['text'] as String,
                      style: const TextStyle(
                        fontSize: KaiaDesignTokens.fontSizeMd,
                        fontWeight: KaiaDesignTokens.fontWeightMedium,
                        color: KaiaDesignTokens.neutralGray700,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildReviews() {
    return Container(
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing20),
      decoration: BoxDecoration(
        color: KaiaDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
        boxShadow: KaiaDesignTokens.shadowMd,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(KaiaDesignTokens.spacing8),
                decoration: BoxDecoration(
                  color: KaiaDesignTokens.warningAmber.withAlpha(26),
                  borderRadius:
                      BorderRadius.circular(KaiaDesignTokens.radiusSm),
                ),
                child: const Icon(
                  Icons.reviews_outlined,
                  size: 20,
                  color: KaiaDesignTokens.warningAmber,
                ),
              ),
              const SizedBox(width: KaiaDesignTokens.spacing12),
              const Expanded(
                child: Text(
                  'Reviews',
                  style: TextStyle(
                    fontSize: KaiaDesignTokens.fontSizeLg,
                    fontWeight: KaiaDesignTokens.fontWeightBold,
                    color: KaiaDesignTokens.neutralGray900,
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  // TODO: Navigate to all reviews
                },
                child: const Text(
                  'See all',
                  style: TextStyle(
                    color: KaiaDesignTokens.primaryIndigo,
                    fontWeight: KaiaDesignTokens.fontWeightMedium,
                    fontSize: KaiaDesignTokens.fontSizeSm,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: KaiaDesignTokens.spacing20),
          // Enhanced sample review
          Container(
            padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
            decoration: BoxDecoration(
              color: KaiaDesignTokens.neutralGray50,
              borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusMd),
              border: Border.all(
                color: KaiaDesignTokens.neutralGray200,
                width: 1,
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        KaiaDesignTokens.primaryIndigo,
                        KaiaDesignTokens.accentPurple,
                      ],
                    ),
                    shape: BoxShape.circle,
                    boxShadow: KaiaDesignTokens.shadowSm,
                  ),
                  child: const Center(
                    child: Text(
                      'JD',
                      style: TextStyle(
                        fontSize: KaiaDesignTokens.fontSizeMd,
                        fontWeight: KaiaDesignTokens.fontWeightBold,
                        color: KaiaDesignTokens.neutralWhite,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: KaiaDesignTokens.spacing16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Expanded(
                            child: Text(
                              'John Doe',
                              style: TextStyle(
                                fontSize: KaiaDesignTokens.fontSizeMd,
                                fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                                color: KaiaDesignTokens.neutralGray900,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: KaiaDesignTokens.spacing8),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: List.generate(5, (index) {
                              return Icon(
                                Icons.star_rounded,
                                size: 16,
                                color: index < 5
                                    ? KaiaDesignTokens.warningAmber
                                    : KaiaDesignTokens.neutralGray300,
                              );
                            }),
                          ),
                        ],
                      ),
                      const SizedBox(height: KaiaDesignTokens.spacing8),
                      const Text(
                        'Amazing experience! The guide was knowledgeable and the locations were breathtaking. Highly recommend!',
                        style: TextStyle(
                          fontSize: KaiaDesignTokens.fontSizeSm,
                          color: KaiaDesignTokens.neutralGray600,
                          height: KaiaDesignTokens.lineHeightNormal,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing20),
      decoration: BoxDecoration(
        color: KaiaDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
        boxShadow: KaiaDesignTokens.shadowLg,
      ),
      child: Row(
        children: [
          // Enhanced Contact Guide Button
          Expanded(
            child: Container(
              height: 56, // Fixed height for visual alignment
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
                border: Border.all(
                  color: KaiaDesignTokens.primaryIndigo,
                  width: 2,
                ),
                gradient: LinearGradient(
                  colors: [
                    KaiaDesignTokens.primaryIndigo.withAlpha(13),
                    KaiaDesignTokens.primaryIndigo.withAlpha(26),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                boxShadow: [
                  BoxShadow(
                    color: KaiaDesignTokens.primaryIndigo.withAlpha(26),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: OutlinedButton(
                onPressed: () {
                  // TODO: Contact guide
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: KaiaDesignTokens.spacing20,
                    vertical: KaiaDesignTokens.spacing16,
                  ),
                  side: BorderSide
                      .none, // Remove default border since container has it
                  shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.circular(KaiaDesignTokens.radiusLg),
                  ),
                  foregroundColor: KaiaDesignTokens.primaryIndigo,
                  backgroundColor: Colors.transparent,
                ),
                child: const Text(
                  'Contact Guide',
                  style: TextStyle(
                    fontSize: KaiaDesignTokens.fontSizeMd,
                    fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: KaiaDesignTokens.spacing16),
          // Enhanced Book Now Button
          Expanded(
            flex: 2,
            child: Container(
              height: 56, // Fixed height for visual alignment
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
                gradient: const LinearGradient(
                  colors: [
                    KaiaDesignTokens.primaryIndigo,
                    KaiaDesignTokens.accentPurple,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: KaiaDesignTokens.primaryIndigo.withAlpha(77),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: _handleBookNow,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: KaiaDesignTokens.neutralWhite,
                  padding: const EdgeInsets.symmetric(
                    horizontal: KaiaDesignTokens.spacing24,
                    vertical: KaiaDesignTokens.spacing16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.circular(KaiaDesignTokens.radiusLg),
                  ),
                  elevation: 0,
                  shadowColor: Colors.transparent,
                ),
                child: const Text(
                  'Book Now',
                  style: TextStyle(
                    fontSize: KaiaDesignTokens.fontSizeMd,
                    fontWeight: KaiaDesignTokens.fontWeightBold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
