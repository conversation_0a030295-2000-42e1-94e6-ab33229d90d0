import React, { useEffect } from "react";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { StatusBar } from "expo-status-bar";
import { useUserStore } from "@/store/userStore";

export const unstable_settings = {
  initialRouteName: "index",
};

SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded, error] = useFonts({});

  useEffect(() => {
    if (error) {
      console.error("Font loading error:", error);
    }
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <>
      <StatusBar style="dark" />
      <RootLayoutNav />
    </>
  );
}

function RootLayoutNav() {
  const { isAuthenticated, isOnboarded } = useUserStore();

  return (
    <Stack screenOptions={{ headerShown: false }}>
      {!isAuthenticated ? (
        <>
          <Stack.Screen name="index" options={{ animation: "fade" }} />
          <Stack.Screen name="auth/login" options={{ animation: "slide_from_right" }} />
          <Stack.Screen name="auth/register" options={{ animation: "slide_from_right" }} />
        </>
      ) : !isOnboarded ? (
        <Stack.Screen name="onboarding" options={{ animation: "fade", gestureEnabled: false }} />
      ) : (
        <>
          <Stack.Screen name="(tabs)" options={{ animation: "fade" }} />
          <Stack.Screen name="destination/[id]" options={{ animation: "slide_from_right" }} />
          <Stack.Screen name="event/[id]" options={{ animation: "slide_from_right" }} />
          <Stack.Screen name="booking/[id]" options={{ animation: "slide_from_right" }} />
          <Stack.Screen name="experience/[id]" options={{ animation: "slide_from_right" }} />
          <Stack.Screen name="booking/flight" options={{ animation: "slide_from_right" }} />
          <Stack.Screen name="booking/flight-results" options={{ animation: "slide_from_right" }} />
          <Stack.Screen name="booking/flight-confirmation" options={{ animation: "slide_from_right" }} />
          <Stack.Screen name="booking/summary" options={{ animation: "slide_from_right" }} />
          <Stack.Screen name="booking/seat-selection" options={{ animation: "slide_from_right" }} />
          <Stack.Screen name="booking/payment" options={{ animation: "slide_from_right" }} />
          <Stack.Screen name="booking/insurance" options={{ animation: "slide_from_right" }} />
          <Stack.Screen name="cars/index" options={{ animation: "slide_from_right" }} />
          <Stack.Screen name="cars/[id]" options={{ animation: "slide_from_right" }} />
          <Stack.Screen name="cars/booking" options={{ animation: "slide_from_right" }} />
          <Stack.Screen name="cars/confirmation" options={{ animation: "slide_from_right" }} />
          <Stack.Screen name="ar-guide" options={{ animation: "slide_from_bottom", presentation: "modal" }} />
        </>
      )}
    </Stack>
  );
}