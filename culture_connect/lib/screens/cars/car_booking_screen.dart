import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:culture_connect/models/car.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/app_button.dart';

import 'package:culture_connect/screens/cars/location_selection_screen.dart';
import 'package:culture_connect/screens/cars/date_selection_screen.dart';
import 'package:culture_connect/screens/payment/universal_payment_screen.dart';

/// Car booking screen with rental details, insurance, and security options
/// Matches React Native implementation with comprehensive booking flow
class CarBookingScreen extends StatefulWidget {
  final Car car;
  final String selectedDuration;

  const CarBookingScreen({
    super.key,
    required this.car,
    required this.selectedDuration,
  });

  @override
  State<CarBookingScreen> createState() => _CarBookingScreenState();
}

class _CarBookingScreenState extends State<CarBookingScreen>
    with TickerProviderStateMixin {
  late AnimationController _scaleAnimationController;
  late Animation<double> _scaleAnimation;

  DateTime _pickupDate = DateTime.now();
  DateTime _returnDate = DateTime.now().add(const Duration(days: 1));
  String _pickupTime = '10:00 AM';
  String _returnTime = '10:00 AM';
  InsuranceType _selectedInsurance = InsuranceType.basic;
  SecurityType _selectedSecurity = SecurityType.none;
  String _selectedLocation = '';

  @override
  void initState() {
    super.initState();
    _selectedLocation = widget.car.location;

    // Set return date based on selected duration
    final days = BookingDuration.getDaysFromDuration(widget.selectedDuration);
    _returnDate = _pickupDate.add(Duration(days: days));

    _scaleAnimationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(
        parent: _scaleAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _scaleAnimationController.dispose();
    super.dispose();
  }

  void _handleBackPress() {
    Navigator.pop(context);
  }

  int _calculateDays() {
    // Handle custom duration
    if (widget.selectedDuration.startsWith('custom:')) {
      final customDays = widget.selectedDuration.split(':')[1];
      return int.tryParse(customDays) ?? 1;
    }
    // Handle preset durations
    return BookingDuration.getDaysFromDuration(widget.selectedDuration);
  }

  double _calculateBaseAmount() {
    return widget.car.pricePerDay * _calculateDays();
  }

  double _calculateInsuranceCost() {
    return _selectedInsurance.dailyCost * _calculateDays();
  }

  double _calculateSecurityCost() {
    return _selectedSecurity.dailyCost * _calculateDays();
  }

  double _calculateTotalAmount() {
    return _calculateBaseAmount() +
        _calculateInsuranceCost() +
        _calculateSecurityCost();
  }

  void _handleProceedToPayment() async {
    // Animate button press
    await _scaleAnimationController.forward();
    await _scaleAnimationController.reverse();

    // Create booking object for payment screen
    final booking = Booking(
      id: 'car_${DateTime.now().millisecondsSinceEpoch}',
      experienceId: 'car_${widget.car.id}',
      date: _pickupDate,
      timeSlot: TimeSlot(
        startTime: _pickupDate,
        endTime: _returnDate,
      ),
      participantCount: 1,
      totalAmount: _calculateTotalAmount(),
      status: BookingStatus.pending,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Create booking details for payment screen
    final bookingDetails = {
      'type': 'car',
      'carModel': widget.car.name,
      'carType': widget.car.category,
      'pickupDate':
          '${_pickupDate.day}/${_pickupDate.month}/${_pickupDate.year}',
      'returnDate':
          '${_returnDate.day}/${_returnDate.month}/${_returnDate.year}',
      'rentalDays': _calculateDays(),
      'location': _selectedLocation.isNotEmpty
          ? _selectedLocation
          : widget.car.location,
      'pickupTime': _pickupTime,
      'returnTime': _returnTime,
      'dailyRate': widget.car.pricePerDay,
      'baseAmount': _calculateBaseAmount(),
      'insuranceCost': _calculateInsuranceCost(),
      'securityCost': _calculateSecurityCost(),
      'insurance': _selectedInsurance.name,
      'security': _selectedSecurity.name,
      'carId': widget.car.id,
    };

    // Navigate to universal payment screen
    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => UniversalPaymentScreen(
            booking: booking,
            userEmail: '<EMAIL>', // TODO: Get from user session
            userName: 'User Name', // TODO: Get from user session
            userPhone: null, // TODO: Get from user session
            bookingDetails: bookingDetails,
          ),
        ),
      );
    }
  }

  void _handleLocationPress() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LocationSelectionScreen(
          initialLocation: _selectedLocation.isNotEmpty
              ? _selectedLocation
              : widget.car.location,
          onLocationSelected: (location, address) {
            setState(() {
              _selectedLocation = location;
            });
          },
        ),
      ),
    );
  }

  void _handleDatePress() {
    final requiredDuration = _calculateDays();
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DateSelectionScreen(
          initialPickupDate: _pickupDate,
          initialReturnDate: _returnDate,
          requiredDuration: requiredDuration,
          onDatesSelected: (pickupDate, returnDate) {
            setState(() {
              _pickupDate = pickupDate;
              _returnDate = returnDate;
            });
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.white,
        elevation: 0,
        leading: GestureDetector(
          onTap: _handleBackPress,
          child: const Icon(
            Icons.arrow_back,
            color: AppTheme.textPrimaryColor,
            size: 24,
          ),
        ),
        title: const Column(
          children: [
            Text(
              'Complete Booking',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            Text(
              'Review and confirm details',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
        centerTitle: true,
        bottom: const PreferredSize(
          preferredSize: Size.fromHeight(1),
          child: Divider(
            height: 1,
            color: AppTheme.borderLight,
          ),
        ),
      ),
      body: Stack(
        children: [
          // Main content
          SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(
              AppTheme.spacingLg,
              0,
              AppTheme.spacingLg,
              180, // Increased bottom padding to prevent overlap with bottom bar
            ),
            child: Column(
              children: [
                // Selected Vehicle Card
                _buildSelectedVehicleCard(),

                // Rental Details Section
                _buildRentalDetailsSection(),

                // Insurance Coverage Section
                _buildInsuranceCoverageSection(),

                // Security Add-ons Section
                _buildSecurityAddonsSection(),

                // Price Breakdown Section
                _buildPriceBreakdownSection(),
              ],
            ),
          ),
          // Bottom bar
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: EdgeInsets.fromLTRB(
                AppTheme.spacingLg,
                AppTheme.spacingMd, // Reduced top padding from Lg to Md
                AppTheme.spacingLg,
                AppTheme.spacingMd +
                    MediaQuery.of(context)
                        .padding
                        .bottom, // Reduced bottom padding
              ),
              decoration: const BoxDecoration(
                color: AppTheme.white,
                border: Border(
                  top: BorderSide(
                    color: AppTheme.borderLight,
                    width: 1,
                  ),
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.shadowColor,
                    offset: Offset(0, -4),
                    blurRadius: 12,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Text(
                          'Total',
                          style: TextStyle(
                            fontSize: 16, // Reduced from 18 to 16
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                        const SizedBox(
                            height:
                                2), // Added small spacing for better visual hierarchy
                        Text(
                          '\$${_calculateTotalAmount().toInt()}',
                          style: const TextStyle(
                            fontSize: 22, // Reduced from 24 to 22
                            fontWeight: FontWeight.bold,
                            color: AppTheme.accentColor,
                          ),
                        ),
                        Text(
                          'for ${_calculateDays()} day${_calculateDays() > 1 ? 's' : ''}',
                          style: const TextStyle(
                            fontSize: 13, // Reduced from 14 to 13
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacingLg),
                  AnimatedBuilder(
                    animation: _scaleAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _scaleAnimation.value,
                        child: SizedBox(
                          width: 180,
                          child: AppButton(
                            text: 'Proceed to Payment',
                            onPressed: _handleProceedToPayment,
                            variant: ButtonVariant.filled,
                            color: AppTheme
                                .accentColor, // Orange for visual consistency
                            height: 48,
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedVehicleCard() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppTheme.spacingLg),
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor,
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and rating
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Selected Vehicle',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.backgroundSecondary,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.star,
                      size: 14,
                      color: AppTheme.warningColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      widget.car.rating.toString(),
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: AppTheme.warningColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),

          // Car content with image and details
          Row(
            children: [
              // Car image with availability badge
              Stack(
                children: [
                  Container(
                    width: 100,
                    height: 70,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: CachedNetworkImage(
                        imageUrl: widget.car.imageUrl,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: AppTheme.backgroundSecondary,
                          child: const Icon(
                            Icons.directions_car,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: AppTheme.backgroundSecondary,
                          child: const Icon(
                            Icons.directions_car,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Availability badge
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.successColor,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.check_circle,
                            size: 12,
                            color: AppTheme.white,
                          ),
                          SizedBox(width: 2),
                          Text(
                            'Available',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                              color: AppTheme.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(width: AppTheme.spacingMd),

              // Car details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Car name and category
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.car.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimaryColor,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: AppTheme.spacingXs),
                        Text(
                          widget.car.category,
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppTheme.accentColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppTheme.spacingSm),

                    // Car specs
                    Wrap(
                      spacing: AppTheme.spacingXs,
                      runSpacing: AppTheme.spacingXs,
                      children: [
                        _buildSpecChip(
                            Icons.people, '${widget.car.seats} seats'),
                        _buildSpecChip(
                            Icons.local_gas_station, widget.car.fuelType),
                        _buildSpecChip(Icons.settings, widget.car.transmission),
                      ],
                    ),
                  ],
                ),
              ),

              // Price display
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '\$${widget.car.pricePerDay.toInt()}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  const Text(
                    'per day',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSpecChip(IconData icon, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 6,
        vertical: 3,
      ),
      decoration: BoxDecoration(
        color: AppTheme.backgroundSecondary,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: AppTheme.accentColor,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRentalDetailsSection() {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingLg),
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor,
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Rental Details',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.accentColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${_calculateDays()} day${_calculateDays() > 1 ? 's' : ''}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),

          // Pickup Location
          GestureDetector(
            onTap: _handleLocationPress,
            child: Container(
              padding: const EdgeInsets.all(AppTheme.spacingMd),
              decoration: BoxDecoration(
                color: AppTheme.backgroundSecondary,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        size: 18,
                        color: AppTheme.accentColor,
                      ),
                      const SizedBox(width: AppTheme.spacingSm),
                      const Expanded(
                        child: Text(
                          'Pickup Location',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                      ),
                      const Icon(
                        Icons.chevron_right,
                        size: 16,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ],
                  ),
                  const SizedBox(height: AppTheme.spacingSm),
                  Text(
                    _selectedLocation.isNotEmpty
                        ? _selectedLocation
                        : widget.car.location,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingXs),
                  const Text(
                    'Available 24/7 • Free pickup service',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),

          // Date & Time Selection
          Row(
            children: [
              // Pickup Date & Time
              Expanded(
                child: GestureDetector(
                  onTap: _handleDatePress,
                  child: Container(
                    padding: const EdgeInsets.all(AppTheme.spacingMd),
                    decoration: BoxDecoration(
                      color: AppTheme.backgroundSecondary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(
                              Icons.calendar_today,
                              size: 16,
                              color: AppTheme.accentColor,
                            ),
                            const SizedBox(width: AppTheme.spacingSm),
                            const Expanded(
                              child: Text(
                                'Pickup',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: AppTheme.textPrimaryColor,
                                ),
                              ),
                            ),
                            const Icon(
                              Icons.chevron_right,
                              size: 16,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ],
                        ),
                        const SizedBox(height: AppTheme.spacingSm),
                        Text(
                          _formatDate(_pickupDate),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                        const SizedBox(height: AppTheme.spacingXs),
                        Text(
                          _pickupTime,
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppTheme.spacingMd),

              // Return Date & Time
              Expanded(
                child: GestureDetector(
                  onTap: _handleDatePress,
                  child: Container(
                    padding: const EdgeInsets.all(AppTheme.spacingMd),
                    decoration: BoxDecoration(
                      color: AppTheme.backgroundSecondary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(
                              Icons.calendar_today,
                              size: 16,
                              color: AppTheme.accentColor,
                            ),
                            const SizedBox(width: AppTheme.spacingSm),
                            const Expanded(
                              child: Text(
                                'Return',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: AppTheme.textPrimaryColor,
                                ),
                              ),
                            ),
                            const Icon(
                              Icons.chevron_right,
                              size: 16,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ],
                        ),
                        const SizedBox(height: AppTheme.spacingSm),
                        Text(
                          _formatDate(_returnDate),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                        const SizedBox(height: AppTheme.spacingXs),
                        Text(
                          _returnTime,
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    const weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

    return '${weekdays[date.weekday - 1]}, ${months[date.month - 1]} ${date.day}';
  }

  Widget _buildInsuranceCoverageSection() {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingLg),
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor,
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Insurance Coverage',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AppTheme.backgroundSecondary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.info_outline,
                  size: 16,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),

          // Insurance options
          Column(
            children: [
              _buildInsuranceOption(
                InsuranceType.basic,
                'Basic Coverage',
                'Standard protection included',
                'Included',
                true,
              ),
              const SizedBox(height: AppTheme.spacingMd),
              _buildInsuranceOption(
                InsuranceType.premium,
                'Premium Coverage',
                'Enhanced protection with lower deductible',
                '+\$${InsuranceType.premium.dailyCost.toInt()}/day',
                false,
              ),
              const SizedBox(height: AppTheme.spacingMd),
              _buildInsuranceOption(
                InsuranceType.comprehensive,
                'Comprehensive Coverage',
                'Full protection with zero deductible',
                '+\$${InsuranceType.comprehensive.dailyCost.toInt()}/day',
                false,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInsuranceOption(
    InsuranceType type,
    String name,
    String description,
    String price,
    bool isIncluded,
  ) {
    final isSelected = _selectedInsurance == type;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedInsurance = type;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.accentColor.withValues(alpha: 0.1)
              : AppTheme.backgroundSecondary,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppTheme.accentColor : AppTheme.borderLight,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // Radio button
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color:
                      isSelected ? AppTheme.accentColor : AppTheme.borderLight,
                  width: 2,
                ),
                color: isSelected ? AppTheme.accentColor : AppTheme.white,
              ),
              child: isSelected
                  ? const Icon(
                      Icons.circle,
                      size: 10,
                      color: AppTheme.white,
                    )
                  : null,
            ),
            const SizedBox(width: AppTheme.spacingMd),

            // Insurance info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isSelected
                          ? AppTheme.accentColor
                          : AppTheme.textPrimaryColor,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingXs),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),

            // Price
            Text(
              price,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: isSelected
                    ? AppTheme.accentColor
                    : AppTheme.textPrimaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityAddonsSection() {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingLg),
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor,
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Security Add-ons',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AppTheme.backgroundSecondary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.security,
                  size: 16,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),

          // Security options
          Column(
            children: [
              _buildSecurityOption(
                SecurityType.none,
                'No Security',
                'Standard rental without additional security',
                [],
                'Included',
              ),
              const SizedBox(height: AppTheme.spacingMd),
              _buildSecurityOption(
                SecurityType.personal,
                'Personal Security',
                'Professional bodyguard without vehicle',
                [
                  'Personal protection',
                  '24/7 availability',
                  'Trained professional',
                ],
                '+\$${SecurityType.personal.dailyCost.toInt()}/day',
              ),
              const SizedBox(height: AppTheme.spacingMd),
              _buildSecurityOption(
                SecurityType.full,
                'Full Security Detail',
                'Complete security team with escort vehicle',
                [
                  'Security team',
                  'Escort vehicle',
                  'Route planning',
                  'Emergency response',
                ],
                '+\$${SecurityType.full.dailyCost.toInt()}/day',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityOption(
    SecurityType type,
    String name,
    String description,
    List<String> features,
    String price,
  ) {
    final isSelected = _selectedSecurity == type;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedSecurity = type;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.accentColor.withValues(alpha: 0.1)
              : AppTheme.backgroundSecondary,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppTheme.accentColor : AppTheme.borderLight,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Radio button
            Container(
              width: 20,
              height: 20,
              margin: const EdgeInsets.only(top: 2),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color:
                      isSelected ? AppTheme.accentColor : AppTheme.borderLight,
                  width: 2,
                ),
                color: isSelected ? AppTheme.accentColor : AppTheme.white,
              ),
              child: isSelected
                  ? const Icon(
                      Icons.circle,
                      size: 10,
                      color: AppTheme.white,
                    )
                  : null,
            ),
            const SizedBox(width: AppTheme.spacingMd),

            // Security info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isSelected
                          ? AppTheme.accentColor
                          : AppTheme.textPrimaryColor,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingXs),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  if (features.isNotEmpty) ...[
                    const SizedBox(height: AppTheme.spacingSm),
                    ...features.map((feature) => Padding(
                          padding: const EdgeInsets.only(bottom: 2),
                          child: Text(
                            '• $feature',
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        )),
                  ],
                ],
              ),
            ),

            // Price
            Text(
              price,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: isSelected
                    ? AppTheme.accentColor
                    : AppTheme.textPrimaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceBreakdownSection() {
    final baseAmount = _calculateBaseAmount();
    final insuranceCost = _calculateInsuranceCost();
    final securityCost = _calculateSecurityCost();
    final totalAmount = _calculateTotalAmount();

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingLg),
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor,
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          const Text(
            'Price Breakdown',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),

          // Price breakdown items
          Column(
            children: [
              // Car rental cost
              _buildPriceRow(
                'Car rental (${_calculateDays()} day${_calculateDays() > 1 ? 's' : ''})',
                '\$${baseAmount.toInt()}',
              ),

              // Insurance cost (if selected)
              if (insuranceCost > 0) ...[
                const SizedBox(height: AppTheme.spacingSm),
                _buildPriceRow(
                  'Insurance (${_selectedInsurance.name})',
                  '\$${insuranceCost.toInt()}',
                ),
              ],

              // Security cost (if selected)
              if (securityCost > 0) ...[
                const SizedBox(height: AppTheme.spacingSm),
                _buildPriceRow(
                  'Security (${_selectedSecurity.name})',
                  '\$${securityCost.toInt()}',
                ),
              ],

              // Divider
              const SizedBox(height: AppTheme.spacingMd),
              Container(
                height: 1,
                color: AppTheme.borderLight,
              ),
              const SizedBox(height: AppTheme.spacingMd),

              // Total amount
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Total Amount',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  Text(
                    '\$${totalAmount.toInt()}',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.accentColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRow(String label, String amount) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        Text(
          amount,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
      ],
    );
  }
}
