import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:culture_connect/theme/app_theme.dart';

class ExperienceCard extends StatefulWidget {
  final String title;
  final String location;
  final String imageUrl;
  final double? rating;
  final String? price;
  final String? duration;
  final VoidCallback? onTap;
  final bool isHorizontal;
  final bool isFeatured;
  final String? category;
  final int? reviewCount;

  const ExperienceCard({
    super.key,
    required this.title,
    required this.location,
    required this.imageUrl,
    this.rating,
    this.price,
    this.duration,
    this.onTap,
    this.isHorizontal = false,
    this.isFeatured = false,
    this.category,
    this.reviewCount,
  });

  @override
  State<ExperienceCard> createState() => _ExperienceCardState();
}

class _ExperienceCardState extends State<ExperienceCard>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _favoriteController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _favoriteAnimation;

  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _hoverController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: AppTheme.curveEmphasized,
    ));

    _favoriteController = AnimationController(
      duration: AppTheme.animationFast,
      vsync: this,
    );
    _favoriteAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _favoriteController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _favoriteController.dispose();
    super.dispose();
  }

  void _handleHoverEnter(PointerEnterEvent event) {
    _hoverController.forward();
  }

  void _handleHoverExit(PointerExitEvent event) {
    _hoverController.reverse();
  }

  void _toggleFavorite() {
    setState(() {
      _isFavorite = !_isFavorite;
    });
    _favoriteController.forward().then((_) {
      _favoriteController.reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: _handleHoverEnter,
      onExit: _handleHoverExit,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(8),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: widget.onTap,
                  borderRadius: BorderRadius.circular(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Flat Design Image Section
                      _buildImageSection(),
                      // Flat Design Content Section
                      _buildContentSection(),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildImageSection() {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
          child: Stack(
            children: [
              CachedNetworkImage(
                imageUrl: widget.imageUrl,
                width: double.infinity,
                height: 200,
                fit: BoxFit.cover,
                fadeInDuration: AppTheme.animationFast,
                // Performance optimizations
                memCacheWidth: 400,
                memCacheHeight: 300,
                maxWidthDiskCache: 800,
                maxHeightDiskCache: 600,
                placeholder: (context, url) => Container(
                  width: double.infinity,
                  height: 200,
                  color: AppTheme.surfaceVariant,
                  child: const Icon(
                    Icons.image_outlined,
                    color: AppTheme.textSecondaryColor,
                    size: 32,
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  width: double.infinity,
                  height: 200,
                  color: AppTheme.surfaceVariant,
                  child: const Icon(
                    Icons.broken_image_outlined,
                    color: AppTheme.textSecondaryColor,
                    size: 32,
                  ),
                ),
              ),
              // Gradient overlay for better text readability
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 60,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withAlpha(128),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        // Flat Category badge
        if (widget.category != null)
          Positioned(
            top: AppTheme.spacingMedium,
            left: AppTheme.spacingMedium,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingSmall,
                vertical: AppTheme.spacingXS,
              ),
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                widget.category!,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        // Flat Favorite Button
        Positioned(
          top: AppTheme.spacingMedium,
          right: AppTheme.spacingMedium,
          child: AnimatedBuilder(
            animation: _favoriteAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _favoriteAnimation.value,
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(12),
                    onTap: _toggleFavorite,
                    child: Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(240),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: AppTheme.outline,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        _isFavorite ? Icons.favorite : Icons.favorite_border,
                        color: _isFavorite
                            ? AppTheme.errorColor
                            : AppTheme.textSecondaryColor,
                        size: 16,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildContentSection() {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          // Location
          Row(
            children: [
              const Icon(
                Icons.location_on_outlined,
                size: 16,
                color: AppTheme.textSecondaryColor,
              ),
              const SizedBox(width: AppTheme.spacingXS),
              Expanded(
                child: Text(
                  widget.location,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          // Rating and Price Row
          Row(
            children: [
              if (widget.rating != null) ...[
                const Icon(
                  Icons.star_rounded,
                  size: 16,
                  color: Color(0xFFFFB800),
                ),
                const SizedBox(width: AppTheme.spacingXS),
                Text(
                  widget.rating!.toStringAsFixed(1),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingXS),
                if (widget.reviewCount != null)
                  Flexible(
                    child: Text(
                      '(${widget.reviewCount})',
                      style: const TextStyle(
                        fontSize: 13,
                        color: AppTheme.textSecondaryColor,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
              ],
              const Spacer(),
              if (widget.price != null)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacingSmall,
                    vertical: AppTheme.spacingXS,
                  ),
                  decoration: BoxDecoration(
                    gradient: AppTheme.accentGradient,
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusSmall),
                  ),
                  child: Text(
                    widget.price!,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
