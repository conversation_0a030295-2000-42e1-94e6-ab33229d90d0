import 'package:flutter/material.dart';
import 'package:culture_connect/theme/kaia_design_tokens.dart';

/// Clean and consistent experience card following the simplified design approach
/// Supports both grid and list modes with proper responsive design
class ExperienceCard extends StatelessWidget {
  final String title;
  final String location;
  final String imageUrl;
  final double rating;
  final int reviewCount;
  final String price;
  final String duration;
  final List<String> tags;
  final String category;
  final bool isGridMode;
  final VoidCallback onPressed;
  final VoidCallback? onFavoritePressed;
  final bool isFavorite;

  const ExperienceCard({
    super.key,
    required this.title,
    required this.location,
    required this.imageUrl,
    required this.rating,
    required this.reviewCount,
    required this.price,
    required this.duration,
    required this.tags,
    required this.category,
    required this.isGridMode,
    required this.onPressed,
    this.onFavoritePressed,
    this.isFavorite = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: isGridMode ? double.infinity : 600,
          // Further reduced height constraint for grid mode to prevent overflow
          maxHeight: isGridMode
              ? 260
              : double.infinity, // Reduced from 270 to 260 for ultra-tight fit
        ),
        decoration: BoxDecoration(
          color: KaiaDesignTokens.neutralWhite,
          borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
          border: Border.all(
            color: KaiaDesignTokens.neutralGray200,
            width: 1,
          ),
          boxShadow: KaiaDesignTokens.shadowMd,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min, // Important: minimize column size
            children: [
              // Image Section
              _buildImageSection(),

              // Content Section - Use Flexible in grid mode to prevent overflow
              isGridMode
                  ? Flexible(child: _buildContentSection())
                  : _buildContentSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Stack(
      children: [
        // Main Image
        Container(
          height: isGridMode ? 140 : 180,
          width: double.infinity,
          color: KaiaDesignTokens.neutralGray100,
          child: imageUrl.isNotEmpty
              ? Image.network(
                  imageUrl,
                  fit: BoxFit.cover,
                  // Performance optimizations (following HotelCard pattern)
                  cacheWidth: isGridMode ? 300 : 400,
                  cacheHeight: isGridMode ? 200 : 300,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: KaiaDesignTokens.neutralGray100,
                      child: Icon(
                        Icons.image_outlined,
                        size: isGridMode ? 32 : 48,
                        color: KaiaDesignTokens.neutralGray400,
                      ),
                    );
                  },
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Container(
                      color: KaiaDesignTokens.neutralGray100,
                      child: Center(
                        child: CircularProgressIndicator(
                          value: loadingProgress.expectedTotalBytes != null
                              ? loadingProgress.cumulativeBytesLoaded /
                                  loadingProgress.expectedTotalBytes!
                              : null,
                          color: KaiaDesignTokens.primaryIndigo,
                          strokeWidth: 2,
                        ),
                      ),
                    );
                  },
                )
              : Icon(
                  Icons.image_outlined,
                  size: isGridMode ? 32 : 48,
                  color: KaiaDesignTokens.neutralGray400,
                ),
        ),

        // Category Badge - Top Left
        Positioned(
          top: KaiaDesignTokens.spacing8,
          left: KaiaDesignTokens.spacing8,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: KaiaDesignTokens.spacing8,
              vertical: KaiaDesignTokens.spacing4,
            ),
            decoration: BoxDecoration(
              color: _getCategoryColor().withAlpha(230),
              borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusSm),
            ),
            child: Text(
              category,
              style: TextStyle(
                fontSize: isGridMode ? 10 : 11,
                fontWeight: FontWeight.w600,
                color: KaiaDesignTokens.neutralWhite,
              ),
            ),
          ),
        ),

        // Favorite Button - Top Right
        if (onFavoritePressed != null)
          Positioned(
            top: KaiaDesignTokens.spacing8,
            right: KaiaDesignTokens.spacing8,
            child: GestureDetector(
              onTap: onFavoritePressed,
              child: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: KaiaDesignTokens.neutralWhite.withAlpha(230),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  isFavorite ? Icons.favorite : Icons.favorite_border,
                  size: 18,
                  color:
                      isFavorite ? Colors.red : KaiaDesignTokens.neutralGray600,
                ),
              ),
            ),
          ),

        // Rating Badge - Bottom Right
        Positioned(
          bottom: KaiaDesignTokens.spacing8,
          right: KaiaDesignTokens.spacing8,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: KaiaDesignTokens.spacing6,
              vertical: KaiaDesignTokens.spacing4,
            ),
            decoration: BoxDecoration(
              color:
                  KaiaDesignTokens.neutralGray900.withAlpha(179), // 70% opacity
              borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusSm),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.star,
                  size: 12,
                  color: Colors.amber,
                ),
                const SizedBox(width: 2),
                Text(
                  rating.toStringAsFixed(1),
                  style: TextStyle(
                    fontSize: isGridMode ? 10 : 11,
                    fontWeight: FontWeight.w600,
                    color: KaiaDesignTokens.neutralWhite,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContentSection() {
    // Ultra-aggressive grid mode optimizations to prevent 8.3px overflow
    final contentPadding = isGridMode
        ? const EdgeInsets.all(8) // Further reduced from 10 to 8
        : const EdgeInsets.all(KaiaDesignTokens.spacing16);

    return Padding(
      padding: contentPadding,
      child: ConstrainedBox(
        constraints: isGridMode
            ? const BoxConstraints(
                maxHeight: 110) // Reduced from 125 to 110 to prevent overflow
            : const BoxConstraints(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min, // Important: minimize column size
          children: [
            // Title
            Flexible(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: isGridMode ? 14 : 16,
                  fontWeight: FontWeight.w700,
                  color: KaiaDesignTokens.neutralGray900,
                  height: 1.2,
                ),
                maxLines: isGridMode
                    ? 1
                    : 2, // Keep 1 line in grid mode to save space
                overflow: TextOverflow.ellipsis,
              ),
            ),

            SizedBox(height: isGridMode ? 2 : 6), // Further reduced from 3 to 2

            // Location
            Flexible(
              child: Row(
                children: [
                  Icon(
                    Icons.location_on_outlined,
                    size: isGridMode ? 12 : 14,
                    color: KaiaDesignTokens.neutralGray500,
                  ),
                  const SizedBox(width: 2),
                  Expanded(
                    child: Text(
                      location,
                      style: TextStyle(
                        fontSize: isGridMode ? 11 : 13,
                        color: KaiaDesignTokens.neutralGray600,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: isGridMode ? 2 : 8), // Further reduced from 4 to 2

            // Tags - Remove completely in grid mode to save space, keep in list mode
            if (tags.isNotEmpty && !isGridMode)
              Wrap(
                spacing: 4,
                runSpacing: 4,
                children: tags
                    .take(3) // Show 3 tags in list mode only
                    .map((tag) => Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: KaiaDesignTokens.neutralGray100,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: KaiaDesignTokens.neutralGray200,
                              width: 0.5,
                            ),
                          ),
                          child: Text(
                            tag,
                            style: const TextStyle(
                              fontSize: 10,
                              color: KaiaDesignTokens.neutralGray700,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ))
                    .toList(),
              ),

            // Add spacing only if tags are shown (list mode)
            if (tags.isNotEmpty && !isGridMode) const SizedBox(height: 10),

            // Spacer to push price to bottom in grid mode
            if (isGridMode) const Spacer(),

            // Bottom Row - Price only in grid mode, full info in list mode
            Flexible(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Price - Use Flexible to prevent overflow
                  Flexible(
                    child: Text(
                      price,
                      style: TextStyle(
                        fontSize: isGridMode ? 14 : 16,
                        fontWeight: FontWeight.w700,
                        color: KaiaDesignTokens.primaryIndigo,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // Duration and Reviews - Only show in list mode
                  if (!isGridMode)
                    Flexible(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.access_time,
                            size: 12,
                            color: KaiaDesignTokens.neutralGray500,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            duration,
                            style: const TextStyle(
                              fontSize: 11,
                              color: KaiaDesignTokens.neutralGray600,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '($reviewCount reviews)',
                            style: const TextStyle(
                              fontSize: 11,
                              color: KaiaDesignTokens.neutralGray500,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),

            // Remove duration section completely in grid mode to prevent overflow
            // All secondary information (duration, reviews) is now only in list mode
          ], // Close the Column children list
        ),
      ),
    );
  }

  Color _getCategoryColor() {
    switch (category.toLowerCase()) {
      case 'cultural':
        return KaiaDesignTokens.primaryIndigo;
      case 'adventure':
        return Colors.orange;
      case 'food & drink':
        return Colors.green;
      case 'nature':
        return Colors.teal;
      case 'historical':
        return Colors.brown;
      case 'art & museums':
        return Colors.purple;
      case 'nightlife':
        return Colors.pink;
      case 'wellness':
        return Colors.blue;
      default:
        return KaiaDesignTokens.neutralGray600;
    }
  }
}
