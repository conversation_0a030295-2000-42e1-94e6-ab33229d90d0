{"expo": {"name": "Culture Connect", "slug": "culture-connect", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "culture-connect", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.cultureconnect.app"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.cultureconnect.app"}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router"], "experiments": {"typedRoutes": true}}}