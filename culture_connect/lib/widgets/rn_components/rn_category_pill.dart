import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Pixel-perfect React Native CategoryPill replica
/// Matches the exact styling from React Native reference images
class RNCategoryPill extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback onPressed;

  const RNCategoryPill({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        constraints: const BoxConstraints(minWidth: 120), // Exact React Native minWidth
        padding: const EdgeInsets.symmetric(
          horizontal: 24, // React Native spacing.lg
          vertical: 16,   // React Native spacing.md
        ),
        margin: const EdgeInsets.only(right: 16), // React Native spacing.md
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.textPrimaryColor : AppTheme.white, // Selected: #222222, Unselected: #FFFFFF
          borderRadius: BorderRadius.circular(28), // Exact React Native pill radius
          border: Border.all(
            color: isSelected ? AppTheme.textPrimaryColor : AppTheme.borderColor, // Selected: #222222, Unselected: #DDDDDD
            width: 1.5, // Exact React Native border width
          ),
          boxShadow: [
            BoxShadow(
              color: isSelected ? AppTheme.shadowDark : AppTheme.shadowColor, // Selected: darker shadow
              offset: isSelected ? const Offset(0, 4) : const Offset(0, 2),
              blurRadius: isSelected ? 8 : 4,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Center(
          child: Text(
            label,
            style: TextStyle(
              fontFamily: AppTheme.fontFamily,
              fontSize: AppTheme.fontSizeSm, // 14px
              fontWeight: isSelected ? AppTheme.fontWeightSemibold : AppTheme.fontWeightMedium,
              color: isSelected ? AppTheme.white : AppTheme.textPrimaryColor,
            ),
          ),
        ),
      ),
    );
  }
}
