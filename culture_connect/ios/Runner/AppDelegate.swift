import Flutter
import UIKit
import GoogleMaps

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> <PERSON><PERSON> {
    // Initialize Google Maps with API key
    // For development/testing, using a placeholder key
    // In production, this should be loaded from a secure configuration
    GMSServices.provideAPIKey("AIzaSyDevelopmentKeyForCultureConnectApp")

    // Firebase is configured in Dart code (main.dart)
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
