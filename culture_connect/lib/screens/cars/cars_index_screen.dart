import 'package:flutter/material.dart';
import 'package:culture_connect/models/car.dart';
import 'package:culture_connect/services/car_service.dart';
import 'package:culture_connect/widgets/cars/car_card.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/screens/cars/car_detail_screen.dart';
import 'package:culture_connect/screens/cars/car_type_listing_screen.dart';

/// Main Cars index screen matching React Native implementation
/// Shows category-based horizontal scrolling sections
class CarsIndexScreen extends StatefulWidget {
  const CarsIndexScreen({super.key});

  @override
  State<CarsIndexScreen> createState() => _CarsIndexScreenState();
}

class _CarsIndexScreenState extends State<CarsIndexScreen> {
  final List<String> _categories = [
    'SUV',
    'Hatchback',
    'Sedan',
    'Luxury',
    'Luxury Bus'
  ];

  void _handleCarPress(Car car) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CarDetailScreen(car: car),
      ),
    );
  }

  void _handleBackPress() {
    Navigator.pop(context);
  }

  void _handleFilterPress() {
    // TODO: Implement filter functionality
    debugPrint('Filter pressed');
  }

  Widget _buildCategorySection(String category) {
    final categoryCars = CarService.getCarsByCategory(category);

    if (categoryCars.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Category header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                category,
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                  letterSpacing: -0.5,
                ),
              ),
              GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          CarTypeListingScreen(category: category),
                    ),
                  );
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'View all',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.accentColor,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingXs),
                    const Icon(
                      Icons.arrow_upward_outlined,
                      size: 16,
                      color: AppTheme.accentColor,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppTheme.spacingXl),
        // Horizontal car list with precise constraint handling
        SizedBox(
          height:
              450, // Space for content (400px) + shadow extent (36px) + breathing room (14px)
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
            itemCount: categoryCars.length,
            itemBuilder: (context, index) {
              final car = categoryCars[index];
              return Container(
                width: MediaQuery.of(context).size.width * 0.85,
                margin: EdgeInsets.only(
                  right:
                      index < categoryCars.length - 1 ? AppTheme.spacingLg : 0,
                  bottom: AppTheme
                      .spacingSm, // Visual breathing room for floating effect
                ),
                child: CarCard(
                  car: car,
                  onPressed: () => _handleCarPress(car),
                  isLarge: true,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF8F9FA),
        elevation: 0,
        leading: GestureDetector(
          onTap: _handleBackPress,
          child: Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.white,
              borderRadius: BorderRadius.circular(22),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.shadowColor,
                  offset: const Offset(0, 2),
                  blurRadius: 8,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: const Icon(
              Icons.arrow_back,
              color: AppTheme.accentColor,
              size: 24,
            ),
          ),
        ),
        title: const Text(
          'Car rent',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
            letterSpacing: -0.5,
          ),
        ),
        centerTitle: true,
        actions: [
          GestureDetector(
            onTap: _handleFilterPress,
            child: Container(
              margin: const EdgeInsets.all(8),
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingMd,
                vertical: AppTheme.spacingSm,
              ),
              decoration: BoxDecoration(
                color: AppTheme.accentColor,
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Text(
                'Filters',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.white,
                ),
              ),
            ),
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1),
          child: Container(
            height: 1,
            color: const Color(0xFFE9ECEF),
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.only(top: AppTheme.spacingXl),
          child: Column(
            children: [
              // Category sections
              ..._categories.map((category) => Column(
                    children: [
                      _buildCategorySection(category),
                      const SizedBox(
                          height: AppTheme.spacingXxl + AppTheme.spacingLg),
                    ],
                  )),
            ],
          ),
        ),
      ),
    );
  }
}
