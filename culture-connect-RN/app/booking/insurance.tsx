import React, { useState } from "react";
import { StyleSheet, View, Text, ScrollView, Pressable, Image } from "react-native";
import { useRouter } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { ArrowLeft, Shield, Check, Info } from "lucide-react-native";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { InsuranceCard } from "@/components/InsuranceCard";
import { Button } from "@/components/Button";
import { insuranceOptions } from "@/mocks/insurance";
import { Insurance } from "@/types";

export default function InsuranceBookingScreen() {
  const router = useRouter();
  const [selectedInsurance, setSelectedInsurance] = useState<string | null>(null);

  const handleInsuranceSelect = (insurance: Insurance) => {
    setSelectedInsurance(insurance.id);
  };

  const handlePurchase = () => {
    if (selectedInsurance) {
      router.push("/booking/summary");
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <View style={styles.header}>
        <Pressable style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </Pressable>
        <Text style={styles.headerTitle}>Travel Insurance</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <Text style={styles.subtitle}>
            Protect your journey with comprehensive coverage
          </Text>
        </View>

        <View style={styles.bannerContainer}>
          <Image
            source={{ uri: "https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80" }}
            style={styles.bannerImage}
          />
          <View style={styles.bannerContent}>
            <Shield size={24} color={colors.white} />
            <Text style={styles.bannerTitle}>Travel with Confidence</Text>
            <Text style={styles.bannerText}>
              Get coverage for medical emergencies, trip cancellations, lost
              baggage, and more.
            </Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Choose Your Coverage</Text>
          
          {insuranceOptions.map((insurance) => (
            <InsuranceCard
              key={insurance.id}
              insurance={insurance}
              onPress={handleInsuranceSelect}
              isSelected={selectedInsurance === insurance.id}
            />
          ))}
        </View>

        <View style={styles.benefitsSection}>
          <Text style={styles.benefitsSectionTitle}>Key Benefits</Text>
          
          <View style={styles.benefitItem}>
            <View style={styles.benefitIconContainer}>
              <Check size={16} color={colors.white} />
            </View>
            <View style={styles.benefitContent}>
              <Text style={styles.benefitTitle}>24/7 Emergency Assistance</Text>
              <Text style={styles.benefitText}>
                Access to round-the-clock emergency support wherever you are.
              </Text>
            </View>
          </View>
          
          <View style={styles.benefitItem}>
            <View style={styles.benefitIconContainer}>
              <Check size={16} color={colors.white} />
            </View>
            <View style={styles.benefitContent}>
              <Text style={styles.benefitTitle}>Medical Coverage</Text>
              <Text style={styles.benefitText}>
                Coverage for medical emergencies, hospital stays, and evacuation.
              </Text>
            </View>
          </View>
          
          <View style={styles.benefitItem}>
            <View style={styles.benefitIconContainer}>
              <Check size={16} color={colors.white} />
            </View>
            <View style={styles.benefitContent}>
              <Text style={styles.benefitTitle}>Trip Cancellation</Text>
              <Text style={styles.benefitText}>
                Reimbursement for prepaid, non-refundable trip costs.
              </Text>
            </View>
          </View>
          
          <View style={styles.benefitItem}>
            <View style={styles.benefitIconContainer}>
              <Check size={16} color={colors.white} />
            </View>
            <View style={styles.benefitContent}>
              <Text style={styles.benefitTitle}>Baggage Protection</Text>
              <Text style={styles.benefitText}>
                Coverage for lost, stolen, or damaged baggage during your trip.
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.infoSection}>
          <View style={styles.infoHeader}>
            <Info size={20} color={colors.primary} />
            <Text style={styles.infoTitle}>Important Information</Text>
          </View>
          <Text style={styles.infoText}>
            Insurance coverage varies by plan and provider. Please review the
            policy details carefully before purchase. Pre-existing conditions may
            not be covered. Contact customer support for assistance.
          </Text>
        </View>

        <View style={styles.actionContainer}>
          <Button
            title="Add to Booking"
            onPress={handlePurchase}
            variant="primary"
            fullWidth
            disabled={!selectedInsurance}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  headerTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  content: {
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.lg,
    paddingBottom: spacing.md,
  },
  subtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textLight,
    textAlign: "center",
  },
  bannerContainer: {
    marginHorizontal: spacing.xl,
    marginVertical: spacing.md,
    borderRadius: 16,
    overflow: "hidden",
    height: 180,
  },
  bannerImage: {
    width: "100%",
    height: "100%",
  },
  bannerContent: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(30, 61, 89, 0.7)",
    padding: spacing.xl,
    justifyContent: "center",
  },
  bannerTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    marginTop: spacing.sm,
    marginBottom: spacing.xs,
  },
  bannerText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.white,
    opacity: 0.9,
  },
  section: {
    paddingHorizontal: spacing.xl,
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.md,
  },
  benefitsSection: {
    paddingHorizontal: spacing.xl,
    marginBottom: spacing.xl,
  },
  benefitsSectionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.md,
  },
  benefitItem: {
    flexDirection: "row",
    marginBottom: spacing.md,
  },
  benefitIconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.sm,
    marginTop: 2,
  },
  benefitContent: {
    flex: 1,
  },
  benefitTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  benefitText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textLight,
  },
  infoSection: {
    marginHorizontal: spacing.xl,
    marginBottom: spacing.xl,
    padding: spacing.lg,
    backgroundColor: colors.white,
    borderRadius: 16,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },
  infoHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  infoTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  infoText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textLight,
    lineHeight: 20,
  },
  actionContainer: {
    paddingHorizontal: spacing.xl,
    paddingBottom: spacing.xl,
  },
});