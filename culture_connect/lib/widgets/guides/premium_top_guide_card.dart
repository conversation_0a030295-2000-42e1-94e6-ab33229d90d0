import 'package:flutter/material.dart';
import 'package:culture_connect/theme/kaia_design_tokens.dart';
import 'package:culture_connect/models/guide.dart';
import 'package:culture_connect/utils/price_formatter.dart';

/// Premium Top Guide Card with enhanced visual design
/// Features purple badges, premium aesthetics, and intelligent price formatting
/// Follows CultureConnect design system with solid colors only
class PremiumTopGuideCard extends StatelessWidget {
  final Guide guide;
  final VoidCallback onPressed;

  const PremiumTopGuideCard({
    super.key,
    required this.guide,
    required this.onPressed,
  });

  /// Calculates optimal font size for specialty tags to prevent text cropping
  /// Uses intelligent sizing based on text length while maintaining readability
  double _getOptimalFontSize(String text) {
    // Base font size from design tokens
    const double baseFontSize = KaiaDesignTokens.fontSizeXs; // 12.0

    // For shorter text (≤12 chars), use base size
    if (text.length <= 12) {
      return baseFontSize;
    }

    // For medium text (13-16 chars), slightly reduce
    if (text.length <= 16) {
      return baseFontSize - 1.0; // 11.0
    }

    // For longer text (17+ chars), use smaller size for better fit
    return baseFontSize - 2.0; // 10.0
  }

  /// Calculates optimal container padding that scales proportionally with font size
  /// Ensures containers are properly sized for the dynamic text content
  EdgeInsets _getOptimalPadding(String text) {
    // For shorter text (≤12 chars) with 12px font, use standard padding
    if (text.length <= 12) {
      return const EdgeInsets.symmetric(
        horizontal: KaiaDesignTokens.spacing8,
        vertical: KaiaDesignTokens.spacing6,
      );
    }

    // For medium text (13-16 chars) with 11px font, slightly reduce vertical padding
    if (text.length <= 16) {
      return const EdgeInsets.symmetric(
        horizontal: KaiaDesignTokens.spacing8,
        vertical: KaiaDesignTokens.spacing4, // Reduced for 11px font
      );
    }

    // For longer text (17+ chars) with 10px font, use minimal padding
    return const EdgeInsets.symmetric(
      horizontal:
          KaiaDesignTokens.spacing6, // Reduced horizontal for more text space
      vertical: KaiaDesignTokens.spacing4, // Minimal vertical for 10px font
    );
  }

  /// Calculates optimal border radius that maintains capsule appearance
  /// Scales proportionally with container size for consistent visual design
  double _getOptimalBorderRadius(String text) {
    // For shorter text with larger containers, use standard radius
    if (text.length <= 12) {
      return KaiaDesignTokens.radiusSm; // 8.0
    }

    // For medium and longer text with smaller containers, use slightly smaller radius
    // This maintains the capsule proportion while accommodating reduced padding
    return KaiaDesignTokens
        .radiusXs; // 4.0 - maintains capsule look with smaller containers
  }

  /// Calculates optimal minimum height for specialty tag containers
  /// Ensures adequate vertical space for text with proper line height
  double _getOptimalMinHeight(String text) {
    final fontSize = _getOptimalFontSize(text);
    final padding = _getOptimalPadding(text);

    // Calculate minimum height based on font size, line height, and padding
    // Line height of 1.2 means text height = fontSize * 1.2
    final textHeight = fontSize * 1.2;
    final totalVerticalPadding = padding.vertical;

    // Add small buffer (2px) to prevent any potential clipping
    return textHeight + totalVerticalPadding + 2.0;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 220,
        margin: const EdgeInsets.symmetric(
          horizontal: KaiaDesignTokens.spacing8,
          vertical: KaiaDesignTokens.spacing12,
        ),
        decoration: BoxDecoration(
          color: KaiaDesignTokens.neutralWhite,
          borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
          boxShadow: KaiaDesignTokens.shadowMd,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image Section with Badges
            Stack(
              children: [
                // Profile Image
                Container(
                  height: 140,
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(KaiaDesignTokens.radiusLg),
                      topRight: Radius.circular(KaiaDesignTokens.radiusLg),
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(KaiaDesignTokens.radiusLg),
                      topRight: Radius.circular(KaiaDesignTokens.radiusLg),
                    ),
                    child: guide.profileImageUrl != null &&
                            guide.profileImageUrl!.isNotEmpty
                        ? Image.network(
                            guide.profileImageUrl!,
                            fit: BoxFit.cover,
                            cacheWidth: 220,
                            cacheHeight: 140,
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Container(
                                color: KaiaDesignTokens.neutralGray100,
                                child: const Center(
                                  child: SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: KaiaDesignTokens.primaryIndigo,
                                    ),
                                  ),
                                ),
                              );
                            },
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: KaiaDesignTokens.neutralGray100,
                                child: Icon(
                                  guide.guideType == GuideType.company
                                      ? Icons.business
                                      : Icons.person,
                                  size: 40,
                                  color: KaiaDesignTokens.neutralGray400,
                                ),
                              );
                            },
                          )
                        : Container(
                            color: KaiaDesignTokens.neutralGray100,
                            child: Icon(
                              guide.guideType == GuideType.company
                                  ? Icons.business
                                  : Icons.person,
                              size: 40,
                              color: KaiaDesignTokens.neutralGray400,
                            ),
                          ),
                  ),
                ),

                // Top Badges
                Positioned(
                  top: KaiaDesignTokens.spacing12,
                  left: KaiaDesignTokens.spacing12,
                  child: Row(
                    children: [
                      // Guide Type Badge
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: KaiaDesignTokens.spacing8,
                          vertical: KaiaDesignTokens.spacing4,
                        ),
                        decoration: BoxDecoration(
                          color: KaiaDesignTokens.primaryIndigo,
                          borderRadius:
                              BorderRadius.circular(KaiaDesignTokens.radiusSm),
                        ),
                        child: Text(
                          guide.guideType == GuideType.company
                              ? 'COMPANY'
                              : 'GUIDE',
                          style: const TextStyle(
                            color: KaiaDesignTokens.neutralWhite,
                            fontSize: KaiaDesignTokens.fontSizeXs,
                            fontWeight: KaiaDesignTokens.fontWeightBold,
                          ),
                        ),
                      ),

                      // Certification Badge
                      if (guide.certifications.isNotEmpty) ...[
                        const SizedBox(width: KaiaDesignTokens.spacing6),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: KaiaDesignTokens.spacing6,
                            vertical: KaiaDesignTokens.spacing4,
                          ),
                          decoration: BoxDecoration(
                            color: KaiaDesignTokens.successGreen,
                            borderRadius: BorderRadius.circular(
                                KaiaDesignTokens.radiusSm),
                          ),
                          child: const Icon(
                            Icons.verified,
                            color: KaiaDesignTokens.neutralWhite,
                            size: 12,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Rating Badge
                Positioned(
                  top: KaiaDesignTokens.spacing12,
                  right: KaiaDesignTokens.spacing12,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: KaiaDesignTokens.spacing8,
                      vertical: KaiaDesignTokens.spacing4,
                    ),
                    decoration: BoxDecoration(
                      color:
                          KaiaDesignTokens.neutralWhite.withValues(alpha: 0.95),
                      borderRadius:
                          BorderRadius.circular(KaiaDesignTokens.radiusSm),
                      boxShadow: KaiaDesignTokens.shadowSm,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.star,
                          color: KaiaDesignTokens.warningAmber,
                          size: 12,
                        ),
                        const SizedBox(width: KaiaDesignTokens.spacing2),
                        Text(
                          (guide.rating ?? 0.0).toStringAsFixed(1),
                          style: const TextStyle(
                            color: KaiaDesignTokens.neutralGray800,
                            fontSize: KaiaDesignTokens.fontSizeXs,
                            fontWeight: KaiaDesignTokens.fontWeightBold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            // Content Section
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Name
                    Text(
                      guide.name,
                      style: const TextStyle(
                        color: KaiaDesignTokens.neutralGray800,
                        fontSize: KaiaDesignTokens.fontSizeMd,
                        fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: KaiaDesignTokens.spacing4),

                    // Location
                    if (guide.location != null && guide.location!.isNotEmpty)
                      Flexible(
                        child: Row(
                          children: [
                            const Icon(
                              Icons.location_on,
                              color: KaiaDesignTokens.neutralGray500,
                              size: 14,
                            ),
                            const SizedBox(width: KaiaDesignTokens.spacing4),
                            Expanded(
                              child: Text(
                                guide.location!,
                                style: const TextStyle(
                                  color: KaiaDesignTokens.neutralGray500,
                                  fontSize: KaiaDesignTokens.fontSizeSm,
                                  fontWeight:
                                      KaiaDesignTokens.fontWeightRegular,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    const SizedBox(height: KaiaDesignTokens.spacing8),

                    // Specialty/Service Highlights
                    if (guide.specialties.isNotEmpty)
                      ConstrainedBox(
                        constraints: BoxConstraints(
                          minHeight:
                              _getOptimalMinHeight(guide.specialties.first),
                        ),
                        child: Container(
                          padding: _getOptimalPadding(guide.specialties.first),
                          decoration: BoxDecoration(
                            color: KaiaDesignTokens.primaryIndigo
                                .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(
                                _getOptimalBorderRadius(
                                    guide.specialties.first)),
                          ),
                          child: Text(
                            guide.specialties.first,
                            style: TextStyle(
                              color: KaiaDesignTokens.primaryIndigo,
                              fontSize:
                                  _getOptimalFontSize(guide.specialties.first),
                              fontWeight: KaiaDesignTokens.fontWeightMedium,
                              height:
                                  1.2, // Consistent with minHeight calculation
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),

                    const Expanded(child: SizedBox()),

                    // Price Range
                    if (guide.priceRange.isNotEmpty)
                      ConstrainedBox(
                        constraints: const BoxConstraints(maxHeight: 32),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: KaiaDesignTokens.spacing8,
                            vertical: KaiaDesignTokens.spacing6,
                          ),
                          decoration: BoxDecoration(
                            color: guide.guideType == GuideType.company
                                ? KaiaDesignTokens.accentEmerald
                                    .withValues(alpha: 0.1)
                                : KaiaDesignTokens.secondaryCyan
                                    .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(
                                KaiaDesignTokens.radiusSm),
                          ),
                          child: Text(
                            PriceFormatter.formatPriceWithLocation(
                                guide.priceRange, guide.location ?? ''),
                            style: TextStyle(
                              color: guide.guideType == GuideType.company
                                  ? KaiaDesignTokens.accentEmerald
                                  : KaiaDesignTokens.secondaryCyan,
                              fontSize: KaiaDesignTokens.fontSizeXs,
                              fontWeight: KaiaDesignTokens.fontWeightBold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
