import { Car } from '@/types';

interface ExtendedCar extends Car {
  brand: string;
  model: string;
  reviewCount: number;
  doors: number;
  airConditioning: boolean;
  description: string;
}

export const cars: ExtendedCar[] = [
  {
    id: '1',
    name: 'Mazda CX-3',
    brand: 'Mazda',
    model: 'CX-3',
    category: 'SUV',
    pricePerDay: 200,
    rating: 5.0,
    reviewCount: 128,
    imageUrl: 'https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    features: ['GPS Navigation', 'Bluetooth', 'USB Charging', 'Backup Camera'],
    fuelType: 'Petrol',
    transmission: 'Automatic',
    seats: 5,
    doors: 4,
    airConditioning: true,
    available: true,
    location: 'Downtown',
    description: 'Compact SUV perfect for city driving and weekend getaways. Features modern technology and excellent fuel efficiency.'
  },
  {
    id: '2',
    name: 'Buick Envision',
    brand: 'Buick',
    model: 'Envision',
    category: 'SUV',
    pricePerDay: 200,
    rating: 4.8,
    reviewCount: 95,
    imageUrl: 'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    features: ['Premium Sound', 'Heated Seats', 'Panoramic Sunroof', 'Advanced Safety'],
    fuelType: 'Petrol',
    transmission: 'Automatic',
    seats: 5,
    doors: 4,
    airConditioning: true,
    available: true,
    location: 'Airport',
    description: 'Luxury SUV with premium features and spacious interior. Perfect for comfortable long-distance travel.'
  },
  {
    id: '3',
    name: 'Mazda 3',
    brand: 'Mazda',
    model: '3',
    category: 'Hatchback',
    pricePerDay: 100,
    rating: 4.8,
    reviewCount: 203,
    imageUrl: 'https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    features: ['Sport Mode', 'Premium Interior', 'Advanced Infotainment', 'LED Headlights'],
    fuelType: 'Petrol',
    transmission: 'Manual',
    seats: 5,
    doors: 4,
    airConditioning: true,
    available: true,
    location: 'City Center',
    description: 'Stylish hatchback with sporty performance and premium interior. Great for urban adventures.'
  },
  {
    id: '4',
    name: 'Toyota Corolla',
    brand: 'Toyota',
    model: 'Corolla',
    category: 'Hatchback',
    pricePerDay: 110,
    rating: 4.9,
    reviewCount: 156,
    imageUrl: 'https://images.unsplash.com/photo-1621007947382-bb3c3994e3fb?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    features: ['Hybrid Engine', 'Toyota Safety Sense', 'Wireless Charging', 'Smart Entry'],
    fuelType: 'Hybrid',
    transmission: 'Automatic',
    seats: 5,
    doors: 4,
    airConditioning: true,
    available: true,
    location: 'Suburbs',
    description: 'Reliable and fuel-efficient hybrid vehicle. Perfect for eco-conscious travelers.'
  },
  {
    id: '5',
    name: 'BMW 3 Series',
    brand: 'BMW',
    model: '3 Series',
    category: 'Sedan',
    pricePerDay: 350,
    rating: 4.9,
    reviewCount: 87,
    imageUrl: 'https://images.unsplash.com/photo-1555215695-3004980ad54e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    features: ['Luxury Interior', 'Performance Package', 'Premium Sound', 'Driver Assistance'],
    fuelType: 'Petrol',
    transmission: 'Automatic',
    seats: 5,
    doors: 4,
    airConditioning: true,
    available: true,
    location: 'Premium Location',
    description: 'Luxury sedan with exceptional performance and premium features. Experience driving excellence.'
  },
  {
    id: '6',
    name: 'Tesla Model 3',
    brand: 'Tesla',
    model: 'Model 3',
    category: 'Electric',
    pricePerDay: 280,
    rating: 4.7,
    reviewCount: 142,
    imageUrl: 'https://images.unsplash.com/photo-1560958089-b8a1929cea89?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    features: ['Autopilot', 'Supercharging', 'Premium Connectivity', 'Over-the-Air Updates'],
    fuelType: 'Electric',
    transmission: 'Automatic',
    seats: 5,
    doors: 4,
    airConditioning: true,
    available: true,
    location: 'Tech District',
    description: 'Revolutionary electric vehicle with cutting-edge technology and zero emissions.'
  },
  {
    id: '7',
    name: 'Audi A4',
    brand: 'Audi',
    model: 'A4',
    category: 'Sedan',
    pricePerDay: 320,
    rating: 4.8,
    reviewCount: 76,
    imageUrl: 'https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    features: ['Quattro AWD', 'Virtual Cockpit', 'Premium Plus', 'Bang & Olufsen Sound'],
    fuelType: 'Petrol',
    transmission: 'Automatic',
    seats: 5,
    doors: 4,
    airConditioning: true,
    available: true,
    location: 'Luxury District',
    description: 'Premium sedan with sophisticated design and advanced technology features.'
  },
  {
    id: '8',
    name: 'Mercedes-Benz C-Class',
    brand: 'Mercedes-Benz',
    model: 'C-Class',
    category: 'Luxury',
    pricePerDay: 450,
    rating: 4.9,
    reviewCount: 63,
    imageUrl: 'https://images.unsplash.com/photo-1618843479313-40f8afb4b4d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    features: ['MBUX Infotainment', 'AMG Line', 'Burmester Sound', 'Active Safety'],
    fuelType: 'Petrol',
    transmission: 'Automatic',
    seats: 5,
    doors: 4,
    airConditioning: true,
    available: true,
    location: 'Premium Location',
    description: 'Luxury sedan with exceptional comfort and cutting-edge technology.'
  },
  {
    id: '9',
    name: 'Lexus ES',
    brand: 'Lexus',
    model: 'ES',
    category: 'Luxury',
    pricePerDay: 380,
    rating: 4.8,
    reviewCount: 91,
    imageUrl: 'https://images.unsplash.com/photo-1609521263047-f8f205293f24?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    features: ['Hybrid Powertrain', 'Mark Levinson Audio', 'Safety System+', 'Luxury Package'],
    fuelType: 'Hybrid',
    transmission: 'Automatic',
    seats: 5,
    doors: 4,
    airConditioning: true,
    available: true,
    location: 'Executive Area',
    description: 'Luxury hybrid sedan combining performance with exceptional fuel efficiency.'
  }
];

export const carCategories = [
  'All',
  'SUV',
  'Hatchback', 
  'Sedan',
  'Luxury',
  'Electric'
];