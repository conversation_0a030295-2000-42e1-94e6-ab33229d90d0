import React, { useState } from "react";
import { StyleSheet, View, Text, ScrollView, Pressable, Dimensions } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Image } from "expo-image";
import { SafeAreaView } from "react-native-safe-area-context";
import { ArrowLeft, Heart, Star, MapPin, Calendar, Clock, Plane, Shield, Car, Hotel, Users } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { Button } from "@/components/Button";
import { ServiceCard } from "@/components/ServiceCard";
import { destinations } from "@/mocks/destinations";

const { width } = Dimensions.get("window");

const additionalServices = [
  {
    id: "flight",
    icon: <Plane size={24} color={colors.primary} />,
    title: "Flight Booking",
    description: "Round-trip flights with flexible dates",
    price: "From $450",
  },
  {
    id: "insurance",
    icon: <Shield size={24} color={colors.primary} />,
    title: "Travel Insurance",
    description: "Comprehensive coverage for your trip",
    price: "From $25",
  },
  {
    id: "hotel",
    icon: <Hotel size={24} color={colors.primary} />,
    title: "Accommodation",
    description: "Hotels and vacation rentals",
    price: "From $120/night",
  },
  {
    id: "pickup",
    icon: <Car size={24} color={colors.primary} />,
    title: "Airport Pickup",
    description: "Private transfer to your hotel",
    price: "From $35",
  },
  {
    id: "security",
    icon: <Users size={24} color={colors.primary} />,
    title: "Private Security",
    description: "Personal security arrangements",
    price: "From $200/day",
  },
];

export default function ExperienceDetailScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const [isFavorite, setIsFavorite] = useState(false);
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  
  const destination = destinations.find((d) => d.id === id);
  
  if (!destination) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Experience not found</Text>
      </SafeAreaView>
    );
  }

  const handleFavoritePress = () => {
    setIsFavorite(!isFavorite);
  };

  const handleServiceToggle = (serviceId: string) => {
    setSelectedServices(prev => 
      prev.includes(serviceId) 
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    );
  };

  const handleBookExperience = () => {
    if (selectedServices.length > 0) {
      // Start with the first selected service
      const firstService = selectedServices[0];
      router.push(`/booking/${firstService}?services=${selectedServices.join(',')}&experience=${id}`);
    } else {
      // Book just the experience
      router.push(`/booking/experience?id=${id}`);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: destination.imageUrl }}
            style={styles.image}
            contentFit="cover"
          />
          <LinearGradient
            colors={["rgba(0,0,0,0.4)", "transparent", "transparent"]}
            style={styles.gradient}
          />
          <SafeAreaView style={styles.header} edges={["top"]}>
            <Pressable style={styles.backButton} onPress={() => router.back()}>
              <ArrowLeft size={24} color={colors.white} />
            </Pressable>
            <Pressable
              style={styles.favoriteButton}
              onPress={handleFavoritePress}
            >
              <Heart
                size={24}
                color={colors.white}
                fill={isFavorite ? colors.primary : "none"}
              />
            </Pressable>
          </SafeAreaView>
        </View>

        <View style={styles.content}>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{destination.name}</Text>
            <View style={styles.ratingContainer}>
              <Star size={16} color={colors.warning} fill={colors.warning} />
              <Text style={styles.rating}>{destination.rating.toFixed(1)}</Text>
            </View>
          </View>

          <View style={styles.locationContainer}>
            <MapPin size={16} color={colors.textSecondary} />
            <Text style={styles.location}>{destination.location}</Text>
          </View>

          <View style={styles.tagsContainer}>
            {destination.tags.map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>About this experience</Text>
            <Text style={styles.description}>{destination.description}</Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>What's included</Text>
            <View style={styles.includedList}>
              <Text style={styles.includedItem}>• Professional local guide</Text>
              <Text style={styles.includedItem}>• Small group experience (max 12 people)</Text>
              <Text style={styles.includedItem}>• All entrance fees</Text>
              <Text style={styles.includedItem}>• Traditional refreshments</Text>
              <Text style={styles.includedItem}>• Photo opportunities</Text>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Additional services</Text>
            <Text style={styles.sectionSubtitle}>
              Make your trip complete with these optional services
            </Text>
            {additionalServices.map((service) => (
              <ServiceCard
                key={service.id}
                icon={service.icon}
                title={service.title}
                description={service.description}
                price={service.price}
                isSelected={selectedServices.includes(service.id)}
                onPress={() => handleServiceToggle(service.id)}
              />
            ))}
          </View>

          <View style={styles.hostSection}>
            <Text style={styles.sectionTitle}>Meet your host</Text>
            <View style={styles.hostCard}>
              <Image
                source={{ uri: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80" }}
                style={styles.hostAvatar}
              />
              <View style={styles.hostInfo}>
                <Text style={styles.hostName}>Kenji Tanaka</Text>
                <Text style={styles.hostTitle}>Cultural Heritage Guide</Text>
                <Text style={styles.hostExperience}>5 years hosting • 4.9 ★ (127 reviews)</Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <View style={styles.priceContainer}>
          <Text style={styles.priceLabel}>From</Text>
          <Text style={styles.price}>
            ${destination.price}
          </Text>
          <Text style={styles.priceUnit}>per person</Text>
        </View>
        <Button
          title={selectedServices.length > 0 ? "Book Experience + Services" : "Book Experience"}
          onPress={handleBookExperience}
          variant="primary"
          size="large"
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  imageContainer: {
    height: 300,
    width: width,
  },
  image: {
    ...StyleSheet.absoluteFillObject,
  },
  gradient: {
    ...StyleSheet.absoluteFillObject,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: spacing.lg,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0,0,0,0.4)",
    justifyContent: "center",
    alignItems: "center",
  },
  favoriteButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0,0,0,0.4)",
    justifyContent: "center",
    alignItems: "center",
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: 100,
  },
  titleContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: spacing.sm,
  },
  title: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    flex: 1,
    marginRight: spacing.md,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.white,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 20,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  rating: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium as any,
    color: colors.text,
    marginLeft: 4,
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  location: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: spacing.xl,
  },
  tag: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 20,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    marginRight: spacing.sm,
    marginBottom: spacing.xs,
  },
  tagText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.text,
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.md,
  },
  sectionSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    marginBottom: spacing.lg,
  },
  description: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.text,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.md,
  },
  includedList: {
    marginTop: spacing.sm,
  },
  includedItem: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.text,
    marginBottom: spacing.sm,
    lineHeight: typography.lineHeights.normal * typography.sizes.md,
  },
  hostSection: {
    marginBottom: spacing.xl,
  },
  hostCard: {
    flexDirection: "row",
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  hostAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: spacing.md,
  },
  hostInfo: {
    flex: 1,
  },
  hostName: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  hostTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  hostExperience: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  footer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    shadowColor: colors.shadowDark,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 10,
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "baseline",
  },
  priceLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginRight: spacing.xs,
  },
  price: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
  priceUnit: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
});