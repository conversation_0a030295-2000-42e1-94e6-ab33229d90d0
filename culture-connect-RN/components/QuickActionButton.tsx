import React, { useState } from "react";
import { StyleSheet, Text, Pressable, Platform, View } from "react-native";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";

interface QuickActionButtonProps {
  title: string;
  icon: React.ReactNode;
  onPress: () => void;
  backgroundColor?: string;
}

// Conditionally import and use reanimated only on mobile
let Animated: any = null;
let useSharedValue: any = null;
let useAnimatedStyle: any = null;
let withSpring: any = null;
let withTiming: any = null;
let AnimatedPressable: any = Pressable;

if (Platform.OS !== 'web') {
  try {
    const reanimated = require('react-native-reanimated');
    Animated = reanimated.default;
    useSharedValue = reanimated.useSharedValue;
    useAnimatedStyle = reanimated.useAnimatedStyle;
    withSpring = reanimated.withSpring;
    withTiming = reanimated.withTiming;
    AnimatedPressable = Animated.createAnimatedComponent(Pressable);
  } catch (error) {
    console.warn('React Native Reanimated not available, using fallback');
  }
}

export const QuickActionButton: React.FC<QuickActionButtonProps> = ({
  title,
  icon,
  onPress,
  backgroundColor = colors.white,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);

  // Only use reanimated on mobile platforms
  const scale = Platform.OS !== 'web' && useSharedValue ? useSharedValue(1) : null;
  const elevation = Platform.OS !== 'web' && useSharedValue ? useSharedValue(2) : null;

  const animatedStyle = Platform.OS !== 'web' && useAnimatedStyle ? useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale?.value || 1 }],
      elevation: Platform.OS === 'android' ? elevation?.value || 2 : 0,
      shadowOpacity: Platform.OS === 'ios' ? (elevation?.value || 2) * 0.05 : 0,
    };
  }) : {};

  const handlePressIn = () => {
    setIsPressed(true);
    if (Platform.OS !== 'web' && scale && elevation && withSpring && withTiming) {
      scale.value = withSpring(0.95, {
        damping: 15,
        stiffness: 300,
      });
      elevation.value = withTiming(8, { duration: 150 });
    }
  };

  const handlePressOut = () => {
    setIsPressed(false);
    if (Platform.OS !== 'web' && scale && elevation && withSpring && withTiming) {
      scale.value = withSpring(1, {
        damping: 15,
        stiffness: 300,
      });
      elevation.value = withTiming(2, { duration: 150 });
    }
  };

  const handleHoverIn = () => {
    if (Platform.OS === 'web') {
      setIsHovered(true);
    }
  };

  const handleHoverOut = () => {
    if (Platform.OS === 'web') {
      setIsHovered(false);
    }
  };

  const containerStyle = [
    styles.container,
    { backgroundColor },
    Platform.OS === 'web' && isHovered && styles.hovered,
    Platform.OS === 'web' && isPressed && styles.pressed,
  ];

  const ButtonComponent = Platform.OS !== 'web' && AnimatedPressable !== Pressable ? AnimatedPressable : Pressable;

  return (
    <ButtonComponent
      style={Platform.OS !== 'web' ? [containerStyle, animatedStyle] : containerStyle}
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onHoverIn={handleHoverIn}
      onHoverOut={handleHoverOut}
    >
      <View style={styles.iconContainer}>
        {icon}
      </View>
      <Text style={styles.title}>{title}</Text>
    </ButtonComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 80,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.sm,
    borderRadius: 16,
    marginRight: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  hovered: {
    shadowColor: colors.shadowDark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    transform: [{ scale: 1.02 }],
  },
  pressed: {
    transform: [{ scale: 0.95 }],
    shadowOpacity: 0.2,
  },
  iconContainer: {
    marginBottom: spacing.sm,
  },
  title: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium as any,
    color: colors.text,
    textAlign: "center",
  },
});