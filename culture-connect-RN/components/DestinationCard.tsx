import React from "react";
import { StyleSheet, View, Text, Pressable } from "react-native";
import { Image } from "expo-image";
import { Heart, Star, Users, Bed, Bath, Square } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Destination } from "@/types";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";

interface DestinationCardProps {
  destination: Destination;
  onPress: (destination: Destination) => void;
}

export const DestinationCard: React.FC<DestinationCardProps> = ({
  destination,
  onPress,
}) => {
  const [isFavorite, setIsFavorite] = React.useState(false);

  const handleFavoritePress = (e: any) => {
    e.stopPropagation();
    setIsFavorite(!isFavorite);
  };

  // Generate mock data for additional details based on destination
  const getDestinationDetails = () => {
    const baseGuests = 2 + (destination.id.length % 4);
    const baseBeds = 1 + (destination.id.length % 3);
    const baseBaths = 1 + (destination.id.length % 2);
    const baseArea = 800 + (destination.id.length * 50);
    
    return {
      guests: baseGuests,
      beds: baseBeds,
      baths: baseBaths,
      area: baseArea,
    };
  };

  // Get vibrant color for tag based on tag type
  const getTagColor = (tag: string) => {
    const tagLower = tag.toLowerCase();
    if (tagLower.includes('cultural') || tagLower.includes('culture')) {
      return colors.secondary; // Teal
    } else if (tagLower.includes('scenic') || tagLower.includes('nature') || tagLower.includes('beach')) {
      return colors.accent; // Orange
    } else if (tagLower.includes('historic') || tagLower.includes('history')) {
      return colors.primary; // Red
    } else if (tagLower.includes('adventure')) {
      return colors.warning; // Yellow
    } else if (tagLower.includes('city')) {
      return '#8B5CF6'; // Purple
    } else if (tagLower.includes('mountain')) {
      return '#10B981'; // Green
    } else if (tagLower.includes('food')) {
      return '#F59E0B'; // Amber
    } else {
      return colors.primary; // Default to primary
    }
  };

  const details = getDestinationDetails();

  return (
    <Pressable
      style={styles.container}
      onPress={() => onPress(destination)}
      android_ripple={{ color: "rgba(255,255,255,0.1)" }}
    >
      <Image
        source={{ uri: destination.imageUrl }}
        style={styles.backgroundImage}
        contentFit="cover"
        transition={300}
      />
      
      <LinearGradient
        colors={["transparent", "rgba(0,0,0,0.3)", "rgba(0,0,0,0.7)"]}
        style={styles.gradient}
      />
      
      <View style={styles.overlay}>
        <View style={styles.topRow}>
          <View style={styles.tagContainer}>
            {destination.tags.slice(0, 1).map((tag, index) => {
              const tagColor = getTagColor(tag);
              return (
                <View 
                  key={index} 
                  style={[
                    styles.tag,
                    { backgroundColor: `${tagColor}E6` } // Adding E6 for 90% opacity
                  ]}
                >
                  <Text style={styles.tagText}>{tag}</Text>
                </View>
              );
            })}
          </View>
          <Pressable
            style={styles.favoriteButton}
            onPress={handleFavoritePress}
            hitSlop={8}
          >
            <Heart
              size={18}
              color={isFavorite ? colors.primary : colors.white}
              fill={isFavorite ? colors.primary : "none"}
            />
          </Pressable>
        </View>

        <View style={styles.bottomContent}>
          <View style={styles.titleSection}>
            <Text style={styles.name} numberOfLines={2}>
              {destination.name}
            </Text>
            <Text style={styles.location} numberOfLines={1}>
              {destination.location}
            </Text>
          </View>

          <View style={styles.priceRatingRow}>
            <Text style={styles.price}>
              ${destination.price || 246}/night
            </Text>
            <View style={styles.ratingContainer}>
              <Star size={14} color={colors.warning} fill={colors.warning} />
              <Text style={styles.rating}>{destination.rating.toFixed(1)}</Text>
            </View>
          </View>

          <View style={styles.detailsRow}>
            <View style={styles.detailItem}>
              <Users size={12} color={colors.white} />
              <Text style={styles.detailText}>{details.guests} Guests</Text>
            </View>
            <View style={styles.detailItem}>
              <Bed size={12} color={colors.white} />
              <Text style={styles.detailText}>{details.beds} Beds</Text>
            </View>
            <View style={styles.detailItem}>
              <Bath size={12} color={colors.white} />
              <Text style={styles.detailText}>{details.baths} Baths</Text>
            </View>
            <View style={styles.detailItem}>
              <Square size={12} color={colors.white} />
              <Text style={styles.detailText}>{details.area} m²</Text>
            </View>
          </View>
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 280,
    borderRadius: 20,
    overflow: "hidden",
    marginBottom: spacing.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  backgroundImage: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: "100%",
    height: "100%",
  },
  gradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  overlay: {
    flex: 1,
    padding: spacing.lg,
    justifyContent: "space-between",
  },
  topRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  tagContainer: {
    flexDirection: "row",
  },
  tag: {
    borderRadius: 12,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  tagText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.semibold as any,
    textShadowColor: "rgba(0,0,0,0.3)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  favoriteButton: {
    backgroundColor: "transparent",
    borderRadius: 16,
    padding: spacing.xs,
    borderWidth: 1.5,
    borderColor: "rgba(255,255,255,0.9)",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  bottomContent: {
    gap: spacing.sm,
  },
  titleSection: {
    gap: spacing.xs,
  },
  name: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    lineHeight: typography.lineHeights.tight * typography.sizes.xl,
  },
  location: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: "rgba(255,255,255,0.8)",
    fontWeight: typography.weights.medium as any,
  },
  priceRatingRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  price: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255,255,255,0.2)",
    borderRadius: 12,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    backdropFilter: "blur(10px)",
  },
  rating: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: colors.white,
    marginLeft: 4,
  },
  detailsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  detailItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  detailText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: "rgba(255,255,255,0.9)",
    fontWeight: typography.weights.medium as any,
  },
});