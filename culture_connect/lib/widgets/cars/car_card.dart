import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:culture_connect/models/car.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Car card widget matching React Native implementation
/// Supports both regular and large variants with pixel-perfect styling
class Car<PERSON>ard extends StatefulWidget {
  final Car car;
  final VoidCallback onPressed;
  final bool isLarge;

  const CarCard({
    super.key,
    required this.car,
    required this.onPressed,
    this.isLarge = false,
  });

  @override
  State<CarCard> createState() => _CarCardState();
}

class _CarCardState extends State<CarCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _handleTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onPressed,
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                color: AppTheme.white,
                borderRadius: BorderRadius.circular(widget.isLarge ? 28 : 24),
                boxShadow: widget.isLarge
                    ? [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.15),
                          offset: const Offset(0, 12),
                          blurRadius: 24,
                          spreadRadius: 0,
                        ),
                      ]
                    : [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.12),
                          offset: const Offset(0, 6),
                          blurRadius: 16,
                          spreadRadius: 0,
                        ),
                      ],
                border: widget.isLarge
                    ? Border.all(color: const Color(0xFFF0F0F0), width: 1)
                    : null,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Image container
                  Container(
                    height: widget.isLarge ? 240 : 180,
                    decoration: BoxDecoration(
                      color: widget.isLarge
                          ? const Color(0xFFF1F3F4)
                          : const Color(0xFFF8F9FA),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(widget.isLarge ? 28 : 24),
                        topRight: Radius.circular(widget.isLarge ? 28 : 24),
                      ),
                    ),
                    child: Stack(
                      children: [
                        // Car image
                        Positioned.fill(
                          child: ClipRRect(
                            borderRadius: BorderRadius.only(
                              topLeft:
                                  Radius.circular(widget.isLarge ? 28 : 24),
                              topRight:
                                  Radius.circular(widget.isLarge ? 28 : 24),
                            ),
                            child: CachedNetworkImage(
                              imageUrl: widget.car.imageUrl,
                              fit: BoxFit.contain,
                              placeholder: (context, url) => Container(
                                color: widget.isLarge
                                    ? const Color(0xFFF1F3F4)
                                    : const Color(0xFFF8F9FA),
                                child: const Center(
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                color: widget.isLarge
                                    ? const Color(0xFFF1F3F4)
                                    : const Color(0xFFF8F9FA),
                                child: const Icon(
                                  Icons.directions_car,
                                  size: 48,
                                  color: AppTheme.textSecondaryColor,
                                ),
                              ),
                            ),
                          ),
                        ),
                        // Rating badge
                        Positioned(
                          top: AppTheme.spacingLg,
                          left: AppTheme.spacingLg,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppTheme.spacingSm,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.95),
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: AppTheme.shadowColor,
                                  offset: const Offset(0, 4),
                                  blurRadius: 8,
                                  spreadRadius: 0,
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.star,
                                  size: 14,
                                  color: AppTheme.warningColor,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  widget.car.rating.toString(),
                                  style: const TextStyle(
                                    fontSize: 13,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.textPrimaryColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        // Category badge (large cards only)
                        if (widget.isLarge)
                          Positioned(
                            top: AppTheme.spacingLg,
                            right: AppTheme.spacingLg,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppTheme.spacingMd,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                color: AppTheme.accentColor,
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                widget.car.category.toUpperCase(),
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: AppTheme.white,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  // Content with precise constraint handling (invisible to user)
                  Expanded(
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        maxHeight: widget.isLarge ? 180 : 140, // Precise content height limit
                      ),
                      child: SingleChildScrollView(
                        physics: const NeverScrollableScrollPhysics(), // Invisible scrolling
                        child: Padding(
                          padding: EdgeInsets.all(
                            widget.isLarge
                                ? AppTheme.spacingXl
                                : AppTheme.spacingLg,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Car info with overflow protection
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    widget.car.name,
                                    style: TextStyle(
                                      fontSize: widget.isLarge ? 22 : 18,
                                      fontWeight: FontWeight.bold,
                                      color: AppTheme.textPrimaryColor,
                                      height: widget.isLarge ? 28 / 22 : 24 / 18,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  SizedBox(
                                    height: widget.isLarge ? AppTheme.spacingSm : 6,
                                  ),
                                  if (widget.isLarge)
                                    Text(
                                      '${widget.car.transmission} • ${widget.car.fuelType} • ${widget.car.seats} seats',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: Color(0xFF8E8E93),
                                        height: 20 / 14,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                ],
                              ),
                              SizedBox(height: AppTheme.spacingMd),
                              // Price
                              Row(
                                children: [
                                  Text(
                                    '\$${widget.car.pricePerDay.toInt()}',
                                    style: TextStyle(
                                      fontSize: widget.isLarge ? 24 : 20,
                                      fontWeight: FontWeight.bold,
                                      color: AppTheme.accentColor,
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  const Text(
                                    '/ day',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xFF8E8E93),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
