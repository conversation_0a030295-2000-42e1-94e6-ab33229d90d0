/// Centralized mock implementations for CultureConnect testing
///
/// This file provides reusable mock classes that adapt to the existing
/// production codebase without requiring modifications to production code.

/// Centralized mock implementations for CultureConnect testing
library common_mocks;

import 'dart:async';
import 'package:mockito/mockito.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/models/landmark.dart';
import 'package:culture_connect/services/auth_service.dart';
import 'package:culture_connect/services/location_service.dart';
import 'package:culture_connect/services/ar_backend_service.dart';
import 'package:culture_connect/services/map_cache_manager.dart';

/// Mock SharedPreferences implementation with in-memory storage
class MockSharedPreferences extends Mock implements SharedPreferences {
  final Map<String, dynamic> _values = {};

  @override
  bool? getBool(String key) => _values[key] as bool?;

  @override
  double? getDouble(String key) => _values[key] as double?;

  @override
  int? getInt(String key) => _values[key] as int?;

  @override
  String? getString(String key) => _values[key] as String?;

  @override
  List<String>? getStringList(String key) => _values[key] as List<String>?;

  @override
  Future<bool> setBool(String key, bool value) async {
    _values[key] = value;
    return true;
  }

  @override
  Future<bool> setDouble(String key, double value) async {
    _values[key] = value;
    return true;
  }

  @override
  Future<bool> setInt(String key, int value) async {
    _values[key] = value;
    return true;
  }

  @override
  Future<bool> setString(String key, String value) async {
    _values[key] = value;
    return true;
  }

  @override
  Future<bool> setStringList(String key, List<String> value) async {
    _values[key] = value;
    return true;
  }

  @override
  Future<bool> remove(String key) async {
    _values.remove(key);
    return true;
  }

  @override
  Future<bool> clear() async {
    _values.clear();
    return true;
  }

  @override
  Set<String> getKeys() => _values.keys.toSet();

  @override
  bool containsKey(String key) => _values.containsKey(key);

  /// Access to internal values for testing
  Map<String, dynamic> get values => _values;
}

/// Mock AuthService with realistic user data
class MockAuthService extends Mock implements AuthService {
  UserModel? _currentUser;

  void setCurrentUser(UserModel? user) {
    _currentUser = user;
  }

  @override
  Future<UserModel?> get currentUserModel async => _currentUser;

  @override
  bool get isAuthenticated => _currentUser != null;

  @override
  Stream<UserModel?> get authStateChanges => Stream.value(_currentUser);
}

/// Mock LocationService with configurable position data
class MockLocationService extends Mock implements LocationService {
  Position? _currentPosition;
  bool _serviceEnabled = true;
  LocationPermission _permission = LocationPermission.always;

  void setCurrentPosition(Position position) {
    _currentPosition = position;
  }

  void setServiceEnabled(bool enabled) {
    _serviceEnabled = enabled;
  }

  void setPermission(LocationPermission permission) {
    _permission = permission;
  }

  @override
  Future<Position?> getCurrentPosition() async => _currentPosition;

  @override
  Future<bool> isLocationServiceEnabled() async => _serviceEnabled;

  @override
  Future<LocationPermission> checkPermission() async => _permission;

  @override
  Future<LocationPermission> requestPermission() async => _permission;

  @override
  double calculateDistance(double startLatitude, double startLongitude,
      double endLatitude, double endLongitude) {
    return Geolocator.distanceBetween(
        startLatitude, startLongitude, endLatitude, endLongitude);
  }

  @override
  String formatDistance(double distanceInMeters) {
    if (distanceInMeters < 1000) {
      return '${distanceInMeters.round()}m';
    } else {
      return '${(distanceInMeters / 1000).toStringAsFixed(1)}km';
    }
  }

  @override
  Future<String> loadMapStyle(String styleName) async => '[]';
}

/// Mock ARBackendService for AR functionality testing
class MockARBackendService extends Mock implements ARBackendService {
  bool _initialized = false;
  List<Landmark> _landmarks = [];

  void setInitialized(bool initialized) {
    _initialized = initialized;
  }

  void setLandmarks(List<Landmark> landmarks) {
    _landmarks = landmarks;
  }

  @override
  Future<bool> initialize() async {
    _initialized = true;
    return _initialized;
  }

  @override
  bool get isInitialized => _initialized;

  @override
  Future<List<Landmark>> fetchNearbyLandmarks({
    required double latitude,
    required double longitude,
    double radius = 1000,
  }) async {
    return _landmarks;
  }

  @override
  Future<bool> downloadArContent({required String arContentId}) async {
    return true;
  }
}

/// Mock MapCacheManager for map caching functionality
class MockMapCacheManager extends Mock implements MapCacheManager {
  final Map<String, dynamic> _cache = {};

  @override
  Future<void> initialize() async {
    // Mock initialization
  }

  @override
  Future<void> cacheMapTile(String tileKey, dynamic tileData) async {
    _cache[tileKey] = tileData;
  }

  @override
  Future<dynamic> getCachedMapTile(String tileKey) async {
    return _cache[tileKey];
  }

  @override
  Future<void> clearCache() async {
    _cache.clear();
  }

  @override
  Future<int> getCacheSize() async {
    return _cache.length;
  }
}

/// Test data factory for creating realistic mock data
class TestDataFactory {
  /// Creates a test user with realistic data
  static UserModel createTestUser({
    String id = 'test-user-id',
    String email = '<EMAIL>',
    String firstName = 'Test',
    String lastName = 'User',
    String phoneNumber = '+1234567890',
    String userType = 'tourist',
    bool emailVerified = true,
    int verificationLevel = 1,
    String status = 'active',
  }) {
    final now = DateTime.now();
    return UserModel(
      id: id,
      email: email,
      firstName: firstName,
      lastName: lastName,
      phoneNumber: phoneNumber,
      userType: userType,
      emailVerified: emailVerified,
      verificationLevel: verificationLevel,
      status: status,
      createdAt: now.toIso8601String(),
      updatedAt: now.toIso8601String(),
      lastLogin: now.toIso8601String(),
    );
  }

  /// Creates a test position with configurable coordinates
  static Position createTestPosition({
    double latitude = 37.7749,
    double longitude = -122.4194,
    double accuracy = 10.0,
    double altitude = 0.0,
    double heading = 0.0,
    double speed = 0.0,
    double speedAccuracy = 0.0,
    double altitudeAccuracy = 0.0,
    double headingAccuracy = 0.0,
  }) {
    return Position(
      latitude: latitude,
      longitude: longitude,
      timestamp: DateTime.now(),
      accuracy: accuracy,
      altitude: altitude,
      heading: heading,
      speed: speed,
      speedAccuracy: speedAccuracy,
      altitudeAccuracy: altitudeAccuracy,
      headingAccuracy: headingAccuracy,
    );
  }

  /// Creates test experiences with realistic data
  static List<Experience> createTestExperiences(int count) {
    return List.generate(count, (index) {
      final now = DateTime.now();
      return Experience(
        id: 'exp-$index',
        title: 'Experience $index',
        description: 'Description for experience $index',
        imageUrl: 'https://example.com/image$index.jpg',
        price: 50.0 + (index % 10) * 10,
        rating: 3.0 + (index % 20) / 10,
        reviewCount: 10 + (index % 50) * 10,
        category: 'Cultural Tours',
        location: 'San Francisco, CA',
        coordinates: LatLng(
          37.7749 + (index % 100) * 0.001,
          -122.4194 - (index % 100) * 0.001,
        ),
        guideId: 'guide-$index',
        guideName: 'Guide $index',
        guideImageUrl: 'https://example.com/guide$index.jpg',
        languages: ['English', 'Spanish'],
        includedItems: ['Transportation', 'Guide'],
        requirements: ['Valid ID'],
        createdAt: now,
        updatedAt: now,
      );
    });
  }

  /// Creates test landmarks with AR content
  static List<Landmark> createTestLandmarks(int count) {
    return List.generate(count, (index) {
      return Landmark(
        id: 'landmark-$index',
        name: 'Landmark $index',
        description: 'Description for landmark $index',
        imageUrl: 'https://example.com/landmark$index.jpg',
        location: {
          'latitude': 37.7749 + (index % 100) * 0.001,
          'longitude': -122.4194 - (index % 100) * 0.001,
        },
        historicalSignificance: 'Historical significance $index',
        rating: 4.0 + (index % 10) / 10,
        reviewCount: 50 + (index % 100),
        tags: ['historical', 'cultural'],
        translations: {
          'en': 'English translation',
          'es': 'Spanish translation'
        },
        arContent: {
          'id': 'ar-content-$index',
          'type': '3d_model',
          'url': 'https://example.com/ar$index.glb',
        },
      );
    });
  }
}
