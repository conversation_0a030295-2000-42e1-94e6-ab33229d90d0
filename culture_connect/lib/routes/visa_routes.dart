import 'package:flutter/material.dart';
import 'package:culture_connect/screens/travel/visa/visa_requirements_screen.dart';
import 'package:culture_connect/screens/travel/visa/visa_provider_marketplace_screen.dart';
import 'package:culture_connect/screens/travel/visa/disclaimer_quiz_screen.dart';
import 'package:culture_connect/screens/travel/visa/self_service_hub_screen.dart';
import 'package:culture_connect/screens/travel/visa/eligibility_checker_screen.dart';
import 'package:culture_connect/screens/travel/visa/document_checklist_screen.dart';
import 'package:culture_connect/screens/travel/visa/timeline_calculator_screen.dart';
import 'package:culture_connect/screens/travel/visa/visa_payment_screen.dart';
import 'package:culture_connect/screens/travel/visa/visa_assistance_screen.dart';
import 'package:culture_connect/models/travel/document/visa_service_provider.dart';

/// Register all visa-related routes
/// Core routing for the VISA assistance feature
Map<String, WidgetBuilder> visaRoutes = {
  // Main entry points
  '/travel/visa': (context) => const VisaRequirementsScreen(),
  '/travel/visa/requirements': (context) => const VisaRequirementsScreen(),
  '/travel/visa/assistance': (context) => const VisaAssistanceScreen(),

  // Provider marketplace
  '/travel/visa/providers': (context) {
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    return VisaProviderMarketplaceScreen(
      destinationCountry: args?['destinationCountry'] as String?,
      visaType: args?['visaType'] as VisaProviderSpecialization?,
    );
  },

  // Legal compliance and onboarding
  '/travel/visa/disclaimer': (context) {
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    return DisclaimerQuizScreen(
      destinationCountry: args?['destinationCountry'] as String?,
      visaType: args?['visaType'] as VisaProviderSpecialization?,
    );
  },

  // Self-service tools
  '/travel/visa/self-service': (context) {
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    return SelfServiceHubScreen(
      passportCountry: args?['passportCountry'] as String?,
      destinationCountry: args?['destinationCountry'] as String?,
      visaType: args?['visaType'] as String?,
    );
  },
  '/travel/visa/eligibility': (context) {
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    return EligibilityCheckerScreen(
      passportCountry: args?['passportCountry'] as String?,
      destinationCountry: args?['destinationCountry'] as String?,
      visaType: args?['visaType'] as String?,
    );
  },
  '/travel/visa/documents': (context) {
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    return DocumentChecklistScreen(
      passportCountry: args?['passportCountry'] as String?,
      destinationCountry: args?['destinationCountry'] as String?,
      visaType: args?['visaType'] as String?,
    );
  },
  '/travel/visa/timeline': (context) {
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    return TimelineCalculatorScreen(
      passportCountry: args?['passportCountry'] as String?,
      destinationCountry: args?['destinationCountry'] as String?,
      visaType: args?['visaType'] as String?,
    );
  },

  // Payment screen (existing)
  '/travel/visa/payment': (context) {
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    return VisaPaymentScreen(
      title: args?['title'] as String? ?? 'Visa Payment',
    );
  },
};
