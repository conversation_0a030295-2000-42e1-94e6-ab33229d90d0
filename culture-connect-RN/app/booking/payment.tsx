import React, { useState, useEffect } from "react";
import { StyleSheet, View, Text, ScrollView, Pressable, TextInput, Dimensions, Platform, Animated, Image, Modal, Alert, Linking } from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { ArrowLeft, CreditCard, Lock, Check, Smartphone, Wallet, Shield, Star, Zap, Globe, Banknote, Plus, ChevronRight, Info, Bitcoin, DollarSign, Sparkles, Award, TrendingUp, X, QrCode, Copy, ExternalLink, CakeIcon as FaceId, Fingerprint, Car } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { Button } from "@/components/Button";


const { width } = Dimensions.get('window');

export default function PaymentScreen() {
  const router = useRouter();
  const { flightId, seats, bookingData, amount, type } = useLocalSearchParams();
  const [selectedPayment, setSelectedPayment] = useState("visa");
  const [cardNumber, setCardNumber] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [cvv, setCvv] = useState("");
  const [cardName, setCardName] = useState("");
  const [saveCard, setSaveCard] = useState(false);
  const [showBreakdown, setShowBreakdown] = useState(false);
  const [selectedCard, setSelectedCard] = useState(''); // Start with no selection
  const [isProcessing, setIsProcessing] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showAddCard, setShowAddCard] = useState(false);
  const [newCardNumber, setNewCardNumber] = useState("");
  const [newExpiryDate, setNewExpiryDate] = useState("");
  const [newCvv, setNewCvv] = useState("");
  const [newCardName, setNewCardName] = useState("");
  const [showPaymentFlow, setShowPaymentFlow] = useState(false);
  const [paymentStep, setPaymentStep] = useState('processing'); // processing, success, error
  const [cryptoAddress, setCryptoAddress] = useState('******************************************');
  const [cryptoAmount, setCryptoAmount] = useState('0.0089');
  const [showValidationError, setShowValidationError] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  const fadeAnim = new Animated.Value(1);
  const scaleAnim = new Animated.Value(1);
  const dot1Anim = new Animated.Value(0);
  const dot2Anim = new Animated.Value(0);
  const dot3Anim = new Animated.Value(0);

  // Mock saved cards data
  const [savedCards, setSavedCards] = useState([
    { id: 'visa1', type: 'visa', last4: '4242', expiry: '12/26', isDefault: true },
    { id: 'mastercard1', type: 'mastercard', last4: '8888', expiry: '08/27', isDefault: false },
  ]);

  // Parse booking data for different types
  let parsedBookingData = null;
  try {
    parsedBookingData = bookingData ? JSON.parse(bookingData as string) : null;
  } catch (error) {
    console.error('Error parsing booking data:', error);
  }

  // Calculate prices based on booking type
  const seatList = typeof seats === 'string' ? seats.split(',') : [];
  const flightPrice = 450;
  const seatFees = seatList.length * 25;
  const taxes = 89;
  const flightTotal = flightPrice + seatFees + taxes;
  
  // Use the amount from params or calculate based on type
  const totalPrice = amount ? parseInt(amount as string) : 
                    type === 'car' && parsedBookingData ? parsedBookingData.totalAmount :
                    flightTotal;

  const handlePayment = () => {
    // Check if a payment method is selected
    if (!selectedCard) {
      setShowValidationError(true);
      return;
    }

    // Animate button
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      })
    ]).start();
    
    // Show payment flow based on selected method
    setShowPaymentFlow(true);
    setPaymentStep('processing');
    
    if (selectedCard === 'apple') {
      handleApplePayFlow();
    } else if (selectedCard === 'crypto') {
      handleCryptoPayFlow();
    } else {
      handleCardPayFlow();
    }
  };

  const handleCardPayFlow = () => {
    // Start loading animation
    startLoadingAnimation();
    
    // Simulate Stripe payment processing
    setTimeout(() => {
      setPaymentStep('success');
      setTimeout(() => {
        setIsNavigating(true);
        setShowPaymentFlow(false);
        // Small delay to ensure modal is fully closed before navigation
        setTimeout(() => {
          if (type === 'car') {
            router.push(`/cars/confirmation?bookingData=${bookingData}`);
          } else {
            router.push(`/booking/flight-confirmation?flightId=${flightId}&seats=${seats}`);
          }
        }, 100);
      }, 2000);
    }, 3000);
  };

  const startLoadingAnimation = () => {
    const animateDot = (dotAnim: Animated.Value, delay: number) => {
      Animated.loop(
        Animated.sequence([
          Animated.delay(delay),
          Animated.timing(dotAnim, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.timing(dotAnim, {
            toValue: 0,
            duration: 400,
            useNativeDriver: true,
          }),
        ])
      ).start();
    };

    animateDot(dot1Anim, 0);
    animateDot(dot2Anim, 200);
    animateDot(dot3Anim, 400);
  };

  const handleApplePayFlow = () => {
    // Simulate Apple Pay authentication
    setTimeout(() => {
      setPaymentStep('success');
      setTimeout(() => {
        setIsNavigating(true);
        setShowPaymentFlow(false);
        // Small delay to ensure modal is fully closed before navigation
        setTimeout(() => {
          if (type === 'car') {
            router.push(`/cars/confirmation?bookingData=${bookingData}`);
          } else {
            router.push(`/booking/flight-confirmation?flightId=${flightId}&seats=${seats}`);
          }
        }, 100);
      }, 2000);
    }, 2500);
  };

  const handleCryptoPayFlow = () => {
    // Show crypto payment interface
    setPaymentStep('crypto-payment');
  };

  const copyCryptoAddress = () => {
    // In a real app, you'd use Clipboard API
    Alert.alert('Address Copied', 'Bitcoin address copied to clipboard');
  };

  const completeCryptoPayment = () => {
    setPaymentStep('success');
    setTimeout(() => {
      setIsNavigating(true);
      setShowPaymentFlow(false);
      // Small delay to ensure modal is fully closed before navigation
      setTimeout(() => {
        router.push(`/booking/flight-confirmation?flightId=${flightId}&seats=${seats}`);
      }, 100);
    }, 2000);
  };

  const handleAddCard = () => {
    if (newCardNumber && newExpiryDate && newCvv && newCardName) {
      const newCard = {
        id: `card_${Date.now()}`,
        type: newCardNumber.startsWith('4') ? 'visa' : 'mastercard',
        last4: newCardNumber.slice(-4),
        expiry: newExpiryDate,
        isDefault: false,
      };
      setSavedCards([...savedCards, newCard]);
      setNewCardNumber("");
      setNewExpiryDate("");
      setNewCvv("");
      setNewCardName("");
      setShowAddCard(false);
    }
  };

  const getCardImage = () => {
    switch(selectedCard) {
      case 'visa1':
        return 'https://images.unsplash.com/photo-1559526324-4b87b5e36e44?w=400&h=250&fit=crop';
      case 'mastercard1':
        return 'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=250&fit=crop';
      case 'crypto':
        return 'https://images.unsplash.com/photo-1621761191319-c6fb62004040?w=400&h=250&fit=crop';
      case 'apple':
        return 'https://images.unsplash.com/photo-1611532736597-de2d4265fba3?w=400&h=250&fit=crop';

      default:
        return 'https://images.unsplash.com/photo-1559526324-4b87b5e36e44?w=400&h=250&fit=crop';
    }
  };

  const getCardGradient = (): [string, string] => {
    switch(selectedCard) {
      case 'visa1':
        return ['#1a1f71', '#4338ca'];
      case 'mastercard1':
        return ['#eb001b', '#ff5f00'];
      case 'crypto':
        return ['#f7931a', '#ffb347'];
      case 'apple':
        return ['#000000', '#434343'];

      default:
        return ['#1a1f71', '#4338ca'];
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <View style={styles.header}>
        <Pressable style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color="#ffffff" />
        </Pressable>
        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>Secure Payment</Text>
          <View style={styles.headerSubtitle}>
            <Shield size={12} color="rgba(255, 255, 255, 0.7)" />
            <Text style={styles.headerSubtitleText}>256-bit SSL Protected</Text>
          </View>
        </View>
        <View style={styles.headerRight}>
          <View style={styles.securityBadge}>
            <Lock size={14} color="rgba(255, 255, 255, 0.8)" />
          </View>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Order Summary */}
        <View style={styles.summaryCard}>
          <View style={styles.summaryHeader}>
            <View style={styles.summaryTitleRow}>
              <Text style={styles.summaryTitle}>Order Summary</Text>
              <View style={styles.premiumBadge}>
                <Sparkles size={12} color="#f59e0b" />
                <Text style={styles.premiumText}>{type === 'car' ? 'Car Rental' : 'Premium'}</Text>
              </View>
            </View>
            <Text style={styles.summarySubtitle}>
              {type === 'car' && parsedBookingData ? 
                `${parsedBookingData.carName} • ${parsedBookingData.days} day${parsedBookingData.days > 1 ? 's' : ''}` :
                'NYC → NRT • Dec 25, 2024'
              }
            </Text>
          </View>
          
          <View style={styles.priceBreakdown}>
            {type === 'car' && parsedBookingData ? (
              // Car rental breakdown
              <>
                <View style={styles.priceRow}>
                  <View style={styles.priceLeft}>
                    <View style={styles.priceIcon}>
                      <Car size={16} color="#6366f1" />
                    </View>
                    <Text style={styles.priceLabel}>Car rental ({parsedBookingData.days} day{parsedBookingData.days > 1 ? 's' : ''})</Text>
                  </View>
                  <Text style={styles.priceValue}>${parsedBookingData.baseAmount || parsedBookingData.dailyRate * parsedBookingData.days}</Text>
                </View>
                
                {parsedBookingData.insuranceCost > 0 && (
                  <View style={styles.priceRow}>
                    <View style={styles.priceLeft}>
                      <View style={styles.priceIcon}>
                        <Shield size={16} color="#10b981" />
                      </View>
                      <Text style={styles.priceLabel}>Insurance coverage</Text>
                    </View>
                    <Text style={styles.priceValue}>${parsedBookingData.insuranceCost}</Text>
                  </View>
                )}
                
                {parsedBookingData.securityCost > 0 && (
                  <View style={styles.priceRow}>
                    <View style={styles.priceLeft}>
                      <View style={styles.priceIcon}>
                        <Shield size={16} color="#f59e0b" />
                      </View>
                      <Text style={styles.priceLabel}>Security service</Text>
                    </View>
                    <Text style={styles.priceValue}>${parsedBookingData.securityCost}</Text>
                  </View>
                )}
                
                {parsedBookingData.extrasCost > 0 && (
                  <View style={styles.priceRow}>
                    <View style={styles.priceLeft}>
                      <View style={styles.priceIcon}>
                        <Star size={16} color="#f59e0b" />
                      </View>
                      <Text style={styles.priceLabel}>Add-ons & extras</Text>
                    </View>
                    <Text style={styles.priceValue}>${parsedBookingData.extrasCost}</Text>
                  </View>
                )}
              </>
            ) : (
              // Flight breakdown
              <>
                <View style={styles.priceRow}>
                  <View style={styles.priceLeft}>
                    <View style={styles.priceIcon}>
                      <Zap size={16} color="#6366f1" />
                    </View>
                    <Text style={styles.priceLabel}>Flight</Text>
                  </View>
                  <Text style={styles.priceValue}>${flightPrice}</Text>
                </View>
                
                <View style={styles.priceRow}>
                  <View style={styles.priceLeft}>
                    <View style={styles.priceIcon}>
                      <Star size={16} color="#f59e0b" />
                    </View>
                    <Text style={styles.priceLabel}>Seat Selection ({seatList.length} seats)</Text>
                  </View>
                  <Text style={styles.priceValue}>${seatFees}</Text>
                </View>
                
                <View style={styles.priceRow}>
                  <View style={styles.priceLeft}>
                    <View style={styles.priceIcon}>
                      <Globe size={16} color="#10b981" />
                    </View>
                    <Text style={styles.priceLabel}>Taxes & Fees</Text>
                  </View>
                  <Text style={styles.priceValue}>${taxes}</Text>
                </View>
              </>
            )}
            
            <LinearGradient
              colors={['#e5e7eb', '#f3f4f6']}
              style={styles.divider}
            />
            
            <View style={styles.totalRow}>
              <View style={styles.totalLeft}>
                <TrendingUp size={20} color="#059669" />
                <Text style={styles.totalLabel}>Total Amount</Text>
              </View>
              <View style={styles.totalRight}>
                <Text style={styles.totalValue}>${totalPrice}</Text>
                <Text style={styles.totalCurrency}>USD</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Payment Methods */}
        <View style={styles.paymentMethodsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Choose Payment Method</Text>
            <View style={styles.trustBadge}>
              <Shield size={14} color="#10b981" />
              <Text style={styles.trustText}>Trusted</Text>
            </View>
          </View>
          
          {/* Credit Cards */}
          <View style={styles.methodCategory}>
            <Text style={styles.categoryTitle}>Credit & Debit Cards</Text>
            
            {/* Add New Card Option */}
            <Pressable 
              style={styles.addNewCardOption}
              onPress={() => setShowAddCard(true)}
            >
              <View style={styles.addCardContent}>
                <View style={styles.dottedBorder}>
                  <View style={styles.addCardIcon}>
                    <Plus size={24} color="#6366f1" />
                  </View>
                  <Text style={styles.addCardText}>Add New Card</Text>
                </View>
              </View>
            </Pressable>
            
            <Pressable 
              style={[styles.paymentOption, selectedCard === 'visa1' && styles.selectedOption]}
              onPress={() => setSelectedCard('visa1')}
            >
              <LinearGradient
                colors={selectedCard === 'visa1' ? ['#1a1f71', '#4338ca'] : ['#f8fafc', '#ffffff']}
                style={styles.optionGradient}
              >
                <View style={styles.optionLeft}>
                  <View style={[styles.optionIcon, { backgroundColor: '#1a1f71' }]}>
                    <CreditCard size={20} color="#ffffff" />
                  </View>
                  <View style={styles.optionInfo}>
                    <Text style={[styles.optionName, selectedCard === 'visa1' && styles.selectedText]}>Visa •••• 4242</Text>
                    <Text style={[styles.optionSubtext, selectedCard === 'visa1' && styles.selectedSubtext]}>Expires 12/26 • Default</Text>
                  </View>
                </View>
                <View style={styles.optionRight}>
                  <View style={styles.visaLogo}>
                    <Text style={styles.visaText}>VISA</Text>
                  </View>
                  {selectedCard === 'visa1' && (
                    <View style={styles.checkmark}>
                      <Check size={16} color="#ffffff" />
                    </View>
                  )}
                </View>
              </LinearGradient>
            </Pressable>
            
            <Pressable 
              style={[styles.paymentOption, selectedCard === 'mastercard1' && styles.selectedOption]}
              onPress={() => setSelectedCard('mastercard1')}
            >
              <LinearGradient
                colors={selectedCard === 'mastercard1' ? ['#eb001b', '#ff5f00'] : ['#f8fafc', '#ffffff']}
                style={styles.optionGradient}
              >
                <View style={styles.optionLeft}>
                  <View style={[styles.optionIcon, { backgroundColor: '#eb001b' }]}>
                    <CreditCard size={20} color="#ffffff" />
                  </View>
                  <View style={styles.optionInfo}>
                    <Text style={[styles.optionName, selectedCard === 'mastercard1' && styles.selectedText]}>Mastercard •••• 8888</Text>
                    <Text style={[styles.optionSubtext, selectedCard === 'mastercard1' && styles.selectedSubtext]}>Expires 08/27</Text>
                  </View>
                </View>
                <View style={styles.optionRight}>
                  <View style={styles.mastercardLogo}>
                    <Text style={styles.mastercardText}>MC</Text>
                  </View>
                  {selectedCard === 'mastercard1' && (
                    <View style={styles.checkmark}>
                      <Check size={16} color="#ffffff" />
                    </View>
                  )}
                </View>
              </LinearGradient>
            </Pressable>
          </View>
        
          {/* Digital Wallets */}
          <View style={styles.methodCategory}>
            <Text style={styles.categoryTitle}>Digital Wallets</Text>
            
            <Pressable 
              style={[styles.paymentOption, selectedCard === 'apple' && styles.selectedOption]}
              onPress={() => setSelectedCard('apple')}
            >
              <LinearGradient
                colors={selectedCard === 'apple' ? ['#000000', '#434343'] : ['#f8fafc', '#ffffff']}
                style={styles.optionGradient}
              >
                <View style={styles.optionLeft}>
                  <View style={[styles.optionIcon, { backgroundColor: '#000' }]}>
                    <Smartphone size={20} color="#fff" />
                  </View>
                  <View style={styles.optionInfo}>
                    <Text style={[styles.optionName, selectedCard === 'apple' && styles.selectedText]}>Apple Pay</Text>
                    <Text style={[styles.optionSubtext, selectedCard === 'apple' && styles.selectedSubtext]}>Touch ID or Face ID</Text>
                  </View>
                </View>
                <View style={styles.optionRight}>
                  {selectedCard === 'apple' && (
                    <View style={styles.checkmark}>
                      <Check size={16} color="#ffffff" />
                    </View>
                  )}
                </View>
              </LinearGradient>
            </Pressable>
            

          </View>
          
          {/* Cryptocurrency */}
          <View style={styles.methodCategory}>
            <Text style={styles.categoryTitle}>Cryptocurrency</Text>
            
            <Pressable 
              style={[styles.paymentOption, selectedCard === 'crypto' && styles.selectedOption]}
              onPress={() => setSelectedCard('crypto')}
            >
              <LinearGradient
                colors={selectedCard === 'crypto' ? ['#f7931a', '#ffb347'] : ['#f8fafc', '#ffffff']}
                style={styles.optionGradient}
              >
                <View style={styles.optionLeft}>
                  <View style={[styles.optionIcon, { backgroundColor: '#f7931a' }]}>
                    <Bitcoin size={20} color="#fff" />
                  </View>
                  <View style={styles.optionInfo}>
                    <Text style={[styles.optionName, selectedCard === 'crypto' && styles.selectedText]}>Bitcoin</Text>
                    <Text style={[styles.optionSubtext, selectedCard === 'crypto' && styles.selectedSubtext]}>Pay with BTC • Instant</Text>
                  </View>
                </View>
                <View style={styles.optionRight}>
                  <View style={styles.cryptoBadge}>
                    <Text style={styles.cryptoText}>NEW</Text>
                  </View>
                  {selectedCard === 'crypto' && (
                    <View style={styles.checkmark}>
                      <Check size={16} color="#ffffff" />
                    </View>
                  )}
                </View>
              </LinearGradient>
            </Pressable>
          </View>
        </View>

        {/* Security Info */}
        <LinearGradient
          colors={['#f0fdf4', '#ecfdf5']}
          style={styles.securityInfo}
        >
          <View style={styles.securityHeader}>
            <Shield size={20} color="#10b981" />
            <Text style={styles.securityTitle}>Bank-Level Security</Text>
          </View>
          <View style={styles.securityFeatures}>
            <View style={styles.securityRow}>
              <Lock size={14} color="#059669" />
              <Text style={styles.securityText}>256-bit SSL encryption</Text>
            </View>
            <View style={styles.securityRow}>
              <Shield size={14} color="#059669" />
              <Text style={styles.securityText}>PCI DSS compliant</Text>
            </View>
            <View style={styles.securityRow}>
              <Award size={14} color="#059669" />
              <Text style={styles.securityText}>Fraud protection</Text>
            </View>
          </View>
        </LinearGradient>
      </ScrollView>

      {/* Payment Button */}
      <View style={styles.paymentFooter}>
        <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
          <Pressable 
            style={[styles.payButton, isProcessing && styles.processingButton]} 
            onPress={handlePayment}
            disabled={isProcessing}
          >
            <View
              style={[styles.payButtonGradient, { backgroundColor: isProcessing ? '#6b7280' : '#000000' }]}
            >
              {isProcessing ? (
                <View style={styles.processingContent}>
                  <View style={styles.loadingSpinner} />
                  <Text style={styles.processingText}>Processing...</Text>
                </View>
              ) : showSuccess ? (
                <View style={styles.successContent}>
                  <Check size={24} color="#ffffff" />
                  <Text style={styles.successText}>Payment Successful!</Text>
                </View>
              ) : (
                <View style={styles.payButtonContent}>
                  <Text style={styles.payButtonText}>Pay ${totalPrice}</Text>
                  <Lock size={20} color="#ffffff" style={styles.payButtonLock} />
                </View>
              )}
            </View>
          </Pressable>
        </Animated.View>
        
        <Text style={styles.footerNote}>
          🔒 By continuing, you agree to our Terms of Service and Privacy Policy
        </Text>
      </View>

      {/* Add New Card Modal */}
      <Modal
        visible={showAddCard}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowAddCard(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Pressable onPress={() => setShowAddCard(false)}>
              <X size={24} color="#374151" />
            </Pressable>
            <Text style={styles.modalTitle}>Add New Card</Text>
            <View style={{ width: 24 }} />
          </View>
          
          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Card Number</Text>
              <TextInput
                style={styles.input}
                value={newCardNumber}
                onChangeText={setNewCardNumber}
                placeholder="1234 5678 9012 3456"
                keyboardType="numeric"
                maxLength={19}
              />
            </View>
            
            <View style={styles.inputRow}>
              <View style={[styles.inputGroup, { flex: 1, marginRight: 12 }]}>
                <Text style={styles.inputLabel}>Expiry Date</Text>
                <TextInput
                  style={styles.input}
                  value={newExpiryDate}
                  onChangeText={setNewExpiryDate}
                  placeholder="MM/YY"
                  keyboardType="numeric"
                  maxLength={5}
                />
              </View>
              
              <View style={[styles.inputGroup, { flex: 1 }]}>
                <Text style={styles.inputLabel}>CVV</Text>
                <TextInput
                  style={styles.input}
                  value={newCvv}
                  onChangeText={setNewCvv}
                  placeholder="123"
                  keyboardType="numeric"
                  maxLength={4}
                  secureTextEntry
                />
              </View>
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Cardholder Name</Text>
              <TextInput
                style={styles.input}
                value={newCardName}
                onChangeText={setNewCardName}
                placeholder="John Doe"
                autoCapitalize="words"
              />
            </View>
            
            <View style={styles.securityNote}>
              <Shield size={16} color="#10b981" />
              <Text style={styles.securityNoteText}>
                Your card information is encrypted and secure
              </Text>
            </View>
          </ScrollView>
          
          <View style={styles.modalFooter}>
            <Pressable 
              style={[styles.saveCardButton, (!newCardNumber || !newExpiryDate || !newCvv || !newCardName) && styles.disabledButton]}
              onPress={handleAddCard}
              disabled={!newCardNumber || !newExpiryDate || !newCvv || !newCardName}
            >
              <Text style={styles.saveCardButtonText}>Save Card</Text>
            </Pressable>
          </View>
        </SafeAreaView>
      </Modal>

      {/* Validation Error Modal */}
      <Modal
        visible={showValidationError}
        animationType="fade"
        transparent={true}
        onRequestClose={() => setShowValidationError(false)}
      >
        <View style={styles.validationModalOverlay}>
          <View style={styles.validationModalContainer}>
            <View style={styles.validationModalContent}>
              <View style={styles.validationIconContainer}>
                <Info size={32} color="#ef4444" />
              </View>
              <Text style={styles.validationTitle}>Payment Method Required</Text>
              <Text style={styles.validationMessage}>
                Please select a payment method to continue with your booking.
              </Text>
              <Pressable 
                style={styles.validationButton}
                onPress={() => setShowValidationError(false)}
              >
                <Text style={styles.validationButtonText}>Got it</Text>
              </Pressable>
            </View>
          </View>
        </View>
      </Modal>

      {/* Payment Flow Modal */}
      <Modal
        visible={showPaymentFlow && !isNavigating}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={() => !isNavigating && setShowPaymentFlow(false)}
      >
        <SafeAreaView style={styles.paymentFlowContainer}>
          {paymentStep === 'processing' && (
            <View style={styles.processingFlow}>
              <View style={styles.processingHeader}>
                <Text style={styles.processingTitle}>
                  {selectedCard === 'apple' ? 'Authenticating with Apple Pay' : 
                   selectedCard === 'crypto' ? 'Preparing Crypto Payment' : 
                   'Processing with Stripe'}
                </Text>
                <Text style={styles.processingSubtitle}>Please wait...</Text>
              </View>
              
              <View style={styles.processingAnimation}>
                {selectedCard === 'apple' ? (
                  <View style={styles.applePayAnimation}>
                    <View style={styles.applePayIcon}>
                      <Smartphone size={48} color="#000" />
                    </View>
                    <View style={styles.biometricIcons}>
                      <FaceId size={32} color="#007AFF" />
                      <Text style={styles.biometricText}>Touch ID or Face ID</Text>
                    </View>
                  </View>
                ) : selectedCard === 'crypto' ? (
                  <View style={styles.cryptoAnimation}>
                    <Bitcoin size={64} color="#f7931a" />
                    <Text style={styles.cryptoAnimationText}>Generating Payment Address...</Text>
                  </View>
                ) : (
                  <View style={styles.stripeAnimation}>
                    <CreditCard size={64} color="#6366f1" />
                    <Text style={styles.stripeAnimationText}>Secure Payment Processing...</Text>
                  </View>
                )}
              </View>
              
              <View style={styles.loadingIndicator}>
                <View style={styles.loadingDots}>
                  <Animated.View style={[styles.dot, { opacity: dot1Anim }]} />
                  <Animated.View style={[styles.dot, { opacity: dot2Anim }]} />
                  <Animated.View style={[styles.dot, { opacity: dot3Anim }]} />
                </View>
              </View>
            </View>
          )}

          {paymentStep === 'crypto-payment' && (
            <View style={styles.cryptoPaymentFlow}>
              <View style={styles.cryptoHeader}>
                <Pressable onPress={() => setShowPaymentFlow(false)} style={styles.closeButton}>
                  <X size={24} color="#374151" />
                </Pressable>
                <Text style={styles.cryptoTitle}>Bitcoin Payment</Text>
                <View style={{ width: 24 }} />
              </View>
              
              <ScrollView style={styles.cryptoContent}>
                <View style={styles.cryptoAmountCard}>
                  <Text style={styles.cryptoAmountLabel}>Amount to Pay</Text>
                  <View style={styles.cryptoAmountRow}>
                    <Bitcoin size={32} color="#f7931a" />
                    <Text style={styles.cryptoAmountValue}>{cryptoAmount} BTC</Text>
                  </View>
                  <Text style={styles.cryptoAmountUsd}>≈ ${totalPrice} USD</Text>
                </View>
                
                <View style={styles.cryptoAddressCard}>
                  <Text style={styles.cryptoAddressLabel}>Send Bitcoin to this address:</Text>
                  <View style={styles.addressContainer}>
                    <Text style={styles.cryptoAddress}>{cryptoAddress}</Text>
                    <Pressable onPress={copyCryptoAddress} style={styles.copyButton}>
                      <Copy size={20} color="#6366f1" />
                    </Pressable>
                  </View>
                </View>
                
                <View style={styles.qrCodeCard}>
                  <Text style={styles.qrCodeLabel}>Or scan QR code:</Text>
                  <View style={styles.qrCodePlaceholder}>
                    <QrCode size={120} color="#1a1a1a" />
                    <Text style={styles.qrCodeText}>QR Code</Text>
                  </View>
                </View>
                
                <View style={styles.cryptoInstructions}>
                  <Text style={styles.instructionsTitle}>Payment Instructions:</Text>
                  <View style={styles.instructionItem}>
                    <Text style={styles.instructionNumber}>1.</Text>
                    <Text style={styles.instructionText}>Copy the Bitcoin address or scan the QR code</Text>
                  </View>
                  <View style={styles.instructionItem}>
                    <Text style={styles.instructionNumber}>2.</Text>
                    <Text style={styles.instructionText}>Send exactly {cryptoAmount} BTC to the address</Text>
                  </View>
                  <View style={styles.instructionItem}>
                    <Text style={styles.instructionNumber}>3.</Text>
                    <Text style={styles.instructionText}>Payment will be confirmed within 10-30 minutes</Text>
                  </View>
                </View>
              </ScrollView>
              
              <View style={styles.cryptoFooter}>
                <Pressable style={styles.cryptoConfirmButton} onPress={completeCryptoPayment}>
                  <Text style={styles.cryptoConfirmText}>I've Sent the Payment</Text>
                </Pressable>
                <Text style={styles.cryptoFooterNote}>⚠️ Only send Bitcoin to this address. Other cryptocurrencies will be lost.</Text>
              </View>
            </View>
          )}

          {paymentStep === 'success' && (
            <View style={styles.successFlow}>
              <View style={styles.successAnimation}>
                <View style={styles.successCheckmark}>
                  <Check size={64} color="#10b981" />
                </View>
                <Text style={styles.successTitle}>Payment Successful!</Text>
                <Text style={styles.successSubtitle}>
                  {selectedCard === 'apple' ? 'Paid with Apple Pay' : 
                   selectedCard === 'crypto' ? 'Bitcoin payment confirmed' : 
                   'Paid with card'}
                </Text>
              </View>
              
              <View style={styles.successDetails}>
                <Text style={styles.successAmount}>${totalPrice}</Text>
                <Text style={styles.successMethod}>
                  {selectedCard === 'apple' ? 'Apple Pay' : 
                   selectedCard === 'crypto' ? 'Bitcoin' : 
                   'Credit Card'}
                </Text>
              </View>
            </View>
          )}
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f1f5f9',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#4844b8',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
    marginBottom: 2,
  },
  headerSubtitle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerSubtitleText: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.7)',
    marginLeft: 4,
    fontWeight: '400',
  },
  headerRight: {
    width: 44,
    alignItems: 'flex-end',
  },
  securityBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 0,
  },
  summaryCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  summaryHeader: {
    marginBottom: 20,
  },
  summaryTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: '#1a1a1a',
  },
  premiumBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fef3c7',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  premiumText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#d97706',
    marginLeft: 4,
  },
  summarySubtitle: {
    fontSize: 16,
    color: '#6b7280',
    fontWeight: '500',
  },
  cardImageContainer: {
    height: 100,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    position: 'relative',
  },
  cardImage: {
    width: '100%',
    height: '100%',
  },
  cardOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardContent: {
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
  },
  cardSubtitle: {
    fontSize: 18,
    color: '#ffffff',
    fontWeight: '700',
    marginTop: 4,
  },
  priceBreakdown: {
    marginTop: 8,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  priceLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priceIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f8fafc',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  priceLabel: {
    fontSize: 15,
    color: '#4b5563',
    fontWeight: '500',
  },
  priceValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  divider: {
    height: 2,
    borderRadius: 1,
    marginVertical: 16,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    paddingBottom: 4,
  },
  totalLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1a1a1a',
    marginLeft: 8,
  },
  totalRight: {
    alignItems: 'flex-end',
  },
  totalValue: {
    fontSize: 24,
    fontWeight: '800',
    color: '#059669',
  },
  totalCurrency: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: '500',
    marginTop: 2,
  },
  paymentMethodsSection: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1a1a1a',
  },
  trustBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0fdf4',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  trustText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#059669',
    marginLeft: 4,
  },
  methodCategory: {
    marginBottom: 20,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  addCardButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  addCardText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6366f1',
    marginLeft: 4,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  paymentOption: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    overflow: 'hidden',
  },
  selectedOption: {
    // No border styling - only fill coverage
  },
  cardOptionContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  cardTypeIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  optionInfo: {
    flex: 1,
  },
  cardOptionName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 2,
  },
  cardOptionSubtext: {
    fontSize: 13,
    color: '#6b7280',
    fontWeight: '400',
  },
  optionRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardBrand: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  cardBrandText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '700',
  },
  cryptoBadge: {
    backgroundColor: '#fbbf24',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginRight: 12,
  },
  cryptoText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: '700',
  },
  selectedIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#f0f4ff',
    justifyContent: 'center',
    alignItems: 'center',
  },

  securityInfo: {
    backgroundColor: '#f0fdf4',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  securityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  securityTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#059669',
    marginLeft: 8,
  },
  securityFeatures: {
    paddingLeft: 28,
  },
  securityRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  securityText: {
    fontSize: 14,
    color: '#059669',
    marginLeft: 8,
    fontWeight: '500',
  },
  paymentFooter: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: Platform.OS === 'ios' ? 32 : 20,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  payButton: {
    borderRadius: 12,
    marginBottom: 12,
    overflow: 'hidden',
  },
  processingButton: {
    opacity: 0.8,
  },

  processingButtonBg: {
    backgroundColor: '#6b7280',
  },
  payButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  payButtonText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#ffffff',
    marginHorizontal: 12,
  },
  payButtonLock: {
    opacity: 0.8,
  },
  processingContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingSpinner: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    borderTopColor: '#ffffff',
    marginRight: 12,
  },
  processingText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  successContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  successText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#ffffff',
    marginLeft: 8,
  },
  footerNote: {
    fontSize: 13,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 18,
    fontWeight: '500',
  },
  optionGradient: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    margin: 0,
  },
  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  optionName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 2,
  },
  selectedText: {
    color: '#ffffff',
  },
  optionSubtext: {
    fontSize: 13,
    color: '#6b7280',
    fontWeight: '400',
  },
  selectedSubtext: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  visaLogo: {
    backgroundColor: '#1a1f71',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  visaText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: '700',
  },
  mastercardLogo: {
    backgroundColor: '#eb001b',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  mastercardText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: '700',
  },
  checkmark: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addNewCardOption: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  addCardContent: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dottedBorder: {
    borderWidth: 2,
    borderColor: '#6366f1',
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  addCardIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#f0f4ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  payButtonGradient: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 24,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    color: '#1a1a1a',
  },
  inputRow: {
    flexDirection: 'row',
  },
  securityNote: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0fdf4',
    padding: 16,
    borderRadius: 12,
    marginTop: 8,
  },
  securityNoteText: {
    fontSize: 14,
    color: '#059669',
    marginLeft: 8,
    fontWeight: '500',
  },
  modalFooter: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: Platform.OS === 'ios' ? 32 : 20,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  saveCardButton: {
    backgroundColor: '#000000',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#9ca3af',
  },
  saveCardButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  
  // Payment Flow Styles
  paymentFlowContainer: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  processingFlow: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  processingHeader: {
    alignItems: 'center',
    marginBottom: 60,
  },
  processingTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1a1a1a',
    textAlign: 'center',
    marginBottom: 8,
  },
  processingSubtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  processingAnimation: {
    alignItems: 'center',
    marginBottom: 60,
  },
  applePayAnimation: {
    alignItems: 'center',
  },
  applePayIcon: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#f3f4f6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  biometricIcons: {
    alignItems: 'center',
  },
  biometricText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
    marginTop: 8,
  },
  cryptoAnimation: {
    alignItems: 'center',
  },
  cryptoAnimationText: {
    fontSize: 16,
    color: '#f7931a',
    fontWeight: '600',
    marginTop: 16,
  },
  stripeAnimation: {
    alignItems: 'center',
  },
  stripeAnimationText: {
    fontSize: 16,
    color: '#6366f1',
    fontWeight: '600',
    marginTop: 16,
  },
  loadingIndicator: {
    alignItems: 'center',
  },
  loadingDots: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#6366f1',
    marginHorizontal: 4,
  },
  
  // Crypto Payment Styles
  cryptoPaymentFlow: {
    flex: 1,
  },
  cryptoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cryptoTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  cryptoContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  cryptoAmountCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  cryptoAmountLabel: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
    marginBottom: 12,
  },
  cryptoAmountRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  cryptoAmountValue: {
    fontSize: 32,
    fontWeight: '700',
    color: '#f7931a',
    marginLeft: 12,
  },
  cryptoAmountUsd: {
    fontSize: 16,
    color: '#6b7280',
    fontWeight: '500',
  },
  cryptoAddressCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  cryptoAddressLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 12,
  },
  addressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    padding: 16,
  },
  cryptoAddress: {
    flex: 1,
    fontSize: 14,
    color: '#374151',
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
    lineHeight: 20,
  },
  copyButton: {
    marginLeft: 12,
    padding: 8,
  },
  qrCodeCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  qrCodeLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 16,
  },
  qrCodePlaceholder: {
    width: 160,
    height: 160,
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#e2e8f0',
    borderStyle: 'dashed',
  },
  qrCodeText: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 8,
  },
  cryptoInstructions: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 16,
  },
  instructionItem: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  instructionNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6366f1',
    width: 20,
  },
  instructionText: {
    flex: 1,
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
  },
  cryptoFooter: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: Platform.OS === 'ios' ? 32 : 20,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  cryptoConfirmButton: {
    backgroundColor: '#f7931a',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  cryptoConfirmText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  cryptoFooterNote: {
    fontSize: 12,
    color: '#ef4444',
    textAlign: 'center',
    lineHeight: 16,
  },
  
  // Success Flow Styles
  successFlow: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  successAnimation: {
    alignItems: 'center',
    marginBottom: 40,
  },
  successCheckmark: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#f0fdf4',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  successTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1a1a1a',
    textAlign: 'center',
    marginBottom: 8,
  },
  successSubtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  successDetails: {
    alignItems: 'center',
  },
  successAmount: {
    fontSize: 36,
    fontWeight: '800',
    color: '#10b981',
    marginBottom: 8,
  },
  successMethod: {
    fontSize: 16,
    color: '#6b7280',
    fontWeight: '500',
  },

  // Validation Modal Styles
  validationModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  validationModalContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 20,
    width: '100%',
    maxWidth: 320,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  validationModalContent: {
    padding: 24,
    alignItems: 'center',
  },
  validationIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#fef2f2',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  validationTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1a1a1a',
    textAlign: 'center',
    marginBottom: 8,
  },
  validationMessage: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  validationButton: {
    backgroundColor: '#000000',
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 32,
    minWidth: 120,
  },
  validationButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
    textAlign: 'center',
  },

});