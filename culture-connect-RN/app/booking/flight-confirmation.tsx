import React, { useState } from "react";
import { StyleSheet, View, Text, ScrollView, Pressable, Image } from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { X, Download, Plane, ArrowRight } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { Button } from "@/components/Button";

export default function FlightConfirmationScreen() {
  const router = useRouter();
  const { flightId, seats } = useLocalSearchParams();
  const seatList = typeof seats === 'string' ? seats.split(',') : [];

  const handleClose = () => {
    router.push("/(tabs)");
  };

  const handleDownloadTicket = () => {
    alert("Boarding pass downloaded successfully!");
  };

  return (
    <LinearGradient
      colors={['#5B5CE6', '#4F46E5', '#3B82F6']}
      style={styles.container}
    >
      <SafeAreaView style={styles.safeArea} edges={["top"]}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Download your{"\n"}boarding pass</Text>
          <Pressable style={styles.closeButton} onPress={handleClose}>
            <X size={24} color="#000" />
          </Pressable>
        </View>

        <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollView}>
          {/* Boarding Pass Card */}
          <View style={styles.boardingPassCard}>
            {/* Passenger Info */}
            <View style={styles.passengerSection}>
              <View style={styles.passengerInfo}>
                <View style={styles.avatarContainer}>
                  <Image
                    source={{ uri: 'https://images.unsplash.com/photo-1494790108755-2616b9c5e8e1?w=100&h=100&fit=crop&crop=face' }}
                    style={styles.avatar}
                  />
                </View>
                <View style={styles.passengerDetails}>
                  <Text style={styles.passengerName}>Margoot Robbie</Text>
                  <Text style={styles.passengerAge}>25 yrs, Female</Text>
                </View>
              </View>
              <View style={styles.airlineIcon}>
                <Text style={styles.airlineEmoji}>✈️</Text>
              </View>
            </View>

            {/* Dotted Separator */}
            <View style={styles.dottedSeparator} />

            {/* Flight Route */}
            <View style={styles.flightRoute}>
              <View style={styles.cityContainer}>
                <Text style={styles.cityCode}>AMS</Text>
                <Text style={styles.cityName}>Amsterdam</Text>
              </View>
              
              <View style={styles.flightPath}>
                <View style={styles.pathLine} />
                <View style={styles.planeIconContainer}>
                  <ArrowRight size={16} color="#fff" />
                </View>
                <View style={styles.pathLine} />
              </View>
              
              <View style={styles.cityContainer}>
                <Text style={styles.cityCode}>PAR</Text>
                <Text style={styles.cityName}>Paris</Text>
              </View>
            </View>

            {/* Flight Details Grid */}
            <View style={styles.detailsGrid}>
              <View style={styles.detailColumn}>
                <Text style={styles.detailLabel}>Date</Text>
                <Text style={styles.detailValue}>20 Nov, 2022</Text>
              </View>
              <View style={styles.detailColumn}>
                <Text style={styles.detailLabel}>Time</Text>
                <Text style={styles.detailValue}>09:00 - 11:20 AM</Text>
              </View>
            </View>

            <View style={styles.detailsGrid}>
              <View style={styles.detailColumn}>
                <Text style={styles.detailLabel}>Baggage</Text>
                <Text style={styles.detailValue}>25 kg</Text>
              </View>
              <View style={styles.detailColumn}>
                <Text style={styles.detailLabel}>Class</Text>
                <Text style={styles.detailValue}>Business class</Text>
              </View>
            </View>

            {/* Dotted Separator */}
            <View style={styles.dottedSeparator} />

            {/* Bottom Details */}
            <View style={styles.bottomDetails}>
              <View style={styles.bottomDetailItem}>
                <Text style={styles.bottomDetailLabel}>Terminal</Text>
                <Text style={styles.bottomDetailValue}>03</Text>
              </View>
              <View style={styles.bottomDetailItem}>
                <Text style={styles.bottomDetailLabel}>Seat No.</Text>
                <Text style={styles.bottomDetailValue}>24C</Text>
              </View>
              <View style={styles.bottomDetailItem}>
                <Text style={styles.bottomDetailLabel}>Gate No.</Text>
                <Text style={styles.bottomDetailValue}>F4</Text>
              </View>
            </View>

            {/* Barcode */}
            <View style={styles.barcodeSection}>
              <View style={styles.barcode}>
                {Array.from({ length: 50 }).map((_, index) => (
                  <View
                    key={index}
                    style={[
                      styles.barcodeLine,
                      { width: Math.random() > 0.5 ? 2 : 1 }
                    ]}
                  />
                ))}
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Download Button */}
        <View style={styles.downloadButtonContainer}>
          <Pressable style={styles.downloadButton} onPress={handleDownloadTicket}>
            <Text style={styles.downloadButtonText}>Download ticket</Text>
          </Pressable>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.md,
    paddingBottom: spacing.xl,
  },
  headerTitle: {
    fontFamily: typography.fontFamily,
    fontSize: 28,
    fontWeight: typography.weights.bold as any,
    color: '#fff',
    lineHeight: 34,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 4,
  },
  scrollView: {
    flex: 1,
  },
  boardingPassCard: {
    backgroundColor: '#fff',
    marginHorizontal: spacing.lg,
    borderRadius: 24,
    padding: spacing.lg,
    marginBottom: spacing.xl,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  passengerSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  passengerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    overflow: 'hidden',
    marginRight: spacing.md,
  },
  avatar: {
    width: '100%',
    height: '100%',
  },
  passengerDetails: {
    flex: 1,
  },
  passengerName: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: '#000',
    marginBottom: 2,
  },
  passengerAge: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: '#666',
  },
  airlineIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFF3E0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  airlineEmoji: {
    fontSize: 20,
  },
  dottedSeparator: {
    height: 1,
    backgroundColor: 'transparent',
    borderStyle: 'dashed',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    marginVertical: spacing.lg,
  },
  flightRoute: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.xl,
  },
  cityContainer: {
    alignItems: 'center',
  },
  cityCode: {
    fontFamily: typography.fontFamily,
    fontSize: 32,
    fontWeight: typography.weights.bold as any,
    color: '#000',
    marginBottom: 4,
  },
  cityName: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: '#666',
  },
  flightPath: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginHorizontal: spacing.lg,
  },
  pathLine: {
    flex: 1,
    height: 2,
    backgroundColor: '#E0E0E0',
  },
  planeIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#5B5CE6',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: spacing.sm,
  },
  detailsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  detailColumn: {
    flex: 1,
  },
  detailLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: '#666',
    marginBottom: 4,
  },
  detailValue: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: '#000',
  },
  bottomDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.xl,
  },
  bottomDetailItem: {
    alignItems: 'center',
    flex: 1,
  },
  bottomDetailLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: '#666',
    marginBottom: 4,
  },
  bottomDetailValue: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: '#000',
  },
  barcodeSection: {
    alignItems: 'center',
    marginTop: spacing.md,
  },
  barcode: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 60,
    width: '80%',
    justifyContent: 'space-between',
  },
  barcodeLine: {
    backgroundColor: '#000',
    height: '100%',
    marginHorizontal: 0.5,
  },
  downloadButtonContainer: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.lg,
  },
  downloadButton: {
    backgroundColor: '#5B5CE6',
    borderRadius: 16,
    paddingVertical: spacing.md,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  downloadButtonText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: '#fff',
  },
});