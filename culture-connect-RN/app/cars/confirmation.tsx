import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, ScrollView, Image, Pressable, Alert, Share, Platform, Animated, Dimensions } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, CheckCircle, Download, Share2, Calendar, Clock, MapPin, Users, Fuel, Settings, Car, Star, Shield, Phone, Mail, Navigation } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';
import { cars } from '@/mocks/cars';
import { Button } from '@/components/Button';

const { width: screenWidth } = Dimensions.get('window');

export default function CarBookingConfirmationScreen() {
  const router = useRouter();
  const { bookingData } = useLocalSearchParams();
  const [animationComplete, setAnimationComplete] = useState(false);
  const fadeAnim = new Animated.Value(0);
  const scaleAnim = new Animated.Value(0.8);
  const slideAnim = new Animated.Value(50);
  
  let booking;
  try {
    booking = JSON.parse(bookingData as string);
  } catch (error) {
    booking = null;
  }

  const car = booking ? cars.find(c => c.id === booking.carId) : null;

  useEffect(() => {
    // Start entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      })
    ]).start(() => {
      setAnimationComplete(true);
    });
  }, []);

  if (!booking || !car) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Booking not found</Text>
      </SafeAreaView>
    );
  }

  const handleBackPress = () => {
    router.push('/(tabs)');
  };

  const handleDownload = async () => {
    try {
      // Create HTML content for PDF
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Car Rental Booking Confirmation</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
            .header { text-align: center; margin-bottom: 30px; }
            .success-icon { font-size: 60px; color: #10B981; margin-bottom: 20px; }
            .title { font-size: 28px; font-weight: bold; margin-bottom: 10px; }
            .reference { background: #F3F4F6; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0; }
            .section { margin: 20px 0; padding: 20px; border: 1px solid #E5E7EB; border-radius: 10px; }
            .section-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #6366F1; }
            .vehicle-info { display: flex; align-items: center; margin-bottom: 15px; }
            .timeline-item { margin: 15px 0; padding: 15px; background: #F9FAFB; border-radius: 8px; }
            .payment-row { display: flex; justify-content: space-between; margin: 10px 0; }
            .total-row { font-weight: bold; font-size: 18px; background: #10B981; color: white; padding: 15px; border-radius: 8px; }
            .contact-info { background: #EEF2FF; padding: 15px; border-radius: 8px; margin: 10px 0; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="success-icon">✓</div>
            <h1 class="title">Booking Confirmed!</h1>
            <p>Your car rental has been successfully booked.</p>
            <div class="reference">
              <strong>Booking Reference: ${generateBookingReference()}</strong>
            </div>
          </div>
          
          <div class="section">
            <h2 class="section-title">Vehicle Details</h2>
            <div class="vehicle-info">
              <div>
                <h3>${car.name}</h3>
                <p>${car.description}</p>
                <p><strong>Category:</strong> ${car.category} | <strong>Rating:</strong> ${car.rating}⭐</p>
                <p><strong>Specs:</strong> ${car.seats} seats, ${car.fuelType}, ${car.transmission}</p>
              </div>
            </div>
          </div>
          
          <div class="section">
            <h2 class="section-title">Rental Timeline</h2>
            <div class="timeline-item">
              <h4>Pickup</h4>
              <p><strong>Date:</strong> ${formatDate(booking.pickupDate)}</p>
              <p><strong>Time:</strong> ${booking.pickupTime}</p>
              <p><strong>Location:</strong> ${typeof booking.location === 'object' && booking.location ? booking.location.name : (booking.location || 'Default location')}</p>
            </div>
            <div class="timeline-item">
              <h4>Return</h4>
              <p><strong>Date:</strong> ${formatDate(booking.returnDate)}</p>
              <p><strong>Time:</strong> ${booking.returnTime}</p>
              <p><strong>Location:</strong> ${typeof booking.location === 'object' && booking.location ? booking.location.name : (booking.location || 'Default location')}</p>
            </div>
          </div>
          
          <div class="section">
            <h2 class="section-title">Payment Summary</h2>
            <div class="payment-row">
              <span>Car rental (${booking.days} day${booking.days > 1 ? 's' : ''})</span>
              <span>${booking.baseAmount || booking.dailyRate * booking.days}</span>
            </div>
            ${booking.insuranceCost > 0 ? `
            <div class="payment-row">
              <span>Insurance coverage</span>
              <span>${booking.insuranceCost}</span>
            </div>` : ''}
            ${booking.extrasCost > 0 ? `
            <div class="payment-row">
              <span>Add-ons & extras</span>
              <span>${booking.extrasCost}</span>
            </div>` : ''}
            <div class="total-row">
              <span>Total Paid: ${booking.totalAmount}</span>
            </div>
          </div>
          
          <div class="section">
            <h2 class="section-title">Contact & Support</h2>
            <div class="contact-info">
              <p><strong>Phone:</strong> +****************</p>
              <p><strong>Email:</strong> <EMAIL></p>
            </div>
          </div>
          
          <div class="section">
            <h2 class="section-title">Important Reminders</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
              <div>
                <h4>Before Pickup</h4>
                <ul>
                  <li>Valid driver's license</li>
                  <li>Credit card for security</li>
                  <li>Arrive 15 minutes early</li>
                </ul>
              </div>
              <div>
                <h4>During Rental</h4>
                <ul>
                  <li>Follow traffic rules</li>
                  <li>Keep vehicle clean</li>
                  <li>Report any issues</li>
                </ul>
              </div>
              <div>
                <h4>At Return</h4>
                <ul>
                  <li>Same fuel level</li>
                  <li>Vehicle inspection</li>
                  <li>Return on time</li>
                </ul>
              </div>
            </div>
          </div>
        </body>
        </html>
      `;
      
      if (Platform.OS === 'web') {
        // Web-specific download using blob and URL.createObjectURL
        const blob = new Blob([htmlContent], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `booking-confirmation-${generateBookingReference()}.html`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        
        Alert.alert(
          'Download Complete',
          'Your booking confirmation has been downloaded successfully! You will be redirected to the home screen in 2 seconds.',
          [
            {
              text: 'Go to Home Now',
              onPress: () => router.push('/(tabs)')
            },
            {
              text: 'Stay Here',
              style: 'cancel',
              onPress: () => {
                // Still auto-redirect after 2 seconds even if they choose to stay
                setTimeout(() => {
                  router.push('/(tabs)');
                }, 2000);
              }
            }
          ]
        );
        
        // Auto-redirect after 2 seconds
        setTimeout(() => {
          router.push('/(tabs)');
        }, 2000);
      } else {
        // Mobile-specific download using FileSystem and Sharing
        const htmlUri = FileSystem.documentDirectory + 'booking-confirmation.html';
        await FileSystem.writeAsStringAsync(htmlUri, htmlContent);
        
        if (await Sharing.isAvailableAsync()) {
          await Sharing.shareAsync(htmlUri, {
            mimeType: 'text/html',
            dialogTitle: 'Download Booking Confirmation'
          });
          
          Alert.alert(
            'Download Complete',
            'Your booking confirmation has been downloaded successfully! You will be redirected to the home screen in 2 seconds.',
            [
              {
                text: 'Go to Home Now',
                onPress: () => router.push('/(tabs)')
              },
              {
                text: 'Stay Here',
                style: 'cancel',
                onPress: () => {
                  // Still auto-redirect after 2 seconds even if they choose to stay
                  setTimeout(() => {
                    router.push('/(tabs)');
                  }, 2000);
                }
              }
            ]
          );
          
          // Auto-redirect after 2 seconds
          setTimeout(() => {
            router.push('/(tabs)');
          }, 2000);
        } else {
          Alert.alert('Error', 'Sharing is not available on this device');
        }
      }
    } catch (error) {
      console.error('Error downloading confirmation:', error);
      Alert.alert('Error', 'Failed to download confirmation. Please try again.');
    }
  };

  const handleShare = async () => {
    try {
      const message = `Car Rental Booking Confirmation\\n\\nCar: ${car.name}\\nPickup: ${new Date(booking.pickupDate).toLocaleDateString()} at ${booking.pickupTime}\\nReturn: ${new Date(booking.returnDate).toLocaleDateString()} at ${booking.returnTime}\\nLocation: ${typeof booking.location === 'object' && booking.location ? booking.location.name : (booking.location || 'Default location')}\\nTotal: ${booking.totalAmount}`;
      
      await Share.share({
        message,
        title: 'Car Rental Booking Confirmation'
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const generateBookingReference = () => {
    return `CR${Date.now().toString().slice(-6)}`;
  };

  const getInsuranceDetails = (type: string) => {
    switch (type) {
      case 'basic':
        return 'Basic Coverage';
      case 'premium':
        return 'Premium Coverage';
      case 'comprehensive':
        return 'Comprehensive Coverage';
      default:
        return 'Basic Coverage';
    }
  };

  const getExtrasNames = (extras: string[]) => {
    const names = {
      'gps': 'GPS Navigation',
      'child_seat': 'Child Seat',
      'additional_driver': 'Additional Driver',
      'wifi': 'WiFi Hotspot'
    };
    return extras.map(extra => names[extra as keyof typeof names] || extra);
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <SafeAreaView edges={['top']} style={styles.header}>
        <View style={styles.headerContent}>
          <Pressable style={styles.backButton} onPress={handleBackPress}>
            <ArrowLeft size={24} color={colors.text} />
          </Pressable>
          <Text style={styles.headerTitle}>Booking Confirmed</Text>
          <View style={styles.headerActions}>
            <Pressable style={styles.actionButton} onPress={handleShare}>
              <Share2 size={20} color={colors.text} />
            </Pressable>
            <Pressable style={styles.actionButton} onPress={handleDownload}>
              <Download size={20} color={colors.text} />
            </Pressable>
          </View>
        </View>
      </SafeAreaView>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.content} contentContainerStyle={styles.contentContainer}>
        {/* Success Animation */}
        <Animated.View style={[styles.successContainer, { 
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }, { translateY: slideAnim }]
        }]}>
          <View style={styles.successIcon}>
            <CheckCircle size={80} color={colors.success} />
          </View>
          <Text style={styles.successTitle}>Booking Confirmed!</Text>
          <Text style={styles.successMessage}>
            Your car rental has been successfully booked. You'll receive a confirmation email shortly.
          </Text>
          
          {/* Booking Reference */}
          <View style={styles.referenceCard}>
            <Text style={styles.referenceLabel}>Booking Reference</Text>
            <Text style={styles.referenceNumber}>{generateBookingReference()}</Text>
            <Text style={styles.referenceNote}>Save this reference for your records</Text>
          </View>
        </Animated.View>

        {/* Vehicle Details Card */}
        <Animated.View style={[styles.section, { opacity: fadeAnim }]}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionIcon}>
              <Car size={20} color={colors.accent} />
            </View>
            <Text style={styles.sectionTitle}>Vehicle Details</Text>
            <View style={styles.ratingBadge}>
              <Star size={14} color={colors.warning} fill={colors.warning} />
              <Text style={styles.ratingText}>{car.rating}</Text>
            </View>
          </View>
          
          <View style={styles.vehicleCard}>
            <View style={styles.vehicleImageContainer}>
              <Image source={{ uri: car.imageUrl }} style={styles.vehicleImage} />
              <View style={styles.categoryBadge}>
                <Text style={styles.categoryText}>{car.category}</Text>
              </View>
            </View>
            
            <View style={styles.vehicleInfo}>
              <Text style={styles.vehicleName}>{car.name}</Text>
              <Text style={styles.vehicleDescription}>{car.description}</Text>
              
              <View style={styles.vehicleSpecs}>
                <View style={styles.specChip}>
                  <Users size={12} color={colors.accent} />
                  <Text style={styles.specText}>{car.seats} seats</Text>
                </View>
                <View style={styles.specChip}>
                  <Fuel size={12} color={colors.accent} />
                  <Text style={styles.specText}>{car.fuelType}</Text>
                </View>
                <View style={styles.specChip}>
                  <Settings size={12} color={colors.accent} />
                  <Text style={styles.specText}>{car.transmission}</Text>
                </View>
              </View>
            </View>
          </View>
        </Animated.View>

        {/* Rental Timeline */}
        <Animated.View style={[styles.section, { opacity: fadeAnim }]}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionIcon}>
              <Calendar size={20} color={colors.accent} />
            </View>
            <Text style={styles.sectionTitle}>Rental Timeline</Text>
            <Text style={styles.durationBadge}>{booking.days} day{booking.days > 1 ? 's' : ''}</Text>
          </View>
          
          <View style={styles.timelineContainer}>
            {/* Pickup */}
            <View style={styles.timelineItem}>
              <View style={styles.timelineIcon}>
                <Navigation size={16} color={colors.success} />
              </View>
              <View style={styles.timelineContent}>
                <Text style={styles.timelineTitle}>Pickup</Text>
                <Text style={styles.timelineDate}>{formatDate(booking.pickupDate)}</Text>
                <Text style={styles.timelineTime}>{booking.pickupTime}</Text>
                <Text style={styles.timelineLocation}>{typeof booking.location === 'object' && booking.location ? booking.location.name : (booking.location || 'Default location')}</Text>
              </View>
            </View>
            
            {/* Timeline Line */}
            <View style={styles.timelineLine} />
            
            {/* Return */}
            <View style={styles.timelineItem}>
              <View style={styles.timelineIcon}>
                <MapPin size={16} color={colors.accent} />
              </View>
              <View style={styles.timelineContent}>
                <Text style={styles.timelineTitle}>Return</Text>
                <Text style={styles.timelineDate}>{formatDate(booking.returnDate)}</Text>
                <Text style={styles.timelineTime}>{booking.returnTime}</Text>
                <Text style={styles.timelineLocation}>{typeof booking.location === 'object' && booking.location ? booking.location.name : (booking.location || 'Default location')}</Text>
              </View>
            </View>
          </View>
        </Animated.View>

        {/* Booking Details */}
        {(booking.insurance !== 'basic' || booking.extras?.length > 0) && (
          <Animated.View style={[styles.section, { opacity: fadeAnim }]}>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionIcon}>
                <Shield size={20} color={colors.accent} />
              </View>
              <Text style={styles.sectionTitle}>Additional Services</Text>
            </View>
            
            <View style={styles.servicesContainer}>
              {booking.insurance !== 'basic' && (
                <View style={styles.serviceItem}>
                  <View style={styles.serviceIcon}>
                    <Shield size={16} color={colors.success} />
                  </View>
                  <View style={styles.serviceInfo}>
                    <Text style={styles.serviceName}>{getInsuranceDetails(booking.insurance)}</Text>
                    <Text style={styles.serviceDescription}>Enhanced protection coverage</Text>
                  </View>
                  <Text style={styles.servicePrice}>+${booking.insuranceCost}</Text>
                </View>
              )}
              
              {booking.extras?.map((extra: string, index: number) => (
                <View key={index} style={styles.serviceItem}>
                  <View style={styles.serviceIcon}>
                    <CheckCircle size={16} color={colors.success} />
                  </View>
                  <View style={styles.serviceInfo}>
                    <Text style={styles.serviceName}>{getExtrasNames([extra])[0]}</Text>
                    <Text style={styles.serviceDescription}>Additional service</Text>
                  </View>
                </View>
              ))}
            </View>
          </Animated.View>
        )}

        {/* Payment Summary */}
        <Animated.View style={[styles.section, { opacity: fadeAnim }]}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionIcon}>
              <CheckCircle size={20} color={colors.success} />
            </View>
            <Text style={styles.sectionTitle}>Payment Summary</Text>
            <View style={styles.paidBadge}>
              <Text style={styles.paidText}>PAID</Text>
            </View>
          </View>
          
          <View style={styles.paymentBreakdown}>
            <View style={styles.paymentRow}>
              <Text style={styles.paymentLabel}>Car rental ({booking.days} day{booking.days > 1 ? 's' : ''})</Text>
              <Text style={styles.paymentValue}>${booking.baseAmount || booking.dailyRate * booking.days}</Text>
            </View>
            
            {booking.insuranceCost > 0 && (
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Insurance coverage</Text>
                <Text style={styles.paymentValue}>${booking.insuranceCost}</Text>
              </View>
            )}
            
            {booking.extrasCost > 0 && (
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Add-ons & extras</Text>
                <Text style={styles.paymentValue}>${booking.extrasCost}</Text>
              </View>
            )}
            
            <View style={styles.paymentDivider} />
            
            <View style={styles.totalPaymentRow}>
              <Text style={styles.totalPaymentLabel}>Total Paid</Text>
              <Text style={styles.totalPaymentValue}>${booking.totalAmount}</Text>
            </View>
          </View>
        </Animated.View>

        {/* Contact & Support */}
        <Animated.View style={[styles.section, { opacity: fadeAnim }]}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionIcon}>
              <Phone size={20} color={colors.accent} />
            </View>
            <Text style={styles.sectionTitle}>Need Help?</Text>
          </View>
          
          <View style={styles.contactContainer}>
            <Pressable style={styles.contactItem}>
              <View style={styles.contactIcon}>
                <Phone size={18} color={colors.accent} />
              </View>
              <View style={styles.contactInfo}>
                <Text style={styles.contactTitle}>Call Support</Text>
                <Text style={styles.contactDetail}>+****************</Text>
              </View>
            </Pressable>
            
            <Pressable style={styles.contactItem}>
              <View style={styles.contactIcon}>
                <Mail size={18} color={colors.accent} />
              </View>
              <View style={styles.contactInfo}>
                <Text style={styles.contactTitle}>Email Support</Text>
                <Text style={styles.contactDetail}><EMAIL></Text>
              </View>
            </Pressable>
          </View>
        </Animated.View>

        {/* Important Information */}
        <Animated.View style={[styles.infoSection, { opacity: fadeAnim }]}>
          <Text style={styles.infoTitle}>Important Reminders</Text>
          <View style={styles.infoGrid}>
            <View style={styles.infoCard}>
              <Text style={styles.infoCardTitle}>Before Pickup</Text>
              <Text style={styles.infoCardText}>• Valid driver's license{"\n"}• Credit card for security{"\n"}• Arrive 15 minutes early</Text>
            </View>
            <View style={styles.infoCard}>
              <Text style={styles.infoCardTitle}>During Rental</Text>
              <Text style={styles.infoCardText}>• Follow traffic rules{"\n"}• Keep vehicle clean{"\n"}• Report any issues</Text>
            </View>
            <View style={styles.infoCard}>
              <Text style={styles.infoCardTitle}>At Return</Text>
              <Text style={styles.infoCardText}>• Same fuel level{"\n"}• Vehicle inspection{"\n"}• Return on time</Text>
            </View>
          </View>
        </Animated.View>
      </ScrollView>

      {/* Footer Actions */}
      <Animated.View style={[styles.footer, { opacity: fadeAnim }]}>
        <View style={styles.footerButtons}>
          <Pressable style={styles.secondaryButton} onPress={handleShare}>
            <Share2 size={20} color={colors.accent} />
            <Text style={styles.secondaryButtonText}>Share</Text>
          </Pressable>
          <View style={styles.primaryButtonContainer}>
            <Button
              title="Download Confirmation"
              onPress={handleDownload}
              variant="primary"
              size="large"
            />
          </View>
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundSecondary,
  },
  header: {
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
  headerActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  actionButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 22,
    backgroundColor: colors.backgroundSecondary,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: 120,
  },
  
  // Success Animation
  successContainer: {
    alignItems: 'center',
    paddingVertical: spacing.xxl,
    marginBottom: spacing.lg,
  },
  successIcon: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.success + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  successTitle: {
    fontSize: 32,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  successMessage: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: typography.lineHeights.relaxed * typography.sizes.md,
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  
  // Reference Card
  referenceCard: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: spacing.xl,
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 2,
    borderColor: colors.accent + '20',
  },
  referenceLabel: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
    marginBottom: spacing.sm,
  },
  referenceNumber: {
    fontSize: 28,
    fontWeight: typography.weights.bold as any,
    color: colors.accent,
    letterSpacing: 2,
    marginBottom: spacing.sm,
  },
  referenceNote: {
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  
  // Section Styles
  section: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: spacing.xl,
    marginBottom: spacing.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.lg,
  },
  sectionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.accent + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  sectionTitle: {
    flex: 1,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
  ratingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.warning + '20',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 16,
    gap: spacing.xs,
  },
  ratingText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: colors.warning,
  },
  durationBadge: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: colors.accent,
    backgroundColor: colors.accent + '20',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderRadius: 16,
  },
  paidBadge: {
    backgroundColor: colors.success,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderRadius: 16,
  },
  paidText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    letterSpacing: 1,
  },
  
  // Vehicle Card
  vehicleCard: {
    gap: spacing.lg,
  },
  vehicleImageContainer: {
    position: 'relative',
    alignItems: 'center',
  },
  vehicleImage: {
    width: screenWidth - 120,
    height: 160,
    borderRadius: 16,
    resizeMode: 'contain',
    backgroundColor: colors.backgroundSecondary,
  },
  categoryBadge: {
    position: 'absolute',
    top: spacing.md,
    right: spacing.md,
    backgroundColor: colors.accent,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderRadius: 16,
  },
  categoryText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  vehicleInfo: {
    alignItems: 'center',
  },
  vehicleName: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  vehicleDescription: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: typography.lineHeights.relaxed * typography.sizes.md,
    marginBottom: spacing.lg,
  },
  vehicleSpecs: {
    flexDirection: 'row',
    gap: spacing.md,
    justifyContent: 'center',
  },
  specChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 16,
    gap: spacing.xs,
  },
  specText: {
    fontSize: typography.sizes.sm,
    color: colors.text,
    fontWeight: typography.weights.medium as any,
  },
  
  // Timeline
  timelineContainer: {
    gap: spacing.lg,
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing.md,
  },
  timelineIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timelineContent: {
    flex: 1,
  },
  timelineTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  timelineDate: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: 2,
  },
  timelineTime: {
    fontSize: typography.sizes.sm,
    color: colors.accent,
    fontWeight: typography.weights.medium as any,
    marginBottom: spacing.xs,
  },
  timelineLocation: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  timelineLine: {
    width: 2,
    height: 30,
    backgroundColor: colors.borderLight,
    marginLeft: 19,
    marginVertical: -spacing.sm,
  },
  
  // Services
  servicesContainer: {
    gap: spacing.md,
  },
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
    backgroundColor: colors.backgroundSecondary,
    padding: spacing.lg,
    borderRadius: 16,
  },
  serviceIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.success + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: 2,
  },
  serviceDescription: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  servicePrice: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: colors.accent,
  },
  
  // Payment Breakdown
  paymentBreakdown: {
    gap: spacing.md,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  paymentLabel: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
  },
  paymentValue: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
  },
  paymentDivider: {
    height: 1,
    backgroundColor: colors.borderLight,
    marginVertical: spacing.sm,
  },
  totalPaymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.success + '10',
    padding: spacing.lg,
    borderRadius: 16,
    marginTop: spacing.sm,
  },
  totalPaymentLabel: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
  totalPaymentValue: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.success,
  },
  
  // Contact
  contactContainer: {
    gap: spacing.md,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
    backgroundColor: colors.backgroundSecondary,
    padding: spacing.lg,
    borderRadius: 16,
  },
  contactIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.accent + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  contactInfo: {
    flex: 1,
  },
  contactTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: 2,
  },
  contactDetail: {
    fontSize: typography.sizes.sm,
    color: colors.accent,
    fontWeight: typography.weights.medium as any,
  },
  
  // Info Section
  infoSection: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: spacing.xl,
    marginBottom: spacing.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  infoTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  infoGrid: {
    gap: spacing.md,
  },
  infoCard: {
    backgroundColor: colors.backgroundSecondary,
    padding: spacing.lg,
    borderRadius: 16,
  },
  infoCardTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  infoCardText: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
  },
  
  // Footer
  footer: {
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    paddingBottom: spacing.lg + 20,
  },
  footerButtons: {
    flexDirection: 'row',
    gap: spacing.md,
    alignItems: 'center',
  },
  secondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.accent + '30',
  },
  secondaryButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.accent,
  },
  primaryButtonContainer: {
    flex: 1,
  },
});