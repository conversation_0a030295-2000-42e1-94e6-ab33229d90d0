// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/visa/self_service_models.dart';
import 'package:culture_connect/services/travel/visa/self_service_visa_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// Screen for displaying document checklist
class DocumentChecklistScreen extends ConsumerStatefulWidget {
  const DocumentChecklistScreen({
    super.key,
    this.passportCountry,
    this.destinationCountry,
    this.visaType,
  });

  final String? passportCountry;
  final String? destinationCountry;
  final String? visaType;

  @override
  ConsumerState<DocumentChecklistScreen> createState() => _DocumentChecklistScreenState();
}

class _DocumentChecklistScreenState extends ConsumerState<DocumentChecklistScreen> {
  List<DocumentChecklistItem> _documents = [];
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadDocuments();
  }

  Future<void> _loadDocuments() async {
    if (widget.passportCountry == null || 
        widget.destinationCountry == null || 
        widget.visaType == null) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final selfServiceService = ref.read(selfServiceVisaServiceProvider);
      final documents = await selfServiceService.generateDocumentChecklist(
        passportCountry: widget.passportCountry!,
        destinationCountry: widget.destinationCountry!,
        visaType: widget.visaType!,
      );

      if (mounted) {
        setState(() {
          _documents = documents;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to load document checklist: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        title: Text(
          'Document Checklist',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: AppTheme.fontWeightBold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showChecklistInfo,
            icon: const Icon(
              Icons.info_outline,
              color: AppTheme.infoColor,
            ),
          ),
        ],
      ),
      body: _buildBody(theme),
    );
  }

  Widget _buildBody(ThemeData theme) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: AppTheme.primaryColor,
            ),
            SizedBox(height: AppTheme.spacingMd),
            Text(
              'Generating document checklist...',
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
                fontWeight: AppTheme.fontWeightMedium,
              ),
            ),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return ErrorView(
        error: _errorMessage!,
        onRetry: _loadDocuments,
      );
    }

    return RefreshIndicator(
      onRefresh: _loadDocuments,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeroSection(theme),
            const SizedBox(height: AppTheme.spacingLg),
            if (_documents.isNotEmpty) ...[
              _buildProgressSummary(theme),
              const SizedBox(height: AppTheme.spacingLg),
              _buildDocumentList(theme),
            ] else ...[
              _buildEmptyState(theme),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeroSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.infoColor.withValues(alpha: 0.1),
            AppTheme.infoColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        border: Border.all(
          color: AppTheme.infoColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMd),
            decoration: BoxDecoration(
              color: AppTheme.infoColor,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
            ),
            child: const Icon(
              Icons.checklist,
              color: AppTheme.white,
              size: 32,
            ),
          ),
          const SizedBox(width: AppTheme.spacingMd),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Document Checklist',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: AppTheme.fontWeightBold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                Text(
                  'Complete list of documents required for your visa application',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSummary(ThemeData theme) {
    final completedCount = _documents.where((doc) => doc.isCompleted).length;
    final totalCount = _documents.length;
    final mandatoryCount = _documents.where((doc) => doc.isMandatory).length;
    final completedMandatory = _documents.where((doc) => doc.isMandatory && doc.isCompleted).length;
    final progress = totalCount > 0 ? completedCount / totalCount : 0.0;

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Progress',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: AppTheme.fontWeightBold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              Text(
                '$completedCount/$totalCount completed',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: AppTheme.fontWeightSemibold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: AppTheme.borderColor,
            valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Row(
            children: [
              _buildStatItem('Mandatory', '$completedMandatory/$mandatoryCount', AppTheme.errorColor),
              const SizedBox(width: AppTheme.spacingLg),
              _buildStatItem('Optional', '${completedCount - completedMandatory}/${totalCount - mandatoryCount}', AppTheme.infoColor),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: AppTheme.fontSizeSm,
            color: AppTheme.textTertiaryColor,
            fontWeight: AppTheme.fontWeightMedium,
          ),
        ),
        const SizedBox(height: AppTheme.spacingXs),
        Text(
          value,
          style: TextStyle(
            fontSize: AppTheme.fontSizeMd,
            color: color,
            fontWeight: AppTheme.fontWeightBold,
          ),
        ),
      ],
    );
  }

  Widget _buildDocumentList(ThemeData theme) {
    // Group documents by category
    final groupedDocs = <String, List<DocumentChecklistItem>>{};
    for (final doc in _documents) {
      groupedDocs.putIfAbsent(doc.category, () => []).add(doc);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Required Documents',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: AppTheme.fontWeightBold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMd),
        ...groupedDocs.entries.map((entry) => _buildDocumentCategory(entry.key, entry.value, theme)),
      ],
    );
  }

  Widget _buildDocumentCategory(String category, List<DocumentChecklistItem> documents, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.spacingSm,
            vertical: AppTheme.spacingXs,
          ),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
          ),
          child: Text(
            category,
            style: TextStyle(
              color: AppTheme.primaryColor,
              fontWeight: AppTheme.fontWeightSemibold,
              fontSize: AppTheme.fontSizeSm,
            ),
          ),
        ),
        const SizedBox(height: AppTheme.spacingMd),
        ...documents.map((doc) => _buildDocumentItem(doc, theme)),
        const SizedBox(height: AppTheme.spacingLg),
      ],
    );
  }

  Widget _buildDocumentItem(DocumentChecklistItem document, ThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMd),
      child: Material(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        elevation: 2,
        child: Container(
          padding: const EdgeInsets.all(AppTheme.spacingMd),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Checkbox(
                    value: document.isCompleted,
                    onChanged: (value) => _toggleDocumentCompletion(document, value ?? false),
                    activeColor: AppTheme.successColor,
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                document.documentName,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: AppTheme.fontWeightBold,
                                  color: AppTheme.textPrimaryColor,
                                  decoration: document.isCompleted ? TextDecoration.lineThrough : null,
                                ),
                              ),
                            ),
                            if (document.isMandatory) ...[
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: AppTheme.spacingXs,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: AppTheme.errorColor,
                                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                                ),
                                child: Text(
                                  'Required',
                                  style: TextStyle(
                                    color: AppTheme.white,
                                    fontSize: AppTheme.fontSizeXs,
                                    fontWeight: AppTheme.fontWeightSemibold,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                        Text(
                          document.description,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              if (document.requirements.isNotEmpty) ...[
                const SizedBox(height: AppTheme.spacingSm),
                Text(
                  'Requirements:',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: AppTheme.fontWeightSemibold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                ...document.requirements.map((req) => Padding(
                  padding: const EdgeInsets.only(left: AppTheme.spacingMd, top: AppTheme.spacingXs),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('• ', style: TextStyle(color: AppTheme.textSecondaryColor)),
                      Expanded(
                        child: Text(
                          req,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingXl),
            decoration: BoxDecoration(
              color: AppTheme.borderColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.description,
              size: 64,
              color: AppTheme.borderColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingLg),
          Text(
            'No Documents Available',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: AppTheme.fontWeightBold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingSm),
          Text(
            'Please provide your travel details to generate a document checklist.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _toggleDocumentCompletion(DocumentChecklistItem document, bool completed) {
    setState(() {
      final index = _documents.indexWhere((doc) => doc.id == document.id);
      if (index != -1) {
        _documents[index] = document.copyWith(isCompleted: completed);
      }
    });

    // TODO: Save progress to backend
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          completed 
              ? '${document.documentName} marked as completed'
              : '${document.documentName} marked as incomplete',
        ),
        backgroundColor: completed ? AppTheme.successColor : AppTheme.warningColor,
      ),
    );
  }

  void _showChecklistInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Document Checklist'),
        content: const SingleChildScrollView(
          child: Text(
            'This checklist shows all documents required for your visa application:\n\n'
            '• Required documents are marked with a red badge\n'
            '• Optional documents can improve your application\n'
            '• Check off documents as you collect them\n'
            '• Tap on any document for detailed requirements\n\n'
            'Make sure to gather all required documents before submitting your application.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}
