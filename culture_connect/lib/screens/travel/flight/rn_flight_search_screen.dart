// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/screens/travel/flight/rn_flight_results_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/rn_components/rn_airport_dropdown.dart';
import 'package:culture_connect/widgets/rn_components/rn_calendar_modal.dart';
import 'package:culture_connect/widgets/rn_components/rn_class_selection_modal.dart';

/// Pixel-perfect React Native FlightBookingScreen replica
class RNFlightSearchScreen extends ConsumerStatefulWidget {
  const RNFlightSearchScreen({super.key});

  @override
  ConsumerState<RNFlightSearchScreen> createState() =>
      _RNFlightSearchScreenState();
}

class _RNFlightSearchScreenState extends ConsumerState<RNFlightSearchScreen> {
  String _from = "New York";
  String _fromCode = "JFK";
  String _to = "London";
  String _toCode = "LHR";
  DateTime _departDate = DateTime(2024, 6, 10);
  DateTime? _returnDate = DateTime(2024, 6, 17);
  int _travelers = 1;
  String _tripType = "round-trip";
  bool _showCalendar = false;
  String _selectedClass = 'Economy Class';
  bool _showClassSelection = false;
  bool _showFromDropdown = false;
  bool _showToDropdown = false;

  void _handleIncrementTravelers() {
    setState(() {
      _travelers++;
    });
  }

  void _handleDecrementTravelers() {
    if (_travelers > 1) {
      setState(() {
        _travelers--;
      });
    }
  }

  void _handleSearchFlights() {
    // Navigate to flight results
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => RNFlightResultsScreen(
          from: _from,
          to: _to,
        ),
      ),
    );
  }

  void _handleDateSelection(
      DateTime selectedDepartDate, DateTime? selectedReturnDate) {
    setState(() {
      _departDate = selectedDepartDate;
      if (_tripType == 'round-trip' && selectedReturnDate != null) {
        _returnDate = selectedReturnDate;
      } else if (_tripType == 'one-way') {
        _returnDate = null;
      }
    });
  }

  void _handleClassSelection(String classType, String className) {
    setState(() {
      _selectedClass = className;
    });
  }

  void _handleFromAirportSelection(Airport airport) {
    setState(() {
      _from = airport.city;
      _fromCode = airport.code;
    });
  }

  void _handleToAirportSelection(Airport airport) {
    setState(() {
      _to = airport.city;
      _toCode = airport.code;
    });
  }

  String _formatDate(DateTime date) {
    return '${date.day} ${_getMonthName(date.month)}';
  }

  String _formatDateYear(DateTime date) {
    return date.year.toString();
  }

  String _getMonthName(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[month - 1];
  }

  void _handleSwapLocations() {
    setState(() {
      final tempFrom = _from;
      final tempFromCode = _fromCode;
      _from = _to;
      _fromCode = _toCode;
      _to = tempFrom;
      _toCode = tempFromCode;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Stack(
        children: [
          // Main Content
          Column(
            children: [
              // Enhanced Header with seamless background coverage
              Container(
                // Complete background coverage
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppTheme.primaryColor,
                      AppTheme.primaryColor.withOpacity(0.8),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryColor.withOpacity(0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: SafeArea(
                  bottom: false,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
                    child: Row(
                      children: [
                        Container(
                          width: 44,
                          height: 44,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(22),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: const Icon(Icons.arrow_back_ios),
                            color: Colors.white,
                            iconSize: 20,
                          ),
                        ),
                        const Expanded(
                          child: Text(
                            'Book Flight',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.w700,
                              color: Colors.white,
                              letterSpacing: -0.5,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        const SizedBox(width: 56), // Balance the back button
                      ],
                    ),
                  ),
                ),
              ),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Trip Type Selection
                      _buildTripTypeSelection(),
                      const SizedBox(height: 24),

                      // Location Selection
                      _buildLocationSelection(),
                      const SizedBox(height: 24),

                      // Date Selection
                      _buildDateSelection(),
                      const SizedBox(height: 24),

                      // Travelers and Class
                      _buildTravelersAndClass(),
                      const SizedBox(height: 32),

                      // Search Button
                      _buildSearchButton(),
                      const SizedBox(height: 16), // Additional bottom spacing
                    ],
                  ),
                ),
              ),
            ],
          ),

          // Modals
          RNAirportDropdown(
            visible: _showFromDropdown,
            onClose: () => setState(() => _showFromDropdown = false),
            onSelectAirport: _handleFromAirportSelection,
            title: 'From',
          ),

          RNAirportDropdown(
            visible: _showToDropdown,
            onClose: () => setState(() => _showToDropdown = false),
            onSelectAirport: _handleToAirportSelection,
            title: 'To',
          ),

          RNCalendarModal(
            visible: _showCalendar,
            onClose: () => setState(() => _showCalendar = false),
            onSelectDates: _handleDateSelection,
            tripType: _tripType,
            initialDepartDate: _departDate,
            initialReturnDate: _returnDate,
          ),

          RNClassSelectionModal(
            visible: _showClassSelection,
            onClose: () => setState(() => _showClassSelection = false),
            onSelectClass: _handleClassSelection,
            initialClass: 'economy',
          ),
        ],
      ),
    );
  }

  Widget _buildTripTypeSelection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Trip Type',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
              letterSpacing: -0.3,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: AppTheme.borderColor.withOpacity(0.3),
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.all(4),
            child: Row(
              children: [
                Expanded(
                  child: _buildTripTypeOption('round-trip', 'Round Trip'),
                ),
                Expanded(
                  child: _buildTripTypeOption('one-way', 'One Way'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTripTypeOption(String type, String label) {
    final isSelected = _tripType == type;
    return GestureDetector(
      onTap: () {
        setState(() {
          _tripType = type;
          if (type == 'one-way') {
            _returnDate = null;
          } else {
            _returnDate = DateTime(2024, 6, 17);
          }
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        margin: const EdgeInsets.symmetric(horizontal: 2),
        padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(10),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: AppTheme.primaryColor.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 15,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: isSelected ? Colors.white : AppTheme.textSecondaryColor,
            letterSpacing: -0.2,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildLocationSelection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Locations',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
              letterSpacing: -0.3,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildLocationField(
                  'From',
                  _from,
                  _fromCode,
                  () => setState(() => _showFromDropdown = true),
                ),
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 12),
                child: GestureDetector(
                  onTap: _handleSwapLocations,
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.primaryColor.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.swap_horiz,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ),
              Expanded(
                child: _buildLocationField(
                  'To',
                  _to,
                  _toCode,
                  () => setState(() => _showToDropdown = true),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLocationField(
      String label, String city, String code, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(18),
        decoration: BoxDecoration(
          color: AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(14),
          border: Border.all(
            color: AppTheme.borderColor.withOpacity(0.5),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.04),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor.withOpacity(0.8),
                fontWeight: FontWeight.w500,
                letterSpacing: 0.5,
              ),
            ),
            const SizedBox(height: 6),
            Text(
              code,
              style: const TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.w700,
                color: AppTheme.textPrimaryColor,
                letterSpacing: -0.5,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              city,
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSelection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Dates',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
              letterSpacing: -0.3,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDateField(
                  'Departure',
                  _formatDate(_departDate),
                  _formatDateYear(_departDate),
                  () => setState(() => _showCalendar = true),
                ),
              ),
              if (_tripType == 'round-trip') ...[
                const SizedBox(width: 12),
                Expanded(
                  child: _buildDateField(
                    'Return',
                    _returnDate != null ? _formatDate(_returnDate!) : 'Select',
                    _returnDate != null ? _formatDateYear(_returnDate!) : '',
                    () => setState(() => _showCalendar = true),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDateField(
      String label, String date, String year, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(18),
        decoration: BoxDecoration(
          color: AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(14),
          border: Border.all(
            color: AppTheme.borderColor.withOpacity(0.5),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.04),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.calendar_today_outlined,
                  size: 16,
                  color: AppTheme.primaryColor.withOpacity(0.7),
                ),
                const SizedBox(width: 6),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor.withOpacity(0.8),
                    fontWeight: FontWeight.w500,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              date,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimaryColor,
                letterSpacing: -0.2,
              ),
            ),
            if (year.isNotEmpty) ...[
              const SizedBox(height: 2),
              Text(
                year,
                style: const TextStyle(
                  fontSize: 13,
                  color: AppTheme.textSecondaryColor,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTravelersAndClass() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Travelers & Class',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
              letterSpacing: -0.3,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildTravelersField(),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildClassField(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTravelersField() {
    return Container(
      padding: const EdgeInsets.all(18),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(14),
        border: Border.all(
          color: AppTheme.borderColor.withOpacity(0.5),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.person_outline,
                size: 16,
                color: AppTheme.primaryColor.withOpacity(0.7),
              ),
              const SizedBox(width: 6),
              Text(
                'Travelers',
                style: TextStyle(
                  fontSize: 12,
                  color: AppTheme.textSecondaryColor.withOpacity(0.8),
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              GestureDetector(
                onTap: _handleDecrementTravelers,
                child: Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: _travelers > 1
                        ? AppTheme.primaryColor
                        : AppTheme.borderColor.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: _travelers > 1
                        ? [
                            BoxShadow(
                              color: AppTheme.primaryColor.withOpacity(0.3),
                              blurRadius: 6,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : null,
                  ),
                  child: Icon(
                    Icons.remove,
                    color: _travelers > 1
                        ? Colors.white
                        : AppTheme.textSecondaryColor,
                    size: 18,
                  ),
                ),
              ),
              Expanded(
                child: Text(
                  '$_travelers',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                    letterSpacing: -0.2,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              GestureDetector(
                onTap: _handleIncrementTravelers,
                child: Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.primaryColor.withOpacity(0.3),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.add,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildClassField() {
    return GestureDetector(
      onTap: () => setState(() => _showClassSelection = true),
      child: Container(
        padding: const EdgeInsets.all(18),
        decoration: BoxDecoration(
          color: AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(14),
          border: Border.all(
            color: AppTheme.borderColor.withOpacity(0.5),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.04),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.airline_seat_recline_normal,
                  size: 16,
                  color: AppTheme.primaryColor.withOpacity(0.7),
                ),
                const SizedBox(width: 6),
                Text(
                  'Class',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor.withOpacity(0.8),
                    fontWeight: FontWeight.w500,
                    letterSpacing: 0.5,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.keyboard_arrow_down,
                  size: 18,
                  color: AppTheme.textSecondaryColor.withOpacity(0.6),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _selectedClass,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimaryColor,
                letterSpacing: -0.2,
              ),
            ),
            const SizedBox(height: 4),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.4),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _handleSearchFlights,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.search,
              size: 20,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            const Text(
              'Search Flights',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
                letterSpacing: -0.2,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
