import { create } from "zustand";
import { Booking } from "@/types";
import { bookings as mockBookings } from "@/mocks/bookings";

interface BookingState {
  bookings: Booking[];
  loading: boolean;
  fetchBookings: () => Promise<void>;
  addBooking: (booking: Booking) => void;
  cancelBooking: (id: string) => void;
}

export const useBookingStore = create<BookingState>((set) => ({
  bookings: [],
  loading: false,
  fetchBookings: async () => {
    set({ loading: true });
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));
    set({ bookings: mockBookings, loading: false });
  },
  addBooking: (booking) =>
    set((state) => ({ bookings: [...state.bookings, booking] })),
  cancelBooking: (id) =>
    set((state) => ({
      bookings: state.bookings.map((booking) =>
        booking.id === id ? { ...booking, status: "cancelled" } : booking
      ),
    })),
}));