import 'package:flutter/material.dart';
import 'package:culture_connect/models/rn_booking.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Statistics dashboard component matching React Native design
/// Displays key booking metrics with animated counters and visual indicators
class BookingStatsDashboard extends StatefulWidget {
  final BookingStatistics statistics;
  final bool isLoading;

  const BookingStatsDashboard({
    super.key,
    required this.statistics,
    this.isLoading = false,
  });

  @override
  State<BookingStatsDashboard> createState() => _BookingStatsDashboardState();
}

class _BookingStatsDashboardState extends State<BookingStatsDashboard>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<double>(
      begin: 30.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return _buildLoadingState();
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: _buildStatsGrid(),
          ),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: GridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: 2,
        mainAxisSpacing: 12,
        crossAxisSpacing: 12,
        childAspectRatio: 1.8,
        children: List.generate(4, (index) => _buildLoadingCard()),
      ),
    );
  }

  Widget _buildLoadingCard() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor,
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
        ),
      ),
    );
  }

  Widget _buildStatsGrid() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: GridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: 2,
        mainAxisSpacing: 12,
        crossAxisSpacing: 12,
        childAspectRatio: 1.8,
        children: [
          _buildStatCard(
            title: 'Total Trips',
            value: widget.statistics.totalTrips.toString(),
            icon: Icons.flight_takeoff,
            color: AppTheme.primaryColor,
            delay: 0,
          ),
          _buildStatCard(
            title: 'Countries',
            value: widget.statistics.countries.toString(),
            icon: Icons.public,
            color: AppTheme.secondaryColor,
            delay: 100,
          ),
          _buildStatCard(
            title: 'Avg Rating',
            value: widget.statistics.getFormattedRating(),
            icon: Icons.star,
            color: const Color(0xFFF59E0B), // Warning/Gold color
            delay: 200,
          ),
          _buildStatCard(
            title: 'Savings',
            value: widget.statistics.getFormattedSavings(),
            icon: Icons.savings,
            color: const Color(0xFF10B981), // Success/Green color
            delay: 300,
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required int delay,
  }) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 600 + delay),
      tween: Tween<double>(begin: 0.0, end: 1.0),
      curve: Curves.easeOutCubic,
      builder: (context, animationValue, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * animationValue),
          child: Opacity(
            opacity: animationValue,
            child: Container(
              decoration: BoxDecoration(
                color: AppTheme.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.shadowColor,
                    offset: const Offset(0, 2),
                    blurRadius: 8,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: color.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            icon,
                            color: color,
                            size: 20,
                          ),
                        ),
                        if (title == 'Avg Rating')
                          Row(
                            children: List.generate(5, (index) {
                              return Icon(
                                Icons.star,
                                size: 12,
                                color: index < widget.statistics.averageRating
                                    ? color
                                    : Colors.grey.shade300,
                              );
                            }),
                          ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AnimatedDefaultTextStyle(
                          duration: const Duration(milliseconds: 300),
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimaryColor,
                            height: 1.0,
                          ),
                          child: TweenAnimationBuilder<double>(
                            duration: Duration(milliseconds: 800 + delay),
                            tween: Tween<double>(
                              begin: 0.0,
                              end: double.tryParse(value.replaceAll(RegExp(r'[^\d.]'), '')) ?? 0.0,
                            ),
                            curve: Curves.easeOutCubic,
                            builder: (context, animatedValue, child) {
                              if (title == 'Savings') {
                                return Text('\$${animatedValue.toInt()}');
                              } else if (title == 'Avg Rating') {
                                return Text(animatedValue.toStringAsFixed(1));
                              } else {
                                return Text(animatedValue.toInt().toString());
                              }
                            },
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          title,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: AppTheme.textSecondaryColor,
                            letterSpacing: 0.3,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
