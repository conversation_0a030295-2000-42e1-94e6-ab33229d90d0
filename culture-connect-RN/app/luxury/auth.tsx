import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  Modal,
  Pressable,
  TextInput,
  Animated,
  Dimensions,
  Platform,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { X, Shield, Smartphone, Key, Crown, Sparkles, Lock } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface LuxuryAuthModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const LuxuryAuthModal: React.FC<LuxuryAuthModalProps> = ({
  visible,
  onClose,
  onSuccess,
}) => {
  const [authMethod, setAuthMethod] = useState<'pin' | 'passkey'>('pin');
  const [pin, setPin] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const successAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const handleAuthenticate = async () => {
    if (authMethod === 'pin' && pin.length !== 6) {
      Alert.alert('Invalid PIN', 'Please enter a 6-digit PIN');
      return;
    }

    setIsLoading(true);
    
    // Simulate authentication
    setTimeout(() => {
      setIsLoading(false);
      setShowSuccess(true);
      
      Animated.sequence([
        Animated.timing(successAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.delay(1500),
        Animated.timing(successAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setShowSuccess(false);
        onSuccess();
      });
    }, 2000);
  };

  const handlePasskeyAuth = async () => {
    setIsLoading(true);
    
    // Simulate passkey authentication
    setTimeout(() => {
      setIsLoading(false);
      setShowSuccess(true);
      
      Animated.sequence([
        Animated.timing(successAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.delay(1500),
        Animated.timing(successAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setShowSuccess(false);
        onSuccess();
      });
    }, 1500);
  };

  const renderPinInput = () => (
    <View style={styles.pinContainer}>
      <Text style={styles.pinLabel}>Enter your 6-digit PIN</Text>
      <Text style={styles.pinSubtext}>A verification code will be sent to your registered phone number</Text>
      
      <View style={styles.pinInputContainer}>
        {[...Array(6)].map((_, index) => (
          <View
            key={index}
            style={[
              styles.pinDigit,
              pin.length > index && styles.pinDigitFilled,
            ]}
          >
            <Text style={styles.pinDigitText}>
              {pin.length > index ? '●' : ''}
            </Text>
          </View>
        ))}
      </View>
      
      <TextInput
        style={styles.hiddenInput}
        value={pin}
        onChangeText={setPin}
        keyboardType="numeric"
        maxLength={6}
        secureTextEntry
        autoFocus
      />
      
      <Pressable
        style={[styles.authButton, pin.length === 6 && styles.authButtonActive]}
        onPress={handleAuthenticate}
        disabled={pin.length !== 6 || isLoading}
      >
        <Text style={[styles.authButtonText, pin.length === 6 && styles.authButtonTextActive]}>
          {isLoading ? 'Verifying...' : 'Verify PIN'}
        </Text>
      </Pressable>
    </View>
  );

  const renderPasskeyAuth = () => (
    <View style={styles.passkeyContainer}>
      <View style={styles.passkeyIcon}>
        <Key size={48} color="#D946EF" />
      </View>
      <Text style={styles.passkeyTitle}>Authenticate with Passkey</Text>
      <Text style={styles.passkeySubtext}>
        Use your device's biometric authentication or security key to access luxury services
      </Text>
      
      <Pressable
        style={[styles.authButton, styles.authButtonActive]}
        onPress={handlePasskeyAuth}
        disabled={isLoading}
      >
        <Shield size={20} color={colors.white} style={{ marginRight: spacing.sm }} />
        <Text style={[styles.authButtonText, styles.authButtonTextActive]}>
          {isLoading ? 'Authenticating...' : 'Use Passkey'}
        </Text>
      </Pressable>
    </View>
  );

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <Animated.View style={[styles.overlay, { opacity: fadeAnim }]}>
        <Pressable style={styles.overlayTouchable} onPress={onClose} />
        
        <Animated.View
          style={[
            styles.modalContainer,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <View style={styles.crownContainer}>
                <Crown size={24} color="#D946EF" />
                <Sparkles size={16} color="#F59E0B" style={styles.sparkle} />
              </View>
              <Text style={styles.title}>Luxury Access</Text>
            </View>
            <Pressable style={styles.closeButton} onPress={onClose}>
              <X size={24} color={colors.text} />
            </Pressable>
          </View>

          {/* Content */}
          <View style={styles.content}>
            <Text style={styles.subtitle}>
              Premium authentication required for exclusive luxury services
            </Text>

            {/* Auth Method Toggle */}
            <View style={styles.toggleContainer}>
              <Pressable
                style={[
                  styles.toggleButton,
                  authMethod === 'pin' && styles.toggleButtonActive,
                ]}
                onPress={() => setAuthMethod('pin')}
              >
                <Smartphone size={20} color={authMethod === 'pin' ? colors.white : '#D946EF'} />
                <Text style={[
                  styles.toggleText,
                  authMethod === 'pin' && styles.toggleTextActive,
                ]}>
                  PIN
                </Text>
              </Pressable>
              
              <Pressable
                style={[
                  styles.toggleButton,
                  authMethod === 'passkey' && styles.toggleButtonActive,
                ]}
                onPress={() => setAuthMethod('passkey')}
              >
                <Key size={20} color={authMethod === 'passkey' ? colors.white : '#D946EF'} />
                <Text style={[
                  styles.toggleText,
                  authMethod === 'passkey' && styles.toggleTextActive,
                ]}>
                  Passkey
                </Text>
              </Pressable>
            </View>

            {/* Auth Content */}
            {authMethod === 'pin' ? renderPinInput() : renderPasskeyAuth()}
          </View>

          {/* Security Notice */}
          <View style={styles.securityNotice}>
            <Lock size={16} color={colors.textSecondary} />
            <Text style={styles.securityText}>
              Your authentication is secured with end-to-end encryption
            </Text>
          </View>
        </Animated.View>

        {/* Success Animation */}
        {showSuccess && (
          <Animated.View
            style={[
              styles.successOverlay,
              {
                opacity: successAnim,
                transform: [{ scale: successAnim }],
              },
            ]}
          >
            <View style={styles.successContainer}>
              <View style={styles.successIcon}>
                <Crown size={48} color="#D946EF" />
              </View>
              <Text style={styles.successTitle}>Welcome to Luxury</Text>
              <Text style={styles.successSubtext}>Authentication successful</Text>
            </View>
          </Animated.View>
        )}
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  overlayTouchable: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    backgroundColor: colors.white,
    borderRadius: 24,
    width: '100%',
    maxWidth: 400,
    overflow: 'hidden',
    shadowColor: colors.shadowDark,
    shadowOffset: { width: 0, height: 20 },
    shadowOpacity: 0.3,
    shadowRadius: 30,
    elevation: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
    background: 'linear-gradient(135deg, #FAF5FF 0%, #F3E8FF 100%)',
    backgroundColor: '#FAF5FF',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  crownContainer: {
    position: 'relative',
    marginRight: spacing.md,
  },
  sparkle: {
    position: 'absolute',
    top: -4,
    right: -4,
  },
  title: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
  closeButton: {
    padding: spacing.sm,
  },
  content: {
    padding: spacing.lg,
  },
  subtitle: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.xl,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.md,
  },
  toggleContainer: {
    flexDirection: 'row',
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: spacing.xs,
    marginBottom: spacing.xl,
  },
  toggleButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    borderRadius: 8,
    gap: spacing.sm,
  },
  toggleButtonActive: {
    backgroundColor: '#D946EF',
    shadowColor: '#D946EF',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  toggleText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: '#D946EF',
  },
  toggleTextActive: {
    color: colors.white,
  },
  pinContainer: {
    alignItems: 'center',
  },
  pinLabel: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  pinSubtext: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.xl,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
  },
  pinInputContainer: {
    flexDirection: 'row',
    gap: spacing.md,
    marginBottom: spacing.xl,
  },
  pinDigit: {
    width: 48,
    height: 56,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.borderLight,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.backgroundSecondary,
  },
  pinDigitFilled: {
    borderColor: '#D946EF',
    backgroundColor: '#FAF5FF',
  },
  pinDigitText: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: '#D946EF',
  },
  hiddenInput: {
    position: 'absolute',
    opacity: 0,
    width: 1,
    height: 1,
  },
  passkeyContainer: {
    alignItems: 'center',
  },
  passkeyIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#FAF5FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  passkeyTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  passkeySubtext: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.xl,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
  },
  authButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.xl,
    borderRadius: 16,
    backgroundColor: colors.backgroundSecondary,
    borderWidth: 2,
    borderColor: colors.borderLight,
  },
  authButtonActive: {
    backgroundColor: '#D946EF',
    borderColor: '#D946EF',
    shadowColor: '#D946EF',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  authButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.textSecondary,
  },
  authButtonTextActive: {
    color: colors.white,
  },
  securityNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.lg,
    backgroundColor: colors.backgroundSecondary,
    gap: spacing.sm,
  },
  securityText: {
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
  },
  successOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(217, 70, 239, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  successContainer: {
    alignItems: 'center',
  },
  successIcon: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  successTitle: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    marginBottom: spacing.sm,
  },
  successSubtext: {
    fontSize: typography.sizes.md,
    color: 'rgba(255, 255, 255, 0.8)',
  },
});