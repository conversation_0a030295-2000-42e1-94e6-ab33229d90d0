import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:culture_connect/models/premium_booking.dart';
import 'package:culture_connect/design_system/premium_design_tokens.dart';

/// Package-specific booking layout for comprehensive travel packages
/// Includes flight + hotel + activities + restaurants + guides + special places
class PackageBookingLayout extends StatefulWidget {
  final PremiumBooking booking;

  const PackageBookingLayout({
    super.key,
    required this.booking,
  });

  @override
  State<PackageBookingLayout> createState() => _PackageBookingLayoutState();
}

class _PackageBookingLayoutState extends State<PackageBookingLayout>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _startAnimations() {
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildPackageOverviewSection(),
            _buildItineraryTimelineSection(),
            _buildFlightDetailsSection(),
            _buildAccommodationSection(),
            _buildActivitiesSection(),
            _buildDiningSection(),
            _buildGuideSection(),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildPackageOverviewSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.card_travel,
                color: const Color(0xFF8B5CF6),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Complete Travel Package',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xFF8B5CF6).withAlpha(26),
                  const Color(0xFF06B6D4).withAlpha(26),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF8B5CF6).withAlpha(51),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildPackageComponent(
                        icon: Icons.flight,
                        title: 'Flights',
                        subtitle: 'Round-trip',
                        color: const Color(0xFF06B6D4),
                      ),
                    ),
                    Expanded(
                      child: _buildPackageComponent(
                        icon: Icons.hotel,
                        title: 'Hotel',
                        subtitle: '5 nights',
                        color: const Color(0xFF10B981),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildPackageComponent(
                        icon: Icons.local_activity,
                        title: 'Activities',
                        subtitle: '8 experiences',
                        color: const Color(0xFFF59E0B),
                      ),
                    ),
                    Expanded(
                      child: _buildPackageComponent(
                        icon: Icons.restaurant,
                        title: 'Dining',
                        subtitle: '6 restaurants',
                        color: const Color(0xFFEF4444),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItineraryTimelineSection() {
    final days = [
      {
        'day': 'Day 1',
        'date': 'Dec 15',
        'title': 'Arrival & City Tour',
        'activities': ['Airport pickup', 'Hotel check-in', 'Welcome dinner'],
      },
      {
        'day': 'Day 2',
        'date': 'Dec 16',
        'title': 'Cultural Exploration',
        'activities': [
          'Museum visit',
          'Local market tour',
          'Traditional cooking class'
        ],
      },
      {
        'day': 'Day 3',
        'date': 'Dec 17',
        'title': 'Adventure Day',
        'activities': [
          'Mountain hiking',
          'Scenic viewpoints',
          'Local cuisine lunch'
        ],
      },
      {
        'day': 'Day 4',
        'date': 'Dec 18',
        'title': 'Relaxation & Spa',
        'activities': ['Spa treatment', 'Beach time', 'Sunset dinner cruise'],
      },
      {
        'day': 'Day 5',
        'date': 'Dec 19',
        'title': 'Departure',
        'activities': [
          'Final shopping',
          'Airport transfer',
          'Flight departure'
        ],
      },
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.timeline,
                color: const Color(0xFF06B6D4),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Travel Itinerary',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: days.length,
            itemBuilder: (context, index) {
              final day = days[index];
              final isLast = index == days.length - 1;

              return _buildTimelineDay(
                day: day['day'] as String,
                date: day['date'] as String,
                title: day['title'] as String,
                activities: day['activities'] as List<String>,
                isLast: isLast,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFlightDetailsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.flight,
                color: const Color(0xFF06B6D4),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Flight Information',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildFlightCard(
            title: 'Outbound Flight',
            route: 'JFK → LHR',
            date: 'Dec 15, 2024',
            time: '14:30 - 23:15',
            flight: 'BA 117',
            seat: '12A',
            isOutbound: true,
          ),
          const SizedBox(height: 12),
          _buildFlightCard(
            title: 'Return Flight',
            route: 'LHR → JFK',
            date: 'Dec 20, 2024',
            time: '10:45 - 14:20',
            flight: 'BA 116',
            seat: '15F',
            isOutbound: false,
          ),
        ],
      ),
    );
  }

  Widget _buildAccommodationSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.hotel,
                color: const Color(0xFF10B981),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Accommodation',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF10B981).withAlpha(26),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF10B981).withAlpha(51),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        width: 60,
                        height: 60,
                        color: const Color(0xFF10B981),
                        child: Icon(
                          Icons.hotel,
                          color: PremiumDesignTokens.neutralWhite,
                          size: 30,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'The Grand Ocean Resort',
                            style: PremiumDesignTokens.bodyLarge.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Deluxe Ocean View Suite • Room 512',
                            style: PremiumDesignTokens.bodyMedium.copyWith(
                              color: PremiumDesignTokens.neutralGray600,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.star,
                                color: const Color(0xFFF59E0B),
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '4.8 (1,234 reviews)',
                                style: PremiumDesignTokens.bodySmall.copyWith(
                                  color: PremiumDesignTokens.neutralGray600,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildHotelFeature(
                        icon: Icons.wifi,
                        label: 'Free WiFi',
                      ),
                    ),
                    Expanded(
                      child: _buildHotelFeature(
                        icon: Icons.pool,
                        label: 'Pool',
                      ),
                    ),
                    Expanded(
                      child: _buildHotelFeature(
                        icon: Icons.spa,
                        label: 'Spa',
                      ),
                    ),
                    Expanded(
                      child: _buildHotelFeature(
                        icon: Icons.restaurant,
                        label: 'Restaurant',
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivitiesSection() {
    final activities = [
      {'name': 'City Walking Tour', 'duration': '3 hours', 'day': 'Day 1'},
      {'name': 'Museum & Gallery Visit', 'duration': '4 hours', 'day': 'Day 2'},
      {
        'name': 'Cooking Class Experience',
        'duration': '2.5 hours',
        'day': 'Day 2'
      },
      {
        'name': 'Mountain Hiking Adventure',
        'duration': '6 hours',
        'day': 'Day 3'
      },
      {'name': 'Sunset Dinner Cruise', 'duration': '3 hours', 'day': 'Day 4'},
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.local_activity,
                color: const Color(0xFFF59E0B),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Included Activities',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...activities.map((activity) => Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFFF59E0B).withAlpha(26),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFFF59E0B).withAlpha(51),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: const Color(0xFFF59E0B),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.local_activity,
                        color: PremiumDesignTokens.neutralWhite,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            activity['name']!,
                            style: PremiumDesignTokens.bodyLarge.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Text(
                                activity['duration']!,
                                style: PremiumDesignTokens.bodySmall.copyWith(
                                  color: PremiumDesignTokens.neutralGray600,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 2),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFF59E0B),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  activity['day']!,
                                  style:
                                      PremiumDesignTokens.labelSmall.copyWith(
                                    color: PremiumDesignTokens.neutralWhite,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildDiningSection() {
    final restaurants = [
      {
        'name': 'Welcome Dinner at La Terrasse',
        'type': 'French Cuisine',
        'day': 'Day 1'
      },
      {'name': 'Local Market Food Tour', 'type': 'Street Food', 'day': 'Day 2'},
      {
        'name': 'Mountain View Restaurant',
        'type': 'Local Cuisine',
        'day': 'Day 3'
      },
      {'name': 'Sunset Cruise Dinner', 'type': 'Seafood', 'day': 'Day 4'},
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.restaurant,
                color: const Color(0xFFEF4444),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Dining Experiences',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...restaurants.map((restaurant) => Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFFEF4444).withAlpha(26),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFFEF4444).withAlpha(51),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: const Color(0xFFEF4444),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.restaurant,
                        color: PremiumDesignTokens.neutralWhite,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            restaurant['name']!,
                            style: PremiumDesignTokens.bodyLarge.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Text(
                                restaurant['type']!,
                                style: PremiumDesignTokens.bodySmall.copyWith(
                                  color: PremiumDesignTokens.neutralGray600,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 2),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFEF4444),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  restaurant['day']!,
                                  style:
                                      PremiumDesignTokens.labelSmall.copyWith(
                                    color: PremiumDesignTokens.neutralWhite,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildGuideSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.person,
                color: const Color(0xFF8B5CF6),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Your Travel Guide',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF8B5CF6).withAlpha(26),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF8B5CF6).withAlpha(51),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: const Color(0xFF8B5CF6),
                  child: Text(
                    'SM',
                    style: PremiumDesignTokens.headlineSmall.copyWith(
                      color: PremiumDesignTokens.neutralWhite,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Sarah Martinez',
                        style: PremiumDesignTokens.bodyLarge.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Certified Local Guide • 8 years experience',
                        style: PremiumDesignTokens.bodyMedium.copyWith(
                          color: PremiumDesignTokens.neutralGray600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.star,
                            color: const Color(0xFFF59E0B),
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '4.9 (247 reviews)',
                            style: PremiumDesignTokens.bodySmall.copyWith(
                              color: PremiumDesignTokens.neutralGray600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    _contactGuide();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: const Color(0xFF8B5CF6),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.message,
                      color: PremiumDesignTokens.neutralWhite,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPackageComponent({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: PremiumDesignTokens.labelMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          subtitle,
          style: PremiumDesignTokens.labelSmall.copyWith(
            color: PremiumDesignTokens.neutralGray600,
          ),
        ),
      ],
    );
  }

  Widget _buildTimelineDay({
    required String day,
    required String date,
    required String title,
    required List<String> activities,
    required bool isLast,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                color: const Color(0xFF06B6D4),
                shape: BoxShape.circle,
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 60,
                color: const Color(0xFF06B6D4).withAlpha(77),
              ),
          ],
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Container(
            margin: EdgeInsets.only(bottom: isLast ? 0 : 20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF06B6D4).withAlpha(26),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF06B6D4).withAlpha(51),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      day,
                      style: PremiumDesignTokens.labelLarge.copyWith(
                        color: const Color(0xFF06B6D4),
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      date,
                      style: PremiumDesignTokens.labelMedium.copyWith(
                        color: PremiumDesignTokens.neutralGray600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: PremiumDesignTokens.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                ...activities.map((activity) => Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: const Color(0xFF10B981),
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              activity,
                              style: PremiumDesignTokens.bodySmall,
                            ),
                          ),
                        ],
                      ),
                    )),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFlightCard({
    required String title,
    required String route,
    required String date,
    required String time,
    required String flight,
    required String seat,
    required bool isOutbound,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF06B6D4).withAlpha(26),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF06B6D4).withAlpha(51),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isOutbound ? Icons.flight_takeoff : Icons.flight_land,
                color: const Color(0xFF06B6D4),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: PremiumDesignTokens.labelLarge.copyWith(
                  color: const Color(0xFF06B6D4),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      route,
                      style: PremiumDesignTokens.bodyLarge.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '$date • $time',
                      style: PremiumDesignTokens.bodyMedium.copyWith(
                        color: PremiumDesignTokens.neutralGray600,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    flight,
                    style: PremiumDesignTokens.labelMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Seat $seat',
                    style: PremiumDesignTokens.bodySmall.copyWith(
                      color: PremiumDesignTokens.neutralGray600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHotelFeature({
    required IconData icon,
    required String label,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: const Color(0xFF10B981),
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: PremiumDesignTokens.labelSmall.copyWith(
            color: PremiumDesignTokens.neutralGray600,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  void _contactGuide() {
    // TODO: Implement guide contact
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Opening chat with guide...'),
        backgroundColor: const Color(0xFF8B5CF6),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
