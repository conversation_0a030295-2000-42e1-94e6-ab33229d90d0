// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/visa/self_service_models.dart';
import 'package:culture_connect/services/travel/visa/self_service_visa_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// Screen for checking visa eligibility
class EligibilityCheckerScreen extends ConsumerStatefulWidget {
  const EligibilityCheckerScreen({
    super.key,
    this.passportCountry,
    this.destinationCountry,
    this.visaType,
  });

  final String? passportCountry;
  final String? destinationCountry;
  final String? visaType;

  @override
  ConsumerState<EligibilityCheckerScreen> createState() =>
      _EligibilityCheckerScreenState();
}

class _EligibilityCheckerScreenState
    extends ConsumerState<EligibilityCheckerScreen> {
  EligibilityCheckResult? _result;
  bool _isLoading = false;
  String? _errorMessage;

  // Form fields
  String? _selectedPassportCountry;
  String? _selectedDestinationCountry;
  String? _selectedVisaType;
  final Map<String, dynamic> _userProfile = {};

  @override
  void initState() {
    super.initState();
    _selectedPassportCountry = widget.passportCountry;
    _selectedDestinationCountry = widget.destinationCountry;
    _selectedVisaType = widget.visaType;

    // Debug logging
    debugPrint('EligibilityCheckerScreen initialized with:');
    debugPrint('  Passport Country: $_selectedPassportCountry');
    debugPrint('  Destination Country: $_selectedDestinationCountry');
    debugPrint('  Visa Type: $_selectedVisaType');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        title: Text(
          'Eligibility Checker',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: AppTheme.fontWeightBold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeroSection(theme),
            const SizedBox(height: AppTheme.spacingLg),
            _buildForm(theme),
            const SizedBox(height: AppTheme.spacingLg),
            if (_result != null) ...[
              _buildResult(theme),
              const SizedBox(height: AppTheme.spacingLg),
            ],
            if (_errorMessage != null) ...[
              ErrorView(
                error: _errorMessage!,
                onRetry: _checkEligibility,
              ),
              const SizedBox(height: AppTheme.spacingLg),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeroSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.successColor.withValues(alpha: 0.1),
            AppTheme.successColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        border: Border.all(
          color: AppTheme.successColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMd),
            decoration: BoxDecoration(
              color: AppTheme.successColor,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
            ),
            child: const Icon(
              Icons.check_circle,
              color: AppTheme.white,
              size: 32,
            ),
          ),
          const SizedBox(width: AppTheme.spacingMd),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Check Your Eligibility',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: AppTheme.fontWeightBold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                Text(
                  'Find out if you meet the requirements for your visa application',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildForm(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Application Details',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: AppTheme.fontWeightBold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          _buildCountrySelector(
            'Passport Country',
            _selectedPassportCountry,
            (value) => setState(() => _selectedPassportCountry = value),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          _buildCountrySelector(
            'Destination Country',
            _selectedDestinationCountry,
            (value) => setState(() => _selectedDestinationCountry = value),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          _buildVisaTypeSelector(),
          const SizedBox(height: AppTheme.spacingLg),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _canCheckEligibility() ? _checkEligibility : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.successColor,
                foregroundColor: AppTheme.white,
                padding:
                    const EdgeInsets.symmetric(vertical: AppTheme.spacingMd),
                shape: RoundedRectangleBorder(
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusLarge),
                ),
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        color: AppTheme.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Text(
                      'Check Eligibility',
                      style: TextStyle(
                        fontWeight: AppTheme.fontWeightSemibold,
                        fontSize: AppTheme.fontSizeMd,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCountrySelector(
      String label, String? value, ValueChanged<String?> onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: AppTheme.fontWeightSemibold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: AppTheme.spacingSm),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingMd),
          decoration: BoxDecoration(
            border: Border.all(color: AppTheme.borderColor),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _getSafeCountryNameFromCode(value),
              hint: Text('Select $label'),
              isExpanded: true,
              items: _getCountryOptions().map((country) {
                return DropdownMenuItem(
                  value: country,
                  child: Text(country),
                );
              }).toList(),
              onChanged: (selectedCountryName) {
                final countryCode =
                    _getCountryCodeFromName(selectedCountryName);
                onChanged(countryCode);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildVisaTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Visa Type',
          style: TextStyle(
            fontWeight: AppTheme.fontWeightSemibold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: AppTheme.spacingSm),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingMd),
          decoration: BoxDecoration(
            border: Border.all(color: AppTheme.borderColor),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _getSafeVisaTypeDisplayName(_selectedVisaType),
              hint: const Text('Select Visa Type'),
              isExpanded: true,
              items: _getVisaTypeOptions().map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type),
                );
              }).toList(),
              onChanged: (selectedDisplayName) {
                final visaTypeCode =
                    _getVisaTypeCodeFromDisplayName(selectedDisplayName);
                setState(() => _selectedVisaType = visaTypeCode);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildResult(ThemeData theme) {
    final result = _result!;
    final isEligible = result.isEligible;
    final color = isEligible ? AppTheme.successColor : AppTheme.warningColor;

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingSm),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Icon(
                  isEligible ? Icons.check_circle : Icons.warning,
                  color: AppTheme.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMd),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      result.resultMessage,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: AppTheme.fontWeightBold,
                        color: color,
                      ),
                    ),
                    Text(
                      'Confidence: ${result.confidenceScore}%',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Text(
            result.explanation,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          if (result.nextSteps.isNotEmpty) ...[
            const SizedBox(height: AppTheme.spacingMd),
            Text(
              'Next Steps:',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: AppTheme.fontWeightBold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSm),
            ...result.nextSteps.map((step) => Padding(
                  padding: const EdgeInsets.only(bottom: AppTheme.spacingXs),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('• ',
                          style: TextStyle(color: AppTheme.textSecondaryColor)),
                      Expanded(
                        child: Text(
                          step,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ],
      ),
    );
  }

  bool _canCheckEligibility() {
    return _selectedPassportCountry != null &&
        _selectedDestinationCountry != null &&
        _selectedVisaType != null &&
        !_isLoading;
  }

  Future<void> _checkEligibility() async {
    if (!_canCheckEligibility()) return;

    // Validate that we have proper country codes
    if (_selectedPassportCountry == null ||
        _selectedDestinationCountry == null) {
      setState(() {
        _errorMessage = 'Please select both passport and destination countries';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _result = null;
    });

    try {
      debugPrint('EligibilityChecker: Checking eligibility with:');
      debugPrint('  Passport: $_selectedPassportCountry');
      debugPrint('  Destination: $_selectedDestinationCountry');
      debugPrint('  Visa Type: $_selectedVisaType');

      final selfServiceService = ref.read(selfServiceVisaServiceProvider);
      final result = await selfServiceService.checkEligibility(
        passportCountry: _selectedPassportCountry!,
        destinationCountry: _selectedDestinationCountry!,
        visaType: _selectedVisaType!,
        userProfile: _userProfile,
      );

      if (mounted) {
        setState(() {
          _result = result;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('EligibilityChecker: Error checking eligibility: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to check eligibility: $e';
        });
      }
    }
  }

  List<String> _getCountryOptions() {
    return [
      'United States',
      'United Kingdom',
      'Canada',
      'Australia',
      'Germany',
      'France',
      'Japan',
      'South Korea',
      'Nigeria',
      'Kenya',
      'South Africa',
      'China',
      'India',
      'Brazil',
      'Mexico',
      'Italy',
      'Spain',
      'Netherlands',
      'Switzerland',
      'Sweden',
    ];
  }

  /// Convert country code to country name for display (safe version for dropdown)
  String? _getSafeCountryNameFromCode(String? countryCode) {
    if (countryCode == null) return null;

    final countryMap = {
      'US': 'United States',
      'GB': 'United Kingdom',
      'UK': 'United Kingdom',
      'CA': 'Canada',
      'AU': 'Australia',
      'DE': 'Germany',
      'FR': 'France',
      'JP': 'Japan',
      'KR': 'South Korea',
      'NG': 'Nigeria',
      'KE': 'Kenya',
      'ZA': 'South Africa',
      'CN': 'China',
      'IN': 'India',
      'BR': 'Brazil',
      'MX': 'Mexico',
      'IT': 'Italy',
      'ES': 'Spain',
      'NL': 'Netherlands',
      'CH': 'Switzerland',
      'SE': 'Sweden',
    };

    final countryName = countryMap[countryCode.toUpperCase()];
    // Only return the name if it exists in our dropdown options
    final availableOptions = _getCountryOptions();
    return availableOptions.contains(countryName) ? countryName : null;
  }

  /// Convert country name to country code for processing
  String? _getCountryCodeFromName(String? countryName) {
    if (countryName == null) return null;

    final nameToCodeMap = {
      'United States': 'US',
      'United Kingdom': 'GB',
      'Canada': 'CA',
      'Australia': 'AU',
      'Germany': 'DE',
      'France': 'FR',
      'Japan': 'JP',
      'South Korea': 'KR',
      'Nigeria': 'NG',
      'Kenya': 'KE',
      'South Africa': 'ZA',
      'China': 'CN',
      'India': 'IN',
      'Brazil': 'BR',
      'Mexico': 'MX',
      'Italy': 'IT',
      'Spain': 'ES',
      'Netherlands': 'NL',
      'Switzerland': 'CH',
      'Sweden': 'SE',
    };

    return nameToCodeMap[countryName];
  }

  /// Get safe visa type display name for dropdown
  String? _getSafeVisaTypeDisplayName(String? visaTypeCode) {
    if (visaTypeCode == null) return null;

    final visaTypeMap = {
      'visaRequired': 'Tourist Visa',
      'tourist': 'Tourist Visa',
      'business': 'Business Visa',
      'student': 'Student Visa',
      'work': 'Work Visa',
      'transit': 'Transit Visa',
      'eVisa': 'Tourist Visa', // Default e-visa to tourist
      'visaOnArrival': 'Tourist Visa', // Default visa on arrival to tourist
      'noVisaRequired':
          'Tourist Visa', // Default no visa to tourist for form purposes
    };

    final displayName = visaTypeMap[visaTypeCode];
    // Only return the name if it exists in our dropdown options
    final availableOptions = _getVisaTypeOptions();
    return availableOptions.contains(displayName)
        ? displayName
        : 'Tourist Visa';
  }

  /// Convert visa type display name to code for processing
  String? _getVisaTypeCodeFromDisplayName(String? displayName) {
    if (displayName == null) return null;

    final displayToCodeMap = {
      'Tourist Visa': 'tourist',
      'Business Visa': 'business',
      'Student Visa': 'student',
      'Work Visa': 'work',
      'Transit Visa': 'transit',
    };

    return displayToCodeMap[displayName] ?? 'tourist';
  }

  List<String> _getVisaTypeOptions() {
    return [
      'Tourist Visa',
      'Business Visa',
      'Student Visa',
      'Work Visa',
      'Transit Visa',
    ];
  }
}
