import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:culture_connect/models/premium_booking.dart';
import 'package:culture_connect/design_system/premium_design_tokens.dart';

/// Flight-specific booking layout with seat info, boarding passes, check-in status
class FlightBookingLayout extends StatefulWidget {
  final PremiumBooking booking;

  const FlightBookingLayout({
    super.key,
    required this.booking,
  });

  @override
  State<FlightBookingLayout> createState() => _FlightBookingLayoutState();
}

class _FlightBookingLayoutState extends State<FlightBookingLayout>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _startAnimations() {
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildFlightDetailsSection(),
            _buildSeatInfoSection(),
            _buildBoardingPassSection(),
            _buildBaggageInfoSection(),
            _buildCheckInStatusSection(),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildFlightDetailsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.flight_takeoff,
                color: const Color(0xFF06B6D4),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Flight Details',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildFlightRoute(),
          const SizedBox(height: 16),
          _buildFlightInfo(),
        ],
      ),
    );
  }

  Widget _buildFlightRoute() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF06B6D4).withAlpha(26),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF06B6D4).withAlpha(51),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'JFK',
                  style: PremiumDesignTokens.headlineMedium.copyWith(
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF06B6D4),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'New York',
                  style: PremiumDesignTokens.bodyMedium.copyWith(
                    color: PremiumDesignTokens.neutralGray600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '14:30',
                  style: PremiumDesignTokens.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          Column(
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: const Color(0xFF06B6D4),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '8h 45m',
                  style: PremiumDesignTokens.labelSmall.copyWith(
                    color: PremiumDesignTokens.neutralWhite,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: const Color(0xFF06B6D4),
                      shape: BoxShape.circle,
                    ),
                  ),
                  Container(
                    width: 40,
                    height: 2,
                    color: const Color(0xFF06B6D4),
                  ),
                  Icon(
                    Icons.flight,
                    color: const Color(0xFF06B6D4),
                    size: 20,
                  ),
                  Container(
                    width: 40,
                    height: 2,
                    color: const Color(0xFF06B6D4),
                  ),
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: const Color(0xFF06B6D4),
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
            ],
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'LHR',
                  style: PremiumDesignTokens.headlineMedium.copyWith(
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF06B6D4),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'London',
                  style: PremiumDesignTokens.bodyMedium.copyWith(
                    color: PremiumDesignTokens.neutralGray600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '23:15',
                  style: PremiumDesignTokens.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFlightInfo() {
    return Row(
      children: [
        Expanded(
          child: _buildInfoCard(
            icon: Icons.confirmation_number,
            title: 'Flight',
            value: 'BA 117',
            color: const Color(0xFF06B6D4),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildInfoCard(
            icon: Icons.airline_seat_recline_normal,
            title: 'Class',
            value: 'Business',
            color: const Color(0xFFF59E0B),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildInfoCard(
            icon: Icons.departure_board,
            title: 'Gate',
            value: 'A12',
            color: const Color(0xFF10B981),
          ),
        ),
      ],
    );
  }

  Widget _buildSeatInfoSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.airline_seat_recline_normal,
                color: const Color(0xFFF59E0B),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Seat Information',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFF59E0B).withAlpha(26),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFFF59E0B).withAlpha(51),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: const Color(0xFFF59E0B),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Text(
                      '12A',
                      style: PremiumDesignTokens.headlineSmall.copyWith(
                        color: PremiumDesignTokens.neutralWhite,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Window Seat',
                        style: PremiumDesignTokens.bodyLarge.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Business Class • Extra Legroom',
                        style: PremiumDesignTokens.bodyMedium.copyWith(
                          color: PremiumDesignTokens.neutralGray600,
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    _showSeatChangeDialog();
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF59E0B),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'Change',
                      style: PremiumDesignTokens.labelMedium.copyWith(
                        color: PremiumDesignTokens.neutralWhite,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBoardingPassSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.qr_code,
                color: const Color(0xFF10B981),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Boarding Pass',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              _showBoardingPass();
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xFF10B981),
                    const Color(0xFF06B6D4),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.qr_code_2,
                    color: PremiumDesignTokens.neutralWhite,
                    size: 48,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Tap to view boarding pass',
                    style: PremiumDesignTokens.bodyLarge.copyWith(
                      color: PremiumDesignTokens.neutralWhite,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Available 24 hours before departure',
                    style: PremiumDesignTokens.bodySmall.copyWith(
                      color: PremiumDesignTokens.neutralWhite.withAlpha(204),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBaggageInfoSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.luggage,
                color: const Color(0xFF8B5CF6),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Baggage Information',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildBaggageCard(
                  icon: Icons.work_outline,
                  title: 'Carry-on',
                  weight: '10 kg',
                  included: true,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildBaggageCard(
                  icon: Icons.luggage,
                  title: 'Checked',
                  weight: '23 kg',
                  included: true,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCheckInStatusSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: PremiumDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: PremiumDesignTokens.neutralGray900.withAlpha(13),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.check_circle_outline,
                color: const Color(0xFF10B981),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Check-in Status',
                style: PremiumDesignTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF10B981).withAlpha(26),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF10B981).withAlpha(51),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: const Color(0xFF10B981),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Checked In',
                        style: PremiumDesignTokens.bodyLarge.copyWith(
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF10B981),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Boarding starts at 13:45',
                        style: PremiumDesignTokens.bodyMedium.copyWith(
                          color: PremiumDesignTokens.neutralGray600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withAlpha(51),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: PremiumDesignTokens.labelSmall.copyWith(
              color: PremiumDesignTokens.neutralGray600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: PremiumDesignTokens.labelMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBaggageCard({
    required IconData icon,
    required String title,
    required String weight,
    required bool included,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF8B5CF6).withAlpha(26),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFF8B5CF6).withAlpha(51),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: const Color(0xFF8B5CF6),
            size: 20,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: PremiumDesignTokens.labelSmall.copyWith(
              color: PremiumDesignTokens.neutralGray600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            weight,
            style: PremiumDesignTokens.labelMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color:
                  included ? const Color(0xFF10B981) : const Color(0xFFEF4444),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              included ? 'Included' : 'Extra',
              style: PremiumDesignTokens.labelSmall.copyWith(
                color: PremiumDesignTokens.neutralWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showSeatChangeDialog() {
    // TODO: Implement seat change dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Seat change options...'),
        backgroundColor: const Color(0xFFF59E0B),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _showBoardingPass() {
    // TODO: Implement boarding pass display
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Opening boarding pass...'),
        backgroundColor: const Color(0xFF10B981),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
