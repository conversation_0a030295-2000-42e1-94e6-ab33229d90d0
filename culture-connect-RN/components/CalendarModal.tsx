import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ScrollView,
  Pressable,
} from 'react-native';
import { ChevronLeft, ChevronRight, X } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';
import { LinearGradient } from 'expo-linear-gradient';

const { width: screenWidth } = Dimensions.get('window');

interface CalendarModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectDates: (departDate: Date, returnDate?: Date) => void;
  tripType: 'round-trip' | 'one-way';
  initialDepartDate?: Date;
  initialReturnDate?: Date;
  maxDays?: number;
  minDays?: number;
  onDurationError?: (message: string) => void;
}

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  isInRange: boolean;
  isStartDate: boolean;
  isEndDate: boolean;
  isPast: boolean;
}

const MONTHS = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

const WEEKDAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

export const CalendarModal: React.FC<CalendarModalProps> = ({
  visible,
  onClose,
  onSelectDates,
  tripType,
  initialDepartDate,
  initialReturnDate,
  maxDays,
  minDays,
  onDurationError,
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDepartDate, setSelectedDepartDate] = useState<Date | null>(initialDepartDate || null);
  const [selectedReturnDate, setSelectedReturnDate] = useState<Date | null>(initialReturnDate || null);
  const [selectingReturn, setSelectingReturn] = useState(false);

  useEffect(() => {
    if (visible) {
      setSelectedDepartDate(initialDepartDate || null);
      setSelectedReturnDate(initialReturnDate || null);
      setSelectingReturn(false);
    }
  }, [visible, initialDepartDate, initialReturnDate]);

  const getDaysInMonth = (date: Date): CalendarDay[] => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const days: CalendarDay[] = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    for (let i = 0; i < 42; i++) {
      const currentDay = new Date(startDate);
      currentDay.setDate(startDate.getDate() + i);
      
      const isCurrentMonth = currentDay.getMonth() === month;
      const isToday = currentDay.getTime() === today.getTime();
      const isPast = currentDay < today;
      
      let isSelected = false;
      let isInRange = false;
      let isStartDate = false;
      let isEndDate = false;
      
      if (selectedDepartDate) {
        const departTime = selectedDepartDate.getTime();
        const currentTime = currentDay.getTime();
        
        if (currentTime === departTime) {
          isSelected = true;
          isStartDate = true;
        }
        
        if (tripType === 'round-trip' && selectedReturnDate) {
          const returnTime = selectedReturnDate.getTime();
          
          if (currentTime === returnTime) {
            isSelected = true;
            isEndDate = true;
          }
          
          if (currentTime > departTime && currentTime < returnTime) {
            isInRange = true;
          }
        }
      }
      
      days.push({
        date: currentDay,
        isCurrentMonth,
        isToday,
        isSelected,
        isInRange,
        isStartDate,
        isEndDate,
        isPast,
      });
    }
    
    return days;
  };

  const calculateDaysDifference = (startDate: Date, endDate: Date): number => {
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const validateDuration = (startDate: Date, endDate: Date): { isValid: boolean; message?: string } => {
    const days = calculateDaysDifference(startDate, endDate);
    
    if (minDays && days < minDays) {
      return {
        isValid: false,
        message: `Minimum rental duration is ${minDays} day${minDays > 1 ? 's' : ''}. Please select a longer period.`
      };
    }
    
    if (maxDays && days > maxDays) {
      return {
        isValid: false,
        message: `Maximum rental duration is ${maxDays} day${maxDays > 1 ? 's' : ''}. Please select a shorter period.`
      };
    }
    
    return { isValid: true };
  };

  const handleDatePress = (day: CalendarDay) => {
    if (day.isPast || !day.isCurrentMonth) return;
    
    if (tripType === 'one-way') {
      setSelectedDepartDate(day.date);
      setSelectedReturnDate(null);
    } else {
      if (!selectedDepartDate || selectingReturn) {
        if (!selectedDepartDate) {
          setSelectedDepartDate(day.date);
          setSelectingReturn(true);
        } else {
          if (day.date > selectedDepartDate) {
            // Validate duration before setting return date
            const validation = validateDuration(selectedDepartDate, day.date);
            if (!validation.isValid) {
              onDurationError?.(validation.message!);
              return;
            }
            setSelectedReturnDate(day.date);
            setSelectingReturn(false);
          } else {
            setSelectedDepartDate(day.date);
            setSelectedReturnDate(null);
            setSelectingReturn(true);
          }
        }
      } else {
        if (day.date > selectedDepartDate) {
          // Validate duration before setting return date
          const validation = validateDuration(selectedDepartDate, day.date);
          if (!validation.isValid) {
            onDurationError?.(validation.message!);
            return;
          }
          setSelectedReturnDate(day.date);
          setSelectingReturn(false);
        } else {
          setSelectedDepartDate(day.date);
          setSelectedReturnDate(null);
          setSelectingReturn(true);
        }
      }
    }
  };

  const handleConfirm = () => {
    if (selectedDepartDate) {
      // Final validation before confirming
      if (tripType === 'round-trip' && selectedReturnDate) {
        const validation = validateDuration(selectedDepartDate, selectedReturnDate);
        if (!validation.isValid) {
          onDurationError?.(validation.message!);
          return;
        }
      }
      onSelectDates(selectedDepartDate, selectedReturnDate || undefined);
      onClose();
    }
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const formatDateRange = () => {
    if (!selectedDepartDate) return 'Select dates';
    
    const departStr = selectedDepartDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
    
    if (tripType === 'one-way' || !selectedReturnDate) {
      return departStr;
    }
    
    const returnStr = selectedReturnDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
    
    return `${departStr} - ${returnStr}`;
  };

  const days = getDaysInMonth(currentDate);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <LinearGradient
          colors={['#4C4DDC', '#3F3D9B']}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <X size={20} color={colors.white} />
            </TouchableOpacity>
            <View style={styles.headerTextContainer}>
              <Text style={styles.headerTitle}>Select Dates</Text>
              <Text style={styles.headerSubtitle}>{formatDateRange()}</Text>
            </View>
          </View>
        </LinearGradient>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Trip Type Indicator */}
          <View style={styles.tripTypeIndicator}>
            <View style={styles.tripTypeContainer}>
              <View style={[styles.tripTypeDot, { backgroundColor: '#4C4DDC' }]} />
              <Text style={styles.tripTypeText}>
                {tripType === 'round-trip' ? 'Round Trip' : 'One Way'}
              </Text>
            </View>
            {tripType === 'round-trip' && (
              <Text style={styles.instructionText}>
                {!selectedDepartDate ? 'Select departure date' : 
                 !selectedReturnDate ? 'Select return date' : 
                 `${calculateDaysDifference(selectedDepartDate, selectedReturnDate!)} day${calculateDaysDifference(selectedDepartDate, selectedReturnDate!) > 1 ? 's' : ''} selected`}
              </Text>
            )}
            {maxDays && (
              <Text style={styles.durationLimitText}>
                Maximum: {maxDays} day{maxDays > 1 ? 's' : ''}
              </Text>
            )}
          </View>

          {/* Calendar Navigation */}
          <View style={styles.calendarHeader}>
            <TouchableOpacity style={styles.navButton} onPress={() => navigateMonth('prev')}>
              <ChevronLeft size={20} color="#4C4DDC" />
            </TouchableOpacity>
            <Text style={styles.monthYear}>
              {MONTHS[currentDate.getMonth()]} {currentDate.getFullYear()}
            </Text>
            <TouchableOpacity style={styles.navButton} onPress={() => navigateMonth('next')}>
              <ChevronRight size={20} color="#4C4DDC" />
            </TouchableOpacity>
          </View>

          {/* Weekday Headers */}
          <View style={styles.weekdaysContainer}>
            {WEEKDAYS.map((day) => (
              <View key={day} style={styles.weekdayHeader}>
                <Text style={styles.weekdayText}>{day}</Text>
              </View>
            ))}
          </View>

          {/* Calendar Grid */}
          <View style={styles.calendarGrid}>
            {days.map((day, index) => {
              const isDisabled = day.isPast || !day.isCurrentMonth;
              
              return (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.dayContainer,
                    day.isInRange && styles.dayInRange,
                    day.isSelected && styles.daySelected,
                    day.isStartDate && styles.dayStartDate,
                    day.isEndDate && styles.dayEndDate,
                    isDisabled && styles.dayDisabled,
                  ]}
                  onPress={() => handleDatePress(day)}
                  disabled={isDisabled}
                  activeOpacity={0.7}
                >
                  <Text
                    style={[
                      styles.dayText,
                      !day.isCurrentMonth && styles.dayTextFaded,
                      day.isToday && styles.dayTextToday,
                      day.isSelected && styles.dayTextSelected,
                      day.isPast && styles.dayTextDisabled,
                    ]}
                  >
                    {day.date.getDate()}
                  </Text>
                  {day.isToday && !day.isSelected && (
                    <View style={styles.todayIndicator} />
                  )}
                </TouchableOpacity>
              );
            })}
          </View>

          {/* Date Selection Summary */}
          {(selectedDepartDate || selectedReturnDate) && (
            <View style={styles.selectionSummary}>
              <View style={styles.summaryCard}>
                <View style={styles.summaryRow}>
                  <View style={styles.summaryItem}>
                    <Text style={styles.summaryLabel}>Departure</Text>
                    <Text style={styles.summaryDate}>
                      {selectedDepartDate ? selectedDepartDate.toLocaleDateString('en-US', {
                        weekday: 'short',
                        month: 'short',
                        day: 'numeric'
                      }) : 'Not selected'}
                    </Text>
                  </View>
                  {tripType === 'round-trip' && (
                    <View style={styles.summaryItem}>
                      <Text style={styles.summaryLabel}>Return</Text>
                      <Text style={styles.summaryDate}>
                        {selectedReturnDate ? selectedReturnDate.toLocaleDateString('en-US', {
                          weekday: 'short',
                          month: 'short',
                          day: 'numeric'
                        }) : 'Not selected'}
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            </View>
          )}
        </ScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          <TouchableOpacity style={styles.clearButton} onPress={() => {
            setSelectedDepartDate(null);
            setSelectedReturnDate(null);
            setSelectingReturn(false);
          }}>
            <Text style={styles.clearButtonText}>Clear</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[
              styles.confirmButton,
              !selectedDepartDate && styles.confirmButtonDisabled
            ]} 
            onPress={handleConfirm}
            disabled={!selectedDepartDate}
          >
            <LinearGradient
              colors={selectedDepartDate ? ['#4C4DDC', '#3F3D9B'] : ['#E2E8F0', '#E2E8F0']}
              style={styles.confirmButtonGradient}
            >
              <Text style={[
                styles.confirmButtonText,
                !selectedDepartDate && styles.confirmButtonTextDisabled
              ]}>
                Confirm Dates
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 24,
    paddingHorizontal: spacing.lg,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontFamily: typography.fontFamily,
    fontSize: 24,
    fontWeight: '700' as any,
    color: colors.white,
    marginBottom: 2,
  },
  headerSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    color: 'rgba(255,255,255,0.8)',
    fontWeight: '500' as any,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  tripTypeIndicator: {
    marginTop: 24,
    marginBottom: 20,
  },
  tripTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tripTypeDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  tripTypeText: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    fontWeight: '600' as any,
    color: '#1E293B',
  },
  instructionText: {
    fontFamily: typography.fontFamily,
    fontSize: 14,
    color: '#64748B',
    fontWeight: '500' as any,
  },
  durationLimitText: {
    fontFamily: typography.fontFamily,
    fontSize: 12,
    color: '#EF4444',
    fontWeight: '500' as any,
    marginTop: 4,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingHorizontal: 4,
  },
  navButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  monthYear: {
    fontFamily: typography.fontFamily,
    fontSize: 18,
    fontWeight: '600' as any,
    color: '#1E293B',
  },
  weekdaysContainer: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  weekdayHeader: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  weekdayText: {
    fontFamily: typography.fontFamily,
    fontSize: 12,
    fontWeight: '600' as any,
    color: '#64748B',
    letterSpacing: 0.5,
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 24,
  },
  dayContainer: {
    width: screenWidth / 7 - (spacing.lg * 2) / 7,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
    borderRadius: 22,
    position: 'relative',
  },
  dayInRange: {
    backgroundColor: 'rgba(76, 77, 220, 0.1)',
  },
  daySelected: {
    backgroundColor: '#4C4DDC',
  },
  dayStartDate: {
    backgroundColor: '#4C4DDC',
  },
  dayEndDate: {
    backgroundColor: '#4C4DDC',
  },
  dayDisabled: {
    opacity: 0.3,
  },
  dayText: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    fontWeight: '500' as any,
    color: '#1E293B',
  },
  dayTextFaded: {
    color: '#CBD5E1',
  },
  dayTextToday: {
    color: '#4C4DDC',
    fontWeight: '700' as any,
  },
  dayTextSelected: {
    color: colors.white,
    fontWeight: '600' as any,
  },
  dayTextDisabled: {
    color: '#CBD5E1',
  },
  todayIndicator: {
    position: 'absolute',
    bottom: 6,
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#4C4DDC',
  },
  selectionSummary: {
    marginBottom: 24,
  },
  summaryCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: 'rgba(76, 77, 220, 0.1)',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
    flex: 1,
  },
  summaryLabel: {
    fontFamily: typography.fontFamily,
    fontSize: 12,
    fontWeight: '600' as any,
    color: '#64748B',
    letterSpacing: 0.5,
    marginBottom: 4,
    textTransform: 'uppercase',
  },
  summaryDate: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    fontWeight: '600' as any,
    color: '#1E293B',
  },
  footer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingVertical: 20,
    paddingBottom: 40,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
  },
  clearButton: {
    flex: 1,
    marginRight: 12,
    paddingVertical: 16,
    borderRadius: 12,
    backgroundColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'center',
  },
  clearButtonText: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    fontWeight: '600' as any,
    color: '#64748B',
  },
  confirmButton: {
    flex: 2,
    borderRadius: 12,
    overflow: 'hidden',
  },
  confirmButtonDisabled: {
    opacity: 0.5,
  },
  confirmButtonGradient: {
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  confirmButtonText: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    fontWeight: '600' as any,
    color: colors.white,
  },
  confirmButtonTextDisabled: {
    color: '#94A3B8',
  },
});