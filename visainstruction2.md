# Flutter Mobile Technical Documentation: Visa Assistance Feature

## 1. Introduction

This document provides comprehensive implementation details for the Visa Assistance feature within your existing Flutter-based tourism application. This documentation is specifically focused on the mobile client implementation and contains all necessary details for an AI coding assistant to implement the feature without additional context.

## 2. Project Structure

```
lib/
├── features/
│   └── visa_assistance/
│       ├── data/
│       │   ├── datasources/
│       │   │   ├── remote/
│       │   │   │   ├── visa_api_service.dart
│       │   │   │   └── visa_api_service_impl.dart
│       │   │   └── local/
│       │   │       └── visa_local_data_source.dart
│       │   ├── repositories/
│       │   │   └── visa_repository_impl.dart
│       │   └── models/
│       │       ├── visa_requirements_model.dart
│       │       ├── consultant_model.dart
│       │       └── visa_service_request_model.dart
│       ├── domain/
│       │   ├── entities/
│       │   │   ├── visa_requirements.dart
│       │   │   ├── consultant.dart
│       │   │   └── visa_service_request.dart
│       │   ├── repositories/
│       │   │   └── visa_repository.dart
│       │   └── usecases/
│       │       ├── get_visa_requirements.dart
│       │       ├── get_consultants.dart
│       │       ├── create_escrow_payment.dart
│       │       └── upload_document.dart
│       ├── presentation/
│       │   ├── pages/
│       │   │   ├── visa_requirements_page.dart
│       │   │   ├── visa_self_service_page.dart
│       │   │   ├── consultant_marketplace_page.dart
│       │   │   ├── consultant_detail_page.dart
│       │   │   ├── disclaimer_quiz_page.dart
│       │   │   ├── payment_page.dart
│       │   │   ├── collaboration_hub_page.dart
│       │   │   └── service_completion_page.dart
│       │   ├── widgets/
│       │   │   ├── visa_requirements_summary.dart
│       │   │   ├── consultant_card.dart
│       │   │   ├── document_upload_widget.dart
│       │   │   ├── secure_chat_widget.dart
│       │   │   └── progress_tracker_widget.dart
│       │   └── blocs/
│       │       ├── visa_requirements/
│       │       │   ├── visa_requirements_bloc.dart
│       │       │   ├── visa_requirements_event.dart
│       │       │   └── visa_requirements_state.dart
│       │       ├── consultant_marketplace/
│       │       │   ├── consultant_marketplace_bloc.dart
│       │       │   ├── consultant_marketplace_event.dart
│       │       │   └── consultant_marketplace_state.dart
│       │       └── collaboration_hub/
│       │           ├── collaboration_hub_bloc.dart
│       │           ├── collaboration_hub_event.dart
│       │           └── collaboration_hub_state.dart
│       └── utils/
│           ├── constants.dart
│           ├── routes.dart
│           └── validators.dart
├── core/
│   ├── network/
│   │   ├── network_info.dart
│   │   └── api_client.dart
│   ├── error/
│   │   ├── exceptions.dart
│   │   └── failures.dart
│   └── utils/
│       ├── app_strings.dart
│       └── app_colors.dart
```

## 3. Dependencies

Add these to your `pubspec.yaml`:

```yaml
dependencies:
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.3
  http: ^0.13.6
  dio: ^5.3.3
  cached_network_image: ^3.3.0
  flutter_secure_storage: ^8.0.0
  file_picker: ^6.1.1
  image_picker: ^1.0.4
  pdf: ^3.10.0
  flutter_pdfview: ^1.2.0
  encrypt: ^5.0.1
  uuid: ^4.3.0
  flutter_staggered_grid_view: ^0.7.0
  intl: ^0.18.1
  flutter_svg: ^2.0.7
  flutter_markdown: ^0.6.17
  flutter_linkify: ^6.0.0
  auto_size_text: ^3.0.0
  shimmer: ^3.0.0
  stripe_sdk: ^2.0.0
  flutter_localizations:
    sdk: flutter
  flutter_dotenv: ^5.1.0
  connectivity_plus: ^4.0.2

dev_dependencies:
  bloc_test: ^9.1.0
  mocktail: ^1.0.5
  flutter_test:
    sdk: flutter
```

## 4. Core Configuration

### 4.1 Constants (`lib/core/utils/constants.dart`)

```dart
// API Configuration
const String VISA_API_BASE_URL = 'https://api.yourapp.com/v1/visa';
const String STRIPE_PUBLISHABLE_KEY = 'pk_test_...'; // Load from .env in production

// Feature Flags
const bool ENABLE_VISA_ASSISTANCE = true;
const int MAX_DOCUMENT_SIZE_MB = 10;
const List<String> ALLOWED_DOCUMENT_TYPES = [
  'image/jpeg',
  'image/png',
  'application/pdf'
];

// UI Constants
const double CARD_BORDER_RADIUS = 16.0;
const double CONTENT_PADDING = 16.0;
const Duration ANIMATION_DURATION = Duration(milliseconds: 300);

// Security
const String ENCRYPTION_KEY = 'visa_assistance_encryption_key'; // Should be stored securely
```

### 4.2 App Strings (`lib/core/utils/app_strings.dart`)

```dart
class AppStrings {
  // Visa Requirements
  static const String visaRequirementsTitle = 'Visa Requirements';
  static const String visaRequirementsDescription = 'Check visa requirements for your destination';
  static const String visaRequired = 'Visa required';
  static const String visaNotRequired = 'No visa required';
  static const String visaFreeEntry = 'Visa-free entry for up to %s days';
  static const String visaOnArrival = 'Visa on arrival available';
  static const String eVisaAvailable = 'eVisa available';
  
  // Disclaimer & Education
  static const String disclaimerTitle = 'Important Information';
  static const String disclaimerDescription = 'Before using visa services, you must understand:';
  static const String disclaimerPoint1 = 'Visa approval is always at the discretion of the embassy/consulate';
  static const String disclaimerPoint2 = 'No consultant can guarantee visa approval or influence embassy decisions';
  static const String disclaimerPoint3 = 'This service provides document preparation and application guidance only';
  static const String disclaimerPoint4 = 'Processing times shown are official government timelines, not service guarantees';
  static const String quizQuestion1 = 'Can consultants guarantee visa approval?';
  static const String quizAnswer1Correct = 'No';
  static const String quizAnswer1Incorrect = 'Yes';
  static const String quizQuestion2 = 'Who makes the final decision on visa applications?';
  static const String quizAnswer2Correct = 'The embassy/consulate';
  static const String quizAnswer2Incorrect = 'The consultant';
  static const String quizQuestion3 = 'What does this service actually provide?';
  static const String quizAnswer3Correct = 'Document preparation and application guidance';
  static const String quizAnswer3Incorrect = 'Guaranteed visa approval';
  
  // Consultant Marketplace
  static const String consultantsTitle = 'Certified Visa Consultants';
  static const String filterByExpertise = 'Filter by expertise';
  static const String filterByLanguage = 'Filter by language';
  static const String sortOptions = 'Sort by';
  static const String sortRating = 'Rating (High to Low)';
  static const String sortPrice = 'Price (Low to High)';
  static const String sortExperience = 'Experience (High to Low)';
  
  // Document Handling
  static const String documentUploadTitle = 'Upload Required Documents';
  static const String documentUploadDescription = 'Upload clear copies of your documents. All documents are encrypted and securely stored.';
  static const String passportLabel = 'Passport Bio Page';
  static const String photoLabel = 'Passport-Sized Photo';
  static const String financialProofLabel = 'Proof of Funds';
  static const String accommodationLabel = 'Proof of Accommodation';
  
  // Security Indicators
  static const String secureConnection = 'End-to-end encrypted';
  static const String documentRedaction = 'Sensitive information automatically redacted';
  static const String activityLogged = 'All activity logged for security';
}
```

## 5. API Service Layer

### 5.1 API Client (`lib/core/network/api_client.dart`)

```dart
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class ApiClient {
  final Dio _dio;
  final FlutterSecureStorage _storage;

  ApiClient(this._dio, this._storage) {
    _dio.options.baseUrl = 'https://api.yourapp.com';
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 45);
    
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        final token = await _storage.read(key: 'auth_token');
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        return handler.next(options);
      },
      onError: (error, handler) {
        // Handle specific API errors
        if (error.response?.statusCode == 401) {
          // Handle unauthorized
        }
        return handler.next(error);
      },
    ));
  }

  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    return _dio.get(
      path,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onReceiveProgress: onReceiveProgress,
    );
  }

  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    return _dio.post(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );
  }

  // Add other HTTP methods as needed (put, delete, etc.)
}
```

### 5.2 Visa API Service (`lib/features/visa_assistance/data/datasources/remote/visa_api_service.dart`)

```dart
import 'package:dio/dio.dart';

abstract class VisaApiService {
  Future<Map<String, dynamic>> getVisaRequirements({
    required String destinationCountry,
    required String nationality,
    String travelPurpose = 'tourism',
  });

  Future<List<Map<String, dynamic>>> getConsultants({
    required String destinationCountry,
    required String visaType,
    String? language,
    String sort = 'rating',
  });

  Future<Map<String, dynamic>> createEscrowPayment({
    required String consultantId,
    required String servicePackageId,
    required String userId,
    required String destinationCountry,
    required String visaType,
    bool disclaimerAcknowledged = false,
    bool quizCompleted = false,
  });

  Future<Map<String, dynamic>> uploadDocument({
    required String paymentId,
    required String documentType,
    required String fileBase64,
    List<String> sensitiveFields = const [],
  });

  Future<Map<String, dynamic>> getDocumentStatus(String documentId);
}
```

### 5.3 Visa API Service Implementation (`lib/features/visa_assistance/data/datasources/remote/visa_api_service_impl.dart`)

```dart
import 'package:dio/dio.dart';
import '../visa_api_service.dart';
import '../../../../../core/network/api_client.dart';

class VisaApiServiceImpl implements VisaApiService {
  final ApiClient apiClient;

  VisaApiServiceImpl(this.apiClient);

  @override
  Future<Map<String, dynamic>> getVisaRequirements({
    required String destinationCountry,
    required String nationality,
    String travelPurpose = 'tourism',
  }) async {
    try {
      final response = await apiClient.get(
        '/api/v1/visa/requirements',
        queryParameters: {
          'destination_country': destinationCountry,
          'nationality': nationality,
          'travel_purpose': travelPurpose,
        },
      );
      return response.data;
    } on DioException catch (e) {
      throw Exception('Failed to fetch visa requirements: ${e.message}');
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getConsultants({
    required String destinationCountry,
    required String visaType,
    String? language,
    String sort = 'rating',
  }) async {
    try {
      final params = {
        'destination_country': destinationCountry,
        'visa_type': visaType,
        'sort': sort,
      };
      if (language != null) params['language'] = language;

      final response = await apiClient.get(
        '/api/v1/consultants',
        queryParameters: params,
      );
      return List<Map<String, dynamic>>.from(response.data['consultants']);
    } on DioException catch (e) {
      throw Exception('Failed to fetch consultants: ${e.message}');
    }
  }

  @override
  Future<Map<String, dynamic>> createEscrowPayment({
    required String consultantId,
    required String servicePackageId,
    required String userId,
    required String destinationCountry,
    required String visaType,
    bool disclaimerAcknowledged = false,
    bool quizCompleted = false,
  }) async {
    try {
      final response = await apiClient.post(
        '/api/v1/payments/escrow',
        data: {
          'consultant_id': consultantId,
          'service_package_id': servicePackageId,
          'user_id': userId,
          'destination_country': destinationCountry,
          'visa_type': visaType,
          'disclaimer_acknowledged': disclaimerAcknowledged,
          'quiz_completed': quizCompleted,
        },
      );
      return response.data;
    } on DioException catch (e) {
      throw Exception('Payment creation failed: ${e.message}');
    }
  }

  @override
  Future<Map<String, dynamic>> uploadDocument({
    required String paymentId,
    required String documentType,
    required String fileBase64,
    List<String> sensitiveFields = const [],
  }) async {
    try {
      final response = await apiClient.post(
        '/api/v1/documents',
        data: {
          'payment_id': paymentId,
          'document_type': documentType,
          'file': fileBase64,
          'sensitive_fields': sensitiveFields,
        },
      );
      return response.data;
    } on DioException catch (e) {
      throw Exception('Document upload failed: ${e.message}');
    }
  }

  @override
  Future<Map<String, dynamic>> getDocumentStatus(String documentId) async {
    try {
      final response = await apiClient.get(
        '/api/v1/documents/$documentId/status',
      );
      return response.data;
    } on DioException catch (e) {
      throw Exception('Failed to get document status: ${e.message}');
    }
  }
}
```

## 6. Domain Layer

### 6.1 Entities (`lib/features/visa_assistance/domain/entities/visa_requirements.dart`)

```dart
class VisaRequirements {
  final String country;
  final String nationality;
  final bool visaRequired;
  final String visaType;
  final List<String> applicationMethod;
  final String processingTime;
  final String fee;
  final String validity;
  final String entryType;
  final List<String> requiredDocuments;
  final List<String> specialRequirements;
  final bool visaFree;
  final bool visaOnArrival;
  final bool eVisaAvailable;
  final String? eVisaUrl;
  final Map<String, dynamic> embassyContact;
  final DateTime lastUpdated;
  final String source;

  VisaRequirements({
    required this.country,
    required this.nationality,
    required this.visaRequired,
    required this.visaType,
    required this.applicationMethod,
    required this.processingTime,
    required this.fee,
    required this.validity,
    required this.entryType,
    required this.requiredDocuments,
    required this.specialRequirements,
    required this.visaFree,
    required this.visaOnArrival,
    required this.eVisaAvailable,
    this.eVisaUrl,
    required this.embassyContact,
    required this.lastUpdated,
    required this.source,
  });

  factory VisaRequirements.fromJson(Map<String, dynamic> json) {
    return VisaRequirements(
      country: json['country'],
      nationality: json['nationality'],
      visaRequired: json['requirements']['visa_required'],
      visaType: json['requirements']['visa_type'],
      applicationMethod: List<String>.from(json['requirements']['application_method']),
      processingTime: json['requirements']['processing_time'],
      fee: json['requirements']['fee'],
      validity: json['requirements']['validity'],
      entryType: json['requirements']['entry_type'],
      requiredDocuments: List<String>.from(json['requirements']['required_documents']),
      specialRequirements: List<String>.from(json['requirements']['special_requirements']),
      visaFree: json['requirements']['visa_free'],
      visaOnArrival: json['requirements']['visa_on_arrival'],
      eVisaAvailable: json['requirements']['eVisa_available'],
      eVisaUrl: json['requirements']['eVisa_url'],
      embassyContact: json['requirements']['embassy_contact'],
      lastUpdated: DateTime.parse(json['last_updated']),
      source: json['source'],
    );
  }
}
```

### 6.2 Repository Interface (`lib/features/visa_assistance/domain/repositories/visa_repository.dart`)

```dart
import 'package:dartz/dartz.dart';
import '../entities/visa_requirements.dart';
import '../entities/consultant.dart';
import '../entities/visa_service_request.dart';
import '../../../../core/error/failures.dart';

abstract class VisaRepository {
  Future<Either<Failure, VisaRequirements>> getVisaRequirements({
    required String destinationCountry,
    required String nationality,
    String travelPurpose = 'tourism',
  });

  Future<Either<Failure, List<Consultant>>> getConsultants({
    required String destinationCountry,
    required String visaType,
    String? language,
    String sort = 'rating',
  });

  Future<Either<Failure, VisaServiceRequest>> createEscrowPayment({
    required String consultantId,
    required String servicePackageId,
    required String userId,
    required String destinationCountry,
    required String visaType,
    bool disclaimerAcknowledged = false,
    bool quizCompleted = false,
  });

  Future<Either<Failure, Document>> uploadDocument({
    required String paymentId,
    required String documentType,
    required String fileBase64,
    List<String> sensitiveFields = const [],
  });
}
```

## 7. BLoC Implementation

### 7.1 Visa Requirements BLoC (`lib/features/visa_assistance/presentation/blocs/visa_requirements/visa_requirements_bloc.dart`)

```dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:dartz/dartz.dart';
import '../../../../../core/error/failures.dart';
import '../../domain/entities/visa_requirements.dart';
import '../../domain/usecases/get_visa_requirements.dart';

part 'visa_requirements_event.dart';
part 'visa_requirements_state.dart';

class VisaRequirementsBloc
    extends Bloc<VisaRequirementsEvent, VisaRequirementsState> {
  final GetVisaRequirements getVisaRequirements;

  VisaRequirementsBloc({required this.getVisaRequirements})
      : super(VisaRequirementsInitial()) {
    on<GetVisaRequirementsEvent>(_onGetVisaRequirements);
  }

  Future<void> _onGetVisaRequirements(
    GetVisaRequirementsEvent event,
    Emitter<VisaRequirementsState> emit,
  ) async {
    emit(VisaRequirementsLoading());
    
    final either = await getVisaRequirements(
      destinationCountry: event.destinationCountry,
      nationality: event.nationality,
      travelPurpose: event.travelPurpose,
    );

    emit(
      either.fold(
        (failure) => VisaRequirementsError(_mapFailureToMessage(failure)),
        (visaRequirements) => VisaRequirementsLoaded(visaRequirements),
      ),
    );
  }

  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure:
        return 'Server error. Please try again later.';
      case NetworkFailure:
        return 'Network error. Please check your connection.';
      default:
        return 'Unexpected error. Please try again.';
    }
  }
}
```

### 7.2 Visa Requirements Events (`lib/features/visa_assistance/presentation/blocs/visa_requirements/visa_requirements_event.dart`)

```dart
part of 'visa_requirements_bloc.dart';

abstract class VisaRequirementsEvent extends Equatable {
  const VisaRequirementsEvent();

  @override
  List<Object> get props => [];
}

class GetVisaRequirementsEvent extends VisaRequirementsEvent {
  final String destinationCountry;
  final String nationality;
  final String travelPurpose;

  const GetVisaRequirementsEvent({
    required this.destinationCountry,
    required this.nationality,
    this.travelPurpose = 'tourism',
  });

  @override
  List<Object> get props => [destinationCountry, nationality, travelPurpose];
}
```

### 7.3 Visa Requirements States (`lib/features/visa_assistance/presentation/blocs/visa_requirements/visa_requirements_state.dart`)

```dart
part of 'visa_requirements_bloc.dart';

abstract class VisaRequirementsState extends Equatable {
  const VisaRequirementsState();

  @override
  List<Object> get props => [];
}

class VisaRequirementsInitial extends VisaRequirementsState {}

class VisaRequirementsLoading extends VisaRequirementsState {}

class VisaRequirementsLoaded extends VisaRequirementsState {
  final VisaRequirements visaRequirements;

  const VisaRequirementsLoaded(this.visaRequirements);

  @override
  List<Object> get props => [visaRequirements];
}

class VisaRequirementsError extends VisaRequirementsState {
  final String message;

  const VisaRequirementsError(this.message);

  @override
  List<Object> get props => [message];
}
```

## 8. UI Implementation

### 8.1 Visa Requirements Page (`lib/features/visa_assistance/presentation/pages/visa_requirements_page.dart`)

```dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:auto_size_text/auto_size_text.dart';
import '../../../../core/utils/app_strings.dart';
import '../../../../core/utils/app_colors.dart';
import '../../blocs/visa_requirements/visa_requirements_bloc.dart';
import '../../widgets/visa_requirements_summary.dart';
import '../../widgets/visa_self_service_options.dart';

class VisaRequirementsPage extends StatelessWidget {
  final String destinationCountry;
  final String nationality;

  const VisaRequirementsPage({
    Key? key,
    required this.destinationCountry,
    required this.nationality,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppStrings.visaRequirementsTitle),
        elevation: 0,
      ),
      body: BlocProvider(
        create: (context) => VisaRequirementsBloc(
          getVisaRequirements: context.read<GetVisaRequirements>(),
        )..add(
            GetVisaRequirementsEvent(
              destinationCountry: destinationCountry,
              nationality: nationality,
            ),
          ),
        child: _buildVisaRequirementsBody(),
      ),
    );
  }

  Widget _buildVisaRequirementsBody() {
    return BlocBuilder<VisaRequirementsBloc, VisaRequirementsState>(
      builder: (context, state) {
        if (state is VisaRequirementsInitial || state is VisaRequirementsLoading) {
          return _buildLoadingState();
        } else if (state is VisaRequirementsLoaded) {
          return _buildLoadedState(state.visaRequirements);
        } else if (state is VisaRequirementsError) {
          return _buildErrorState(state.message);
        }
        return _buildLoadingState();
      },
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: CircularProgressIndicator(
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error,
            size: 64,
            color: AppColors.error,
          ),
          SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Text(
              message,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () {
              context.read<VisaRequirementsBloc>().add(
                    GetVisaRequirementsEvent(
                      destinationCountry: destinationCountry,
                      nationality: nationality,
                    ),
                  );
            },
            child: Text(
              'Try Again',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadedState(VisaRequirements requirements) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(requirements),
          SizedBox(height: 24),
          VisaRequirementsSummary(requirements: requirements),
          SizedBox(height: 24),
          VisaSelfServiceOptions(
            requirements: requirements,
            destinationCountry: destinationCountry,
            nationality: nationality,
          ),
          SizedBox(height: 24),
          _buildConsultantOption(requirements),
        ],
      ),
    );
  }

  Widget _buildHeader(VisaRequirements requirements) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AutoSizeText(
          'Visa Requirements for $destinationCountry',
          maxLines: 2,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: 8),
        if (requirements.visaRequired)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.error.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.warning_amber,
                  color: AppColors.error,
                  size: 16,
                ),
                SizedBox(width: 8),
                Text(
                  AppStrings.visaRequired,
                  style: TextStyle(
                    color: AppColors.error,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          )
        else if (requirements.visaFree)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.success.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.check_circle,
                  color: AppColors.success,
                  size: 16,
                ),
                SizedBox(width: 8),
                Text(
                  AppStrings.visaNotRequired,
                  style: TextStyle(
                    color: AppColors.success,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        SizedBox(height: 16),
        Text(
          'Last updated: ${_formatDate(requirements.lastUpdated)}',
          style: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildConsultantOption(VisaRequirements requirements) {
    if (!requirements.visaRequired) return SizedBox.shrink();

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(CARD_BORDER_RADIUS),
        side: BorderSide(color: AppColors.border, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.people_alt,
                  color: AppColors.primary,
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Need help with your visa application?',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            Text(
              'Our certified visa consultants can guide you through the application process, review your documents, and provide personalized advice.',
              style: TextStyle(
                color: AppColors.textSecondary,
                height: 1.5,
              ),
            ),
            SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: EdgeInsets.symmetric(vertical: 16),
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ConsultantMarketplacePage(
                        destinationCountry: destinationCountry,
                        nationality: nationality,
                        visaType: requirements.visaType,
                      ),
                    ),
                  );
                },
                child: Text(
                  'Find a Certified Consultant',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
```

### 8.2 Consultant Marketplace Page (`lib/features/visa_assistance/presentation/pages/consultant_marketplace_page.dart`)

```dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../../../../core/utils/app_strings.dart';
import '../../../../core/utils/app_colors.dart';
import '../../blocs/consultant_marketplace/consultant_marketplace_bloc.dart';
import '../../widgets/consultant_card.dart';

class ConsultantMarketplacePage extends StatelessWidget {
  final String destinationCountry;
  final String nationality;
  final String visaType;

  const ConsultantMarketplacePage({
    Key? key,
    required this.destinationCountry,
    required this.nationality,
    required this.visaType,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppStrings.consultantsTitle),
        elevation: 0,
      ),
      body: BlocProvider(
        create: (context) => ConsultantMarketplaceBloc(
          getConsultants: context.read<GetConsultants>(),
        )..add(
            FetchConsultantsEvent(
              destinationCountry: destinationCountry,
              visaType: visaType,
            ),
          ),
        child: _buildConsultantMarketplaceBody(),
      ),
    );
  }

  Widget _buildConsultantMarketplaceBody() {
    return BlocBuilder<ConsultantMarketplaceBloc, ConsultantMarketplaceState>(
      builder: (context, state) {
        if (state is ConsultantMarketplaceLoading) {
          return _buildLoadingState();
        } else if (state is ConsultantMarketplaceLoaded) {
          return _buildLoadedState(state.consultants);
        } else if (state is ConsultantMarketplaceError) {
          return _buildErrorState(state.message);
        }
        return _buildLoadingState();
      },
    );
  }

  Widget _buildLoadingState() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: StaggeredGrid.count(
          crossAxisCount: 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          children: List.generate(
            6,
            (index) => Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(CARD_BORDER_RADIUS),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error,
            size: 64,
            color: AppColors.error,
          ),
          SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Text(
              message,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () {
              context.read<ConsultantMarketplaceBloc>().add(
                    FetchConsultantsEvent(
                      destinationCountry: destinationCountry,
                      visaType: visaType,
                    ),
                  );
            },
            child: Text(
              'Try Again',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadedState(List<Consultant> consultants) {
    if (consultants.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: 16),
            Text(
              'No consultants available for this visa type',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Try adjusting your filters or check back later',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: AppColors.textTertiary,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<ConsultantMarketplaceBloc>().add(
              FetchConsultantsEvent(
                destinationCountry: destinationCountry,
                visaType: visaType,
              ),
            );
      },
      child: CustomScrollView(
        slivers: [
          SliverPadding(
            padding: const EdgeInsets.all(16.0),
            sliver: SliverToBoxAdapter(
              child: _buildFilterAndSortSection(),
            ),
          ),
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            sliver: SliverStaggeredGrid.countBuilder(
              crossAxisCount: 2,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              itemCount: consultants.length,
              itemBuilder: (context, index) {
                return ConsultantCard(consultant: consultants[index]);
              },
              staggeredTileBuilder: (index) => StaggeredTile.fit(1),
            ),
          ),
          SliverPadding(
            padding: const EdgeInsets.all(16.0),
            sliver: SliverToBoxAdapter(
              child: Text(
                'All consultants are verified through our triple-vetting process including ID verification, professional certification checks, and manual background screening.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: AppColors.textTertiary,
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterAndSortSection() {
    return Row(
      children: [
        Expanded(
          child: _buildFilterButton(
            icon: Icons.filter_list,
            label: AppStrings.filterByExpertise,
            onPressed: () => _showExpertiseFilterDialog(),
          ),
        ),
        SizedBox(width: 8),
        Expanded(
          child: _buildFilterButton(
            icon: Icons.language,
            label: AppStrings.filterByLanguage,
            onPressed: () => _showLanguageFilterDialog(),
          ),
        ),
        SizedBox(width: 8),
        _buildSortButton(),
      ],
    );
  }

  Widget _buildFilterButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton.icon(
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white,
        foregroundColor: AppColors.textPrimary,
        side: BorderSide(color: AppColors.border, width: 1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: EdgeInsets.symmetric(vertical: 12),
      ),
      icon: Icon(icon, size: 18),
      label: Text(
        label,
        style: TextStyle(fontSize: 14),
        overflow: TextOverflow.ellipsis,
      ),
      onPressed: onPressed,
    );
  }

  Widget _buildSortButton() {
    return BlocBuilder<ConsultantMarketplaceBloc, ConsultantMarketplaceState>(
      builder: (context, state) {
        String currentSort = 'rating';
        if (state is ConsultantMarketplaceLoaded) {
          currentSort = state.currentSort;
        }

        return PopupMenuButton<String>(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 4,
          padding: EdgeInsets.zero,
          icon: Icon(
            Icons.sort,
            color: AppColors.primary,
          ),
          onSelected: (value) {
            context.read<ConsultantMarketplaceBloc>().add(
                  SortConsultantsEvent(
                    sortOption: value,
                    destinationCountry: destinationCountry,
                    visaType: visaType,
                  ),
                );
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'rating',
              child: _buildSortOption(
                label: AppStrings.sortRating,
                isSelected: currentSort == 'rating',
              ),
            ),
            PopupMenuItem(
              value: 'price',
              child: _buildSortOption(
                label: AppStrings.sortPrice,
                isSelected: currentSort == 'price',
              ),
            ),
            PopupMenuItem(
              value: 'experience',
              child: _buildSortOption(
                label: AppStrings.sortExperience,
                isSelected: currentSort == 'experience',
              ),
            ),
          ],
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: AppColors.border),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.sort, size: 18, color: AppColors.primary),
                SizedBox(width: 4),
                Text(
                  _getSortLabel(currentSort),
                  style: TextStyle(
                    color: AppColors.textPrimary,
                    fontSize: 14,
                  ),
                ),
                Icon(Icons.arrow_drop_down, size: 18, color: AppColors.textTertiary),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSortOption({required String label, required bool isSelected}) {
    return Row(
      children: [
        if (isSelected)
          Icon(
            Icons.check,
            size: 16,
            color: AppColors.primary,
          )
        else
          SizedBox(width: 24),
        SizedBox(width: 8),
        Text(label),
      ],
    );
  }

  String _getSortLabel(String sortOption) {
    switch (sortOption) {
      case 'rating':
        return 'Rating';
      case 'price':
        return 'Price';
      case 'experience':
        return 'Experience';
      default:
        return 'Rating';
    }
  }

  void _showExpertiseFilterDialog() {
    // Implementation for expertise filter dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Filter by Expertise'),
        content: Text('Expertise filter implementation goes here'),
        actions: [
          TextButton(
            onPressed: Navigator.of(context).pop,
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Apply filter
              Navigator.of(context).pop();
            },
            child: Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _showLanguageFilterDialog() {
    // Implementation for language filter dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Filter by Language'),
        content: Text('Language filter implementation goes here'),
        actions: [
          TextButton(
            onPressed: Navigator.of(context).pop,
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Apply filter
              Navigator.of(context).pop();
            },
            child: Text('Apply'),
          ),
        ],
      ),
    );
  }
}
```

### 8.3 Disclaimer Quiz Page (`lib/features/visa_assistance/presentation/pages/disclaimer_quiz_page.dart`)

```dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:uuid/uuid.dart';
import '../../../../core/utils/app_strings.dart';
import '../../../../core/utils/app_colors.dart';
import '../../domain/entities/visa_service_request.dart';
import '../../blocs/collaboration_hub/collaboration_hub_bloc.dart';
import '../../widgets/quiz_question_widget.dart';

class DisclaimerQuizPage extends StatelessWidget {
  final Consultant consultant;
  final ServicePackage servicePackage;
  final String destinationCountry;
  final String nationality;
  final String visaType;

  const DisclaimerQuizPage({
    Key? key,
    required this.consultant,
    required this.servicePackage,
    required this.destinationCountry,
    required this.nationality,
    required this.visaType,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Important Information'),
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: BlocProvider(
        create: (context) => CollaborationHubBloc(
          createEscrowPayment: context.read<CreateEscrowPayment>(),
        ),
        child: _buildDisclaimerQuizBody(),
      ),
    );
  }

  Widget _buildDisclaimerQuizBody() {
    return BlocConsumer<CollaborationHubBloc, CollaborationHubState>(
      listener: (context, state) {
        if (state is PaymentCreationSuccess) {
          // Navigate to payment page
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PaymentPage(
                paymentId: state.paymentId,
                consultant: consultant,
                servicePackage: servicePackage,
                destinationCountry: destinationCountry,
                nationality: nationality,
                visaType: visaType,
              ),
            ),
          );
        } else if (state is PaymentCreationError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppColors.error,
            ),
          );
        }
      },
      builder: (context, state) {
        return _buildContent();
      },
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDisclaimerHeader(),
          SizedBox(height: 24),
          _buildDisclaimerPoints(),
          SizedBox(height: 24),
          _buildQuizSection(),
        ],
      ),
    );
  }

  Widget _buildDisclaimerHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          Icons.warning_amber,
          size: 48,
          color: AppColors.warning,
        ),
        SizedBox(height: 16),
        Text(
          AppStrings.disclaimerTitle,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: 8),
        Text(
          AppStrings.disclaimerDescription,
          style: TextStyle(
            fontSize: 16,
            color: AppColors.textSecondary,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildDisclaimerPoints() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(CARD_BORDER_RADIUS),
        side: BorderSide(color: AppColors.warning.withOpacity(0.3), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _buildDisclaimerPoint(
              icon: Icons.gpp_bad,
              text: AppStrings.disclaimerPoint1,
            ),
            SizedBox(height: 12),
            _buildDisclaimerPoint(
              icon: Icons.gpp_bad,
              text: AppStrings.disclaimerPoint2,
            ),
            SizedBox(height: 12),
            _buildDisclaimerPoint(
              icon: Icons.gpp_bad,
              text: AppStrings.disclaimerPoint3,
            ),
            SizedBox(height: 12),
            _buildDisclaimerPoint(
              icon: Icons.gpp_bad,
              text: AppStrings.disclaimerPoint4,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDisclaimerPoint({required IconData icon, required String text}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: AppColors.warning,
          size: 20,
        ),
        SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              color: AppColors.textPrimary,
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuizSection() {
    return Expanded(
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(CARD_BORDER_RADIUS),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Please confirm your understanding',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              SizedBox(height: 16),
              Expanded(
                child: _buildQuizQuestions(),
              ),
              SizedBox(height: 16),
              _buildContinueButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuizQuestions() {
    final questions = [
      QuizQuestion(
        id: 'q1',
        question: AppStrings.quizQuestion1,
        options: [
          QuizOption(
            id: 'q1a1',
            text: AppStrings.quizAnswer1Correct,
            isCorrect: true,
          ),
          QuizOption(
            id: 'q1a2',
            text: AppStrings.quizAnswer1Incorrect,
            isCorrect: false,
          ),
        ],
      ),
      QuizQuestion(
        id: 'q2',
        question: AppStrings.quizQuestion2,
        options: [
          QuizOption(
            id: 'q2a1',
            text: AppStrings.quizAnswer2Correct,
            isCorrect: true,
          ),
          QuizOption(
            id: 'q2a2',
            text: AppStrings.quizAnswer2Incorrect,
            isCorrect: false,
          ),
        ],
      ),
      QuizQuestion(
        id: 'q3',
        question: AppStrings.quizQuestion3,
        options: [
          QuizOption(
            id: 'q3a1',
            text: AppStrings.quizAnswer3Correct,
            isCorrect: true,
          ),
          QuizOption(
            id: 'q3a2',
            text: AppStrings.quizAnswer3Incorrect,
            isCorrect: false,
          ),
        ],
      ),
    ];

    return ListView.separated(
      itemCount: questions.length,
      separatorBuilder: (context, index) => SizedBox(height: 24),
      itemBuilder: (context, index) {
        return QuizQuestionWidget(
          question: questions[index],
          onAnswerSelected: (optionId) {
            // Could implement tracking of answers here
          },
        );
      },
    );
  }

  Widget _buildContinueButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: EdgeInsets.symmetric(vertical: 16),
        ),
        onPressed: () {
          // Validate all questions have been answered correctly
          _createEscrowPayment(context);
        },
        child: Text(
          'Continue to Payment',
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  void _createEscrowPayment(BuildContext context) {
    context.read<CollaborationHubBloc>().add(
          CreateEscrowPaymentEvent(
            consultantId: consultant.id,
            servicePackageId: servicePackage.id,
            userId: 'current_user_id', // Get from auth
            destinationCountry: destinationCountry,
            visaType: visaType,
            disclaimerAcknowledged: true,
            quizCompleted: true,
          ),
        );
  }
}
```

### 8.4 Collaboration Hub Page (`lib/features/visa_assistance/presentation/pages/collaboration_hub_page.dart`)

```dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import '../../../../core/utils/app_strings.dart';
import '../../../../core/utils/app_colors.dart';
import '../../../../core/utils/constants.dart';
import '../../domain/entities/document.dart';
import '../../blocs/collaboration_hub/collaboration_hub_bloc.dart';
import '../../widgets/document_upload_widget.dart';
import '../../widgets/secure_chat_widget.dart';
import '../../widgets/progress_tracker_widget.dart';

class CollaborationHubPage extends StatelessWidget {
  final String paymentId;

  const CollaborationHubPage({
    Key? key,
    required this.paymentId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Visa Application Support'),
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(Icons.info_outline),
            onPressed: () => _showSecurityInfo(context),
          ),
        ],
      ),
      body: BlocProvider(
        create: (context) => CollaborationHubBloc(
          getCollaborationHub: context.read<GetCollaborationHub>(),
          uploadDocument: context.read<UploadDocument>(),
        )..add(
            LoadCollaborationHubEvent(paymentId: paymentId),
          ),
        child: _buildCollaborationHubBody(),
      ),
    );
  }

  Widget _buildCollaborationHubBody() {
    return BlocBuilder<CollaborationHubBloc, CollaborationHubState>(
      builder: (context, state) {
        if (state is CollaborationHubLoading) {
          return _buildLoadingState();
        } else if (state is CollaborationHubLoaded) {
          return _buildLoadedState(state.hubData);
        } else if (state is CollaborationHubError) {
          return _buildErrorState(state.message);
        }
        return _buildLoadingState();
      },
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: CircularProgressIndicator(
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error,
            size: 64,
            color: AppColors.error,
          ),
          SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Text(
              message,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () {
              context.read<CollaborationHubBloc>().add(
                    LoadCollaborationHubEvent(paymentId: paymentId),
                  );
            },
            child: Text(
              'Try Again',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadedState(CollaborationHubData hubData) {
    return Column(
      children: [
        ProgressTrackerWidget(
          currentStage: hubData.currentStage,
          stages: [
            'Document Preparation',
            'Document Review',
            'Embassy Submission',
            'Application Complete',
          ],
        ),
        Expanded(
          child: Row(
            children: [
              _buildDocumentPanel(hubData),
              VerticalDivider(width: 1, color: AppColors.border),
              Expanded(
                flex: 2,
                child: SecureChatWidget(
                  paymentId: paymentId,
                  messages: hubData.messages,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDocumentPanel(CollaborationHubData hubData) {
    return SizedBox(
      width: 300,
      child: Card(
        margin: EdgeInsets.zero,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.zero),
        child: Column(
          children: [
            _buildDocumentHeader(),
            Expanded(
              child: _buildDocumentList(hubData.documents),
            ),
            _buildDocumentUploadArea(hubData),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentHeader() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: AppColors.border, width: 1)),
      ),
      child: Row(
        children: [
          Icon(Icons.folder, color: AppColors.primary),
          SizedBox(width: 8),
          Text(
            'Documents',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentList(List<Document> documents) {
    if (documents.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.insert_drive_file,
              size: 48,
              color: AppColors.textTertiary,
            ),
            SizedBox(height: 16),
            Text(
              'No documents uploaded yet',
              style: TextStyle(
                color: AppColors.textTertiary,
              ),
            ),
            Text(
              'Upload your first document to begin',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.zero,
      itemCount: documents.length,
      itemBuilder: (context, index) {
        return _buildDocumentItem(documents[index]);
      },
    );
  }

  Widget _buildDocumentItem(Document document) {
    return ListTile(
      contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: _getDocumentIcon(document.type),
      title: Text(
        _getDocumentLabel(document.type),
        style: TextStyle(
          fontWeight: FontWeight.bold,
        ),
      ),
      subtitle: Text(
        _getDocumentStatusText(document.status),
        style: TextStyle(
          color: _getDocumentStatusColor(document.status),
        ),
      ),
      trailing: document.status == 'approved'
          ? Icon(Icons.check_circle, color: AppColors.success)
          : null,
      onTap: () => _showDocumentPreview(document),
    );
  }

  Icon _getDocumentIcon(String documentType) {
    switch (documentType) {
      case 'passport':
        return Icon(Icons.credit_card, color: AppColors.primary);
      case 'photo':
        return Icon(Icons.camera_alt, color: AppColors.primary);
      case 'financial_proof':
        return Icon(Icons.attach_money, color: AppColors.primary);
      case 'accommodation':
        return Icon(Icons.hotel, color: AppColors.primary);
      default:
        return Icon(Icons.insert_drive_file, color: AppColors.primary);
    }
  }

  String _getDocumentLabel(String documentType) {
    switch (documentType) {
      case 'passport':
        return AppStrings.passportLabel;
      case 'photo':
        return AppStrings.photoLabel;
      case 'financial_proof':
        return AppStrings.financialProofLabel;
      case 'accommodation':
        return AppStrings.accommodationLabel;
      default:
        return documentType.replaceAll('_', ' ').capitalize();
    }
  }

  String _getDocumentStatusText(String status) {
    switch (status) {
      case 'uploaded':
        return 'Uploaded - Awaiting review';
      case 'reviewed':
        return 'Reviewed - Feedback provided';
      case 'approved':
        return 'Approved for submission';
      case 'rejected':
        return 'Needs correction';
      default:
        return status;
    }
  }

  Color _getDocumentStatusColor(String status) {
    switch (status) {
      case 'uploaded':
        return AppColors.warning;
      case 'reviewed':
        return AppColors.textSecondary;
      case 'approved':
        return AppColors.success;
      case 'rejected':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  void _showDocumentPreview(Document document) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DocumentPreviewScreen(document: document),
      ),
    );
  }

  Widget _buildDocumentUploadArea(CollaborationHubData hubData) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(top: BorderSide(color: AppColors.border, width: 1)),
      ),
      child: DocumentUploadWidget(
        onDocumentUploaded: (documentType, file) {
          _handleDocumentUpload(documentType, file, hubData.paymentId);
        },
        availableDocumentTypes: _getAvailableDocumentTypes(hubData),
      ),
    );
  }

  List<String> _getAvailableDocumentTypes(CollaborationHubData hubData) {
    // Determine which documents still need to be uploaded
    final uploadedTypes = hubData.documents.map((d) => d.type).toSet();
    return [
      'passport',
      'photo',
      'financial_proof',
      'accommodation',
    ].where((type) => !uploadedTypes.contains(type)).toList();
  }

  void _handleDocumentUpload(
    String documentType,
    PlatformFile file,
    String paymentId,
  ) async {
    try {
      // Read file content
      final fileBytes = file.bytes;
      if (fileBytes == null) return;

      // Encrypt the file content
      final encryptedFile = _encryptFile(fileBytes);
      
      // Convert to base64 for API
      final base64String = base64Encode(encryptedFile);
      
      // Upload to server
      context.read<CollaborationHubBloc>().add(
            UploadDocumentEvent(
              paymentId: paymentId,
              documentType: documentType,
              fileBase64: base64String,
              sensitiveFields: _getSensitiveFieldsForType(documentType),
            ),
          );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to process document: ${e.toString()}'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Uint8List _encryptFile(Uint8List fileBytes) {
    final key = encrypt.Key.fromUtf8(ENCRYPTION_KEY.substring(0, 32));
    final iv = encrypt.IV.fromSecureRandom(16);
    
    final encrypter = encrypt.Encrypter(encrypt.AES(key));
    final encrypted = encrypter.encryptBytes(fileBytes, iv: iv);
    
    // Prepend IV to encrypted data for decryption later
    final result = Uint8List(iv.bytes.length + encrypted.bytes.length);
    result.setAll(0, iv.bytes);
    result.setAll(iv.bytes.length, encrypted.bytes);
    
    return result;
  }

  List<String> _getSensitiveFieldsForType(String documentType) {
    switch (documentType) {
      case 'passport':
        return ['passport_number', 'issue_date', 'expiry_date'];
      case 'financial_proof':
        return ['account_number', 'balance'];
      default:
        return [];
    }
  }

  void _showSecurityInfo(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Security Information',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            _buildSecurityInfoItem(
              icon: Icons.lock,
              title: AppStrings.secureConnection,
              description: 'All communication is encrypted end-to-end',
            ),
            SizedBox(height: 12),
            _buildSecurityInfoItem(
              icon: Icons.visibility_off,
              title: AppStrings.documentRedaction,
              description: 'Sensitive fields are automatically redacted in processed documents',
            ),
            SizedBox(height: 12),
            _buildSecurityInfoItem(
              icon: Icons.history,
              title: AppStrings.activityLogged,
              description: 'All activity is logged for security and accountability',
            ),
            SizedBox(height: 24),
            Text(
              'Your privacy and security are our top priority. All documents are stored in secure AWS GovCloud infrastructure with strict access controls.',
              style: TextStyle(
                color: AppColors.textSecondary,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityInfoItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: AppColors.primary,
          size: 20,
        ),
        SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              Text(
                description,
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class DocumentPreviewScreen extends StatefulWidget {
  final Document document;

  const DocumentPreviewScreen({Key? key, required this.document}) : super(key: key);

  @override
  _DocumentPreviewScreenState createState() => _DocumentPreviewScreenState();
}

class _DocumentPreviewScreenState extends State<DocumentPreviewScreen> {
  bool _isLoading = true;
  bool _isError = false;
  PdfDocument? _pdfDocument;
  int _currentPage = 1;
  int _ totalPages = 0;

  @override
  void initState() {
    super.initState();
    _loadDocument();
  }

  Future<void> _loadDocument() async {
    try {
      setState(() {
        _isLoading = true;
        _isError = false;
      });

      // In a real app, you'd fetch the document from the API
      // For this example, we'll simulate loading
      await Future.delayed(Duration(seconds: 1));
      
      // Set up dummy data for preview
      setState(() {
        _isLoading = false;
        _totalPages = 3; // Simulated page count
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _isError = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getDocumentLabel(widget.document.type)),
        actions: [
          IconButton(
            icon: Icon(Icons.download),
            onPressed: _downloadDocument,
          ),
        ],
      ),
      body: _buildDocumentContent(),
    );
  }

  Widget _buildDocumentContent() {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: AppColors.primary,
        ),
      );
    }

    if (_isError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error,
              size: 48,
              color: AppColors.error,
            ),
            SizedBox(height: 16),
            Text(
              'Failed to load document',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: 8),
            TextButton(
              onPressed: _loadDocument,
              child: Text('Try Again'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        Expanded(
          child: _buildDocumentViewer(),
        ),
        if (_totalPages > 1)
          _buildPaginationControls(),
      ],
    );
  }

  Widget _buildDocumentViewer() {
    // In a real app, this would show the actual document
    // For this example, we'll show a placeholder
    return Container(
      color: Colors.grey[200],
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.picture_as_pdf,
              size: 64,
              color: AppColors.primary,
            ),
            SizedBox(height: 16),
            Text(
              'Document Preview',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Page $_currentPage of $_totalPages',
              style: TextStyle(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: 16),
            if (widget.document.status == 'rejected')
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Document Needs Correction',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.error,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      widget.document.feedback ?? 'Please review the feedback from your consultant and make the necessary corrections.',
                      style: TextStyle(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaginationControls() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: AppColors.border, width: 1)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          IconButton(
            icon: Icon(Icons.chevron_left),
            onPressed: _currentPage > 1 ? () => _changePage(_currentPage - 1) : null,
            color: _currentPage > 1 ? AppColors.primary : AppColors.textTertiary,
          ),
          Text(
            '$_currentPage / $_totalPages',
            style: TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          IconButton(
            icon: Icon(Icons.chevron_right),
            onPressed: _currentPage < _totalPages ? () => _changePage(_currentPage + 1) : null,
            color: _currentPage < _totalPages ? AppColors.primary : AppColors.textTertiary,
          ),
        ],
      ),
    );
  }

  void _changePage(int page) {
    setState(() {
      _currentPage = page;
    });
  }

  void _downloadDocument() {
    // In a real app, this would trigger document download
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Downloading document...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  String _getDocumentLabel(String documentType) {
    switch (documentType) {
      case 'passport':
        return AppStrings.passportLabel;
      case 'photo':
        return AppStrings.photoLabel;
      case 'financial_proof':
        return AppStrings.financialProofLabel;
      case 'accommodation':
        return AppStrings.accommodationLabel;
      default:
        return documentType.replaceAll('_', ' ').capitalize();
    }
  }
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}
```

## 9. Widget Implementations

### 9.1 Consultant Card Widget (`lib/features/visa_assistance/presentation/widgets/consultant_card.dart`)

```dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import '../../../../core/utils/app_colors.dart';
import '../../../domain/entities/consultant.dart';

class ConsultantCard extends StatelessWidget {
  final Consultant consultant;

  const ConsultantCard({
    Key? key,
    required this.consultant,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(CARD_BORDER_RADIUS),
        side: BorderSide(color: AppColors.border, width: 1),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(CARD_BORDER_RADIUS),
        onTap: () => _navigateToConsultantDetail(context),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProfileSection(),
              SizedBox(height: 12),
              _buildRatingSection(),
              SizedBox(height: 12),
              _buildSpecializations(),
              SizedBox(height: 12),
              _buildServicePackages(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileSection() {
    return Row(
      children: [
        _buildProfileImage(),
        SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                consultant.name,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: AppColors.textPrimary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 4),
              Text(
                '${consultant.experienceYears} years experience',
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProfileImage() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: AppColors.border, width: 1),
      ),
      child: consultant.profilePhoto != null
          ? ClipOval(
              child: CachedNetworkImage(
                imageUrl: consultant.profilePhoto!,
                fit: BoxFit.cover,
                placeholder: (context, url) => _buildProfileImagePlaceholder(),
                errorWidget: (context, url, error) => _buildProfileImagePlaceholder(),
              ),
            )
          : _buildProfileImagePlaceholder(),
    );
  }

  Widget _buildProfileImagePlaceholder() {
    return Container(
      color: Colors.grey[200],
      child: Icon(
        Icons.person,
        color: AppColors.textTertiary,
      ),
    );
  }

  Widget _buildRatingSection() {
    return Row(
      children: [
        Icon(
          Icons.star,
          color: AppColors.warning,
          size: 16,
        ),
        SizedBox(width: 4),
        Text(
          '${consultant.rating}',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(width: 4),
        Text(
          '(${consultant.reviewsCount})',
          style: TextStyle(
            color: AppColors.textSecondary,
          ),
        ),
        Spacer(),
        _buildPriceTag(),
      ],
    );
  }

  Widget _buildPriceTag() {
    // Get the lowest price package
    final minPrice = consultant.servicePackages
        .map((package) => package.price)
        .reduce((a, b) => a < b ? a : b);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        '\$$minPrice+',
        style: TextStyle(
          color: AppColors.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildSpecializations() {
    if (consultant.specializations.isEmpty) return SizedBox.shrink();

    return Wrap(
      spacing: 6,
      runSpacing: 6,
      children: consultant.specializations
          .take(2)
          .map((specialization) => _buildSpecializationTag(specialization))
          .toList(),
    );
  }

  Widget _buildSpecializationTag(String specialization) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        specialization,
        style: TextStyle(
          color: AppColors.primary,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildServicePackages() {
    return SizedBox(
      height: 36,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: consultant.servicePackages.length,
        separatorBuilder: (context, index) => SizedBox(width: 8),
        itemBuilder: (context, index) {
          final package = consultant.servicePackages[index];
          return _buildServicePackageChip(package);
        },
      ),
    );
  }

  Widget _buildServicePackageChip(ServicePackage package) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        package.name,
        style: TextStyle(
          fontSize: 12,
          color: AppColors.textSecondary,
        ),
      ),
    );
  }

  void _navigateToConsultantDetail(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ConsultantDetailPage(consultant: consultant),
      ),
    );
  }
}
```

### 9.2 Quiz Question Widget (`lib/features/visa_assistance/presentation/widgets/quiz_question_widget.dart`)

```dart
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../../../../core/utils/app_colors.dart';

class QuizQuestion {
  final String id;
  final String question;
  final List<QuizOption> options;

  QuizQuestion({
    required this.id,
    required this.question,
    required this.options,
  });
}

class QuizOption {
  final String id;
  final String text;
  final bool isCorrect;

  QuizOption({
    required this.id,
    required this.text,
    required this.isCorrect,
  });
}

class QuizQuestionWidget extends StatefulWidget {
  final QuizQuestion question;
  final Function(String optionId) onAnswerSelected;

  const QuizQuestionWidget({
    Key? key,
    required this.question,
    required this.onAnswerSelected,
  }) : super(key: key);

  @override
  _QuizQuestionWidgetState createState() => _QuizQuestionWidgetState();
}

class _QuizQuestionWidgetState extends State<QuizQuestionWidget> {
  String? _selectedOptionId;
  bool _isAnswered = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.question.question,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: 16),
        ...widget.question.options.map((option) {
          final isSelected = _selectedOptionId == option.id;
          final isCorrect = option.isCorrect;
          
          Color getOptionColor() {
            if (!_isAnswered) {
              return isSelected ? AppColors.primary.withOpacity(0.1) : Colors.transparent;
            }
            
            if (isCorrect) {
              return AppColors.success.withOpacity(0.1);
            }
            
            if (isSelected && !isCorrect) {
              return AppColors.error.withOpacity(0.1);
            }
            
            return Colors.transparent;
          }
          
          IconData getOptionIcon() {
            if (!_isAnswered) return Icons.circle_outlined;
            if (isCorrect) return Icons.check_circle;
            if (isSelected) return Icons.cancel;
            return Icons.circle_outlined;
          }
          
          Color getOptionIconColor() {
            if (!_isAnswered) {
              return isSelected ? AppColors.primary : AppColors.textTertiary;
            }
            
            if (isCorrect) {
              return AppColors.success;
            }
            
            if (isSelected && !isCorrect) {
              return AppColors.error;
            }
            
            return AppColors.textTertiary;
          }

          return GestureDetector(
            onTap: () {
              if (_isAnswered) return;
              
              setState(() {
                _selectedOptionId = option.id;
              });
              
              // Automatically validate after selection
              Future.delayed(Duration(milliseconds: 500), () {
                setState(() {
                  _isAnswered = true;
                });
                
                // Notify parent if answer is correct
                if (option.isCorrect) {
                  widget.onAnswerSelected(option.id);
                }
              });
            },
            child: Container(
              margin: EdgeInsets.only(bottom: 8),
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: getOptionColor(),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _isAnswered 
                      ? (isCorrect ? AppColors.success : AppColors.error) 
                      : AppColors.border,
                  width: _isAnswered ? 1.5 : 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    getOptionIcon(),
                    color: getOptionIconColor(),
                    size: 20,
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      option.text,
                      style: TextStyle(
                        color: AppColors.textPrimary,
                        fontWeight: _isAnswered && isCorrect 
                            ? FontWeight.bold 
                            : FontWeight.normal,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
        if (_isAnswered && !widget.question.options.any((o) => o.id == _selectedOptionId && o.isCorrect))
          Padding(
            padding: const EdgeInsets.only(top: 8, left: 32),
            child: Text(
              'This answer is incorrect. Please review the disclaimer information.',
              style: TextStyle(
                color: AppColors.error,
                fontSize: 14,
              ),
            ),
          ),
      ],
    );
  }
}
```

## 10. State Management Setup

### 10.1 Bloc Providers (`lib/main.dart` - relevant section)

```dart
// Add these to your main.dart where you set up your BlocProviders

MultiBlocProvider(
  providers: [
    // Visa Assistance Feature
    BlocProvider<VisaRequirementsBloc>(
      create: (context) => VisaRequirementsBloc(
        getVisaRequirements: GetVisaRequirements(
          repository: context.read<VisaRepository>(),
        ),
      ),
    ),
    BlocProvider<ConsultantMarketplaceBloc>(
      create: (context) => ConsultantMarketplaceBloc(
        getConsultants: GetConsultants(
          repository: context.read<VisaRepository>(),
        ),
      ),
    ),
    BlocProvider<CollaborationHubBloc>(
      create: (context) => CollaborationHubBloc(
        createEscrowPayment: CreateEscrowPayment(
          repository: context.read<VisaRepository>(),
        ),
        uploadDocument: UploadDocument(
          repository: context.read<VisaRepository>(),
        ),
      ),
    ),
    
    // Other providers...
  ],
  child: MaterialApp(
    // App configuration
  ),
)
```

### 10.2 Dependency Injection (`lib/injection_container.dart` - relevant section)

```dart
// Add these to your dependency injection container

//! Use Cases
// Visa Assistance
sl.registerLazySingleton(() => GetVisaRequirements(repository: sl<VisaRepository>()));
sl.registerLazySingleton(() => GetConsultants(repository: sl<VisaRepository>()));
sl.registerLazySingleton(() => CreateEscrowPayment(repository: sl<VisaRepository>()));
sl.registerLazySingleton(() => UploadDocument(repository: sl<VisaRepository>()));

//! Repository
sl.registerLazySingleton<VisaRepository>(
  () => VisaRepositoryImpl(
    localDataSource: sl<VisaLocalDataSource>(),
    apiService: sl<VisaApiService>(),
  ),
);

//! Data Sources
sl.registerLazySingleton<VisaApiService>(
  () => VisaApiServiceImpl(apiClient: sl<ApiClient>()),
);

sl.registerLazySingleton<VisaLocalDataSource>(
  () => VisaLocalDataSourceImpl(
    sharedPreferences: sl<SharedPreferences>(),
  ),
);
```

## 11. Security Implementation Details

### 11.1 Document Encryption

The document upload process includes client-side encryption before sending to the server:

```dart
// This is implemented in the CollaborationHubPage as shown earlier

Uint8List _encryptFile(Uint8List fileBytes) {
  final key = encrypt.Key.fromUtf8(ENCRYPTION_KEY.substring(0, 32));
  final iv = encrypt.IV.fromSecureRandom(16);
  
  final encrypter = encrypt.Encrypter(encrypt.AES(key));
  final encrypted = encrypter.encryptBytes(fileBytes, iv: iv);
  
  // Prepend IV to encrypted data for decryption later
  final result = Uint8List(iv.bytes.length + encrypted.bytes.length);
  result.setAll(0, iv.bytes);
  result.setAll(iv.bytes.length, encrypted.bytes);
  
  return result;
}
```

### 11.2 Secure Storage

All sensitive data should be stored using `flutter_secure_storage`:

```dart
// Example usage in API client

final storage = FlutterSecureStorage();
final token = await storage.read(key: 'auth_token');
```

### 11.3 Security Headers

Ensure all API requests include security headers:

```dart
// In ApiClient setup

_dio.options.headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  'X-App-Version': '1.0.0',
  'X-Platform': Platform.isAndroid ? 'android' : 'ios',
};
```

## 12. Testing Considerations

### 12.1 Widget Tests

```dart
// Example test for ConsultantCard

void main() {
  late Consultant consultant;

  setUp(() {
    consultant = Consultant(
      id: 'cons_123',
      name: 'Jane Smith',
      profilePhoto: 'https://example.com/jane.jpg',
      bio: 'Experienced visa consultant',
      languages: ['en', 'es'],
      specializations: ['Schengen Visas', 'US Visas'],
      experienceYears: 8,
      rating: 4.8,
      reviewsCount: 142,
      verificationStatus: 'advanced',
      verificationBadges: ['ICCRC Certified', '10+ Years Experience'],
      verificationExpiration: DateTime.now().add(Duration(days: 365)),
      servicePackages: [
        ServicePackage(
          id: 'pkg_basic',
          name: 'Basic Document Review',
          description: 'Document preparation and review',
          price: 49,
          currency: 'USD',
          durationDays: 3,
          features: [
            'Document checklist',
            'Document review (1 round)',
            'Submission guidance'
          ],
          requirements: [],
          terms: '',
        ),
      ],
      availabilityStatus: 'available',
      responseTimeHours: 24.0,
    );
  });

  testWidgets('Displays consultant information correctly', (tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: ConsultantCard(consultant: consultant),
        ),
      ),
    );

    // Verify name is displayed
    expect(find.text('Jane Smith'), findsOneWidget);
    
    // Verify rating is displayed
    expect(find.text('4.8'), findsOneWidget);
    
    // Verify experience is displayed
    expect(find.text('8 years experience'), findsOneWidget);
    
    // Verify specialization tags
    expect(find.text('Schengen Visas'), findsOneWidget);
    expect(find.text('US Visas'), findsOneWidget);
    
    // Verify service package
    expect(find.text('Basic Document Review'), findsOneWidget);
    
    // Verify price tag
    expect(find.text('\$49+'), findsOneWidget);
  });

  testWidgets('Navigates to consultant detail when tapped', (tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: ConsultantCard(consultant: consultant),
        ),
      ),
    );

    await tester.tap(find.byType(ConsultantCard));
    await tester.pumpAndSettle();

    expect(find.byType(ConsultantDetailPage), findsOneWidget);
  });
}
```

### 12.2 BLoC Tests

```dart
// Example test for VisaRequirementsBloc

void main() {
  late MockGetVisaRequirements mockGetVisaRequirements;
  late VisaRequirementsBloc bloc;

  setUp(() {
    mockGetVisaRequirements = MockGetVisaRequirements();
    bloc = VisaRequirementsBloc(
      getVisaRequirements: mockGetVisaRequirements,
    );
  });

  tearDown(() {
    bloc.close();
  });

  test('Initial state is VisaRequirementsInitial', () {
    expect(bloc.state, equals(VisaRequirementsInitial()));
  });

  group('GetVisaRequirementsEvent', () {
    final tVisaRequirements = VisaRequirements(
      country: 'France',
      nationality: 'United States',
      visaRequired: true,
      visaType: 'Schengen Tourist Visa',
      applicationMethod: ['embassy', 'eVisa'],
      processingTime: '5-10 business days',
      fee: '$40',
      validity: '60 days',
      entryType: 'Single',
      requiredDocuments: [
        'Passport valid 6 months',
        'Passport photo',
        'Proof of accommodation',
        'Proof of sufficient funds'
      ],
      specialRequirements: ['Return ticket required'],
      visaFree: false,
      visaOnArrival: false,
      eVisaAvailable: true,
      eVisaUrl: 'https://www.evisathailand.com',
      embassyContact: {
        'website': 'https://thaiembdc.org',
        'address': '1024 Wisconsin Ave NW, Washington, DC 20007',
        'phone': '******-944-3600'
      },
      lastUpdated: DateTime.now(),
      source: 'VisaGuide.World API',
    );

    test('gets data from the use case', () async {
      // arrange
      when(mockGetVisaRequirements(any))
          .thenAnswer((_) async => Right(tVisaRequirements));

      // act
      bloc.add(GetVisaRequirementsEvent(
        destinationCountry: 'France',
        nationality: 'United States',
      ));
      await untilCalled(mockGetVisaRequirements(any));

      // assert
      verify(mockGetVisaRequirements(
        GetVisaRequirementsParams(
          destinationCountry: 'France',
          nationality: 'United States',
          travelPurpose: 'tourism',
        ),
      ));
    });

    test('emits [Loading, Loaded] when data is gotten successfully', () async {
      // arrange
      when(mockGetVisaRequirements(any))
          .thenAnswer((_) async => Right(tVisaRequirements));

      // assert later
      final expected = [
        VisaRequirementsInitial(),
        VisaRequirementsLoading(),
        VisaRequirementsLoaded(tVisaRequirements),
      ];
      expectLater(bloc.stream, emitsInOrder(expected));

      // act
      bloc.add(GetVisaRequirementsEvent(
        destinationCountry: 'France',
        nationality: 'United States',
      ));
    });

    test('emits [Loading, Error] when getting data fails', () async {
      // arrange
      when(mockGetVisaRequirements(any))
          .thenAnswer((_) async => Left(ServerFailure()));

      // assert later
      final expected = [
        VisaRequirementsInitial(),
        VisaRequirementsLoading(),
        VisaRequirementsError('Server error. Please try again later.'),
      ];
      expectLater(bloc.stream, emitsInOrder(expected));

      // act
      bloc.add(GetVisaRequirementsEvent(
        destinationCountry: 'France',
        nationality: 'United States',
      ));
    });
  });
}
```

## 13. Localization Setup

### 13.1 App Strings Localization

Add this to your existing localization setup:

```dart
// lib/core/utils/app_strings.dart

class AppStrings {
  // Existing strings...
  
  // Visa Assistance Feature
  static String get visaRequirementsTitle => Intl.message(
        'Visa Requirements',
        name: 'visaRequirementsTitle',
        desc: 'Title for visa requirements screen',
      );
  
  static String get visaRequirementsDescription => Intl.message(
        'Check visa requirements for your destination',
        name: 'visaRequirementsDescription',
        desc: 'Description for visa requirements screen',
      );
  
  // Add all other strings with Intl.message wrappers
  // ...
}
```

### 13.2 Multi-language Support

Ensure your MaterialApp is configured for localization:

```dart
MaterialApp(
  supportedLocales: [
    const Locale('en', ''),
    const Locale('es', ''),
    const Locale('fr', ''),
    // Add other supported languages
  ],
  localizationsDelegates: [
    AppLocalizations.delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
  ],
  localeResolutionCallback: (locale, supportedLocales) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale?.languageCode) {
        return supportedLocale;
      }
    }
    return supportedLocales.first;
  },
  // Other configuration...
)
```

## 14. Feature Flags

### 14.1 Feature Flag Management

```dart
// lib/core/utils/feature_flags.dart

class FeatureFlags {
  static bool get visaAssistanceEnabled {
    // In production, this would check a remote config
    // For now, we'll use the constant
    return ENABLE_VISA_ASSISTANCE;
  }
}
```

### 14.2 Feature Flag Usage

```dart
// In your main navigation or wherever you need to conditionally show the feature

if (FeatureFlags.visaAssistanceEnabled) {
  // Show visa assistance feature
  Navigator.push(context, MaterialPageRoute(builder: (context) => VisaRequirementsPage()));
} else {
  // Show alternative or error message
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(content: Text('Visa assistance feature is not available')),
  );
}
```

## 15. Error Handling and User Feedback

### 15.1 Error Types and Handling

```dart
// lib/core/error/failures.dart

abstract class Failure {
  final String message;

  const Failure(this.message);
}

class ServerFailure extends Failure {
  const ServerFailure(String message) : super(message);
}

class NetworkFailure extends Failure {
  const NetworkFailure(String message) : super(message);
}

class InvalidInputFailure extends Failure {
  const InvalidInputFailure(String message) : super(message);
}

class PaymentFailure extends Failure {
  const PaymentFailure(String message) : super(message);
}

class DocumentUploadFailure extends Failure {
  const DocumentUploadFailure(String message) : super(message);
}

class FraudDetectionFailure extends Failure {
  const FraudDetectionFailure(String message) : super(message);
}
```

### 15.2 User-Friendly Error Messages

```dart
// In your UI code, convert failures to user-friendly messages

String _mapFailureToMessage(Failure failure) {
  switch (failure.runtimeType) {
    case ServerFailure:
      return 'We\'re having trouble connecting to our servers. Please try again later.';
    case NetworkFailure:
      return 'Please check your internet connection and try again.';
    case InvalidInputFailure:
      return failure.message;
    case PaymentFailure:
      return 'Payment processing failed. Please check your payment details and try again.';
    case DocumentUploadFailure:
      return 'Failed to upload document. Please check the file and try again.';
    case FraudDetectionFailure:
      return 'This action has been blocked for security reasons. Please contact support.';
    default:
      return 'An unexpected error occurred. Please try again.';
  }
}
```

## 16. Integration Points with Existing App

### 16.1 Entry Point from Travel Planning

```dart
// In your existing travel planning screen

// Add this to your destination selection logic
void _onDestinationSelected(String destination) {
  // Check if visa is likely required (simplified for example)
  final requiresVisa = _checkIfVisaRequired(destination);
  
  if (requiresVisa) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VisaRequirementsPage(
          destinationCountry: destination,
          nationality: _getCurrentUserNationality(),
        ),
      ),
    );
  } else {
    // Continue with travel planning
  }
}

bool _checkIfVisaRequired(String destination) {
  // In a real app, this would check against a database or API
  // For this example, we'll use a simple check
  final visaRequiredCountries = ['France', 'Japan', 'Australia'];
  return visaRequiredCountries.contains(destination);
}

String _getCurrentUserNationality() {
  // Get from user profile
  return 'United States'; // Example
}
```

### 16.2 Navigation Routes

```dart
// lib/features/visa_assistance/utils/routes.dart

import 'package:flutter/material.dart';
import '../presentation/pages/visa_requirements_page.dart';
import '../presentation/pages/consultant_marketplace_page.dart';
import '../presentation/pages/consultant_detail_page.dart';
import '../presentation/pages/disclaimer_quiz_page.dart';
import '../presentation/pages/payment_page.dart';
import '../presentation/pages/collaboration_hub_page.dart';
import '../presentation/pages/service_completion_page.dart';

class VisaRoutes {
  static const String visaRequirements = '/visa_requirements';
  static const String consultantMarketplace = '/consultant_marketplace';
  static const String consultantDetail = '/consultant_detail';
  static const String disclaimerQuiz = '/disclaimer_quiz';
  static const String payment = '/payment';
  static const String collaborationHub = '/collaboration_hub';
  static const String serviceCompletion = '/service_completion';

  static Map<String, WidgetBuilder> get routes => {
        visaRequirements: (context) => VisaRequirementsPage(
              destinationCountry: ModalRoute.of(context)!.settings.arguments as String,
              nationality: 'United States', // Get from user profile
            ),
        consultantMarketplace: (context) {
          final args = ModalRoute.of(context)!.settings.arguments as Map;
          return ConsultantMarketplacePage(
            destinationCountry: args['destinationCountry'],
            nationality: args['nationality'],
            visaType: args['visaType'],
          );
        },
        consultantDetail: (context) {
          final args = ModalRoute.of(context)!.settings.arguments as Map;
          return ConsultantDetailPage(
            consultant: args['consultant'],
            destinationCountry: args['destinationCountry'],
            nationality: args['nationality'],
            visaType: args['visaType'],
          );
        },
        disclaimerQuiz: (context) {
          final args = ModalRoute.of(context)!.settings.arguments as Map;
          return DisclaimerQuizPage(
            consultant: args['consultant'],
            servicePackage: args['servicePackage'],
            destinationCountry: args['destinationCountry'],
            nationality: args['nationality'],
            visaType: args['visaType'],
          );
        },
        payment: (context) {
          final args = ModalRoute.of(context)!.settings.arguments as Map;
          return PaymentPage(
            paymentId: args['paymentId'],
            consultant: args['consultant'],
            servicePackage: args['servicePackage'],
            destinationCountry: args['destinationCountry'],
            nationality: args['nationality'],
            visaType: args['visaType'],
          );
        },
        collaborationHub: (context) {
          final args = ModalRoute.of(context)!.settings.arguments as Map;
          return CollaborationHubPage(
            paymentId: args['paymentId'],
          );
        },
        serviceCompletion: (context) {
          final args = ModalRoute.of(context)!.settings.arguments as Map;
          return ServiceCompletionPage(
            paymentId: args['paymentId'],
            consultant: args['consultant'],
          );
        },
      };
}
```

## 17. Compliance and Legal Considerations

### 17.1 Mandatory Disclaimers Implementation

All screens related to visa services must include these disclaimer elements:

```dart
// Add this to the bottom of relevant screens

Container(
  padding: EdgeInsets.all(12),
  decoration: BoxDecoration(
    color: Colors.grey[50],
    border: Border(top: BorderSide(color: AppColors.border, width: 1)),
  ),
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        'Important Information',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: AppColors.warning,
        ),
      ),
      SizedBox(height: 4),
      Text(
        'This is not an official government service. We do not represent any embassy or consulate. Visa approval is always at the discretion of the embassy/consulate. No consultant can guarantee approval or influence embassy decisions.',
        style: TextStyle(
          fontSize: 12,
          color: AppColors.textSecondary,
          height: 1.5,
        ),
      ),
    ],
  ),
)
```

### 17.2 Country-Specific Disclaimers

```dart
// Helper function to get country-specific disclaimer

String getCountrySpecificDisclaimer(String country) {
  switch (country) {
    case 'France':
      return 'Consultants cannot influence the "harmonized criteria" used by Schengen embassies for visa decisions.';
    case 'United States':
      return 'No consultant can affect the "221g administrative processing" decisions made by US consulates.';
    case 'United Kingdom':
      return 'Visa decisions for the UK are made solely by UK Visas and Immigration. Consultants provide document preparation services only.';
    default:
      return 'Visa approval is always at the discretion of the embassy/consulate. No consultant can guarantee visa approval.';
  }
}

// Usage in VisaRequirementsPage
Text(
  getCountrySpecificDisclaimer(destinationCountry),
  style: TextStyle(
    fontSize: 12,
    color: AppColors.textTertiary,
    fontStyle: FontStyle.italic,
  ),
),
```

## 18. Final Implementation Checklist

Before merging this feature, ensure all of these items are completed:

- [ ] All API endpoints verified with backend team
- [ ] Proper error handling for all network requests
- [ ] All disclaimers properly implemented per country
- [ ] Document encryption verified to work correctly
- [ ] Mandatory quiz implemented with proper validation
- [ ] Escrow payment flow tested with Stripe sandbox
- [ ] All user-facing text properly localized
- [ ] Security headers properly implemented
- [ ] All widget tests passing
- [ ] BLoC tests covering all scenarios
- [ ] Performance testing for document upload
- [ ] Legal review of all disclaimers completed
- [ ] Feature flag properly implemented for staged rollout
- [ ] Analytics tracking added for key user flows
- [ ] Proper logging implemented for security monitoring

This comprehensive Flutter technical documentation provides all necessary details for implementing the Visa Assistance feature. An AI coding assistant could use this document to generate the complete Flutter mobile implementation without needing additional context or clarification.