// Flutter imports
import 'package:flutter/foundation.dart';

/// Types of self-service resources
enum SelfServiceResourceType {
  /// Step-by-step application guide
  applicationGuide,
  
  /// Document checklist
  documentChecklist,
  
  /// Eligibility checker tool
  eligibilityChecker,
  
  /// Timeline calculator
  timelineCalculator,
  
  /// FAQ section
  faq,
  
  /// Country-specific information
  countryInfo,
  
  /// Embassy information
  embassyInfo,
  
  /// Processing tips
  processingTips,
  
  /// Common mistakes guide
  commonMistakes,
  
  /// Fee calculator
  feeCalculator,
}

/// Difficulty level for self-service application
enum ApplicationDifficulty {
  easy,
  moderate,
  difficult,
  expert,
}

/// Status of self-service progress
enum SelfServiceProgress {
  notStarted,
  inProgress,
  completed,
  needsReview,
}

/// Represents a self-service visa resource
@immutable
class SelfServiceResource {
  /// Unique identifier for the resource
  final String id;
  
  /// Type of resource
  final SelfServiceResourceType type;
  
  /// Title of the resource
  final String title;
  
  /// Brief description
  final String description;
  
  /// Detailed content
  final String content;
  
  /// Country this resource applies to
  final String? country;
  
  /// Visa type this resource applies to
  final String? visaType;
  
  /// Estimated time to complete
  final Duration? estimatedTime;
  
  /// Difficulty level
  final ApplicationDifficulty? difficulty;
  
  /// Whether this resource is featured
  final bool isFeatured;
  
  /// Tags for categorization
  final List<String> tags;
  
  /// Icon for the resource
  final String iconName;
  
  /// Color theme for the resource
  final String colorTheme;
  
  /// When the resource was created
  final DateTime createdAt;
  
  /// When the resource was last updated
  final DateTime updatedAt;

  const SelfServiceResource({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.content,
    this.country,
    this.visaType,
    this.estimatedTime,
    this.difficulty,
    required this.isFeatured,
    required this.tags,
    required this.iconName,
    required this.colorTheme,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Creates a copy of this resource with the given fields replaced
  SelfServiceResource copyWith({
    String? id,
    SelfServiceResourceType? type,
    String? title,
    String? description,
    String? content,
    String? country,
    String? visaType,
    Duration? estimatedTime,
    ApplicationDifficulty? difficulty,
    bool? isFeatured,
    List<String>? tags,
    String? iconName,
    String? colorTheme,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SelfServiceResource(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      content: content ?? this.content,
      country: country ?? this.country,
      visaType: visaType ?? this.visaType,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      difficulty: difficulty ?? this.difficulty,
      isFeatured: isFeatured ?? this.isFeatured,
      tags: tags ?? this.tags,
      iconName: iconName ?? this.iconName,
      colorTheme: colorTheme ?? this.colorTheme,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// Represents an eligibility check result
@immutable
class EligibilityCheckResult {
  /// Whether the user is eligible
  final bool isEligible;
  
  /// Confidence score (0-100)
  final int confidenceScore;
  
  /// Main result message
  final String resultMessage;
  
  /// Detailed explanation
  final String explanation;
  
  /// List of requirements met
  final List<String> requirementsMet;
  
  /// List of requirements not met
  final List<String> requirementsNotMet;
  
  /// Recommended next steps
  final List<String> nextSteps;
  
  /// Estimated processing time
  final Duration? estimatedProcessingTime;
  
  /// Estimated total cost
  final double? estimatedCost;
  
  /// Currency for the cost
  final String? currency;
  
  /// Whether professional help is recommended
  final bool recommendsProfessionalHelp;
  
  /// Reason for professional help recommendation
  final String? professionalHelpReason;

  const EligibilityCheckResult({
    required this.isEligible,
    required this.confidenceScore,
    required this.resultMessage,
    required this.explanation,
    required this.requirementsMet,
    required this.requirementsNotMet,
    required this.nextSteps,
    this.estimatedProcessingTime,
    this.estimatedCost,
    this.currency,
    required this.recommendsProfessionalHelp,
    this.professionalHelpReason,
  });
}

/// Represents a document checklist item
@immutable
class DocumentChecklistItem {
  /// Unique identifier for the item
  final String id;
  
  /// Name of the document
  final String documentName;
  
  /// Description of the document
  final String description;
  
  /// Whether this document is mandatory
  final bool isMandatory;
  
  /// Whether the user has this document
  final bool isCompleted;
  
  /// Specific requirements for this document
  final List<String> requirements;
  
  /// Tips for obtaining this document
  final List<String> tips;
  
  /// Estimated time to obtain
  final Duration? estimatedTime;
  
  /// Estimated cost to obtain
  final double? estimatedCost;
  
  /// Where to obtain this document
  final String? whereToObtain;
  
  /// Category of the document
  final String category;

  const DocumentChecklistItem({
    required this.id,
    required this.documentName,
    required this.description,
    required this.isMandatory,
    required this.isCompleted,
    required this.requirements,
    required this.tips,
    this.estimatedTime,
    this.estimatedCost,
    this.whereToObtain,
    required this.category,
  });

  /// Creates a copy of this item with the given fields replaced
  DocumentChecklistItem copyWith({
    String? id,
    String? documentName,
    String? description,
    bool? isMandatory,
    bool? isCompleted,
    List<String>? requirements,
    List<String>? tips,
    Duration? estimatedTime,
    double? estimatedCost,
    String? whereToObtain,
    String? category,
  }) {
    return DocumentChecklistItem(
      id: id ?? this.id,
      documentName: documentName ?? this.documentName,
      description: description ?? this.description,
      isMandatory: isMandatory ?? this.isMandatory,
      isCompleted: isCompleted ?? this.isCompleted,
      requirements: requirements ?? this.requirements,
      tips: tips ?? this.tips,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      estimatedCost: estimatedCost ?? this.estimatedCost,
      whereToObtain: whereToObtain ?? this.whereToObtain,
      category: category ?? this.category,
    );
  }
}

/// Represents an application timeline milestone
@immutable
class ApplicationTimelineMilestone {
  /// Unique identifier for the milestone
  final String id;
  
  /// Name of the milestone
  final String name;
  
  /// Description of what happens at this milestone
  final String description;
  
  /// Estimated duration for this milestone
  final Duration estimatedDuration;
  
  /// Whether this milestone is completed
  final bool isCompleted;
  
  /// Order in the timeline
  final int order;
  
  /// Tips for this milestone
  final List<String> tips;
  
  /// Common issues at this milestone
  final List<String> commonIssues;

  const ApplicationTimelineMilestone({
    required this.id,
    required this.name,
    required this.description,
    required this.estimatedDuration,
    required this.isCompleted,
    required this.order,
    required this.tips,
    required this.commonIssues,
  });
}

/// Represents a frequently asked question
@immutable
class VisaFAQ {
  /// Unique identifier for the FAQ
  final String id;
  
  /// The question
  final String question;
  
  /// The answer
  final String answer;
  
  /// Category of the FAQ
  final String category;
  
  /// Tags for searching
  final List<String> tags;
  
  /// Whether this FAQ is featured
  final bool isFeatured;
  
  /// How many times this FAQ was helpful
  final int helpfulCount;
  
  /// Related FAQ IDs
  final List<String> relatedFAQs;

  const VisaFAQ({
    required this.id,
    required this.question,
    required this.answer,
    required this.category,
    required this.tags,
    required this.isFeatured,
    required this.helpfulCount,
    required this.relatedFAQs,
  });
}

/// Represents user progress through self-service resources
@immutable
class SelfServiceUserProgress {
  /// User ID
  final String userId;
  
  /// Country for which progress is tracked
  final String country;
  
  /// Visa type for which progress is tracked
  final String visaType;
  
  /// Overall progress status
  final SelfServiceProgress status;
  
  /// Percentage of completion (0-100)
  final int completionPercentage;
  
  /// Resources completed
  final List<String> completedResources;
  
  /// Current step in the process
  final String? currentStep;
  
  /// Estimated time remaining
  final Duration? estimatedTimeRemaining;
  
  /// Last activity timestamp
  final DateTime lastActivity;
  
  /// Notes from the user
  final String? notes;

  const SelfServiceUserProgress({
    required this.userId,
    required this.country,
    required this.visaType,
    required this.status,
    required this.completionPercentage,
    required this.completedResources,
    this.currentStep,
    this.estimatedTimeRemaining,
    required this.lastActivity,
    this.notes,
  });
}
