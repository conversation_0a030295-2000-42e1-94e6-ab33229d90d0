import React, { useEffect, useState } from "react";
import { StyleSheet, View, Text, ScrollView, RefreshControl, Pressable, FlatList, Dimensions } from "react-native";
import { useRouter } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { Calendar, Clock, CheckCircle, XCircle, Bell, Filter, Search, Plus, MapPin, Star, TrendingUp, Award, Users, Sparkles } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { BookingCard } from "@/components/BookingCard";
import { SearchBar } from "@/components/SearchBar";
import { useBookingStore } from "@/store/bookingStore";
import { useUserStore } from "@/store/userStore";
import { Booking } from "@/types";

const { width: screenWidth } = Dimensions.get('window');

const quickStats = [
  {
    id: "1",
    title: "Total Trips",
    value: "24",
    icon: <MapPin size={18} color={colors.primary} />,
    trend: "+3 this month",
    color: colors.primary
  },
  {
    id: "2",
    title: "Countries",
    value: "12",
    icon: <Award size={18} color={colors.secondary} />,
    trend: "+2 new",
    color: colors.secondary
  },
  {
    id: "3",
    title: "Avg Rating",
    value: "4.8",
    icon: <Star size={18} color={colors.accent} />,
    trend: "Excellent",
    color: colors.accent
  },
  {
    id: "4",
    title: "Saved",
    value: "$2.4k",
    icon: <TrendingUp size={18} color="#10B981" />,
    trend: "vs regular",
    color: "#10B981"
  }
];

const upcomingHighlights = [
  {
    id: "1",
    title: "Tokyo Adventure",
    subtitle: "Cultural exploration",
    date: "Dec 15-22",
    imageUrl: "https://images.unsplash.com/photo-1503899036084-c55cdd92da26?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80",
    status: "confirmed",
    daysLeft: 12
  },
  {
    id: "2",
    title: "Bali Retreat",
    subtitle: "Beach & wellness",
    date: "Jan 8-15",
    imageUrl: "https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80",
    status: "pending",
    daysLeft: 36
  }
];

export default function BookingsScreen() {
  const router = useRouter();
  const { user } = useUserStore();
  const { bookings, fetchBookings, loading } = useBookingStore();
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<"upcoming" | "completed" | "cancelled">("upcoming");

  useEffect(() => {
    fetchBookings();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchBookings();
    setTimeout(() => {
      setRefreshing(false);
    }, 1500);
  };

  const handleFilterPress = () => {
    console.log("Filter pressed");
  };

  const handleNewBookingPress = () => {
    router.push("/");
  };

  const handleHighlightPress = (highlight: any) => {
    router.push(`/booking/${highlight.id}`);
  };

  const handleBookingPress = (booking: Booking) => {
    router.push(`/booking/${booking.id}`);
  };

  const filteredBookings = bookings.filter(
    (booking) => booking.status === activeTab
  );

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Header - Matching Home Screen Style */}
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <Text style={styles.greeting}>Hello, {user?.name || "Traveler"}</Text>
            <View style={styles.headerActions}>
              <Pressable style={styles.iconButton}>
                <Bell size={24} color={colors.text} />
              </Pressable>
              <Pressable style={styles.iconButton} onPress={handleNewBookingPress}>
                <Plus size={24} color={colors.text} />
              </Pressable>
            </View>
          </View>
          <View style={styles.headerBottom}>
            <Text style={styles.mainTitle}>Your Travel Journey</Text>
          </View>
        </View>

        {/* Search Bar - Matching Home Screen */}
        <View style={styles.searchContainer}>
          <SearchBar
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search your bookings and trips"
            onFilterPress={handleFilterPress}
          />
        </View>

        {/* Quick Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statsHeader}>
            <TrendingUp size={20} color={colors.primary} />
            <Text style={styles.statsTitle}>Travel Stats</Text>
          </View>
          <View style={styles.statsGrid}>
            {quickStats.map((stat, index) => (
              <View key={stat.id} style={[styles.statCard, { backgroundColor: index % 2 === 0 ? colors.white : colors.backgroundSecondary }]}>
                <LinearGradient
                  colors={[stat.color + '08', stat.color + '03']}
                  style={styles.statGradient}
                />
                <View style={styles.statHeader}>
                  <View style={[styles.statIconContainer, { backgroundColor: stat.color + '15' }]}>
                    {stat.icon}
                  </View>
                </View>
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.title}</Text>
                <Text style={styles.statTrend}>{stat.trend}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Upcoming Highlights */}
        {activeTab === "upcoming" && upcomingHighlights.length > 0 && (
          <View style={styles.highlightsContainer}>
            <View style={styles.highlightsHeader}>
              <View style={styles.highlightsHeaderLeft}>
                <View style={styles.highlightsIconBadge}>
                  <Sparkles size={18} color={colors.white} />
                </View>
                <View>
                  <Text style={styles.highlightsTitle}>Next Adventures</Text>
                  <Text style={styles.highlightsSubtitle}>Your upcoming trips await</Text>
                </View>
              </View>
            </View>
            <FlatList
              data={upcomingHighlights}
              renderItem={({ item }) => (
                <Pressable
                  style={styles.highlightCard}
                  onPress={() => handleHighlightPress(item)}
                >
                  <View style={styles.highlightImageContainer}>
                    <View style={styles.highlightImage}>
                      <LinearGradient
                        colors={[colors.primary + '20', colors.secondary + '20']}
                        style={styles.highlightImageGradient}
                      />
                    </View>
                    <View style={styles.highlightBadge}>
                      <Text style={styles.highlightBadgeText}>{item.daysLeft}d</Text>
                    </View>
                  </View>
                  <View style={styles.highlightContent}>
                    <Text style={styles.highlightTitle}>{item.title}</Text>
                    <Text style={styles.highlightSubtitle}>{item.subtitle}</Text>
                    <View style={styles.highlightMeta}>
                      <Calendar size={14} color={colors.textSecondary} />
                      <Text style={styles.highlightDate}>{item.date}</Text>
                      <View style={[styles.highlightStatus, { backgroundColor: item.status === 'confirmed' ? colors.success + '20' : colors.warning + '20' }]}>
                        <Text style={[styles.highlightStatusText, { color: item.status === 'confirmed' ? colors.success : colors.warning }]}>
                          {item.status === 'confirmed' ? 'Confirmed' : 'Pending'}
                        </Text>
                      </View>
                    </View>
                  </View>
                </Pressable>
              )}
              horizontal
              showsHorizontalScrollIndicator={false}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.highlightsList}
            />
          </View>
        )}

        {/* Enhanced Tab Navigation */}
        <View style={styles.tabsContainer}>
          <Text style={styles.tabsTitle}>All Bookings</Text>
          <View style={styles.tabsWrapper}>
            <Pressable
              style={[styles.tab, activeTab === "upcoming" && styles.activeTab]}
              onPress={() => setActiveTab("upcoming")}
            >
              <Clock
                size={16}
                color={activeTab === "upcoming" ? colors.white : colors.textLight}
              />
              <Text
                style={[
                  styles.tabText,
                  activeTab === "upcoming" && styles.activeTabText,
                ]}
              >
                Upcoming
              </Text>
              {activeTab === "upcoming" && <View style={styles.tabIndicator} />}
            </Pressable>
            <Pressable
              style={[styles.tab, activeTab === "completed" && styles.activeTab]}
              onPress={() => setActiveTab("completed")}
            >
              <CheckCircle
                size={16}
                color={activeTab === "completed" ? colors.white : colors.textLight}
              />
              <Text
                style={[
                  styles.tabText,
                  activeTab === "completed" && styles.activeTabText,
                ]}
              >
                Completed
              </Text>
              {activeTab === "completed" && <View style={styles.tabIndicator} />}
            </Pressable>
            <Pressable
              style={[styles.tab, activeTab === "cancelled" && styles.activeTab]}
              onPress={() => setActiveTab("cancelled")}
            >
              <XCircle
                size={16}
                color={activeTab === "cancelled" ? colors.white : colors.textLight}
              />
              <Text
                style={[
                  styles.tabText,
                  activeTab === "cancelled" && styles.activeTabText,
                ]}
              >
                Cancelled
              </Text>
              {activeTab === "cancelled" && <View style={styles.tabIndicator} />}
            </Pressable>
          </View>
        </View>

        {/* Bookings List */}
        <View style={styles.bookingsContainer}>
          {loading && !refreshing ? (
            <View style={styles.loadingContainer}>
              <View style={styles.loadingCard}>
                <LinearGradient
                  colors={[colors.primary + '10', colors.secondary + '10']}
                  style={styles.loadingGradient}
                />
                <Text style={styles.loadingText}>Loading your bookings...</Text>
                <Text style={styles.loadingSubtext}>Please wait while we fetch your travel history</Text>
              </View>
            </View>
          ) : filteredBookings.length === 0 ? (
            <View style={styles.emptyContainer}>
              <LinearGradient
                colors={[colors.backgroundSecondary, colors.white]}
                style={styles.emptyGradient}
              />
              <View style={styles.emptyIconContainer}>
                <Calendar size={48} color={colors.textLight} />
              </View>
              <Text style={styles.emptyTitle}>No {activeTab} bookings</Text>
              <Text style={styles.emptyText}>
                {activeTab === "upcoming"
                  ? "Ready for your next adventure? Discover amazing destinations and create unforgettable memories!"
                  : activeTab === "completed"
                  ? "Your completed journeys will appear here. Start exploring to build your travel story!"
                  : "No cancelled bookings found. That's great news for your travel plans!"}
              </Text>
              {activeTab === "upcoming" && (
                <View style={styles.emptyActions}>
                  <Pressable
                    style={styles.exploreButton}
                    onPress={() => router.push("/")}
                  >
                    <Sparkles size={18} color={colors.white} />
                    <Text style={styles.exploreButtonText}>Explore Destinations</Text>
                  </Pressable>
                  <Pressable
                    style={styles.secondaryButton}
                    onPress={() => router.push("/booking/flight")}
                  >
                    <Text style={styles.secondaryButtonText}>Book Flight</Text>
                  </Pressable>
                </View>
              )}
            </View>
          ) : (
            <View style={styles.bookingsList}>
              {filteredBookings.map((booking) => (
                <BookingCard
                  key={booking.id}
                  booking={booking}
                  onPress={handleBookingPress}
                />
              ))}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.xl,
  },
  headerTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  headerBottom: {
    paddingRight: spacing.xl,
  },
  greeting: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
  },
  mainTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    lineHeight: typography.lineHeights.tight * typography.sizes.xxl,
  },
  headerActions: {
    flexDirection: "row",
    gap: spacing.sm,
  },
  iconButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.white,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  searchContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  statsContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  statsHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.lg,
  },
  statsTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  statsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  statCard: {
    width: (screenWidth - spacing.lg * 2 - spacing.sm) / 2,
    borderRadius: 20,
    padding: spacing.lg,
    marginBottom: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 6,
    overflow: "hidden",
  },
  statGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  statHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  statIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  statValue: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium as any,
    marginBottom: spacing.xs,
  },
  statTrend: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    color: colors.textLight,
  },
  highlightsContainer: {
    marginBottom: spacing.xl,
  },
  highlightsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  highlightsHeaderLeft: {
    flexDirection: "row",
    alignItems: "flex-start",
    flex: 1,
  },
  highlightsIconBadge: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  highlightsTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  highlightsSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
  },
  highlightsList: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
  },
  highlightCard: {
    width: screenWidth * 0.75,
    backgroundColor: colors.white,
    borderRadius: 20,
    marginRight: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
    overflow: "hidden",
  },
  highlightImageContainer: {
    position: "relative",
  },
  highlightImage: {
    height: 120,
    backgroundColor: colors.backgroundSecondary,
  },
  highlightImageGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  highlightBadge: {
    position: "absolute",
    top: spacing.md,
    right: spacing.md,
    backgroundColor: colors.primary,
    borderRadius: 16,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },
  highlightBadgeText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
  },
  highlightContent: {
    padding: spacing.lg,
  },
  highlightTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  highlightSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  highlightMeta: {
    flexDirection: "row",
    alignItems: "center",
    gap: spacing.sm,
  },
  highlightDate: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
    flex: 1,
  },
  highlightStatus: {
    borderRadius: 12,
    paddingHorizontal: spacing.sm,
    paddingVertical: 4,
  },
  highlightStatusText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium as any,
  },
  tabsContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  tabsTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.md,
  },
  tabsWrapper: {
    flexDirection: "row",
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 16,
    padding: spacing.xs,
  },
  tab: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: 12,
    position: "relative",
  },
  activeTab: {
    backgroundColor: colors.primary,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  tabText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textLight,
    marginLeft: spacing.xs,
    fontWeight: typography.weights.medium as any,
  },
  activeTabText: {
    color: colors.white,
    fontWeight: typography.weights.semibold as any,
  },
  tabIndicator: {
    position: "absolute",
    bottom: -2,
    left: "50%",
    marginLeft: -8,
    width: 16,
    height: 3,
    backgroundColor: colors.white,
    borderRadius: 2,
  },
  bookingsContainer: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
  },
  bookingsList: {
    gap: spacing.md,
  },
  loadingContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: spacing.xxl,
  },
  loadingCard: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: spacing.xl,
    alignItems: "center",
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
    overflow: "hidden",
    width: "100%",
    maxWidth: 300,
  },
  loadingGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  loadingText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  loadingSubtext: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    textAlign: "center",
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: spacing.xxl,
    backgroundColor: colors.white,
    borderRadius: 24,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 8,
    overflow: "hidden",
  },
  emptyGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  emptyIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: spacing.lg,
  },
  emptyTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  emptyText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: spacing.xl,
    paddingHorizontal: spacing.lg,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.md,
  },
  emptyActions: {
    flexDirection: "row",
    gap: spacing.md,
  },
  exploreButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: 16,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  exploreButtonText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: colors.white,
    marginLeft: spacing.sm,
  },
  secondaryButton: {
    backgroundColor: colors.backgroundSecondary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  secondaryButtonText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium as any,
    color: colors.text,
  },
});