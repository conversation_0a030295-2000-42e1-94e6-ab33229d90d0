import React from "react";
import { StyleSheet, View, Text, ScrollView, Pressable, Dimensions } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Image } from "expo-image";
import { SafeAreaView } from "react-native-safe-area-context";
import { ArrowLeft, Calendar, Clock, MapPin, CheckCircle, XCircle, Download, Share2 } from "lucide-react-native";
import { colors } from "@/constants/colors";
import { typography } from "@/constants/typography";
import { spacing } from "@/constants/spacing";
import { Button } from "@/components/Button";
import { bookings } from "@/mocks/bookings";

const { width } = Dimensions.get("window");

export default function BookingDetailScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  
  const booking = bookings.find((b) => b.id === id);
  
  if (!booking) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Booking not found</Text>
      </SafeAreaView>
    );
  }

  const getStatusIcon = () => {
    switch (booking.status) {
      case "upcoming":
        return <Clock size={20} color={colors.info} />;
      case "completed":
        return <CheckCircle size={20} color={colors.success} />;
      case "cancelled":
        return <XCircle size={20} color={colors.error} />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    switch (booking.status) {
      case "upcoming":
        return "Upcoming";
      case "completed":
        return "Completed";
      case "cancelled":
        return "Cancelled";
      default:
        return "";
    }
  };

  const getStatusColor = () => {
    switch (booking.status) {
      case "upcoming":
        return colors.info;
      case "completed":
        return colors.success;
      case "cancelled":
        return colors.error;
      default:
        return colors.textLight;
    }
  };

  const handleCancel = () => {
    // Handle booking cancellation
    alert("Booking cancellation flow would start here");
    router.back();
  };

  const handleDownload = () => {
    // Handle ticket download
    alert("Ticket download would start here");
  };

  const handleShare = () => {
    // Handle booking sharing
    alert("Booking sharing would start here");
  };

  return (
    <View style={styles.container}>
      <SafeAreaView style={styles.header} edges={["top"]}>
        <Pressable style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </Pressable>
        <Text style={styles.headerTitle}>Booking Details</Text>
        <View style={{ width: 24 }} />
      </SafeAreaView>

      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: booking.imageUrl }}
            style={styles.image}
            contentFit="cover"
          />
        </View>

        <View style={styles.content}>
          <View style={styles.statusContainer}>
            {getStatusIcon()}
            <Text
              style={[
                styles.statusText,
                { color: getStatusColor() },
              ]}
            >
              {getStatusText()}
            </Text>
          </View>

          <Text style={styles.title}>{booking.title}</Text>

          <View style={styles.infoContainer}>
            <View style={styles.infoItem}>
              <Calendar size={18} color={colors.primary} />
              <Text style={styles.infoText}>{booking.date}</Text>
            </View>
            <View style={styles.infoItem}>
              <MapPin size={18} color={colors.primary} />
              <Text style={styles.infoText}>Meeting Point: Central Square</Text>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Booking Information</Text>
            <View style={styles.bookingInfoContainer}>
              <View style={styles.bookingInfoRow}>
                <Text style={styles.bookingInfoLabel}>Booking ID</Text>
                <Text style={styles.bookingInfoValue}>{booking.confirmationCode}</Text>
              </View>
              <View style={styles.bookingInfoRow}>
                <Text style={styles.bookingInfoLabel}>Booking Date</Text>
                <Text style={styles.bookingInfoValue}>July 10, 2025</Text>
              </View>
              <View style={styles.bookingInfoRow}>
                <Text style={styles.bookingInfoLabel}>Number of Guests</Text>
                <Text style={styles.bookingInfoValue}>2 Adults</Text>
              </View>
              <View style={styles.bookingInfoRow}>
                <Text style={styles.bookingInfoLabel}>Total Amount</Text>
                <Text style={styles.bookingInfoValue}>$120.00</Text>
              </View>
              <View style={styles.bookingInfoRow}>
                <Text style={styles.bookingInfoLabel}>Payment Method</Text>
                <Text style={styles.bookingInfoValue}>Credit Card (****4582)</Text>
              </View>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Important Information</Text>
            <Text style={styles.importantText}>
              • Please arrive 15 minutes before the scheduled time.
              {"\n"}• Bring a valid ID for verification.
              {"\n"}• Comfortable walking shoes are recommended.
              {"\n"}• The tour will proceed in light rain, but may be rescheduled in case of severe weather.
            </Text>
          </View>

          <View style={styles.actionsContainer}>
            <Pressable style={styles.actionButton} onPress={handleDownload}>
              <Download size={20} color={colors.primary} />
              <Text style={styles.actionText}>Download</Text>
            </Pressable>
            <Pressable style={styles.actionButton} onPress={handleShare}>
              <Share2 size={20} color={colors.primary} />
              <Text style={styles.actionText}>Share</Text>
            </Pressable>
          </View>

          {booking.status === "upcoming" && (
            <Button
              title="Cancel Booking"
              onPress={handleCancel}
              variant="outline"
              fullWidth
            />
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  headerTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
  },
  imageContainer: {
    height: 200,
    width: width,
  },
  image: {
    width: "100%",
    height: "100%",
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.xl,
    paddingBottom: spacing.xl,
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  statusText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium as any,
    marginLeft: spacing.sm,
  },
  title: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.md,
  },
  infoContainer: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.xl,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  infoText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.text,
    marginBottom: spacing.md,
  },
  bookingInfoContainer: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.md,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  bookingInfoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  bookingInfoLabel: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.textLight,
  },
  bookingInfoValue: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium as any,
    color: colors.text,
  },
  importantText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.md,
    color: colors.text,
    lineHeight: 24,
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.md,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  actionsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginBottom: spacing.xl,
  },
  actionButton: {
    alignItems: "center",
    padding: spacing.md,
  },
  actionText: {
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    color: colors.primary,
    marginTop: spacing.xs,
  },
});