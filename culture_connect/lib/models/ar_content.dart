/// AR Content model for managing AR experiences and 3D models
class ARContent {
  final String id;
  final String title;
  final String description;
  final double latitude;
  final double longitude;
  final String modelUrl;
  final String thumbnailUrl;
  final String category;
  final bool hasAudio;
  final bool hasTour;
  final Duration estimatedDuration;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  const ARContent({
    required this.id,
    required this.title,
    required this.description,
    required this.latitude,
    required this.longitude,
    required this.modelUrl,
    required this.thumbnailUrl,
    required this.category,
    this.hasAudio = false,
    this.hasTour = false,
    required this.estimatedDuration,
    this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  /// Create ARContent from JSON
  factory ARContent.fromJson(Map<String, dynamic> json) {
    return ARContent(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      modelUrl: json['modelUrl'] as String,
      thumbnailUrl: json['thumbnailUrl'] as String,
      category: json['category'] as String,
      hasAudio: json['hasAudio'] as bool? ?? false,
      hasTour: json['hasTour'] as bool? ?? false,
      estimatedDuration: Duration(
        minutes: json['estimatedDurationMinutes'] as int? ?? 10,
      ),
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert ARContent to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'modelUrl': modelUrl,
      'thumbnailUrl': thumbnailUrl,
      'category': category,
      'hasAudio': hasAudio,
      'hasTour': hasTour,
      'estimatedDurationMinutes': estimatedDuration.inMinutes,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Create a copy with updated fields
  ARContent copyWith({
    String? id,
    String? title,
    String? description,
    double? latitude,
    double? longitude,
    String? modelUrl,
    String? thumbnailUrl,
    String? category,
    bool? hasAudio,
    bool? hasTour,
    Duration? estimatedDuration,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return ARContent(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      modelUrl: modelUrl ?? this.modelUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      category: category ?? this.category,
      hasAudio: hasAudio ?? this.hasAudio,
      hasTour: hasTour ?? this.hasTour,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'ARContent(id: $id, title: $title, category: $category, hasAudio: $hasAudio, hasTour: $hasTour)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ARContent && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
