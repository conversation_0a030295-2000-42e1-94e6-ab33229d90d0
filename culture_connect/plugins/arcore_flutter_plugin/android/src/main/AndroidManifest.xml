<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  package="com.difrancescogianmarco.arcore_flutter_plugin">

    <uses-permission android:name="android.permission.CAMERA" />

    <!-- Declare ARCore as optional so the app can install/run on devices without ARCore (e.g., emulators). -->
    <uses-feature android:name="android.hardware.camera.ar" android:required="false" />

    <!-- Sceneform requires OpenGL ES 3.0 or later. -->
    <uses-feature android:glEsVersion="0x00030000" android:required="true" />

    <application>
        <!-- Indicates that app requires ARCore ("AR Required"). Causes Google
             Play Store to download and install ARCore when the app is installed.
        -->
        <meta-data android:name="com.google.ar.core" android:value="optional" />
    </application>

</manifest>
