import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Pressable,
  Image,
  Dimensions,
  Animated,
  Alert,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack } from 'expo-router';
import {
  Crown,
  Star,
  Shield,
  Clock,
  Users,
  MapPin,
  Phone,
  Calendar,
  CreditCard,
  ArrowLeft,
  Heart,
  Share,
  CheckCircle,
  Zap,
} from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';
import { luxuryServices, LuxuryService } from '@/mocks/luxuryServices';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const serviceDetails = luxuryServices.reduce((acc, service) => {
  acc[service.id] = service;
  return acc;
}, {} as { [key: string]: LuxuryService });

export default function ServiceDetailScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isBooking, setIsBooking] = useState(false);
  
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  const service = serviceDetails[id as string];

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  if (!service) {
    return (
      <SafeAreaView style={styles.container}>
        <Text style={styles.errorText}>Service not found</Text>
      </SafeAreaView>
    );
  }

  const handleBookNow = () => {
    setIsBooking(true);
    
    // Simulate booking process
    setTimeout(() => {
      setIsBooking(false);
      Alert.alert(
        'Booking Initiated',
        'Your luxury service booking request has been submitted. Our concierge team will contact you within 15 minutes to confirm details.',
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          },
        ]
      );
    }, 2000);
  };

  const handleShare = () => {
    Alert.alert('Share', 'Share this luxury service with others');
  };

  const handleFavorite = () => {
    Alert.alert('Favorite', 'Added to your favorites');
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Stack.Screen
        options={{
          title: service.title,
          headerStyle: {
            backgroundColor: '#1a1a1a',
          },
          headerTintColor: '#D946EF',
          headerTitleStyle: {
            fontWeight: 'bold',
            fontSize: 16,
          },
          headerLeft: () => (
            <Pressable style={styles.headerButton} onPress={() => router.back()}>
              <ArrowLeft size={24} color="#D946EF" />
            </Pressable>
          ),
          headerRight: () => (
            <View style={styles.headerActions}>
              <Pressable style={styles.headerButton} onPress={handleShare}>
                <Share size={20} color="#D946EF" />
              </Pressable>
              <Pressable style={styles.headerButton} onPress={handleFavorite}>
                <Heart size={20} color="#D946EF" />
              </Pressable>
            </View>
          ),
        }}
      />

      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Image Gallery */}
          <View style={styles.imageSection}>
            <Image
              source={{ uri: service.galleryImages?.[selectedImageIndex] || service.imageUrl }}
              style={styles.mainImage}
            />
            <View style={styles.imageOverlay}>
              <View style={styles.luxuryBadge}>
                <Crown size={16} color="#D946EF" />
                <Text style={styles.luxuryBadgeText}>LUXURY</Text>
              </View>
              {service.securityIncluded && (
                <View style={styles.securityBadge}>
                  <Shield size={16} color={colors.white} />
                  <Text style={styles.securityBadgeText}>Security Included</Text>
                </View>
              )}
            </View>
            
            {service.galleryImages && service.galleryImages.length > 1 && (
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.galleryThumbnails}
                contentContainerStyle={styles.galleryContent}
              >
                {service.galleryImages.map((image, index) => (
                  <Pressable
                    key={index}
                    style={[
                      styles.thumbnail,
                      selectedImageIndex === index && styles.thumbnailActive,
                    ]}
                    onPress={() => setSelectedImageIndex(index)}
                  >
                    <Image source={{ uri: image }} style={styles.thumbnailImage} />
                  </Pressable>
                ))}
              </ScrollView>
            )}
          </View>

          {/* Service Info */}
          <View style={styles.infoSection}>
            <View style={styles.titleSection}>
              <Text style={styles.serviceTitle}>{service.title}</Text>
              <Text style={styles.serviceSubtitle}>{service.subtitle}</Text>
              
              <View style={styles.ratingSection}>
                <View style={styles.ratingContainer}>
                  <Star size={16} color="#F59E0B" fill="#F59E0B" />
                  <Text style={styles.ratingText}>{service.rating}</Text>
                  <Text style={styles.reviewText}>({service.reviewCount} reviews)</Text>
                </View>
                <View style={styles.providerInfo}>
                  <Text style={styles.providerName}>{service.provider.name}</Text>
                  {service.provider.verified && (
                    <CheckCircle size={16} color="#22C55E" />
                  )}
                </View>
              </View>
            </View>

            {/* Quick Info */}
            <View style={styles.quickInfoSection}>
              <View style={styles.quickInfoItem}>
                <MapPin size={20} color="#D946EF" />
                <Text style={styles.quickInfoText}>{service.location}</Text>
              </View>
              <View style={styles.quickInfoItem}>
                <Clock size={20} color="#D946EF" />
                <Text style={styles.quickInfoText}>{service.duration}</Text>
              </View>
              <View style={styles.quickInfoItem}>
                <Users size={20} color="#D946EF" />
                <Text style={styles.quickInfoText}>{service.capacity}</Text>
              </View>
            </View>

            {/* Description */}
            <View style={styles.descriptionSection}>
              <Text style={styles.sectionTitle}>About This Service</Text>
              <Text style={styles.description}>{service.longDescription}</Text>
            </View>

            {/* Features */}
            <View style={styles.featuresSection}>
              <Text style={styles.sectionTitle}>Key Features</Text>
              <View style={styles.featuresList}>
                {service.features.map((feature, index) => (
                  <View key={index} style={styles.featureItem}>
                    <Zap size={16} color="#D946EF" />
                    <Text style={styles.featureText}>{feature}</Text>
                  </View>
                ))}
              </View>
            </View>

            {/* Amenities */}
            <View style={styles.amenitiesSection}>
              <Text style={styles.sectionTitle}>Included Amenities</Text>
              <View style={styles.amenitiesList}>
                {service.amenities.map((amenity, index) => (
                  <View key={index} style={styles.amenityItem}>
                    <CheckCircle size={16} color="#22C55E" />
                    <Text style={styles.amenityText}>{amenity}</Text>
                  </View>
                ))}
              </View>
            </View>

            {/* Availability */}
            <View style={styles.availabilitySection}>
              <Text style={styles.sectionTitle}>Availability</Text>
              <View style={styles.availabilityInfo}>
                <View style={styles.availabilityStatus}>
                  <View style={styles.availabilityDot} />
                  <Text style={styles.availabilityText}>{service.availability}</Text>
                </View>
                <Pressable style={styles.calendarButton}>
                  <Calendar size={16} color="#D946EF" />
                  <Text style={styles.calendarButtonText}>Check Calendar</Text>
                </Pressable>
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Bottom Booking Section */}
        <View style={styles.bookingSection}>
          <View style={styles.priceSection}>
            <Text style={styles.priceLabel}>Starting from</Text>
            <Text style={styles.priceText}>{service.price}</Text>
          </View>
          
          <Pressable
            style={[styles.bookButton, isBooking && styles.bookButtonLoading]}
            onPress={handleBookNow}
            disabled={isBooking}
          >
            <CreditCard size={20} color={colors.white} />
            <Text style={styles.bookButtonText}>
              {isBooking ? 'Processing...' : 'Book Now'}
            </Text>
          </Pressable>
        </View>
      </Animated.View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0a0a0a',
  },
  headerButton: {
    padding: spacing.sm,
  },
  headerActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  content: {
    flex: 1,
  },
  errorText: {
    fontSize: typography.sizes.lg,
    color: colors.white,
    textAlign: 'center',
    marginTop: spacing.xl,
  },
  imageSection: {
    position: 'relative',
  },
  mainImage: {
    width: screenWidth,
    height: screenHeight * 0.4,
    resizeMode: 'cover',
  },
  imageOverlay: {
    position: 'absolute',
    top: spacing.lg,
    left: spacing.lg,
    right: spacing.lg,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  luxuryBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 16,
    gap: spacing.xs,
  },
  luxuryBadgeText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.bold as any,
    color: '#D946EF',
    letterSpacing: 0.5,
  },
  securityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(34, 197, 94, 0.9)',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 16,
    gap: spacing.xs,
  },
  securityBadgeText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold as any,
    color: colors.white,
  },
  galleryThumbnails: {
    position: 'absolute',
    bottom: spacing.lg,
    left: 0,
    right: 0,
  },
  galleryContent: {
    paddingHorizontal: spacing.lg,
    gap: spacing.sm,
  },
  thumbnail: {
    width: 60,
    height: 60,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  thumbnailActive: {
    borderColor: '#D946EF',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  infoSection: {
    backgroundColor: '#1a1a1a',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    marginTop: -24,
    paddingTop: spacing.xl,
    paddingHorizontal: spacing.lg,
    paddingBottom: 120, // Space for bottom booking section
  },
  titleSection: {
    marginBottom: spacing.xl,
  },
  serviceTitle: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    marginBottom: spacing.sm,
  },
  serviceSubtitle: {
    fontSize: typography.sizes.lg,
    color: '#D946EF',
    fontWeight: typography.weights.semibold as any,
    marginBottom: spacing.lg,
  },
  ratingSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  ratingText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: '#F59E0B',
  },
  reviewText: {
    fontSize: typography.sizes.sm,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  providerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  providerName: {
    fontSize: typography.sizes.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: typography.weights.medium as any,
  },
  quickInfoSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(217, 70, 239, 0.1)',
    borderRadius: 16,
    padding: spacing.lg,
    marginBottom: spacing.xl,
    borderWidth: 1,
    borderColor: 'rgba(217, 70, 239, 0.2)',
  },
  quickInfoItem: {
    alignItems: 'center',
    gap: spacing.sm,
  },
  quickInfoText: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontWeight: typography.weights.medium as any,
    textAlign: 'center',
  },
  descriptionSection: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
    marginBottom: spacing.md,
  },
  description: {
    fontSize: typography.sizes.md,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: typography.lineHeights.relaxed * typography.sizes.md,
  },
  featuresSection: {
    marginBottom: spacing.xl,
  },
  featuresList: {
    gap: spacing.md,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  featureText: {
    fontSize: typography.sizes.md,
    color: colors.white,
    fontWeight: typography.weights.medium as any,
  },
  amenitiesSection: {
    marginBottom: spacing.xl,
  },
  amenitiesList: {
    gap: spacing.md,
  },
  amenityItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing.md,
  },
  amenityText: {
    fontSize: typography.sizes.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
    flex: 1,
  },
  availabilitySection: {
    marginBottom: spacing.xl,
  },
  availabilityInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  availabilityStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  availabilityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#22C55E',
  },
  availabilityText: {
    fontSize: typography.sizes.md,
    color: '#22C55E',
    fontWeight: typography.weights.semibold as any,
  },
  calendarButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(217, 70, 239, 0.3)',
    backgroundColor: 'rgba(217, 70, 239, 0.1)',
  },
  calendarButtonText: {
    fontSize: typography.sizes.sm,
    color: '#D946EF',
    fontWeight: typography.weights.medium as any,
  },
  bookingSection: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#1a1a1a',
    borderTopWidth: 1,
    borderTopColor: 'rgba(217, 70, 239, 0.2)',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    paddingBottom: spacing.xl,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  priceSection: {
    flex: 1,
  },
  priceLabel: {
    fontSize: typography.sizes.sm,
    color: 'rgba(255, 255, 255, 0.6)',
    marginBottom: spacing.xs,
  },
  priceText: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
  },
  bookButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#D946EF',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    borderRadius: 16,
    gap: spacing.sm,
    shadowColor: '#D946EF',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  bookButtonLoading: {
    backgroundColor: 'rgba(217, 70, 239, 0.6)',
  },
  bookButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.white,
  },
});