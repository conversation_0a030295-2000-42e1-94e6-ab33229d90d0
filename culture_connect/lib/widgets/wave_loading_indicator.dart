import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A professional wave-like loading animation widget
class WaveLoadingIndicator extends StatefulWidget {
  final double progress; // 0.0 to 1.0
  final double width;
  final double height;
  final Color waveColor;
  final Color backgroundColor;
  final String? progressText;
  final TextStyle? textStyle;
  final Duration animationDuration;

  const WaveLoadingIndicator({
    super.key,
    required this.progress,
    this.width = 200,
    this.height = 60,
    this.waveColor = Colors.white,
    this.backgroundColor = Colors.transparent,
    this.progressText,
    this.textStyle,
    this.animationDuration = const Duration(milliseconds: 1500),
  });

  @override
  State<WaveLoadingIndicator> createState() => _WaveLoadingIndicatorState();
}

class _WaveLoadingIndicatorState extends State<WaveLoadingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _waveController;
  late Animation<double> _waveAnimation;

  @override
  void initState() {
    super.initState();
    _waveController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _waveAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _waveController,
      curve: Curves.linear,
    ));

    _waveController.repeat();
  }

  @override
  void dispose() {
    _waveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(77), // 0.3 opacity for better contrast
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Wave animation container
          Container(
            width: widget.width - 32, // Account for padding
            height: widget.height,
            decoration: BoxDecoration(
              color: widget.backgroundColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: widget.waveColor.withAlpha(77), // 0.3 opacity
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(7),
              child: AnimatedBuilder(
                animation: _waveAnimation,
                builder: (context, child) {
                  return CustomPaint(
                    painter: WavePainter(
                      progress: widget.progress,
                      waveColor: widget.waveColor,
                      animationValue: _waveAnimation.value,
                    ),
                    size: Size(widget.width - 32, widget.height),
                  );
                },
              ),
            ),
          ),

          if (widget.progressText != null) ...[
            const SizedBox(height: 8),
            Text(
              widget.progressText!,
              style: widget.textStyle ??
                  const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    shadows: [
                      Shadow(
                        offset: Offset(1, 1),
                        blurRadius: 2,
                        color: Colors.black45,
                      ),
                    ],
                  ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Custom painter for the wave animation
class WavePainter extends CustomPainter {
  final double progress;
  final Color waveColor;
  final double animationValue;

  WavePainter({
    required this.progress,
    required this.waveColor,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = waveColor.withAlpha(51) // 0.2 opacity for background
      ..style = PaintingStyle.fill;

    final wavePaint = Paint()
      ..color = waveColor.withAlpha(128) // 0.5 opacity for wave
      ..style = PaintingStyle.fill;

    final progressPaint = Paint()
      ..color = waveColor
      ..style = PaintingStyle.fill;

    // Draw background
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);

    // Calculate progress width
    final progressWidth = size.width * progress;

    if (progressWidth > 0) {
      // Draw progress background
      canvas.drawRect(
        Rect.fromLTWH(0, 0, progressWidth, size.height),
        wavePaint,
      );

      // Draw animated wave on top of progress
      final path = Path();
      final waveHeight = size.height * 0.3; // Wave amplitude
      final waveLength = size.width * 0.5; // Wave length
      final centerY = size.height * 0.5;

      path.moveTo(0, centerY);

      for (double x = 0; x <= progressWidth; x += 1) {
        final normalizedX = x / waveLength;
        final waveY = centerY +
            math.sin((normalizedX * 2 * math.pi) + animationValue) *
                waveHeight *
                0.3; // Reduced amplitude for subtlety
        path.lineTo(x, waveY);
      }

      // Complete the path to fill the area
      path.lineTo(progressWidth, size.height);
      path.lineTo(0, size.height);
      path.close();

      canvas.drawPath(path, progressPaint);

      // Add a subtle shimmer effect
      final shimmerPaint = Paint()
        ..color = Colors.white.withAlpha(51) // 0.2 opacity
        ..style = PaintingStyle.fill;

      final shimmerPath = Path();
      shimmerPath.moveTo(0, centerY - waveHeight * 0.1);

      for (double x = 0; x <= progressWidth; x += 2) {
        final normalizedX = x / (waveLength * 0.7);
        final shimmerY = centerY -
            waveHeight * 0.1 +
            math.sin((normalizedX * 2 * math.pi) +
                    animationValue +
                    math.pi / 4) *
                waveHeight *
                0.1;
        shimmerPath.lineTo(x, shimmerY);
      }

      shimmerPath.lineTo(progressWidth, centerY + waveHeight * 0.1);
      shimmerPath.lineTo(0, centerY + waveHeight * 0.1);
      shimmerPath.close();

      canvas.drawPath(shimmerPath, shimmerPaint);
    }
  }

  @override
  bool shouldRepaint(covariant WavePainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.animationValue != animationValue;
  }
}

/// Specialized wave loading indicator for splash screens
class SplashWaveLoadingIndicator extends StatelessWidget {
  final double progress;
  final String progressText;

  const SplashWaveLoadingIndicator({
    super.key,
    required this.progress,
    required this.progressText,
  });

  @override
  Widget build(BuildContext context) {
    return WaveLoadingIndicator(
      progress: progress,
      width: 200,
      height: 40,
      waveColor: Colors.white,
      backgroundColor: Colors.transparent,
      progressText: progressText,
      animationDuration: const Duration(milliseconds: 2000),
    );
  }
}
