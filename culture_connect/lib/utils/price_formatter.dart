import 'dart:math';

/// Utility class for formatting prices with intelligent truncation and currency handling
/// Implements k/m suffixes for large numbers and defensive overflow handling
class PriceFormatter {
  /// Format a price range string with intelligent truncation
  /// Handles different currencies and implements k/m suffixes for readability
  static String formatPriceRange(String priceRange) {
    if (priceRange.isEmpty) return '';
    
    try {
      // Handle range formats like "$40-60/hour", "€150-350/day", etc.
      final rangePattern = RegExp(r'([€$£¥₦₹₽₿Ξ]?)(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)(.*)');
      final match = rangePattern.firstMatch(priceRange);
      
      if (match != null) {
        final currency = match.group(1) ?? '';
        final minPrice = double.tryParse(match.group(2) ?? '0') ?? 0;
        final maxPrice = double.tryParse(match.group(3) ?? '0') ?? 0;
        final suffix = match.group(4) ?? '';
        
        final formattedMin = _formatNumber(minPrice);
        final formattedMax = _formatNumber(maxPrice);
        
        return '$currency$formattedMin-$formattedMax$suffix';
      }
      
      // Handle single price formats like "$500/day"
      final singlePattern = RegExp(r'([€$£¥₦₹₽₿Ξ]?)(\d+(?:\.\d+)?)(.*)');
      final singleMatch = singlePattern.firstMatch(priceRange);
      
      if (singleMatch != null) {
        final currency = singleMatch.group(1) ?? '';
        final price = double.tryParse(singleMatch.group(2) ?? '0') ?? 0;
        final suffix = singleMatch.group(3) ?? '';
        
        final formattedPrice = _formatNumber(price);
        return '$currency$formattedPrice$suffix';
      }
      
      // If no pattern matches, return original with truncation
      return _truncateString(priceRange, 12);
      
    } catch (e) {
      // Fallback to truncated original string
      return _truncateString(priceRange, 12);
    }
  }
  
  /// Format a number with k/m suffixes for better readability
  static String _formatNumber(double number) {
    if (number >= 1000000) {
      // Format millions
      final millions = number / 1000000;
      if (millions == millions.floor()) {
        return '${millions.toInt()}m';
      } else {
        return '${millions.toStringAsFixed(1)}m';
      }
    } else if (number >= 1000) {
      // Format thousands
      final thousands = number / 1000;
      if (thousands == thousands.floor()) {
        return '${thousands.toInt()}k';
      } else {
        return '${thousands.toStringAsFixed(1)}k';
      }
    } else {
      // Format regular numbers
      if (number == number.floor()) {
        return number.toInt().toString();
      } else {
        return number.toStringAsFixed(1);
      }
    }
  }
  
  /// Truncate string with ellipsis for defensive overflow handling
  static String _truncateString(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength - 1)}…';
  }
  
  /// Get currency symbol from location/country for intelligent currency handling
  static String getCurrencyFromLocation(String location) {
    final locationLower = location.toLowerCase();
    
    // European countries
    if (locationLower.contains('spain') || 
        locationLower.contains('barcelona') ||
        locationLower.contains('madrid') ||
        locationLower.contains('france') ||
        locationLower.contains('paris') ||
        locationLower.contains('italy') ||
        locationLower.contains('rome') ||
        locationLower.contains('germany') ||
        locationLower.contains('berlin') ||
        locationLower.contains('netherlands') ||
        locationLower.contains('amsterdam') ||
        locationLower.contains('greece') ||
        locationLower.contains('athens')) {
      return '€';
    }
    
    // UK
    if (locationLower.contains('uk') || 
        locationLower.contains('london') ||
        locationLower.contains('england') ||
        locationLower.contains('scotland') ||
        locationLower.contains('wales')) {
      return '£';
    }
    
    // Japan
    if (locationLower.contains('japan') || 
        locationLower.contains('tokyo') ||
        locationLower.contains('kyoto') ||
        locationLower.contains('osaka')) {
      return '¥';
    }
    
    // Kenya/Africa
    if (locationLower.contains('kenya') || 
        locationLower.contains('nairobi') ||
        locationLower.contains('africa')) {
      return 'KSh';
    }
    
    // Morocco
    if (locationLower.contains('morocco') || 
        locationLower.contains('marrakech') ||
        locationLower.contains('casablanca')) {
      return 'MAD';
    }
    
    // Default to USD
    return '\$';
  }
  
  /// Format price with location-based currency detection
  static String formatPriceWithLocation(String priceRange, String location) {
    if (priceRange.isEmpty) return '';
    
    // If price already has currency symbol, format as-is
    if (RegExp(r'^[€$£¥₦₹₽₿Ξ]').hasMatch(priceRange)) {
      return formatPriceRange(priceRange);
    }
    
    // Extract numbers and add appropriate currency
    final numberPattern = RegExp(r'(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)(.*)');
    final match = numberPattern.firstMatch(priceRange);
    
    if (match != null) {
      final currency = getCurrencyFromLocation(location);
      final minPrice = match.group(1) ?? '';
      final maxPrice = match.group(2) ?? '';
      final suffix = match.group(3) ?? '';
      
      return formatPriceRange('$currency$minPrice-$maxPrice$suffix');
    }
    
    return formatPriceRange(priceRange);
  }
}
