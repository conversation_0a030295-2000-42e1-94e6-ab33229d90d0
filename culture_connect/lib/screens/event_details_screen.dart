import 'package:flutter/material.dart';
import 'package:culture_connect/theme/kaia_design_tokens.dart';

/// Comprehensive event details screen for CultureConnect
/// Displays detailed information about upcoming events from the Explore Screen
/// Follows CultureConnect premium design system with defensive overflow handling
class EventDetailsScreen extends StatefulWidget {
  final String id;
  final String title;
  final String date;
  final String time;
  final String location;
  final String imageUrl;
  final String category;
  final String price;
  final int attendees;
  final bool isBookmarked;

  const EventDetailsScreen({
    super.key,
    required this.id,
    required this.title,
    required this.date,
    required this.time,
    required this.location,
    required this.imageUrl,
    required this.category,
    required this.price,
    required this.attendees,
    required this.isBookmarked,
  });

  @override
  State<EventDetailsScreen> createState() => _EventDetailsScreenState();
}

class _EventDetailsScreenState extends State<EventDetailsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late ScrollController _scrollController;

  bool _isFavorited = false;
  bool _isDescriptionExpanded = false;
  int _currentImageIndex = 0;

  // Mock additional images for carousel
  List<String> get _images => [
        widget.imageUrl,
        'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        'https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      ];

  @override
  void initState() {
    super.initState();
    _isFavorited = widget.isBookmarked;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scrollController = ScrollController();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: KaiaDesignTokens.neutralGray50,
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          _buildSliverAppBar(),
          SliverToBoxAdapter(
            child: Column(
              children: [
                _buildEventHeader(),
                _buildEventDetails(),
                _buildDescription(),
                _buildOrganizerSection(),
                _buildLocationSection(),
                _buildPricingSection(),
                _buildRelatedEventsSection(),
                const SizedBox(height: KaiaDesignTokens.spacing80),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomActionBar(),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 300,
      pinned: true,
      backgroundColor: KaiaDesignTokens.neutralWhite,
      elevation: 0,
      leading: Container(
        margin: const EdgeInsets.all(KaiaDesignTokens.spacing8),
        decoration: BoxDecoration(
          color: KaiaDesignTokens.neutralWhite.withOpacity(0.9),
          borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusFull),
          boxShadow: KaiaDesignTokens.shadowSm,
        ),
        child: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: KaiaDesignTokens.neutralGray900,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.all(KaiaDesignTokens.spacing8),
          decoration: BoxDecoration(
            color: KaiaDesignTokens.neutralWhite.withOpacity(0.9),
            borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusFull),
            boxShadow: KaiaDesignTokens.shadowSm,
          ),
          child: IconButton(
            icon: Icon(
              _isFavorited ? Icons.favorite : Icons.favorite_border,
              color: _isFavorited
                  ? KaiaDesignTokens.errorRed
                  : KaiaDesignTokens.neutralGray900,
            ),
            onPressed: _toggleFavorite,
          ),
        ),
        Container(
          margin: const EdgeInsets.all(KaiaDesignTokens.spacing8),
          decoration: BoxDecoration(
            color: KaiaDesignTokens.neutralWhite.withOpacity(0.9),
            borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusFull),
            boxShadow: KaiaDesignTokens.shadowSm,
          ),
          child: IconButton(
            icon: const Icon(
              Icons.share,
              color: KaiaDesignTokens.neutralGray900,
            ),
            onPressed: _shareEvent,
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: _buildImageCarousel(),
      ),
    );
  }

  Widget _buildImageCarousel() {
    return Stack(
      children: [
        PageView.builder(
          itemCount: _images.length,
          onPageChanged: (index) {
            setState(() {
              _currentImageIndex = index;
            });
          },
          itemBuilder: (context, index) {
            return Image.network(
              _images[index],
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: KaiaDesignTokens.neutralGray200,
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      size: 64,
                      color: KaiaDesignTokens.neutralGray500,
                    ),
                  ),
                );
              },
            );
          },
        ),
        // Image indicators
        Positioned(
          bottom: KaiaDesignTokens.spacing16,
          left: 0,
          right: 0,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: _images.asMap().entries.map((entry) {
              return Container(
                width: 8,
                height: 8,
                margin: const EdgeInsets.symmetric(horizontal: 4),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _currentImageIndex == entry.key
                      ? KaiaDesignTokens.neutralWhite
                      : KaiaDesignTokens.neutralWhite.withOpacity(0.5),
                ),
              );
            }).toList(),
          ),
        ),
        // Category badge
        Positioned(
          top: KaiaDesignTokens.spacing48,
          left: KaiaDesignTokens.spacing16,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: KaiaDesignTokens.spacing12,
              vertical: KaiaDesignTokens.spacing6,
            ),
            decoration: BoxDecoration(
              color: KaiaDesignTokens.primaryIndigo,
              borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
              boxShadow: KaiaDesignTokens.shadowSm,
            ),
            child: Text(
              widget.category,
              style: const TextStyle(
                fontSize: KaiaDesignTokens.fontSizeSm,
                fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                color: KaiaDesignTokens.neutralWhite,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEventHeader() {
    return Container(
      margin: const EdgeInsets.all(KaiaDesignTokens.spacing16),
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing20),
      decoration: BoxDecoration(
        color: KaiaDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
        boxShadow: KaiaDesignTokens.shadowSm,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Event title
          Text(
            widget.title,
            style: const TextStyle(
              fontSize: KaiaDesignTokens.fontSize2Xl,
              fontWeight: KaiaDesignTokens.fontWeightBold,
              color: KaiaDesignTokens.neutralGray900,
              height: 1.2,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: KaiaDesignTokens.spacing16),

          // Date and time
          _buildInfoRow(
            Icons.calendar_today,
            '${widget.date} • ${widget.time}',
            KaiaDesignTokens.primaryIndigo,
          ),
          const SizedBox(height: KaiaDesignTokens.spacing8),

          // Location
          _buildInfoRow(
            Icons.location_on,
            widget.location,
            KaiaDesignTokens.secondaryCyan,
          ),
          const SizedBox(height: KaiaDesignTokens.spacing8),

          // Attendees
          _buildInfoRow(
            Icons.people,
            '${widget.attendees} people attending',
            KaiaDesignTokens.successGreen,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String text, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(KaiaDesignTokens.spacing6),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusSm),
          ),
          child: Icon(
            icon,
            size: 16,
            color: color,
          ),
        ),
        const SizedBox(width: KaiaDesignTokens.spacing12),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(
              fontSize: KaiaDesignTokens.fontSizeMd,
              fontWeight: KaiaDesignTokens.fontWeightMedium,
              color: KaiaDesignTokens.neutralGray700,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildEventDetails() {
    return Container(
      margin:
          const EdgeInsets.symmetric(horizontal: KaiaDesignTokens.spacing16),
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing20),
      decoration: BoxDecoration(
        color: KaiaDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
        boxShadow: KaiaDesignTokens.shadowSm,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Event Highlights',
            style: TextStyle(
              fontSize: KaiaDesignTokens.fontSizeLg,
              fontWeight: KaiaDesignTokens.fontWeightBold,
              color: KaiaDesignTokens.neutralGray900,
            ),
          ),
          const SizedBox(height: KaiaDesignTokens.spacing16),

          // Highlights list
          ..._getEventHighlights().map((highlight) {
            return Container(
              margin: const EdgeInsets.only(bottom: KaiaDesignTokens.spacing12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 2),
                    width: 6,
                    height: 6,
                    decoration: const BoxDecoration(
                      color: KaiaDesignTokens.primaryIndigo,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: KaiaDesignTokens.spacing12),
                  Expanded(
                    child: Text(
                      highlight,
                      style: const TextStyle(
                        fontSize: KaiaDesignTokens.fontSizeMd,
                        color: KaiaDesignTokens.neutralGray700,
                        height: 1.5,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildDescription() {
    final description = _getEventDescription();
    final isLongDescription = description.length > 200;

    return Container(
      margin: const EdgeInsets.all(KaiaDesignTokens.spacing16),
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing20),
      decoration: BoxDecoration(
        color: KaiaDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
        boxShadow: KaiaDesignTokens.shadowSm,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'About This Event',
            style: TextStyle(
              fontSize: KaiaDesignTokens.fontSizeLg,
              fontWeight: KaiaDesignTokens.fontWeightBold,
              color: KaiaDesignTokens.neutralGray900,
            ),
          ),
          const SizedBox(height: KaiaDesignTokens.spacing16),
          AnimatedCrossFade(
            duration: const Duration(milliseconds: 300),
            crossFadeState: _isDescriptionExpanded || !isLongDescription
                ? CrossFadeState.showSecond
                : CrossFadeState.showFirst,
            firstChild: Text(
              '${description.substring(0, 200)}...',
              style: const TextStyle(
                fontSize: KaiaDesignTokens.fontSizeMd,
                color: KaiaDesignTokens.neutralGray700,
                height: 1.6,
              ),
            ),
            secondChild: Text(
              description,
              style: const TextStyle(
                fontSize: KaiaDesignTokens.fontSizeMd,
                color: KaiaDesignTokens.neutralGray700,
                height: 1.6,
              ),
            ),
          ),
          if (isLongDescription) ...[
            const SizedBox(height: KaiaDesignTokens.spacing12),
            GestureDetector(
              onTap: () {
                setState(() {
                  _isDescriptionExpanded = !_isDescriptionExpanded;
                });
              },
              child: Text(
                _isDescriptionExpanded ? 'Show Less' : 'Read More',
                style: const TextStyle(
                  fontSize: KaiaDesignTokens.fontSizeMd,
                  fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                  color: KaiaDesignTokens.primaryIndigo,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOrganizerSection() {
    final organizer = _getEventOrganizer();

    return Container(
      margin:
          const EdgeInsets.symmetric(horizontal: KaiaDesignTokens.spacing16),
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing20),
      decoration: BoxDecoration(
        color: KaiaDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
        boxShadow: KaiaDesignTokens.shadowSm,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Event Organizer',
            style: TextStyle(
              fontSize: KaiaDesignTokens.fontSizeLg,
              fontWeight: KaiaDesignTokens.fontWeightBold,
              color: KaiaDesignTokens.neutralGray900,
            ),
          ),
          const SizedBox(height: KaiaDesignTokens.spacing16),
          Row(
            children: [
              // Organizer avatar
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(KaiaDesignTokens.radiusFull),
                  boxShadow: KaiaDesignTokens.shadowSm,
                ),
                child: ClipRRect(
                  borderRadius:
                      BorderRadius.circular(KaiaDesignTokens.radiusFull),
                  child: Image.network(
                    organizer['imageUrl'],
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: KaiaDesignTokens.neutralGray200,
                        child: const Icon(
                          Icons.person,
                          color: KaiaDesignTokens.neutralGray500,
                        ),
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(width: KaiaDesignTokens.spacing16),

              // Organizer info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      organizer['name'],
                      style: const TextStyle(
                        fontSize: KaiaDesignTokens.fontSizeLg,
                        fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                        color: KaiaDesignTokens.neutralGray900,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: KaiaDesignTokens.spacing4),
                    Text(
                      organizer['title'],
                      style: const TextStyle(
                        fontSize: KaiaDesignTokens.fontSizeMd,
                        color: KaiaDesignTokens.neutralGray600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: KaiaDesignTokens.spacing4),
                    Row(
                      children: [
                        const Icon(
                          Icons.star,
                          size: 16,
                          color: KaiaDesignTokens.warningAmber,
                        ),
                        const SizedBox(width: KaiaDesignTokens.spacing4),
                        Text(
                          '${organizer['rating']} (${organizer['reviewCount']} reviews)',
                          style: const TextStyle(
                            fontSize: KaiaDesignTokens.fontSizeSm,
                            color: KaiaDesignTokens.neutralGray600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Contact button
              OutlinedButton(
                onPressed: _contactOrganizer,
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: KaiaDesignTokens.primaryIndigo),
                  shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.circular(KaiaDesignTokens.radiusSm),
                  ),
                ),
                child: const Text(
                  'Contact',
                  style: TextStyle(
                    color: KaiaDesignTokens.primaryIndigo,
                    fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLocationSection() {
    return Container(
      margin: const EdgeInsets.all(KaiaDesignTokens.spacing16),
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing20),
      decoration: BoxDecoration(
        color: KaiaDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
        boxShadow: KaiaDesignTokens.shadowSm,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Location & Venue',
            style: TextStyle(
              fontSize: KaiaDesignTokens.fontSizeLg,
              fontWeight: KaiaDesignTokens.fontWeightBold,
              color: KaiaDesignTokens.neutralGray900,
            ),
          ),
          const SizedBox(height: KaiaDesignTokens.spacing16),

          // Location info
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(KaiaDesignTokens.spacing8),
                decoration: BoxDecoration(
                  color: KaiaDesignTokens.secondaryCyan.withOpacity(0.1),
                  borderRadius:
                      BorderRadius.circular(KaiaDesignTokens.radiusSm),
                ),
                child: const Icon(
                  Icons.location_on,
                  color: KaiaDesignTokens.secondaryCyan,
                  size: 20,
                ),
              ),
              const SizedBox(width: KaiaDesignTokens.spacing12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.location,
                      style: const TextStyle(
                        fontSize: KaiaDesignTokens.fontSizeMd,
                        fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                        color: KaiaDesignTokens.neutralGray900,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: KaiaDesignTokens.spacing4),
                    const Text(
                      'Tap to view on map',
                      style: TextStyle(
                        fontSize: KaiaDesignTokens.fontSizeSm,
                        color: KaiaDesignTokens.neutralGray600,
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: _openMap,
                icon: const Icon(
                  Icons.map,
                  color: KaiaDesignTokens.primaryIndigo,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPricingSection() {
    return Container(
      margin:
          const EdgeInsets.symmetric(horizontal: KaiaDesignTokens.spacing16),
      padding: const EdgeInsets.all(KaiaDesignTokens.spacing20),
      decoration: BoxDecoration(
        color: KaiaDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
        boxShadow: KaiaDesignTokens.shadowSm,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Pricing & Tickets',
            style: TextStyle(
              fontSize: KaiaDesignTokens.fontSizeLg,
              fontWeight: KaiaDesignTokens.fontWeightBold,
              color: KaiaDesignTokens.neutralGray900,
            ),
          ),
          const SizedBox(height: KaiaDesignTokens.spacing16),

          // Price display
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: KaiaDesignTokens.spacing16,
                  vertical: KaiaDesignTokens.spacing8,
                ),
                decoration: BoxDecoration(
                  color: KaiaDesignTokens.successGreen.withOpacity(0.1),
                  borderRadius:
                      BorderRadius.circular(KaiaDesignTokens.radiusSm),
                ),
                child: Text(
                  widget.price == 'Free' ? 'FREE' : widget.price,
                  style: TextStyle(
                    fontSize: KaiaDesignTokens.fontSizeLg,
                    fontWeight: KaiaDesignTokens.fontWeightBold,
                    color: widget.price == 'Free'
                        ? KaiaDesignTokens.successGreen
                        : KaiaDesignTokens.primaryIndigo,
                  ),
                ),
              ),
              const Spacer(),
              if (widget.price != 'Free')
                const Text(
                  'per person',
                  style: TextStyle(
                    fontSize: KaiaDesignTokens.fontSizeSm,
                    color: KaiaDesignTokens.neutralGray600,
                  ),
                ),
            ],
          ),

          if (widget.price != 'Free') ...[
            const SizedBox(height: KaiaDesignTokens.spacing12),
            const Text(
              '• Includes all activities and materials\n• Group discounts available for 5+ people\n• Refundable up to 24 hours before event',
              style: TextStyle(
                fontSize: KaiaDesignTokens.fontSizeSm,
                color: KaiaDesignTokens.neutralGray600,
                height: 1.5,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRelatedEventsSection() {
    final relatedEvents = _getRelatedEvents();

    return Container(
      margin: const EdgeInsets.all(KaiaDesignTokens.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'You Might Also Like',
            style: TextStyle(
              fontSize: KaiaDesignTokens.fontSizeLg,
              fontWeight: KaiaDesignTokens.fontWeightBold,
              color: KaiaDesignTokens.neutralGray900,
            ),
          ),
          const SizedBox(height: KaiaDesignTokens.spacing16),
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: relatedEvents.length,
              itemBuilder: (context, index) {
                final event = relatedEvents[index];
                return Container(
                  width: 280,
                  margin: EdgeInsets.only(
                    right: index < relatedEvents.length - 1
                        ? KaiaDesignTokens.spacing16
                        : 0,
                  ),
                  child: _buildRelatedEventCard(event),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRelatedEventCard(Map<String, dynamic> event) {
    return Container(
      decoration: BoxDecoration(
        color: KaiaDesignTokens.neutralWhite,
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
        boxShadow: KaiaDesignTokens.shadowSm,
      ),
      child: InkWell(
        onTap: () => _navigateToEvent(event['id']),
        borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Event image
            ClipRRect(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(KaiaDesignTokens.radiusLg),
              ),
              child: Image.network(
                event['imageUrl'],
                height: 100,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 100,
                    color: KaiaDesignTokens.neutralGray200,
                    child: const Center(
                      child: Icon(
                        Icons.image_not_supported,
                        color: KaiaDesignTokens.neutralGray500,
                      ),
                    ),
                  );
                },
              ),
            ),

            // Event info
            Padding(
              padding: const EdgeInsets.all(KaiaDesignTokens.spacing12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    event['title'],
                    style: const TextStyle(
                      fontSize: KaiaDesignTokens.fontSizeMd,
                      fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                      color: KaiaDesignTokens.neutralGray900,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: KaiaDesignTokens.spacing4),
                  Text(
                    event['date'],
                    style: const TextStyle(
                      fontSize: KaiaDesignTokens.fontSizeSm,
                      color: KaiaDesignTokens.neutralGray600,
                    ),
                  ),
                  const SizedBox(height: KaiaDesignTokens.spacing4),
                  Text(
                    event['price'],
                    style: const TextStyle(
                      fontSize: KaiaDesignTokens.fontSizeSm,
                      fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                      color: KaiaDesignTokens.primaryIndigo,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomActionBar() {
    return Container(
      padding: EdgeInsets.only(
        left: KaiaDesignTokens.spacing16,
        right: KaiaDesignTokens.spacing16,
        top: KaiaDesignTokens.spacing16,
        bottom:
            MediaQuery.of(context).padding.bottom + KaiaDesignTokens.spacing16,
      ),
      decoration: BoxDecoration(
        color: KaiaDesignTokens.neutralWhite,
        boxShadow: [
          BoxShadow(
            color: KaiaDesignTokens.neutralGray900.withOpacity(0.1),
            offset: const Offset(0, -2),
            blurRadius: 8,
          ),
        ],
      ),
      child: Row(
        children: [
          // Price display
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Price',
                style: TextStyle(
                  fontSize: KaiaDesignTokens.fontSizeSm,
                  color: KaiaDesignTokens.neutralGray600,
                ),
              ),
              Text(
                widget.price,
                style: const TextStyle(
                  fontSize: KaiaDesignTokens.fontSizeLg,
                  fontWeight: KaiaDesignTokens.fontWeightBold,
                  color: KaiaDesignTokens.neutralGray900,
                ),
              ),
            ],
          ),
          const SizedBox(width: KaiaDesignTokens.spacing16),

          // Action buttons
          Expanded(
            child: Row(
              children: [
                // Register/Book button
                Expanded(
                  flex: 3,
                  child: ElevatedButton(
                    onPressed: _registerForEvent,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: KaiaDesignTokens.primaryIndigo,
                      foregroundColor: KaiaDesignTokens.neutralWhite,
                      padding: const EdgeInsets.symmetric(
                        vertical: KaiaDesignTokens.spacing16,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(KaiaDesignTokens.radiusLg),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      widget.price == 'Free' ? 'Register' : 'Book Now',
                      style: const TextStyle(
                        fontSize: KaiaDesignTokens.fontSizeMd,
                        fontWeight: KaiaDesignTokens.fontWeightSemiBold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: KaiaDesignTokens.spacing12),

                // Share button
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: KaiaDesignTokens.neutralGray300),
                    borderRadius:
                        BorderRadius.circular(KaiaDesignTokens.radiusLg),
                  ),
                  child: IconButton(
                    onPressed: _shareEvent,
                    icon: const Icon(
                      Icons.share,
                      color: KaiaDesignTokens.neutralGray700,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Event handlers
  void _toggleFavorite() {
    setState(() {
      _isFavorited = !_isFavorited;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _isFavorited ? 'Added to favorites' : 'Removed from favorites',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _shareEvent() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality coming soon'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _registerForEvent() {
    // TODO: Navigate to booking/registration flow
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          widget.price == 'Free'
              ? 'Registration functionality coming soon'
              : 'Booking functionality coming soon',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _contactOrganizer() {
    // TODO: Implement contact organizer functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Contact functionality coming soon'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _openMap() {
    // TODO: Implement map functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Map functionality coming soon'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _navigateToEvent(String eventId) {
    // TODO: Navigate to another event details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Navigate to event: $eventId'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  // Mock data methods
  List<String> _getEventHighlights() {
    switch (widget.category.toLowerCase()) {
      case 'festival':
        return [
          'Traditional performances and cultural displays',
          'Local food vendors and authentic cuisine',
          'Interactive workshops and activities',
          'Live music and entertainment',
          'Family-friendly atmosphere',
        ];
      case 'food tour':
        return [
          'Visit 5+ authentic local restaurants',
          'Expert local guide with insider knowledge',
          'Taste traditional dishes and specialties',
          'Learn about culinary history and culture',
          'Small group experience (max 12 people)',
        ];
      case 'history':
        return [
          'Professional historian guide',
          'Access to historical sites and monuments',
          'Fascinating stories and local legends',
          'Educational and entertaining experience',
          'Photo opportunities at iconic locations',
        ];
      default:
        return [
          'Unique cultural experience',
          'Expert local guidance',
          'Authentic local interactions',
          'Educational and fun activities',
          'Memorable photo opportunities',
        ];
    }
  }

  String _getEventDescription() {
    switch (widget.category.toLowerCase()) {
      case 'festival':
        return 'Join us for an unforgettable celebration of culture and tradition! This vibrant festival brings together the best of local customs, featuring traditional performances, authentic cuisine, and interactive experiences that will immerse you in the rich heritage of the region. From colorful parades to hands-on workshops, there\'s something for everyone to enjoy. Local artisans will showcase their crafts, while food vendors offer delicious traditional dishes that have been passed down through generations. The festival creates a perfect opportunity to connect with locals, learn about their customs, and create lasting memories in a festive, welcoming atmosphere.';
      case 'food tour':
        return 'Embark on a culinary journey through the heart of the city\'s most authentic neighborhoods! Our expert local guide will take you to hidden gems and family-run establishments that locals have cherished for generations. You\'ll taste signature dishes, learn about cooking techniques, and discover the stories behind each recipe. This isn\'t just about eating – it\'s about understanding the culture through its cuisine. We\'ll visit bustling markets, cozy tavernas, and specialty shops, giving you insider access to the city\'s best-kept culinary secrets. Perfect for food lovers who want to experience authentic flavors and connect with local food traditions.';
      case 'history':
        return 'Step back in time and explore the fascinating history that shaped this remarkable destination. Led by a professional historian, this walking tour will take you through ancient streets, past magnificent monuments, and into the stories that define this place. You\'ll discover hidden historical gems, learn about significant events, and understand how the past continues to influence the present. From architectural marvels to archaeological sites, every stop reveals another layer of the rich historical tapestry. This educational yet entertaining experience is perfect for history enthusiasts and curious travelers who want to gain deeper insights into the cultural heritage of the region.';
      default:
        return 'Experience something truly special with this unique cultural event that showcases the best of local traditions and contemporary culture. Whether you\'re a first-time visitor or a seasoned traveler, this event offers fresh perspectives and authentic experiences that will enrich your understanding of the destination. Join fellow culture enthusiasts in an engaging environment where learning meets entertainment, and every moment offers new discoveries.';
    }
  }

  Map<String, dynamic> _getEventOrganizer() {
    return {
      'name': 'Cultural Experiences Co.',
      'title': 'Local Event Organizer',
      'imageUrl':
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      'rating': 4.8,
      'reviewCount': 127,
    };
  }

  List<Map<String, dynamic>> _getRelatedEvents() {
    return [
      {
        'id': 'related_1',
        'title': 'Traditional Cooking Workshop',
        'date': 'April 15, 2025',
        'price': '\$35',
        'imageUrl':
            'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      },
      {
        'id': 'related_2',
        'title': 'Art Gallery Opening Night',
        'date': 'April 18, 2025',
        'price': 'Free',
        'imageUrl':
            'https://images.unsplash.com/photo-1541961017774-22349e4a1262?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      },
      {
        'id': 'related_3',
        'title': 'Local Music Concert',
        'date': 'April 22, 2025',
        'price': '\$20',
        'imageUrl':
            'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      },
    ];
  }
}
