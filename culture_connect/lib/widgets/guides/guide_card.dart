import 'package:flutter/material.dart';
import 'package:culture_connect/theme/kaia_design_tokens.dart';
import 'package:culture_connect/models/guide.dart';
import 'package:culture_connect/utils/price_formatter.dart';

/// Individual guide card component for the guides listing screen
/// Follows CultureConnect design system with premium aesthetics
class GuideCard extends StatelessWidget {
  final Guide guide;
  final VoidCallback onPressed;

  const GuideCard({
    super.key,
    required this.guide,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: KaiaDesignTokens.neutralWhite,
          borderRadius: BorderRadius.circular(KaiaDesignTokens.radiusLg),
          boxShadow: KaiaDesignTokens.shadowMd,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile Header Section
            Padding(
              padding: const EdgeInsets.all(KaiaDesignTokens.spacing16),
              child: Row(
                children: [
                  // Profile Image
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: KaiaDesignTokens.neutralGray200,
                        width: 2,
                      ),
                      boxShadow: KaiaDesignTokens.shadowSm,
                    ),
                    child: ClipOval(
                      child: guide.profileImageUrl != null
                          ? Image.network(
                              guide.profileImageUrl!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  color: KaiaDesignTokens.neutralGray100,
                                  child: Icon(
                                    Icons.person,
                                    color: KaiaDesignTokens.neutralGray400,
                                    size: 30,
                                  ),
                                );
                              },
                            )
                          : Container(
                              color: KaiaDesignTokens.neutralGray100,
                              child: Icon(
                                Icons.person,
                                color: KaiaDesignTokens.neutralGray400,
                                size: 30,
                              ),
                            ),
                    ),
                  ),

                  const SizedBox(width: KaiaDesignTokens.spacing12),

                  // Guide Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Flexible(
                              child: Text(
                                guide.name,
                                style: TextStyle(
                                  color: KaiaDesignTokens.neutralGray800,
                                  fontSize: KaiaDesignTokens.fontSizeLg,
                                  fontWeight:
                                      KaiaDesignTokens.fontWeightSemiBold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (guide.isCertified) ...[
                              const SizedBox(width: KaiaDesignTokens.spacing6),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: KaiaDesignTokens.spacing6,
                                  vertical: KaiaDesignTokens.spacing2,
                                ),
                                decoration: BoxDecoration(
                                  color: KaiaDesignTokens.primaryIndigo,
                                  borderRadius: BorderRadius.circular(
                                      KaiaDesignTokens.radiusXs),
                                ),
                                child: Text(
                                  'Certified',
                                  style: TextStyle(
                                    color: KaiaDesignTokens.neutralWhite,
                                    fontSize: KaiaDesignTokens.fontSizeXs,
                                    fontWeight:
                                        KaiaDesignTokens.fontWeightMedium,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),

                        const SizedBox(height: KaiaDesignTokens.spacing4),

                        if (guide.location != null)
                          Row(
                            children: [
                              Icon(
                                Icons.location_on_outlined,
                                color: KaiaDesignTokens.neutralGray500,
                                size: 14,
                              ),
                              const SizedBox(width: KaiaDesignTokens.spacing4),
                              Flexible(
                                child: Text(
                                  guide.location!,
                                  style: TextStyle(
                                    color: KaiaDesignTokens.neutralGray500,
                                    fontSize: KaiaDesignTokens.fontSizeSm,
                                    fontWeight:
                                        KaiaDesignTokens.fontWeightRegular,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),

                        const SizedBox(height: KaiaDesignTokens.spacing4),

                        // Rating and Experience
                        Row(
                          children: [
                            if (guide.rating != null) ...[
                              Icon(
                                Icons.star,
                                color: KaiaDesignTokens.warningAmber,
                                size: 16,
                              ),
                              const SizedBox(width: KaiaDesignTokens.spacing4),
                              Text(
                                guide.rating!.toStringAsFixed(1),
                                style: TextStyle(
                                  color: KaiaDesignTokens.neutralGray700,
                                  fontSize: KaiaDesignTokens.fontSizeSm,
                                  fontWeight: KaiaDesignTokens.fontWeightMedium,
                                ),
                              ),
                              if (guide.reviewCount != null) ...[
                                const SizedBox(
                                    width: KaiaDesignTokens.spacing4),
                                Text(
                                  '(${guide.reviewCount})',
                                  style: TextStyle(
                                    color: KaiaDesignTokens.neutralGray500,
                                    fontSize: KaiaDesignTokens.fontSizeSm,
                                    fontWeight:
                                        KaiaDesignTokens.fontWeightRegular,
                                  ),
                                ),
                              ],
                              const SizedBox(width: KaiaDesignTokens.spacing12),
                            ],
                            Text(
                              '${guide.yearsOfExperience} years exp.',
                              style: TextStyle(
                                color: KaiaDesignTokens.neutralGray500,
                                fontSize: KaiaDesignTokens.fontSizeSm,
                                fontWeight: KaiaDesignTokens.fontWeightRegular,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Price Range
                  if (guide.priceRange.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: KaiaDesignTokens.spacing8,
                        vertical: KaiaDesignTokens.spacing4,
                      ),
                      decoration: BoxDecoration(
                        color: KaiaDesignTokens.secondaryCyan.withOpacity(0.1),
                        borderRadius:
                            BorderRadius.circular(KaiaDesignTokens.radiusSm),
                      ),
                      child: Text(
                        PriceFormatter.formatPriceWithLocation(
                            guide.priceRange, guide.location ?? ''),
                        style: TextStyle(
                          color: KaiaDesignTokens.secondaryCyan,
                          fontSize: KaiaDesignTokens.fontSizeXs,
                          fontWeight: KaiaDesignTokens.fontWeightMedium,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Specialties Section
            if (guide.specialties.isNotEmpty)
              Padding(
                padding: const EdgeInsets.fromLTRB(
                  KaiaDesignTokens.spacing16,
                  0,
                  KaiaDesignTokens.spacing16,
                  KaiaDesignTokens.spacing12,
                ),
                child: Wrap(
                  spacing: KaiaDesignTokens.spacing8,
                  runSpacing: KaiaDesignTokens.spacing6,
                  children: guide.specialties.take(3).map((specialty) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: KaiaDesignTokens.spacing8,
                        vertical: KaiaDesignTokens.spacing4,
                      ),
                      decoration: BoxDecoration(
                        color: KaiaDesignTokens.neutralGray100,
                        borderRadius:
                            BorderRadius.circular(KaiaDesignTokens.radiusSm),
                      ),
                      child: Text(
                        specialty,
                        style: TextStyle(
                          color: KaiaDesignTokens.neutralGray600,
                          fontSize: KaiaDesignTokens.fontSizeXs,
                          fontWeight: KaiaDesignTokens.fontWeightMedium,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),

            // Service Highlights Section
            if (guide.serviceHighlights.isNotEmpty)
              Padding(
                padding: const EdgeInsets.fromLTRB(
                  KaiaDesignTokens.spacing16,
                  0,
                  KaiaDesignTokens.spacing16,
                  KaiaDesignTokens.spacing16,
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.verified_outlined,
                      color: KaiaDesignTokens.successGreen,
                      size: 16,
                    ),
                    const SizedBox(width: KaiaDesignTokens.spacing6),
                    Expanded(
                      child: Text(
                        guide.serviceHighlights.take(2).join(' • '),
                        style: TextStyle(
                          color: KaiaDesignTokens.neutralGray600,
                          fontSize: KaiaDesignTokens.fontSizeSm,
                          fontWeight: KaiaDesignTokens.fontWeightRegular,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (guide.responseTime.isNotEmpty) ...[
                      const SizedBox(width: KaiaDesignTokens.spacing8),
                      Text(
                        guide.responseTime,
                        style: TextStyle(
                          color: KaiaDesignTokens.primaryIndigo,
                          fontSize: KaiaDesignTokens.fontSizeXs,
                          fontWeight: KaiaDesignTokens.fontWeightMedium,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
