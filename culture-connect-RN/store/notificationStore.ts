import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Notification } from '@/types/notification';
import { notifications as mockNotifications } from '@/mocks/notifications';

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  isOverlayVisible: boolean;
  displayedNotifications: Notification[];
  currentPage: number;
  notificationsPerPage: number;
}

interface NotificationActions {
  loadNotifications: () => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearNotification: (id: string) => void;
  clearAllNotifications: () => void;
  showOverlay: () => void;
  hideOverlay: () => void;
  loadMoreNotifications: () => void;
  getUnreadCount: () => number;
}

type NotificationStore = NotificationState & NotificationActions;

// Helper function to convert string timestamps back to Date objects
const deserializeNotifications = (notifications: any[]): Notification[] => {
  return notifications.map(notification => ({
    ...notification,
    timestamp: new Date(notification.timestamp)
  }));
};

// Helper function to serialize Date objects to strings
const serializeNotifications = (notifications: Notification[]): any[] => {
  return notifications.map(notification => ({
    ...notification,
    timestamp: notification.timestamp.toISOString()
  }));
};

export const useNotificationStore = create<NotificationStore>()(
  persist(
    (set, get) => ({
      notifications: [],
      unreadCount: 0,
      isOverlayVisible: false,
      displayedNotifications: [],
      currentPage: 0,
      notificationsPerPage: 5,

      loadNotifications: () => {
        const { notifications, notificationsPerPage } = get();
        if (notifications.length === 0) {
          const sortedNotifications = mockNotifications.sort(
            (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
          );
          const unreadCount = sortedNotifications.filter(n => !n.isRead).length;
          const displayedNotifications = sortedNotifications.slice(0, notificationsPerPage);
          
          set({
            notifications: sortedNotifications,
            unreadCount,
            displayedNotifications,
            currentPage: 0
          });
        } else {
          // Recalculate displayed notifications and unread count
          const unreadCount = notifications.filter(n => !n.isRead).length;
          const displayedNotifications = notifications.slice(0, notificationsPerPage);
          
          set({
            unreadCount,
            displayedNotifications
          });
        }
      },

      markAsRead: (id: string) => {
        set((state) => {
          const updatedNotifications = state.notifications.map(notification =>
            notification.id === id ? { ...notification, isRead: true } : notification
          );
          const unreadCount = updatedNotifications.filter(n => !n.isRead).length;
          const updatedDisplayed = state.displayedNotifications.map(notification =>
            notification.id === id ? { ...notification, isRead: true } : notification
          );
          
          return {
            notifications: updatedNotifications,
            unreadCount,
            displayedNotifications: updatedDisplayed
          };
        });
      },

      markAllAsRead: () => {
        set((state) => {
          const updatedNotifications = state.notifications.map(notification => ({
            ...notification,
            isRead: true
          }));
          const updatedDisplayed = state.displayedNotifications.map(notification => ({
            ...notification,
            isRead: true
          }));
          
          return {
            notifications: updatedNotifications,
            unreadCount: 0,
            displayedNotifications: updatedDisplayed
          };
        });
      },

      clearNotification: (id: string) => {
        set((state) => {
          const updatedNotifications = state.notifications.filter(n => n.id !== id);
          const updatedDisplayed = state.displayedNotifications.filter(n => n.id !== id);
          const unreadCount = updatedNotifications.filter(n => !n.isRead).length;
          
          return {
            notifications: updatedNotifications,
            unreadCount,
            displayedNotifications: updatedDisplayed
          };
        });
      },

      clearAllNotifications: () => {
        set({
          notifications: [],
          unreadCount: 0,
          displayedNotifications: [],
          currentPage: 0
        });
      },

      showOverlay: () => {
        set({ isOverlayVisible: true });
      },

      hideOverlay: () => {
        set({ isOverlayVisible: false });
      },

      loadMoreNotifications: () => {
        set((state) => {
          const { notifications, currentPage, notificationsPerPage } = state;
          const nextPage = currentPage + 1;
          const startIndex = 0;
          const endIndex = (nextPage + 1) * notificationsPerPage;
          const newDisplayedNotifications = notifications.slice(startIndex, endIndex);
          
          return {
            displayedNotifications: newDisplayedNotifications,
            currentPage: nextPage
          };
        });
      },

      getUnreadCount: () => {
        return get().unreadCount;
      }
    }),
    {
      name: 'notification-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        notifications: serializeNotifications(state.notifications),
        unreadCount: state.unreadCount
      }),
      onRehydrateStorage: () => (state) => {
        if (state?.notifications) {
          state.notifications = deserializeNotifications(state.notifications as any);
          // Recalculate displayed notifications after rehydration
          const { notificationsPerPage } = state;
          state.displayedNotifications = state.notifications.slice(0, notificationsPerPage);
          state.currentPage = 0;
        }
      }
    }
  )
);