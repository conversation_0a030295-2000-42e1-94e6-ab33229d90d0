import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';

/// Service to check AR device compatibility and capabilities
class ARDeviceCompatibilityService {
  static const String _logTag = 'ARDeviceCompatibility';
  
  /// Check if the current device supports AR features
  static Future<ARCompatibilityResult> checkARCompatibility() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      
      if (Platform.isAndroid) {
        return await _checkAndroidARCompatibility(deviceInfo);
      } else if (Platform.isIOS) {
        return await _checkIOSARCompatibility(deviceInfo);
      } else {
        return ARCompatibilityResult(
          isSupported: false,
          reason: 'Platform not supported',
          recommendations: ['AR is only supported on Android and iOS devices'],
        );
      }
    } catch (e) {
      debugPrint('$_logTag: Error checking AR compatibility: $e');
      return ARCompatibilityResult(
        isSupported: false,
        reason: 'Error checking device compatibility: $e',
        recommendations: ['Please try again or contact support'],
      );
    }
  }
  
  /// Check Android AR compatibility
  static Future<ARCompatibilityResult> _checkAndroidARCompatibility(
    DeviceInfoPlugin deviceInfo,
  ) async {
    try {
      final androidInfo = await deviceInfo.androidInfo;
      final recommendations = <String>[];
      
      // Check Android version (ARCore requires Android 7.0+)
      if (androidInfo.version.sdkInt < 24) {
        return ARCompatibilityResult(
          isSupported: false,
          reason: 'Android version too old (requires Android 7.0+)',
          recommendations: ['Update your Android device to version 7.0 or higher'],
        );
      }
      
      // Check RAM (ARCore recommends 3GB+ for optimal performance)
      // Note: This is an approximation as exact RAM isn't always available
      final isLowEndDevice = _isLowEndAndroidDevice(androidInfo);
      if (isLowEndDevice) {
        recommendations.add('Device may have limited AR performance due to hardware constraints');
        recommendations.add('Close other apps before using AR features');
      }
      
      // Check for known incompatible devices
      final deviceModel = '${androidInfo.manufacturer} ${androidInfo.model}'.toLowerCase();
      if (_isKnownIncompatibleDevice(deviceModel)) {
        return ARCompatibilityResult(
          isSupported: false,
          reason: 'Device model not compatible with ARCore',
          recommendations: [
            'This device model is not supported by ARCore',
            'Check Google\'s ARCore supported devices list for alternatives'
          ],
        );
      }
      
      return ARCompatibilityResult(
        isSupported: true,
        reason: 'Device supports AR features',
        recommendations: recommendations,
        deviceInfo: ARDeviceInfo(
          platform: 'Android',
          version: androidInfo.version.release,
          model: '${androidInfo.manufacturer} ${androidInfo.model}',
          isHighPerformance: !isLowEndDevice,
        ),
      );
    } catch (e) {
      debugPrint('$_logTag: Error checking Android compatibility: $e');
      return ARCompatibilityResult(
        isSupported: false,
        reason: 'Error checking Android device compatibility',
        recommendations: ['Please try again or contact support'],
      );
    }
  }
  
  /// Check iOS AR compatibility
  static Future<ARCompatibilityResult> _checkIOSARCompatibility(
    DeviceInfoPlugin deviceInfo,
  ) async {
    try {
      final iosInfo = await deviceInfo.iosInfo;
      final recommendations = <String>[];
      
      // Check iOS version (ARKit requires iOS 11.0+)
      final version = iosInfo.systemVersion.split('.').map(int.parse).toList();
      if (version.first < 11) {
        return ARCompatibilityResult(
          isSupported: false,
          reason: 'iOS version too old (requires iOS 11.0+)',
          recommendations: ['Update your iOS device to version 11.0 or higher'],
        );
      }
      
      // Check device model for ARKit compatibility
      final deviceModel = iosInfo.model.toLowerCase();
      if (!_isARKitCompatibleDevice(deviceModel)) {
        return ARCompatibilityResult(
          isSupported: false,
          reason: 'Device model not compatible with ARKit',
          recommendations: [
            'This device model does not support ARKit',
            'ARKit requires iPhone 6s or later, iPad Pro, or iPad (5th generation) or later'
          ],
        );
      }
      
      // Check for optimal performance devices
      final isHighPerformance = _isHighPerformanceIOSDevice(deviceModel);
      if (!isHighPerformance) {
        recommendations.add('Device may have limited AR performance');
        recommendations.add('Close other apps before using AR features');
      }
      
      return ARCompatibilityResult(
        isSupported: true,
        reason: 'Device supports AR features',
        recommendations: recommendations,
        deviceInfo: ARDeviceInfo(
          platform: 'iOS',
          version: iosInfo.systemVersion,
          model: iosInfo.model,
          isHighPerformance: isHighPerformance,
        ),
      );
    } catch (e) {
      debugPrint('$_logTag: Error checking iOS compatibility: $e');
      return ARCompatibilityResult(
        isSupported: false,
        reason: 'Error checking iOS device compatibility',
        recommendations: ['Please try again or contact support'],
      );
    }
  }
  
  /// Check if Android device is low-end
  static bool _isLowEndAndroidDevice(AndroidDeviceInfo androidInfo) {
    // This is a heuristic based on known low-end device characteristics
    final model = androidInfo.model.toLowerCase();
    final manufacturer = androidInfo.manufacturer.toLowerCase();
    
    // Known low-end device patterns
    if (model.contains('go') || 
        model.contains('lite') || 
        model.contains('mini') ||
        (manufacturer == 'samsung' && model.contains('a0')) ||
        (manufacturer == 'xiaomi' && model.contains('redmi') && model.contains('a'))) {
      return true;
    }
    
    return false;
  }
  
  /// Check if device is known to be incompatible
  static bool _isKnownIncompatibleDevice(String deviceModel) {
    // List of known incompatible devices (this would be maintained based on testing)
    final incompatiblePatterns = [
      'emulator',
      'simulator',
      'generic',
    ];
    
    return incompatiblePatterns.any((pattern) => deviceModel.contains(pattern));
  }
  
  /// Check if iOS device is ARKit compatible
  static bool _isARKitCompatibleDevice(String deviceModel) {
    // ARKit compatible devices (iPhone 6s+, iPad Pro, iPad 5th gen+)
    final compatiblePatterns = [
      'iphone6s',
      'iphone7',
      'iphone8',
      'iphonex',
      'iphone11',
      'iphone12',
      'iphone13',
      'iphone14',
      'iphone15',
      'iphonese',
      'ipadpro',
      'ipad6', // iPad 6th generation
      'ipad7', // iPad 7th generation
      'ipad8', // iPad 8th generation
      'ipad9', // iPad 9th generation
      'ipadair',
      'ipadmini5',
      'ipadmini6',
    ];
    
    return compatiblePatterns.any((pattern) => 
      deviceModel.toLowerCase().replaceAll(' ', '').contains(pattern));
  }
  
  /// Check if iOS device is high performance
  static bool _isHighPerformanceIOSDevice(String deviceModel) {
    final highPerformancePatterns = [
      'iphone11',
      'iphone12',
      'iphone13',
      'iphone14',
      'iphone15',
      'ipadpro',
      'ipadair4',
      'ipadair5',
      'ipadmini6',
    ];
    
    return highPerformancePatterns.any((pattern) => 
      deviceModel.toLowerCase().replaceAll(' ', '').contains(pattern));
  }
}

/// Result of AR compatibility check
class ARCompatibilityResult {
  final bool isSupported;
  final String reason;
  final List<String> recommendations;
  final ARDeviceInfo? deviceInfo;
  
  const ARCompatibilityResult({
    required this.isSupported,
    required this.reason,
    required this.recommendations,
    this.deviceInfo,
  });
  
  @override
  String toString() {
    return 'ARCompatibilityResult(isSupported: $isSupported, reason: $reason, recommendations: $recommendations)';
  }
}

/// Device information for AR capabilities
class ARDeviceInfo {
  final String platform;
  final String version;
  final String model;
  final bool isHighPerformance;
  
  const ARDeviceInfo({
    required this.platform,
    required this.version,
    required this.model,
    required this.isHighPerformance,
  });
  
  @override
  String toString() {
    return 'ARDeviceInfo(platform: $platform, version: $version, model: $model, isHighPerformance: $isHighPerformance)';
  }
}
