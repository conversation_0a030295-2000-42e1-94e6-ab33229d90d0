# Travel Insurance Feature Guide

## Overview

The Travel Insurance feature in CultureConnect provides comprehensive insurance coverage for travelers, including policy search, purchase, claims management, and provider comparison. This guide covers the complete implementation, user flows, API integration, and troubleshooting.

## Table of Contents

1. [Feature Overview](#feature-overview)
2. [User Flows](#user-flows)
3. [Architecture](#architecture)
4. [API Integration](#api-integration)
5. [UI Components](#ui-components)
6. [Testing](#testing)
7. [Troubleshooting](#troubleshooting)
8. [Developer Guide](#developer-guide)

## Feature Overview

### Key Features

- **Insurance Search**: Find and compare travel insurance policies
- **Policy Purchase**: Complete insurance purchase flow with payment integration
- **Claims Management**: Submit and track insurance claims
- **Provider Comparison**: Compare different insurance providers and policies
- **Policy Management**: View and manage purchased insurance policies

### Coverage Types

The system supports 12 different coverage types:

1. **Medical Coverage** - Medical expenses due to illness or injury
2. **Trip Cancellation** - Reimbursement for prepaid, nonrefundable trip costs
3. **Trip Interruption** - Reimbursement if trip is interrupted after departure
4. **Baggage Delay** - Coverage for essential items if baggage is delayed
5. **Baggage Loss/Damage** - Coverage for lost, stolen, or damaged baggage
6. **Travel Delay** - Coverage for additional expenses due to travel delays
7. **Missed Connection** - Coverage if you miss a connection due to covered delay
8. **Emergency Evacuation** - Emergency medical evacuation and repatriation
9. **Accidental Death** - Coverage for accidental death or dismemberment
10. **Rental Car Damage** - Coverage for damage to rental car during trip
11. **Adventure Activities** - Coverage for injuries during adventure activities
12. **Pre-existing Conditions** - Coverage for pre-existing medical conditions

## User Flows

### 1. Insurance Search Flow

```
Home Screen → Travel Services → Insurance → Search
↓
Enter travel details (destination, dates, travelers)
↓
View search results with policy comparison
↓
Filter by coverage type, price, provider
↓
Select policy for detailed view
```

### 2. Policy Purchase Flow

```
Policy Details → Purchase Button
↓
Trip Details Form (dates, destinations, travelers)
↓
Traveler Information Collection
↓
Payment Method Selection
↓
Terms and Conditions Acceptance
↓
Purchase Confirmation
```

### 3. Claims Submission Flow

```
My Policies → Select Policy → Submit Claim
↓
Policy Selection
↓
Incident Details (date, description, location)
↓
Claim Amount Input
↓
Document Upload (receipts, photos, reports)
↓
Claim Submission Confirmation
```

### 4. Claims Tracking Flow

```
Claims Dashboard → Select Claim
↓
View claim status and timeline
↓
View submitted documents
↓
Check settlement details
↓
Update claim if additional info requested
```

## Architecture

### Core Components

```
├── models/travel/insurance/
│   ├── insurance_provider.dart
│   ├── insurance_policy.dart
│   ├── insurance_coverage.dart
│   ├── insurance_claim.dart
│   ├── insurance_coverage_type.dart
│   └── insurance_claim_status.dart
├── services/travel/insurance/
│   ├── insurance_service.dart
│   └── insurance_api_service.dart
├── screens/travel/insurance/
│   ├── insurance_home_screen.dart
│   ├── insurance_search_screen.dart
│   ├── insurance_purchase_screen.dart
│   ├── claim_submission_screen.dart
│   └── claims_dashboard_screen.dart
├── widgets/travel/insurance/
│   ├── insurance_card.dart
│   ├── insurance_policy_card.dart
│   ├── insurance_claim_card.dart
│   ├── coverage_breakdown_widget.dart
│   └── claim_status_widget.dart
└── providers/travel/insurance/
    └── insurance_providers.dart
```

### Data Models

#### InsuranceProvider
```dart
class InsuranceProvider {
  final String id;
  final String name;
  final String description;
  final String logoUrl;
  final double rating;
  final int reviewCount;
  final bool isFeatured;
  final bool isPartner;
  final List<String> countries;
}
```

#### InsurancePolicy
```dart
class InsurancePolicy {
  final String id;
  final String name;
  final InsurancePolicyType type;
  final InsuranceProvider provider;
  final double price;
  final String currency;
  final List<InsuranceCoverage> coverages;
  final InsurancePolicyStatus status;
}
```

#### InsuranceClaim
```dart
class InsuranceClaim {
  final String id;
  final InsurancePolicy policy;
  final InsuranceCoverageType coverageType;
  final InsuranceClaimStatus status;
  final DateTime incidentDate;
  final String incidentDescription;
  final double claimAmount;
  final String referenceNumber;
}
```

## API Integration

### Backend Integration Points

The insurance feature requires integration with the following backend APIs:

#### 1. Insurance Policies API

**Endpoint**: `GET /api/v1/insurance/policies`

**Purpose**: Retrieve available insurance policies

**Request Parameters**:
```json
{
  "destinationCountries": ["France", "Italy"],
  "startDate": "2024-06-01",
  "endDate": "2024-06-15",
  "travelerCount": 2,
  "coverageTypes": ["medical", "cancellation"]
}
```

**Response Format**:
```json
{
  "policies": [
    {
      "id": "policy-1",
      "name": "Comprehensive Travel Insurance",
      "type": "singleTrip",
      "provider": {
        "id": "provider-1",
        "name": "Global Protect Insurance",
        "rating": 4.7
      },
      "price": 89.99,
      "currency": "USD",
      "coverages": [
        {
          "type": "medical",
          "amount": 100000,
          "isIncluded": true
        }
      ]
    }
  ]
}
```

#### 2. Policy Purchase API

**Endpoint**: `POST /api/v1/insurance/policies/{policyId}/purchase`

**Purpose**: Purchase an insurance policy

**Request Body**:
```json
{
  "tripDetails": {
    "startDate": "2024-06-01",
    "endDate": "2024-06-15",
    "destinationCountries": ["France"],
    "travelerCount": 2
  },
  "travelers": [
    {
      "firstName": "John",
      "lastName": "Doe",
      "dateOfBirth": "1990-01-01",
      "passportNumber": "A12345678"
    }
  ],
  "paymentMethod": {
    "type": "card",
    "token": "payment-token"
  }
}
```

#### 3. Claims Submission API

**Endpoint**: `POST /api/v1/insurance/claims`

**Purpose**: Submit a new insurance claim

**Request Body**:
```json
{
  "policyId": "policy-1",
  "coverageType": "medical",
  "incidentDate": "2024-06-05",
  "incidentDescription": "Medical emergency",
  "incidentLocation": "Paris, France",
  "claimAmount": 2500.00,
  "documents": [
    {
      "type": "receipt",
      "url": "https://storage.example.com/receipt.pdf"
    }
  ]
}
```

#### 4. Claims Tracking API

**Endpoint**: `GET /api/v1/insurance/claims/{claimId}`

**Purpose**: Get claim status and details

**Response Format**:
```json
{
  "id": "claim-1",
  "status": "inReview",
  "submittedDate": "2024-06-08",
  "lastUpdatedDate": "2024-06-10",
  "timeline": [
    {
      "status": "submitted",
      "date": "2024-06-08",
      "description": "Claim submitted successfully"
    },
    {
      "status": "inReview",
      "date": "2024-06-09",
      "description": "Claim is under review"
    }
  ]
}
```

### Authentication

All API calls require authentication using Bearer tokens:

```dart
headers: {
  'Authorization': 'Bearer $userToken',
  'Content-Type': 'application/json',
}
```

### Error Handling

The API uses standard HTTP status codes:

- `200` - Success
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid token)
- `404` - Not Found (resource doesn't exist)
- `500` - Internal Server Error

Error response format:
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid travel dates",
    "details": {
      "field": "startDate",
      "reason": "Start date cannot be in the past"
    }
  }
}
```

## UI Components

### Insurance Cards

The insurance feature uses several card components for displaying information:

#### InsuranceCard
- Displays provider or policy information
- Supports horizontal and vertical layouts
- Configurable display options (rating, price, coverage)
- Action buttons for viewing details and purchasing

#### InsurancePolicyCard
- Specialized for displaying policy information
- Shows policy type, provider, price, and status
- Supports different layouts and interaction callbacks

#### InsuranceClaimCard
- Displays claim information and status
- Shows reference number, amount, and timeline
- Status-specific styling and icons

### Coverage Display

#### CoverageBreakdownWidget
- Shows detailed coverage information
- Filterable by included/excluded coverages
- Expandable sections for detailed descriptions
- Visual indicators for coverage status

#### ClaimStatusWidget
- Displays claim status with appropriate styling
- Timeline view for claim progress
- Status-specific colors and icons
- Interactive elements for status updates

## Testing

### Unit Tests

The insurance feature includes comprehensive unit tests:

```bash
# Run insurance model tests
flutter test test/models/travel/insurance/

# Run insurance service tests
flutter test test/services/travel/insurance/

# Run insurance widget tests
flutter test test/widgets/travel/insurance/
```

### Test Coverage

- **Models**: 95%+ coverage for all insurance models
- **Services**: 90%+ coverage for business logic and API integration
- **Widgets**: 85%+ coverage for UI components and interactions

### Mock Data

Test fixtures are available for:
- Sample insurance providers
- Test policies with various coverage types
- Mock claims with different statuses
- API response examples

## Troubleshooting

### Common Issues

#### 1. Policy Search Returns No Results

**Symptoms**: Empty search results despite valid parameters

**Possible Causes**:
- Invalid destination countries
- Date range issues
- No policies available for criteria

**Solutions**:
1. Verify destination country names match API expectations
2. Check date range is valid (future dates, reasonable duration)
3. Broaden search criteria (remove optional filters)
4. Check API connectivity and authentication

#### 2. Purchase Flow Fails

**Symptoms**: Error during policy purchase

**Possible Causes**:
- Payment method issues
- Invalid traveler information
- Policy no longer available

**Solutions**:
1. Validate payment method and billing information
2. Ensure all required traveler fields are completed
3. Refresh policy availability before purchase
4. Check for policy-specific restrictions

#### 3. Claim Submission Errors

**Symptoms**: Unable to submit insurance claim

**Possible Causes**:
- Missing required documents
- Invalid claim amount
- Policy not active or expired

**Solutions**:
1. Ensure all required documents are uploaded
2. Verify claim amount is within policy limits
3. Check policy status and coverage dates
4. Validate incident date is within coverage period

### Debug Mode

Enable debug logging for insurance operations:

```dart
// In main.dart or app initialization
Logger.setLevel(LogLevel.debug);

// Insurance service will log detailed information
final insuranceService = InsuranceService();
await insuranceService.initialize();
```

### API Testing

Use the following tools for API testing:

1. **Postman Collection**: Available in `docs/api/insurance_api.postman_collection.json`
2. **API Documentation**: Swagger UI at `/api/docs`
3. **Test Environment**: Use staging API endpoints for testing

## Developer Guide

### Adding New Coverage Types

1. Update `InsuranceCoverageType` enum:
```dart
enum InsuranceCoverageType {
  // ... existing types
  newCoverageType(
    displayName: 'New Coverage',
    description: 'Description of new coverage',
    icon: Icons.new_icon,
  ),
}
```

2. Update UI components to handle new type
3. Add tests for new coverage type
4. Update documentation

### Extending Claims Workflow

1. Add new claim status to `InsuranceClaimStatus` enum
2. Update claim status widget styling
3. Implement new workflow logic in service layer
4. Add corresponding API integration
5. Update tests and documentation

### Custom Provider Integration

1. Implement provider-specific API service
2. Create provider adapter for common interface
3. Add provider configuration
4. Update provider selection logic
5. Test integration thoroughly

### Performance Optimization

1. **Caching**: Implement policy and provider caching
2. **Pagination**: Add pagination for large result sets
3. **Image Optimization**: Optimize provider logos and claim documents
4. **Offline Support**: Cache essential data for offline access

### Security Considerations

1. **Data Encryption**: Encrypt sensitive traveler information
2. **Token Management**: Secure storage of authentication tokens
3. **Input Validation**: Validate all user inputs on client and server
4. **Document Security**: Secure upload and storage of claim documents

---

## Support

For technical support or questions about the insurance feature:

1. Check this documentation first
2. Review test cases for usage examples
3. Check API documentation for backend integration
4. Contact the development team for additional support

**Last Updated**: December 2024
**Version**: 1.0.0
