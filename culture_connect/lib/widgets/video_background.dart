import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

/// A reusable video background widget that handles video loading, scaling,
/// error handling, and fallback scenarios for splash screens and other uses.
class VideoBackground extends StatefulWidget {
  final String videoPath;
  final Color fallbackColor;
  final Widget? child;
  final BoxFit fit;
  final bool loop;
  final bool autoPlay;
  final VoidCallback? onVideoLoaded;
  final VoidCallback? onVideoError;

  const VideoBackground({
    super.key,
    required this.videoPath,
    required this.fallbackColor,
    this.child,
    this.fit = BoxFit.cover,
    this.loop = true,
    this.autoPlay = true,
    this.onVideoLoaded,
    this.onVideoError,
  });

  @override
  State<VideoBackground> createState() => _VideoBackgroundState();
}

class _VideoBackgroundState extends State<VideoBackground>
    with WidgetsBindingObserver {
  VideoPlayerController? _controller;
  bool _isVideoReady = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeVideo();
  }

  Future<void> _initializeVideo() async {
    try {
      // Add timeout to prevent indefinite loading
      _controller = VideoPlayerController.asset(widget.videoPath);

      await _controller!.initialize().timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          throw Exception('Video initialization timeout');
        },
      );

      if (mounted) {
        setState(() {
          _isVideoReady = true;
        });

        // Configure video settings
        if (widget.loop) {
          _controller!.setLooping(true);
        }

        if (widget.autoPlay) {
          _controller!.play();
        }

        // Set volume to 0 for splash screen (no audio needed)
        _controller!.setVolume(0.0);

        // Callback for successful video loading
        widget.onVideoLoaded?.call();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
        });

        // Clean up controller on error
        _controller?.dispose();
        _controller = null;

        // Callback for video error
        widget.onVideoError?.call();

        // Log error for debugging
        debugPrint('VideoBackground: Failed to load video - $e');
      }
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _controller?.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_controller != null && _isVideoReady && !_hasError) {
      switch (state) {
        case AppLifecycleState.paused:
        case AppLifecycleState.inactive:
          _controller!.pause();
          break;
        case AppLifecycleState.resumed:
          if (widget.autoPlay) {
            _controller!.play();
          }
          break;
        case AppLifecycleState.detached:
        case AppLifecycleState.hidden:
          _controller!.pause();
          break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: widget.fallbackColor,
      child: Stack(
        children: [
          // Video layer (only shown when ready and no error)
          if (_isVideoReady && !_hasError && _controller != null)
            Positioned.fill(
              child: _buildVideoPlayer(),
            ),

          // Content overlay
          if (widget.child != null)
            Positioned.fill(
              child: widget.child!,
            ),
        ],
      ),
    );
  }

  /// Build video player with proper scaling for full screen coverage
  Widget _buildVideoPlayer() {
    if (_controller == null || !_controller!.value.isInitialized) {
      return Container();
    }

    final videoAspectRatio = _controller!.value.aspectRatio;

    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final screenHeight = constraints.maxHeight;
        final screenAspectRatio = screenWidth / screenHeight;

        double videoWidth;
        double videoHeight;

        // Calculate dimensions to fill screen completely (may crop video)
        if (videoAspectRatio > screenAspectRatio) {
          // Video is wider than screen - fit to height and crop sides
          videoHeight = screenHeight;
          videoWidth = videoHeight * videoAspectRatio;
        } else {
          // Video is taller than screen - fit to width and crop top/bottom
          videoWidth = screenWidth;
          videoHeight = videoWidth / videoAspectRatio;
        }

        return Center(
          child: SizedBox(
            width: videoWidth,
            height: videoHeight,
            child: VideoPlayer(_controller!),
          ),
        );
      },
    );
  }
}

/// A specialized video background widget optimized for splash screens
class SplashVideoBackground extends StatelessWidget {
  final String videoPath;
  final Color fallbackColor;
  final Widget child;

  const SplashVideoBackground({
    super.key,
    required this.videoPath,
    required this.fallbackColor,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return VideoBackground(
      videoPath: videoPath,
      fallbackColor: fallbackColor,
      fit: BoxFit.cover,
      loop: true,
      autoPlay: true,
      onVideoError: () {
        // Log error but don't show user-facing error for splash screen
        debugPrint(
            'SplashVideoBackground: Video failed to load, using fallback');
      },
      child: child,
    );
  }
}
